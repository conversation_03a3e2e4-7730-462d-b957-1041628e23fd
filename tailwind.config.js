/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./**/*.latte', './www/js/**/*.{html,js}'],
	theme: {
		container: {
			center: true,
			screens: {
				sm: '640px',
				md: '768px',
				lg: '1024px',
				xl: '1240px',
				'2xl': '1240px',
			},
			padding: '20px',
		},
		extend: {
			fontFamily: {
				body: ['Roboto', 'sans-serif'],
				consolas: ['Consolas', 'monospace']
			},
			boxShadow: {
				custom: '0px 15px 83px 0px rgba(0, 0, 0, 0.08)',
			},
			colors: {
				primary: {
					orange: '#EF7F1A',
					'blue-dark': '#182B4A',
					'blue-second-dark': '#101B2D',
				},
				secondary: {
					green: '#66B940',
					'light-green': 'rgba(240, 248, 236, 0.50)',
					yellow: '#FDBB47',
					'old-lavender': '#6B5E62',
					'linen-grey': '#F0E4D9',
					red: '#F72F49',
				},
				pastel: {
					'green-light': '#F0F8EC',
					'yellow-light': '#FFF5E3',
					'linen-grey-light': '#F8F3EE',
					'orange-light': '#FEF3E9',
					'red-light': '#FFEBED;;',
				},
				dark: {
					1: '#080B10',
					2: '#646C7C',
					3: '#80899C',
					4: '#ADB3BF',
				},
				light: {
					1: '#BDC2CC',
					2: '#CCD0D7',
					3: '#D7DBE0',
					4: '#E1E4E8',
					5: '#ECEDF0',
					6: '#F4F4F6',
				},
			},
			animation: {
				marquee: 'marquee 10s linear infinite',
				marquee2: 'marquee2 10s linear infinite',
			},
			keyframes: {
				marquee: {
					'0%': { transform: 'translateX(0%)' },
					'100%': { transform: 'translateX(-100%)' },
				},
				marquee2: {
					'0%': { transform: 'translateX(100%)' },
					'100%': { transform: 'translateX(0%)' },
				},
			},
		},
		backgroundImage: {
			'orange-gradient':
				'linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%)',
			'orange-gradient-hover':
				'linear-gradient(309deg, #EF7F1A 13.11%, #FFA439 96.21%)',
			'green-gradient':
				'linear-gradient(90deg, #FFF 24.84%, #F0F8EC 52.65%);',
			'gray-gradient-hover':
				'linear-gradient(90deg, #F3F4F6 0%, #E5E7EB 100%)',
			'blue-dark-gradient-hover':
				'linear-gradient(90deg, #182B4A 10%, #304E73 90%)',
		},
	},
	plugins: [
		require('@tailwindcss/typography'),
		require('@tailwindcss/forms'),
	],
}
