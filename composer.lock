{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9c49a418f02ffb235919088891200a26", "packages": [{"name": "latte/latte", "version": "v3.0.20", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/application": "<3.1.7", "nette/caching": "<3.1.4"}, "require-dev": {"nette/php-generator": "^4.0", "nette/tester": "^2.5", "nette/utils": "^4.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.10"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-intl": "to use Latte\\Engine::setLocale()", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v3.0.20"}, "time": "2024-10-08T00:58:27+00:00"}, {"name": "nette/application", "version": "v3.2.6", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "9c288cc45df467dc012504f4ad64791279720af8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/9c288cc45df467dc012504f4ad64791279720af8", "reference": "9c288cc45df467dc012504f4ad64791279720af8", "shasum": ""}, "require": {"nette/component-model": "^3.1", "nette/http": "^3.3", "nette/routing": "^3.1", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "conflict": {"latte/latte": "<2.7.1 || >=3.0.0 <3.0.18 || >=3.1", "nette/caching": "<3.2", "nette/di": "<3.2", "nette/forms": "<3.2", "nette/schema": "<1.3", "tracy/tracy": "<2.9"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "latte/latte": "^2.10.2 || ^3.0.18", "mockery/mockery": "^2.0", "nette/di": "^3.2", "nette/forms": "^3.2", "nette/robot-loader": "^4.0", "nette/security": "^3.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.2.6"}, "time": "2024-09-10T10:08:04+00:00"}, {"name": "nette/bootstrap", "version": "v3.2.5", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "91d08432cb33d6c08d58b215c769d04f20580624"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/91d08432cb33d6c08d58b215c769d04f20580624", "reference": "91d08432cb33d6c08d58b215c769d04f20580624", "shasum": ""}, "require": {"nette/di": "^3.1", "nette/utils": "^3.2.1 || ^4.0", "php": "8.0 - 8.4"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8 || ^3.0", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/robot-loader": "^3.0 || ^4.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.2.5"}, "time": "2024-11-14T00:49:46+00:00"}, {"name": "nette/caching", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "b37d2c9647b41a9d04f099f10300dc5496c4eb77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/b37d2c9647b41a9d04f099f10300dc5496c4eb77", "reference": "b37d2c9647b41a9d04f099f10300dc5496c4eb77", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.0 - 8.4"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12"}, "require-dev": {"latte/latte": "^2.11 || ^3.0.12", "nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "psr/simple-cache": "^2.0 || ^3.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.3.1"}, "time": "2024-08-07T00:01:58+00:00"}, {"name": "nette/component-model", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77", "reference": "fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.1.1"}, "time": "2024-08-07T00:35:59+00:00"}, {"name": "nette/database", "version": "v3.2.6", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "cb825ac1cffe1ede98388a9c4e6c4445c26b961d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/cb825ac1cffe1ede98388a9c4e6c4445c26b961d", "reference": "cb825ac1cffe1ede98388a9c4e6c4445c26b961d", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^3.2", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "^1.6", "nette/di": "^3.1", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "support": {"issues": "https://github.com/nette/database/issues", "source": "https://github.com/nette/database/tree/v3.2.6"}, "time": "2025-01-12T15:33:57+00:00"}, {"name": "nette/di", "version": "v3.2.4", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "57f923a7af32435b6e4921c0adbc70c619625a17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/57f923a7af32435b6e4921c0adbc70c619625a17", "reference": "57f923a7af32435b6e4921c0adbc70c619625a17", "shasum": ""}, "require": {"ext-ctype": "*", "ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^4.1.6", "nette/robot-loader": "^4.0", "nette/schema": "^1.2.5", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5.2", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.2.4"}, "time": "2025-01-10T04:57:37+00:00"}, {"name": "nette/forms", "version": "v3.2.5", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "7e59cee3a16e0382f83680c94babb85a0a167dd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/7e59cee3a16e0382f83680c94babb85a0a167dd0", "reference": "7e59cee3a16e0382f83680c94babb85a0a167dd0", "shasum": ""}, "require": {"nette/component-model": "^3.1", "nette/http": "^3.3", "nette/utils": "^4.0.4", "php": "8.1 - 8.4"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12 || >=3.1"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0.12", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.5.2", "phpstan/phpstan-nette": "^1", "tracy/tracy": "^2.9"}, "suggest": {"ext-intl": "to use date/time controls"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.2.5"}, "time": "2024-10-22T18:42:14+00:00"}, {"name": "nette/http", "version": "v3.3.2", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "shasum": ""}, "require": {"nette/utils": "^4.0.4", "php": "8.1 - 8.4"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect MIME type of uploaded files by Nette\\Http\\FileUpload", "ext-gd": "to use image function in Nette\\Http\\FileUpload", "ext-intl": "to support punycode by Nette\\Http\\Url", "ext-session": "to use Nette\\Http\\Session"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.3.2"}, "time": "2025-01-12T16:27:57+00:00"}, {"name": "nette/mail", "version": "v4.0.3", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "d99839701c48031d6f35e3be95bdd9418f66ad2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/d99839701c48031d6f35e3be95bdd9418f66ad2d", "reference": "d99839701c48031d6f35e3be95bdd9418f66ad2d", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^4.0", "php": "8.0 - 8.4"}, "require-dev": {"nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: A handy library for creating and sending emails in PHP.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v4.0.3"}, "time": "2024-10-05T03:15:12+00:00"}, {"name": "nette/neon", "version": "v3.4.4", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/3411aa86b104e2d5b7e760da4600865ead963c3c", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c", "shasum": ""}, "require": {"ext-json": "*", "php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.4.4"}, "time": "2024-10-04T22:00:08+00:00"}, {"name": "nette/php-generator", "version": "v4.1.7", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "d201c9bc217e0969d1b678d286be49302972fb56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/d201c9bc217e0969d1b678d286be49302972fb56", "reference": "d201c9bc217e0969d1b678d286be49302972fb56", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.4"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.18 || ^5.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.7"}, "time": "2024-11-29T01:41:18+00:00"}, {"name": "nette/robot-loader", "version": "v4.0.3", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "45d67753fb4865bb718e9a6c9be69cc9470137b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/45d67753fb4865bb718e9a6c9be69cc9470137b7", "reference": "45d67753fb4865bb718e9a6c9be69cc9470137b7", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/utils": "^4.0", "php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v4.0.3"}, "time": "2024-06-18T20:26:39+00:00"}, {"name": "nette/routing", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "5b0782d3b50af68614253a373fa663ed03206a3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/5b0782d3b50af68614253a373fa663ed03206a3f", "reference": "5b0782d3b50af68614253a373fa663ed03206a3f", "shasum": ""}, "require": {"nette/http": "^3.2 || ~4.0.0", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5", "phpstan/phpstan": "^1", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.1.1"}, "time": "2024-11-04T11:59:47+00:00"}, {"name": "nette/schema", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "da801d52f0354f70a638673c4a0f04e16529431d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/da801d52f0354f70a638673c4a0f04e16529431d", "reference": "da801d52f0354f70a638673c4a0f04e16529431d", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5.2", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.2"}, "time": "2024-10-06T23:10:23+00:00"}, {"name": "nette/security", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "6e19bf604934aec0cd3343a307e28fd997e40e96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/6e19bf604934aec0cd3343a307e28fd997e40e96", "reference": "6e19bf604934aec0cd3343a307e28fd997e40e96", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"mockery/mockery": "^1.5", "nette/di": "^3.1", "nette/http": "^3.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.2.1"}, "time": "2024-11-04T12:25:05+00:00"}, {"name": "nette/utils", "version": "v4.0.5", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.5"}, "time": "2024-08-07T15:39:19+00:00"}, {"name": "tracy/tracy", "version": "v2.10.9", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "e7af75205b184ca8895bc57fafd331f8d5022d26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/e7af75205b184ca8895bc57fafd331f8d5022d26", "reference": "e7af75205b184ca8895bc57fafd331f8d5022d26", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5 || ^3.0", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/tester": "^2.2", "nette/utils": "^3.0 || ^4.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.10.9"}, "time": "2024-11-07T14:48:00+00:00"}], "packages-dev": [{"name": "nette/tester", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/nette/tester.git", "reference": "c11863785779e87b40adebf150364f2e5938c111"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tester/zipball/c11863785779e87b40adebf150364f2e5938c111", "reference": "c11863785779e87b40adebf150364f2e5938c111", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "require-dev": {"ext-simplexml": "*", "phpstan/phpstan": "^1.0"}, "bin": ["src/tester"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/milo"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Tester: enjoyable unit testing in PHP with code coverage reporter. 🍏🍏🍎🍏", "homepage": "https://tester.nette.org", "keywords": ["Xdebug", "assertions", "clover", "code coverage", "nette", "pcov", "phpdbg", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/nette/tester/issues", "source": "https://github.com/nette/tester/tree/v2.5.4"}, "time": "2024-10-23T23:57:10+00:00"}, {"name": "symfony/thanks", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/symfony/thanks.git", "reference": "ad3f07af819f058666f0cac3f0737f18d31e3d05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/thanks/zipball/ad3f07af819f058666f0cac3f0737f18d31e3d05", "reference": "ad3f07af819f058666f0cac3f0737f18d31e3d05", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=8.1"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Thanks\\Thanks", "branch-alias": {"dev-main": "1.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Thanks\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Encourages sending ⭐ and 💵 to fellow PHP package maintainers (not limited to Symfony components)!", "support": {"issues": "https://github.com/symfony/thanks/issues", "source": "https://github.com/symfony/thanks/tree/v1.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-01T09:47:21+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">= 8.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}