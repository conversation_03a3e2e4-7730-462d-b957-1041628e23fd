{% extends 'base.html.twig' %}

{% block title %}DataDex - Profesionální databáze firemních kontaktů{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-purple-800">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
                Profesionální databáze<br>
                <span class="text-yellow-300">firemn<PERSON>ch kontaktů</span>
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto animate-slide-up">
                Získejte přístup k více než <strong class="text-yellow-300">{{ total_contacts|number_format(0, ',', ' ') }}</strong> ověřených firemních kontaktů z České republiky a Slovenska
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
                <button class="btn-primary text-lg px-8 py-4 bg-yellow-500 hover:bg-yellow-400 text-gray-900">
                    Získat přístup za {{ price_per_year }} Kč/rok
                </button>
                <a href="#preview" class="btn-secondary text-lg px-8 py-4 bg-transparent border-white text-white hover:bg-white hover:text-primary-600">
                    Zobrazit náhled dat
                </a>
            </div>
        </div>
    </div>
    
    <!-- Floating elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-300 rounded-full opacity-20 animate-bounce-slow"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-blue-300 rounded-full opacity-20 animate-bounce-slow" style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-purple-300 rounded-full opacity-20 animate-bounce-slow" style="animation-delay: 2s;"></div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Proč zvolit DataDex?
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Naše databáze vám poskytne všechny nástroje potřebné pro úspěšný B2B marketing a prodej
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {% for feature in features %}
            <div class="card p-6 text-center hover:scale-105 transition-transform duration-300">
                <div class="w-12 h-12 bg-gradient-to-r from-primary-600 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    {% if loop.index == 1 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    {% elseif loop.index == 2 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    {% elseif loop.index == 3 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    {% elseif loop.index == 4 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    {% elseif loop.index == 5 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    {% elseif loop.index == 6 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    {% elseif loop.index == 7 %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    {% else %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    {% endif %}
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ feature }}</h3>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Data Preview Section -->
<section id="preview" class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Náhled dat v databázi
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Podívejte se na ukázku dat, která získáte přístupem k naší databázi
            </p>
        </div>
        
        <div class="card overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-primary-600 to-purple-600">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Firma</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Kontaktní osoba</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Pozice</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Email</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Telefon</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Město</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Odvětví</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for contact in sample_contacts %}
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ contact.company }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ contact.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ contact.position }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-primary-600">{{ contact.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ contact.phone }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ contact.city }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800">
                                    {{ contact.industry }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <p class="text-sm text-gray-600 text-center">
                    <strong>{{ total_contacts|number_format(0, ',', ' ') }}</strong> podobných záznamů čeká na vaše objevení
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Jednoduchá a transparentní cena
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Bez skrytých poplatků, bez omezení. Jeden roční poplatek za neomezený přístup.
            </p>
        </div>

        <div class="max-w-lg mx-auto">
            <div class="card p-8 text-center relative overflow-hidden">
                <!-- Background decoration -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-10 transform translate-x-16 -translate-y-16"></div>

                <div class="relative">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Roční přístup</h3>
                    <div class="mb-6">
                        <span class="text-5xl font-bold text-primary-600">{{ price_per_year }}</span>
                        <span class="text-xl text-gray-600 ml-2">Kč</span>
                        <div class="text-gray-500 mt-1">za celý rok</div>
                    </div>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">Neomezený přístup k databázi</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">{{ total_contacts|number_format(0, ',', ' ') }}+ ověřených kontaktů</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">Pravidelné aktualizace</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">Export do CSV/Excel</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">API přístup</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">24/7 technická podpora</span>
                        </div>
                    </div>

                    <button class="btn-primary w-full text-lg py-4 bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700">
                        Získat přístup nyní
                    </button>

                    <p class="text-sm text-gray-500 mt-4">
                        Bez závazků • Zrušitelné kdykoliv • GDPR compliant
                    </p>
                </div>
            </div>
        </div>

        <div class="text-center mt-12">
            <p class="text-gray-600 mb-4">Potřebujete více informací?</p>
            <a href="#contact" class="text-primary-600 hover:text-primary-700 font-semibold">
                Kontaktujte nás pro individuální nabídku
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-primary-600 to-purple-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
            Připraveni začít?
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Získejte okamžitý přístup k největší databázi firemních kontaktů v ČR a SR
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="btn-primary text-lg px-8 py-4 bg-yellow-500 hover:bg-yellow-400 text-gray-900">
                Začít za {{ price_per_year }} Kč/rok
            </button>
            <button class="btn-secondary text-lg px-8 py-4 bg-transparent border-white text-white hover:bg-white hover:text-primary-600">
                Zobrazit demo
            </button>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Máte otázky?
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Náš tým je připraven vám pomoci s jakýmikoliv dotazy ohledně naší databáze
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="card p-6 text-center">
                <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Email</h3>
                <p class="text-gray-600"><EMAIL></p>
            </div>

            <div class="card p-6 text-center">
                <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Telefon</h3>
                <p class="text-gray-600">+420 123 456 789</p>
            </div>

            <div class="card p-6 text-center">
                <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Adresa</h3>
                <p class="text-gray-600">Praha, Česká republika</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
