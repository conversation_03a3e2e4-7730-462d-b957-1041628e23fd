{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@symfony/webpack-encore": "^5.0.0", "core-js": "^3.38.0", "regenerator-runtime": "^0.13.9", "tailwindcss": "^4.0.0-beta.1", "webpack": "^5.74.0", "webpack-cli": "^5.1.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "npm run build:css && encore dev", "watch": "npm run build:css && encore dev --watch", "build": "npm run build:css && encore production --progress", "build:css": "node build-css.js"}}