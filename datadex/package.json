{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@symfony/webpack-encore": "^5.0.0", "@tailwindcss/postcss": "^4.1.8", "autoprefixer": "^10.4.21", "core-js": "^3.38.0", "postcss": "^8.5.4", "postcss-loader": "^8.1.1", "regenerator-runtime": "^0.13.9", "tailwindcss": "^4.1.8", "webpack": "^5.74.0", "webpack-cli": "^5.1.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}}