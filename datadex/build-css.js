const fs = require('fs');
const path = require('path');

// Pro TailwindCSS 4 beta - jedno<PERSON><PERSON><PERSON> build script
async function buildCSS() {
  try {
    // Načteme TailwindCSS 4
    const tailwindcss = require('tailwindcss');
    
    // Načteme input CSS
    const inputCSS = fs.readFileSync('./assets/styles/tailwind.css', 'utf8');
    
    // Zpracujeme CSS
    const result = await tailwindcss.compile(inputCSS, {
      content: ['./templates/**/*.html.twig']
    });
    
    // Zapíšeme výsledek
    fs.writeFileSync('./public/build/tailwind.css', result.css);
    
    console.log('✅ TailwindCSS compiled successfully!');
  } catch (error) {
    console.error('❌ Error compiling TailwindCSS:', error.message);
    
    // Fallback - zkopírujeme základní CSS
    const basicCSS = `
/* TailwindCSS 4 Fallback */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
}

/* Reset */
*, *::before, *::after { box-sizing: border-box; }
* { margin: 0; }
body { line-height: 1.5; -webkit-font-smoothing: antialiased; }

/* Basic utilities */
.font-sans { font-family: 'Inter', ui-sans-serif, system-ui; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-white { color: white; }
.text-gray-900 { color: #111827; }
.text-gray-600 { color: #4b5563; }
.text-primary-600 { color: var(--color-primary-600); }
.bg-primary-600 { background-color: var(--color-primary-600); }
.bg-primary-700 { background-color: var(--color-primary-700); }
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.rounded-lg { border-radius: 0.5rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1); }
.transition-all { transition: all 0.15s ease-in-out; }
.hover\\:bg-primary-700:hover { background-color: var(--color-primary-700); }
.hover\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1); }
.transform { transform: translateZ(0); }
.hover\\:-translate-y-0\\.5:hover { transform: translateY(-0.125rem); }

/* Custom components */
.btn-primary {
  background-color: var(--color-primary-600);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.15s ease-in-out;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  transform: translateZ(0);
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
}

.btn-primary:hover {
  background-color: var(--color-primary-700);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  transform: translateY(-0.125rem);
}

.btn-secondary {
  background-color: white;
  color: var(--color-primary-600);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.15s ease-in-out;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  transform: translateZ(0);
  border: 2px solid var(--color-primary-600);
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
}

.btn-secondary:hover {
  background-color: #f9fafb;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  transform: translateY(-0.125rem);
}
`;
    
    fs.writeFileSync('./public/build/tailwind.css', basicCSS);
    console.log('⚠️  Using fallback CSS due to TailwindCSS 4 compilation error');
  }
}

buildCSS();
