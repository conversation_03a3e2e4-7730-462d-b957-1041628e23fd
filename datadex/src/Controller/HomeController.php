<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class HomeController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    public function index(): Response
    {
        // Ukázková data pro preview tabulky
        $sampleContacts = [
            [
                'company' => 'TechCorp s.r.o.',
                'name' => 'Jan Novák',
                'position' => 'Ředitel',
                'email' => '<EMAIL>',
                'phone' => '+*********** 789',
                'city' => 'Praha',
                'industry' => 'IT & Software'
            ],
            [
                'company' => 'BuildMax a.s.',
                'name' => '<PERSON> Svobodová',
                'position' => 'Obchodní ředitelka',
                'email' => '<EMAIL>',
                'phone' => '+*********** 321',
                'city' => 'Brno',
                'industry' => 'Stavebnictví'
            ],
            [
                'company' => 'GreenEnergy spol. s r.o.',
                'name' => '<PERSON><PERSON>',
                'position' => 'CEO',
                'email' => '<EMAIL>',
                'phone' => '+*********** 456',
                'city' => 'Ostrava',
                'industry' => 'Energetika'
            ],
            [
                'company' => 'FoodChain s.r.o.',
                'name' => 'Anna Procházková',
                'position' => 'Marketing Manager',
                'email' => '<EMAIL>',
                'phone' => '+*********** 999',
                'city' => 'České Budějovice',
                'industry' => 'Potravinářství'
            ],
            [
                'company' => 'AutoParts Plus s.r.o.',
                'name' => 'Tomáš Černý',
                'position' => 'Vedoucí prodeje',
                'email' => '<EMAIL>',
                'phone' => '+*********** 888',
                'city' => 'Plzeň',
                'industry' => 'Automotive'
            ]
        ];

        return $this->render('home/index.html.twig', [
            'sample_contacts' => $sampleContacts,
            'total_contacts' => 523847,
            'price_per_year' => 2490,
            'features' => [
                'Více než 500 000 ověřených kontaktů',
                'Pravidelné aktualizace databáze',
                'Pokročilé vyhledávání a filtry',
                'Export dat do CSV/Excel',
                'API přístup pro integraci',
                'Deduplikace a čištění dat',
                '24/7 technická podpora',
                'GDPR compliance'
            ]
        ]);
    }
}
