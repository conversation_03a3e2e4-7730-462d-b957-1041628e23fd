# DataDex - Profesionální databáze firemních kontaktů

DataDex je moderní webová aplikace postavená na Symfony frameworku s TailwindCSS pro stylování. Jedná se o placenou databázi firemních kontaktů s více než 500 000 ověřených kontaktů z České republiky a Slovenska.

## Funkce

- 🎯 **Více než 500 000 ověřených kontaktů** - Rozsáhlá databáze firemních kontaktů
- 🔄 **Pravidelné aktualizace** - Data jsou pravidelně aktualizována z veřejně dostupných zdrojů
- 🔍 **Pokročilé vyhledávání** - Filtrování podle odvětví, města, pozice a dalších kritérií
- 📊 **Export dat** - Export do CSV/Excel formátů
- 🔌 **API přístup** - RESTful API pro integraci s externími systémy
- 🧹 **Deduplikace dat** - Automatické č<PERSON>ní a deduplikace záznamů
- 🛡️ **GDPR compliance** - Plná shoda s GDPR požadavky
- 🎨 **Moderní UI/UX** - Responzivní design s TailwindCSS

## Technologie

- **Backend**: Symfony 7.3
- **Frontend**: TailwindCSS 3.x
- **Build tool**: Webpack Encore
- **PHP**: 8.2+
- **Node.js**: 18+

## Instalace

### Požadavky

- PHP 8.2 nebo vyšší
- Composer
- Node.js 18+ a npm
- Git

### Kroky instalace

1. **Klonování repozitáře**
   ```bash
   git clone <repository-url>
   cd datadex
   ```

2. **Instalace PHP závislostí**
   ```bash
   composer install
   ```

3. **Instalace Node.js závislostí**
   ```bash
   npm install
   ```

4. **Kompilace assets**
   ```bash
   # Pro development
   npm run dev
   
   # Pro production
   npm run build
   
   # Pro development s watch módem
   npm run watch
   ```

5. **Spuštění development serveru**
   ```bash
   # Symfony CLI (doporučeno)
   symfony server:start
   
   # Nebo PHP built-in server
   php -S localhost:8000 -t public
   ```

6. **Otevření v prohlížeči**
   ```
   http://localhost:8000
   ```

## Struktura projektu

```
datadex/
├── assets/                 # Frontend assets (CSS, JS)
│   ├── app.js             # Hlavní JavaScript soubor
│   └── styles/
│       └── app.css        # Hlavní CSS soubor s TailwindCSS
├── config/                # Symfony konfigurace
├── public/                # Veřejně přístupné soubory
│   ├── build/             # Zkompilované assets
│   └── index.php          # Entry point
├── src/
│   └── Controller/
│       └── HomeController.php  # Hlavní controller
├── templates/             # Twig templates
│   ├── base.html.twig     # Základní layout
│   └── home/
│       └── index.html.twig # Homepage template
├── tailwind.config.js     # TailwindCSS konfigurace
├── webpack.config.js      # Webpack Encore konfigurace
└── postcss.config.js      # PostCSS konfigurace
```

## Vývoj

### Přidání nových stylů

Styly se přidávají do `assets/styles/app.css`. Projekt používá TailwindCSS utility classes a custom komponenty.

### Přidání nových stránek

1. Vytvořte nový controller v `src/Controller/`
2. Vytvořte odpovídající template v `templates/`
3. Přidejte routing do `config/routes.yaml` nebo použijte atributy

### Build proces

```bash
# Development build
npm run dev

# Production build (minifikace, optimalizace)
npm run build

# Watch mód pro development
npm run watch

# Development server s hot reload
npm run dev-server
```

## Ceny

- **Roční přístup**: 2 490 Kč/rok
- Neomezený přístup k celé databázi
- Všechny funkce zahrnuty
- 24/7 technická podpora

## Kontakt

- **Email**: <EMAIL>
- **Telefon**: +420 123 456 789
- **Adresa**: Praha, Česká republika

## Licence

© 2025 DataDex. Všechna práva vyhrazena.
