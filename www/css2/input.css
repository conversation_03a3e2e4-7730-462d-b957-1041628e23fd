@tailwind base;
@tailwind components;
@tailwind utilities;

/* roboto-300 - latin_latin-ext */
@font-face {
	font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
	font-family: 'Roboto';
	font-style: normal;
	font-weight: 300;
	src: url('../fonts/roboto-v32-latin_latin-ext-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* roboto-regular - latin_latin-ext */
@font-face {
	font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
	font-family: 'Roboto';
	font-style: normal;
	font-weight: 400;
	src: url('../fonts/roboto-v32-latin_latin-ext-regular.woff2')
		format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* roboto-500 - latin_latin-ext */
@font-face {
	font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
	font-family: 'Roboto';
	font-style: normal;
	font-weight: 500;
	src: url('../fonts/roboto-v32-latin_latin-ext-500.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* roboto-700 - latin_latin-ext */
@font-face {
	font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
	font-family: 'Roboto';
	font-style: normal;
	font-weight: 700;
	src: url('../fonts/roboto-v32-latin_latin-ext-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* roboto-900 - latin_latin-ext */
@font-face {
	font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
	font-family: 'Roboto';
	font-style: normal;
	font-weight: 900;
	src: url('../fonts/roboto-v32-latin_latin-ext-900.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

@font-face {
	font-display: swap;
	font-family: 'DS-Digital';
	font-style: normal;
	font-weight: 400;
	src: url("../fonts/ds-digital/DS-DIGI.woff2") format("woff2");
  }

body {
	@apply font-body font-normal;
}

.shadow-hover {
	@apply hover:cursor-pointer rounded-2xl duration-200 hover:shadow-lg hover:border-transparent;
}

._value {
	@apply text-[26px] lg:text-[33px] font-bold text-secondary-green;
}

._symbol {
	@apply text-lg lg:text-[22px] font-bold text-secondary-green;
}

._value.minimal {
	@apply text-dark-2 text-lg lg:text-2xl;
}

._symbol.minimal {
	@apply text-dark-2 text-xs lg:text-base;
}

._suffix {
	@apply block;
}

.reward-box--small ._suffix {
	@apply inline-block md:block;
}

.shop-offers ._symbol,
.shop-offers ._value {
	@apply text-xl;
}

.search-reward ._symbol {
	@apply text-sm;
}

.search-reward ._value {
	@apply text-lg;
}

.search-reward ._suffix {
	@apply inline;
}

:root {
	--aa-detached-media-query: none;
}

#autocomplete {
	@apply md:flex items-center w-[365px] h-[47px] bg-white rounded-[14px] border border-zinc-200 ml-9;
}

.aa-Autocomplete {
	@apply w-full;
}

.aa-Form {
	@apply flex items-center w-full pl-4 pr-[3px];
}

.aa-Panel {
	@apply absolute left-0 right-0 !w-full ml-[-63px] mt-3.5 md:ml-0 md:mt-[22px] md:!w-[477px] h-auto bg-white rounded-tl-[3px] rounded-tr-2xl rounded-bl-2xl rounded-br-2xl z-50 overflow-hidden;
}

.aa-InputWrapperPrefix {
	@apply order-1;
}

.aa-Label {
	@apply block;
}

.aa-SubmitButton {
	@apply w-[41px] h-[41px] flex items-center justify-center;
}

.aa-InputWrapper {
	@apply grow;
}

input.aa-Input {
	@apply block w-full appearance-none border-0 outline-none text-base;
}

input[type='search']:focus {
	@apply ring-transparent;
}

.aa-InputWrapperSuffix {
	@apply h-[18px];
}

input[type='search']::-webkit-search-cancel-button {
	display: none;
}

.similar-shop__value ._value {
	@apply text-lg font-bold leading-[18.75px];
}

.similar-shop__value ._symbol {
	@apply text-sm font-bold leading-[18.75px];
}

.similar-shop__value ._suffix,
.similar-shop__value ._upTo {
	@apply inline text-sm leading-[18.75px];
}

.shop-description p {
	@apply mb-4;
}

.content-block h2 {
	@apply text-dark-1 text-lg font-bold leading-[28px] mb-2.5 mt-5;
}

.content-block h3 {
	@apply text-dark-1 text-base font-bold leading-[28px] mb-2.5;
}

.content-block p {
	@apply text-dark-1 text-sm leading-[24.5px] mb-5;
}

.content-block a {
	@apply underline hover:no-underline;
}

.content-block img.img-responsive {
	@apply mb-5;
}

.content-block ul,
.content-block ol {
	@apply list-disc pl-3;
}

.content-block ol {
	@apply list-decimal;
}

.content-block li {
	@apply text-dark-1 text-sm leading-[24.5px] mb-3.5;
}

.content-block table {
	@apply w-full bg-white border-collapse mt-8 mb-8;
}

.content-block th {
	@apply text-left;
}

.content-block th,
.content-block td {
	@apply border p-2 md:px-8 md:py-4;
}

.faq-content p,
.faq-content li {
	@apply mb-5;
}

.faq-content ul {
	@apply list-disc pl-3;
}

.shop-item-dropdown__reward ._value {
	@apply text-2xl;
}

.shop-item-dropdown__reward ._symbol {
	@apply text-base;
}

/* FORM CONTENT */
.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
	@apply text-dark-1 text-[25px] font-medium leading-[39px] m-auto mb-2;
}

.content p,
.content div {
	@apply leading-[31.5px] text-lg mb-5 md:mb-10 m-auto;
}

.content img {
	@apply mb-[60px] rounded-lg;
}

.content ul {
	@apply list-disc leading-[31.5px] mb-5;
}

.content ul ul {
	@apply ml-4;
}

.content ol {
	@apply list-decimal leading-[31.5px] mb-5;
}

.content ol ol {
	@apply list-decimal leading-[31.5px] mb-5;
	counter-reset: item;
}

.content li {
	@apply mb-2;
}

.content li a {
	@apply underline;
}

.content__outline li {
	@apply mb-0;
}

.content table {
	@apply table-auto bg-white border-collapse mb-[60px];
}

.content td {
	@apply border px-4 py-2;
}

.g-recaptcha {
	@apply border-none p-0 mt-1;
}

.search-shop-inner__active {
	@apply bg-secondary-green/10 border-secondary-green backdrop-blur-[11px];
}

/* SWIPER - MOJE OBLIBENE */
.swiper-button-prev.swiper-coupon-button-prev.swiper-button-disabled {
	@apply hidden;
}
.swiper-button-next.swiper-coupon-button-next.swiper-button-disabled {
	@apply hidden;
}
.swiper-button-prev.swiper1-button-prev.swiper-button-disabled {
	@apply hidden;
}
.swiper-button-next.swiper1-button-next.swiper-button-disabled {
	@apply hidden;
}

.swiper-button-next.banner-swiper-button-next.swiper-button-disabled {
	@apply hidden;
}
.swiper-button-prev.banner-swiper-button-prev.swiper-button-disabled {
	@apply hidden;
}

.swiper-button-prev.swiper-tipli-extra-button-prev.swiper-button-disabled {
	@apply hidden;
}
.swiper-button-next.swiper-tipli-extra-button-next.swiper-button-disabled {
	@apply hidden;
}

/* TIPPY - TOOLTIPS */
.tippy-box[data-theme~='transaction-tooltip'] {
	background-color: #ecedf0;
	padding: 6px;
	border-radius: 6px;
	max-width: 288px;
	font-size: 12px;
	line-height: 21px;
	text-align: center;
	color: #000;
}
.tippy-box[data-theme~='transaction-tooltip'][data-placement^='bottom']
	> .tippy-arrow::before {
	border-bottom-color: #ecedf0;
}

.no-default-style {
	outline: none !important;
	box-shadow: none !important;
}
.no-default-style:checked {
	background-image: none !important;
	outline: none !important;
	box-shadow: none !important;
}

.footer-sticker {
	@apply absolute top-[72.5%] left-[20%];
}
@media only screen and (min-width: 310px) {
	.footer-sticker {
		@apply left-[15%];
	}
}
@media only screen and (min-width: 390px) {
	.footer-sticker {
		@apply left-[21%];
	}
}
@media only screen and (min-width: 414px) {
	.footer-sticker {
		@apply left-[23%];
	}
}
@media only screen and (min-width: 430px) {
	.footer-sticker {
		@apply left-[24%] top-[73.5%];
	}
}
@media only screen and (min-width: 500px) {
	.footer-sticker {
		@apply left-[25%] top-[78.5%];
	}
}
@media only screen and (min-width: 600px) {
	.footer-sticker {
		@apply left-[26%] top-[87.5%];
	}
}
