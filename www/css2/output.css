/*
! tailwindcss v3.4.10 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

.\!container {
  width: 100% !important;
  margin-right: auto !important;
  margin-left: auto !important;
  padding-right: 20px !important;
  padding-left: 20px !important;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 20px;
  padding-left: 20px;
}

@media (min-width: 640px) {
  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1240px) {
  .\!container {
    max-width: 1240px !important;
  }

  .container {
    max-width: 1240px;
  }
}

.form-input,.form-textarea,.form-select,.form-multiselect {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::placeholder,.form-textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

.form-input::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

.form-input::-webkit-datetime-edit {
  display: inline-flex;
}

.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

.form-checkbox,.form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

.form-checkbox {
  border-radius: 0px;
}

.form-checkbox:focus,.form-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.form-checkbox:checked,.form-radio:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  .form-checkbox:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-checkbox:checked:hover,.form-checkbox:checked:focus,.form-radio:checked:hover,.form-radio:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

.form-checkbox:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  .form-checkbox:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-checkbox:indeterminate:hover,.form-checkbox:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.\!relative {
  position: relative !important;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.\!top-\[218px\] {
  top: 218px !important;
}

.-bottom-6 {
  bottom: -1.5rem;
}

.-left-\[20px\] {
  left: -20px;
}

.-left-\[23px\] {
  left: -23px;
}

.-right-11 {
  right: -2.75rem;
}

.-right-\[23px\] {
  right: -23px;
}

.-top-10 {
  top: -2.5rem;
}

.-top-2\.5 {
  top: -0.625rem;
}

.-top-\[10px\] {
  top: -10px;
}

.-top-\[20px\] {
  top: -20px;
}

.-top-\[60px\] {
  top: -60px;
}

.-top-\[85px\] {
  top: -85px;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-1 {
  bottom: 0.25rem;
}

.bottom-10 {
  bottom: 2.5rem;
}

.bottom-2 {
  bottom: 0.5rem;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-\[-114px\] {
  bottom: -114px;
}

.bottom-\[-12px\] {
  bottom: -12px;
}

.bottom-\[-16px\] {
  bottom: -16px;
}

.bottom-\[-18px\] {
  bottom: -18px;
}

.bottom-\[-20px\] {
  bottom: -20px;
}

.bottom-\[-28px\] {
  bottom: -28px;
}

.bottom-\[-32px\] {
  bottom: -32px;
}

.bottom-\[-38px\] {
  bottom: -38px;
}

.bottom-\[-3px\] {
  bottom: -3px;
}

.bottom-\[-40px\] {
  bottom: -40px;
}

.bottom-\[-42px\] {
  bottom: -42px;
}

.bottom-\[-44px\] {
  bottom: -44px;
}

.bottom-\[-46px\] {
  bottom: -46px;
}

.bottom-\[-51px\] {
  bottom: -51px;
}

.bottom-\[-6px\] {
  bottom: -6px;
}

.bottom-\[-7px\] {
  bottom: -7px;
}

.bottom-\[118px\] {
  bottom: 118px;
}

.bottom-\[134px\] {
  bottom: 134px;
}

.bottom-\[155px\] {
  bottom: 155px;
}

.bottom-\[19px\] {
  bottom: 19px;
}

.bottom-\[1px\] {
  bottom: 1px;
}

.bottom-\[22px\] {
  bottom: 22px;
}

.bottom-\[66px\] {
  bottom: 66px;
}

.bottom-\[80px\] {
  bottom: 80px;
}

.bottom-\[90px\] {
  bottom: 90px;
}

.bottom-\[98px\] {
  bottom: 98px;
}

.end-0\.5 {
  inset-inline-end: 0.125rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-2\/4 {
  left: 50%;
}

.left-5 {
  left: 1.25rem;
}

.left-\[-1000px\] {
  left: -1000px;
}

.left-\[-10px\] {
  left: -10px;
}

.left-\[-1200px\] {
  left: -1200px;
}

.left-\[-120px\] {
  left: -120px;
}

.left-\[-1230px\] {
  left: -1230px;
}

.left-\[-130px\] {
  left: -130px;
}

.left-\[-13px\] {
  left: -13px;
}

.left-\[-14px\] {
  left: -14px;
}

.left-\[-160px\] {
  left: -160px;
}

.left-\[-16px\] {
  left: -16px;
}

.left-\[-18px\] {
  left: -18px;
}

.left-\[-250px\] {
  left: -250px;
}

.left-\[-290px\] {
  left: -290px;
}

.left-\[-2px\] {
  left: -2px;
}

.left-\[-30px\] {
  left: -30px;
}

.left-\[-40px\] {
  left: -40px;
}

.left-\[-43px\] {
  left: -43px;
}

.left-\[-6px\] {
  left: -6px;
}

.left-\[-73px\] {
  left: -73px;
}

.left-\[-74px\] {
  left: -74px;
}

.left-\[-80px\] {
  left: -80px;
}

.left-\[103px\] {
  left: 103px;
}

.left-\[10px\] {
  left: 10px;
}

.left-\[115px\] {
  left: 115px;
}

.left-\[11px\] {
  left: 11px;
}

.left-\[126px\] {
  left: 126px;
}

.left-\[135px\] {
  left: 135px;
}

.left-\[165px\] {
  left: 165px;
}

.left-\[16px\] {
  left: 16px;
}

.left-\[17\%\] {
  left: 17%;
}

.left-\[190px\] {
  left: 190px;
}

.left-\[19px\] {
  left: 19px;
}

.left-\[1px\] {
  left: 1px;
}

.left-\[20px\] {
  left: 20px;
}

.left-\[21px\] {
  left: 21px;
}

.left-\[22px\] {
  left: 22px;
}

.left-\[233px\] {
  left: 233px;
}

.left-\[249px\] {
  left: 249px;
}

.left-\[24px\] {
  left: 24px;
}

.left-\[25px\] {
  left: 25px;
}

.left-\[26px\] {
  left: 26px;
}

.left-\[270px\] {
  left: 270px;
}

.left-\[279px\] {
  left: 279px;
}

.left-\[282px\] {
  left: 282px;
}

.left-\[296px\] {
  left: 296px;
}

.left-\[307px\] {
  left: 307px;
}

.left-\[336px\] {
  left: 336px;
}

.left-\[337px\] {
  left: 337px;
}

.left-\[33px\] {
  left: 33px;
}

.left-\[346px\] {
  left: 346px;
}

.left-\[37px\] {
  left: 37px;
}

.left-\[385px\] {
  left: 385px;
}

.left-\[38px\] {
  left: 38px;
}

.left-\[40\%\] {
  left: 40%;
}

.left-\[405px\] {
  left: 405px;
}

.left-\[42px\] {
  left: 42px;
}

.left-\[45\%\] {
  left: 45%;
}

.left-\[50\%\] {
  left: 50%;
}

.left-\[52\%\] {
  left: 52%;
}

.left-\[58px\] {
  left: 58px;
}

.left-\[59px\] {
  left: 59px;
}

.left-\[5px\] {
  left: 5px;
}

.left-\[80\%\] {
  left: 80%;
}

.left-\[80px\] {
  left: 80px;
}

.left-\[829px\] {
  left: 829px;
}

.left-\[874px\] {
  left: 874px;
}

.left-\[91px\] {
  left: 91px;
}

.left-\[92\%\] {
  left: 92%;
}

.left-\[95px\] {
  left: 95px;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.right-10 {
  right: 2.5rem;
}

.right-2 {
  right: 0.5rem;
}

.right-2\.5 {
  right: 0.625rem;
}

.right-3 {
  right: 0.75rem;
}

.right-5 {
  right: 1.25rem;
}

.right-\[-103px\] {
  right: -103px;
}

.right-\[-120px\] {
  right: -120px;
}

.right-\[-122px\] {
  right: -122px;
}

.right-\[-123px\] {
  right: -123px;
}

.right-\[-12px\] {
  right: -12px;
}

.right-\[-13px\] {
  right: -13px;
}

.right-\[-156px\] {
  right: -156px;
}

.right-\[-15px\] {
  right: -15px;
}

.right-\[-175px\] {
  right: -175px;
}

.right-\[-17px\] {
  right: -17px;
}

.right-\[-19px\] {
  right: -19px;
}

.right-\[-20px\] {
  right: -20px;
}

.right-\[-21px\] {
  right: -21px;
}

.right-\[-220px\] {
  right: -220px;
}

.right-\[-230px\] {
  right: -230px;
}

.right-\[-26px\] {
  right: -26px;
}

.right-\[-28px\] {
  right: -28px;
}

.right-\[-2px\] {
  right: -2px;
}

.right-\[-30px\] {
  right: -30px;
}

.right-\[-32px\] {
  right: -32px;
}

.right-\[-3px\] {
  right: -3px;
}

.right-\[-425px\] {
  right: -425px;
}

.right-\[-44px\] {
  right: -44px;
}

.right-\[-500px\] {
  right: -500px;
}

.right-\[-533px\] {
  right: -533px;
}

.right-\[-59px\] {
  right: -59px;
}

.right-\[-60px\] {
  right: -60px;
}

.right-\[-640px\] {
  right: -640px;
}

.right-\[-680px\] {
  right: -680px;
}

.right-\[-6px\] {
  right: -6px;
}

.right-\[-70px\] {
  right: -70px;
}

.right-\[-8px\] {
  right: -8px;
}

.right-\[0px\] {
  right: 0px;
}

.right-\[10px\] {
  right: 10px;
}

.right-\[127px\] {
  right: 127px;
}

.right-\[14px\] {
  right: 14px;
}

.right-\[15px\] {
  right: 15px;
}

.right-\[18px\] {
  right: 18px;
}

.right-\[191px\] {
  right: 191px;
}

.right-\[20px\] {
  right: 20px;
}

.right-\[23px\] {
  right: 23px;
}

.right-\[25px\] {
  right: 25px;
}

.right-\[29px\] {
  right: 29px;
}

.right-\[314px\] {
  right: 314px;
}

.right-\[31px\] {
  right: 31px;
}

.right-\[32px\] {
  right: 32px;
}

.right-\[33px\] {
  right: 33px;
}

.right-\[3px\] {
  right: 3px;
}

.right-\[40\%\] {
  right: 40%;
}

.right-\[430px\] {
  right: 430px;
}

.right-\[50\%\] {
  right: 50%;
}

.right-\[63px\] {
  right: 63px;
}

.right-\[645px\] {
  right: 645px;
}

.right-\[7px\] {
  right: 7px;
}

.right-\[93px\] {
  right: 93px;
}

.start-0\.5 {
  inset-inline-start: 0.125rem;
}

.top-0 {
  top: 0px;
}

.top-0\.5 {
  top: 0.125rem;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.top-2\.5 {
  top: 0.625rem;
}

.top-2\/4 {
  top: 50%;
}

.top-\[-101px\] {
  top: -101px;
}

.top-\[-10px\] {
  top: -10px;
}

.top-\[-113px\] {
  top: -113px;
}

.top-\[-130px\] {
  top: -130px;
}

.top-\[-137px\] {
  top: -137px;
}

.top-\[-13px\] {
  top: -13px;
}

.top-\[-144px\] {
  top: -144px;
}

.top-\[-14px\] {
  top: -14px;
}

.top-\[-15px\] {
  top: -15px;
}

.top-\[-164px\] {
  top: -164px;
}

.top-\[-16px\] {
  top: -16px;
}

.top-\[-18px\] {
  top: -18px;
}

.top-\[-19px\] {
  top: -19px;
}

.top-\[-1px\] {
  top: -1px;
}

.top-\[-200px\] {
  top: -200px;
}

.top-\[-20px\] {
  top: -20px;
}

.top-\[-218px\] {
  top: -218px;
}

.top-\[-21px\] {
  top: -21px;
}

.top-\[-23\%\] {
  top: -23%;
}

.top-\[-230px\] {
  top: -230px;
}

.top-\[-24px\] {
  top: -24px;
}

.top-\[-260px\] {
  top: -260px;
}

.top-\[-30\%\] {
  top: -30%;
}

.top-\[-34px\] {
  top: -34px;
}

.top-\[-35px\] {
  top: -35px;
}

.top-\[-37px\] {
  top: -37px;
}

.top-\[-38px\] {
  top: -38px;
}

.top-\[-3px\] {
  top: -3px;
}

.top-\[-40px\] {
  top: -40px;
}

.top-\[-42px\] {
  top: -42px;
}

.top-\[-43px\] {
  top: -43px;
}

.top-\[-46px\] {
  top: -46px;
}

.top-\[-49\%\] {
  top: -49%;
}

.top-\[-4px\] {
  top: -4px;
}

.top-\[-50px\] {
  top: -50px;
}

.top-\[-53px\] {
  top: -53px;
}

.top-\[-56px\] {
  top: -56px;
}

.top-\[-57px\] {
  top: -57px;
}

.top-\[-580px\] {
  top: -580px;
}

.top-\[-590px\] {
  top: -590px;
}

.top-\[-660px\] {
  top: -660px;
}

.top-\[-6px\] {
  top: -6px;
}

.top-\[-70px\] {
  top: -70px;
}

.top-\[-710px\] {
  top: -710px;
}

.top-\[-72px\] {
  top: -72px;
}

.top-\[-84\%\] {
  top: -84%;
}

.top-\[-8px\] {
  top: -8px;
}

.top-\[-90px\] {
  top: -90px;
}

.top-\[-9px\] {
  top: -9px;
}

.top-\[0px\] {
  top: 0px;
}

.top-\[100px\] {
  top: 100px;
}

.top-\[10px\] {
  top: 10px;
}

.top-\[113px\] {
  top: 113px;
}

.top-\[114px\] {
  top: 114px;
}

.top-\[11px\] {
  top: 11px;
}

.top-\[12px\] {
  top: 12px;
}

.top-\[135px\] {
  top: 135px;
}

.top-\[141px\] {
  top: 141px;
}

.top-\[14px\] {
  top: 14px;
}

.top-\[153px\] {
  top: 153px;
}

.top-\[15px\] {
  top: 15px;
}

.top-\[161px\] {
  top: 161px;
}

.top-\[162px\] {
  top: 162px;
}

.top-\[16px\] {
  top: 16px;
}

.top-\[170px\] {
  top: 170px;
}

.top-\[172px\] {
  top: 172px;
}

.top-\[186px\] {
  top: 186px;
}

.top-\[18px\] {
  top: 18px;
}

.top-\[192px\] {
  top: 192px;
}

.top-\[19px\] {
  top: 19px;
}

.top-\[1px\] {
  top: 1px;
}

.top-\[21px\] {
  top: 21px;
}

.top-\[24px\] {
  top: 24px;
}

.top-\[260px\] {
  top: 260px;
}

.top-\[265px\] {
  top: 265px;
}

.top-\[270px\] {
  top: 270px;
}

.top-\[276px\] {
  top: 276px;
}

.top-\[2px\] {
  top: 2px;
}

.top-\[312px\] {
  top: 312px;
}

.top-\[31px\] {
  top: 31px;
}

.top-\[320px\] {
  top: 320px;
}

.top-\[32px\] {
  top: 32px;
}

.top-\[33px\] {
  top: 33px;
}

.top-\[36px\] {
  top: 36px;
}

.top-\[389px\] {
  top: 389px;
}

.top-\[39px\] {
  top: 39px;
}

.top-\[3px\] {
  top: 3px;
}

.top-\[45\%\] {
  top: 45%;
}

.top-\[453px\] {
  top: 453px;
}

.top-\[47px\] {
  top: 47px;
}

.top-\[495px\] {
  top: 495px;
}

.top-\[49px\] {
  top: 49px;
}

.top-\[4px\] {
  top: 4px;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[56px\] {
  top: 56px;
}

.top-\[58px\] {
  top: 58px;
}

.top-\[59px\] {
  top: 59px;
}

.top-\[5px\] {
  top: 5px;
}

.top-\[60px\] {
  top: 60px;
}

.top-\[64px\] {
  top: 64px;
}

.top-\[65px\] {
  top: 65px;
}

.top-\[66px\] {
  top: 66px;
}

.top-\[70px\] {
  top: 70px;
}

.top-\[7px\] {
  top: 7px;
}

.top-\[80\%\] {
  top: 80%;
}

.top-\[82px\] {
  top: 82px;
}

.top-\[9px\] {
  top: 9px;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.-z-20 {
  z-index: -20;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[-1\] {
  z-index: -1;
}

.z-\[100\] {
  z-index: 100;
}

.z-\[15\] {
  z-index: 15;
}

.z-\[50\] {
  z-index: 50;
}

.z-\[51\] {
  z-index: 51;
}

.z-\[55\] {
  z-index: 55;
}

.z-\[60\] {
  z-index: 60;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.col-auto {
  grid-column: auto;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.m-0 {
  margin: 0px;
}

.m-1 {
  margin: 0.25rem;
}

.m-auto {
  margin: auto;
}

.-mx-5 {
  margin-left: -1.25rem;
  margin-right: -1.25rem;
}

.-my-2\.5 {
  margin-top: -0.625rem;
  margin-bottom: -0.625rem;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mx-\[-12px\] {
  margin-left: -12px;
  margin-right: -12px;
}

.mx-\[-30px\] {
  margin-left: -30px;
  margin-right: -30px;
}

.mx-\[25px\] {
  margin-left: 25px;
  margin-right: 25px;
}

.mx-\[6px\] {
  margin-left: 6px;
  margin-right: 6px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-\[15px\] {
  margin-top: 15px;
  margin-bottom: 15px;
}

.my-\[18px\] {
  margin-top: 18px;
  margin-bottom: 18px;
}

.my-\[19px\] {
  margin-top: 19px;
  margin-bottom: 19px;
}

.my-\[21px\] {
  margin-top: 21px;
  margin-bottom: 21px;
}

.my-\[23px\] {
  margin-top: 23px;
  margin-bottom: 23px;
}

.my-\[25px\] {
  margin-top: 25px;
  margin-bottom: 25px;
}

.my-\[30px\] {
  margin-top: 30px;
  margin-bottom: 30px;
}

.my-\[34px\] {
  margin-top: 34px;
  margin-bottom: 34px;
}

.my-\[50px\] {
  margin-top: 50px;
  margin-bottom: 50px;
}

.my-\[6px\] {
  margin-top: 6px;
  margin-bottom: 6px;
}

.my-\[7px\] {
  margin-top: 7px;
  margin-bottom: 7px;
}

.my-\[9px\] {
  margin-top: 9px;
  margin-bottom: 9px;
}

.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.\!mr-\[-20px\] {
  margin-right: -20px !important;
}

.-mb-10 {
  margin-bottom: -2.5rem;
}

.-mb-5 {
  margin-bottom: -1.25rem;
}

.-mb-\[18px\] {
  margin-bottom: -18px;
}

.-ml-5 {
  margin-left: -1.25rem;
}

.-mr-5 {
  margin-right: -1.25rem;
}

.-mt-1 {
  margin-top: -0.25rem;
}

.-mt-10 {
  margin-top: -2.5rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.-mt-20 {
  margin-top: -5rem;
}

.-mt-3 {
  margin-top: -0.75rem;
}

.-mt-3\.5 {
  margin-top: -0.875rem;
}

.-mt-5 {
  margin-top: -1.25rem;
}

.-mt-\[12px\] {
  margin-top: -12px;
}

.-mt-\[15px\] {
  margin-top: -15px;
}

.-mt-\[36px\] {
  margin-top: -36px;
}

.-mt-\[46px\] {
  margin-top: -46px;
}

.-mt-\[67px\] {
  margin-top: -67px;
}

.-mt-\[84px\] {
  margin-top: -84px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-14 {
  margin-bottom: 3.5rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-3\.5 {
  margin-bottom: 0.875rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-40 {
  margin-bottom: 10rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-\[-23px\] {
  margin-bottom: -23px;
}

.mb-\[100px\] {
  margin-bottom: 100px;
}

.mb-\[105px\] {
  margin-bottom: 105px;
}

.mb-\[112px\] {
  margin-bottom: 112px;
}

.mb-\[118px\] {
  margin-bottom: 118px;
}

.mb-\[11px\] {
  margin-bottom: 11px;
}

.mb-\[13px\] {
  margin-bottom: 13px;
}

.mb-\[14px\] {
  margin-bottom: 14px;
}

.mb-\[15px\] {
  margin-bottom: 15px;
}

.mb-\[17px\] {
  margin-bottom: 17px;
}

.mb-\[18px\] {
  margin-bottom: 18px;
}

.mb-\[19px\] {
  margin-bottom: 19px;
}

.mb-\[20px\] {
  margin-bottom: 20px;
}

.mb-\[21px\] {
  margin-bottom: 21px;
}

.mb-\[22px\] {
  margin-bottom: 22px;
}

.mb-\[23px\] {
  margin-bottom: 23px;
}

.mb-\[25px\] {
  margin-bottom: 25px;
}

.mb-\[26px\] {
  margin-bottom: 26px;
}

.mb-\[27px\] {
  margin-bottom: 27px;
}

.mb-\[2px\] {
  margin-bottom: 2px;
}

.mb-\[30px\] {
  margin-bottom: 30px;
}

.mb-\[32px\] {
  margin-bottom: 32px;
}

.mb-\[34px\] {
  margin-bottom: 34px;
}

.mb-\[35px\] {
  margin-bottom: 35px;
}

.mb-\[36px\] {
  margin-bottom: 36px;
}

.mb-\[37px\] {
  margin-bottom: 37px;
}

.mb-\[3px\] {
  margin-bottom: 3px;
}

.mb-\[41px\] {
  margin-bottom: 41px;
}

.mb-\[46px\] {
  margin-bottom: 46px;
}

.mb-\[4px\] {
  margin-bottom: 4px;
}

.mb-\[50px\] {
  margin-bottom: 50px;
}

.mb-\[55px\] {
  margin-bottom: 55px;
}

.mb-\[56px\] {
  margin-bottom: 56px;
}

.mb-\[58px\] {
  margin-bottom: 58px;
}

.mb-\[59px\] {
  margin-bottom: 59px;
}

.mb-\[5px\] {
  margin-bottom: 5px;
}

.mb-\[60px\] {
  margin-bottom: 60px;
}

.mb-\[61\.5px\] {
  margin-bottom: 61.5px;
}

.mb-\[65px\] {
  margin-bottom: 65px;
}

.mb-\[66px\] {
  margin-bottom: 66px;
}

.mb-\[67px\] {
  margin-bottom: 67px;
}

.mb-\[6px\] {
  margin-bottom: 6px;
}

.mb-\[70px\] {
  margin-bottom: 70px;
}

.mb-\[75px\] {
  margin-bottom: 75px;
}

.mb-\[7px\] {
  margin-bottom: 7px;
}

.mb-\[90px\] {
  margin-bottom: 90px;
}

.mb-\[9px\] {
  margin-bottom: 9px;
}

.mb-auto {
  margin-bottom: auto;
}

.me-1 {
  margin-inline-end: 0.25rem;
}

.me-3 {
  margin-inline-end: 0.75rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-10 {
  margin-left: 2.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-2\.5 {
  margin-left: 0.625rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-9 {
  margin-left: 2.25rem;
}

.ml-\[-16px\] {
  margin-left: -16px;
}

.ml-\[-500px\] {
  margin-left: -500px;
}

.ml-\[-919px\] {
  margin-left: -919px;
}

.ml-\[102px\] {
  margin-left: 102px;
}

.ml-\[115px\] {
  margin-left: 115px;
}

.ml-\[118px\] {
  margin-left: 118px;
}

.ml-\[11px\] {
  margin-left: 11px;
}

.ml-\[16px\] {
  margin-left: 16px;
}

.ml-\[18px\] {
  margin-left: 18px;
}

.ml-\[191px\] {
  margin-left: 191px;
}

.ml-\[26px\] {
  margin-left: 26px;
}

.ml-\[275px\] {
  margin-left: 275px;
}

.ml-\[30px\] {
  margin-left: 30px;
}

.ml-\[3px\] {
  margin-left: 3px;
}

.ml-\[46px\] {
  margin-left: 46px;
}

.ml-\[56px\] {
  margin-left: 56px;
}

.ml-\[62px\] {
  margin-left: 62px;
}

.ml-\[76px\] {
  margin-left: 76px;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-10 {
  margin-right: 2.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-3\.5 {
  margin-right: 0.875rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-\[-10px\] {
  margin-right: -10px;
}

.mr-\[-20px\] {
  margin-right: -20px;
}

.mr-\[-23px\] {
  margin-right: -23px;
}

.mr-\[-919px\] {
  margin-right: -919px;
}

.mr-\[146px\] {
  margin-right: 146px;
}

.mr-\[20\.5px\] {
  margin-right: 20.5px;
}

.mr-\[22px\] {
  margin-right: 22px;
}

.mr-\[26px\] {
  margin-right: 26px;
}

.mr-\[30px\] {
  margin-right: 30px;
}

.mr-\[6px\] {
  margin-right: 6px;
}

.mr-auto {
  margin-right: auto;
}

.ms-2 {
  margin-inline-start: 0.5rem;
}

.ms-auto {
  margin-inline-start: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-3\.5 {
  margin-top: 0.875rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-\[-100px\] {
  margin-top: -100px;
}

.mt-\[-112px\] {
  margin-top: -112px;
}

.mt-\[-172px\] {
  margin-top: -172px;
}

.mt-\[-220px\] {
  margin-top: -220px;
}

.mt-\[-24px\] {
  margin-top: -24px;
}

.mt-\[-28px\] {
  margin-top: -28px;
}

.mt-\[-30px\] {
  margin-top: -30px;
}

.mt-\[-32px\] {
  margin-top: -32px;
}

.mt-\[-33px\] {
  margin-top: -33px;
}

.mt-\[-42px\] {
  margin-top: -42px;
}

.mt-\[-52px\] {
  margin-top: -52px;
}

.mt-\[-60px\] {
  margin-top: -60px;
}

.mt-\[-90px\] {
  margin-top: -90px;
}

.mt-\[100px\] {
  margin-top: 100px;
}

.mt-\[101px\] {
  margin-top: 101px;
}

.mt-\[119px\] {
  margin-top: 119px;
}

.mt-\[11px\] {
  margin-top: 11px;
}

.mt-\[124px\] {
  margin-top: 124px;
}

.mt-\[130px\] {
  margin-top: 130px;
}

.mt-\[134px\] {
  margin-top: 134px;
}

.mt-\[144px\] {
  margin-top: 144px;
}

.mt-\[145px\] {
  margin-top: 145px;
}

.mt-\[14px\] {
  margin-top: 14px;
}

.mt-\[150px\] {
  margin-top: 150px;
}

.mt-\[15px\] {
  margin-top: 15px;
}

.mt-\[178px\] {
  margin-top: 178px;
}

.mt-\[17px\] {
  margin-top: 17px;
}

.mt-\[18px\] {
  margin-top: 18px;
}

.mt-\[191px\] {
  margin-top: 191px;
}

.mt-\[19px\] {
  margin-top: 19px;
}

.mt-\[203px\] {
  margin-top: 203px;
}

.mt-\[21px\] {
  margin-top: 21px;
}

.mt-\[22px\] {
  margin-top: 22px;
}

.mt-\[23px\] {
  margin-top: 23px;
}

.mt-\[25px\] {
  margin-top: 25px;
}

.mt-\[26px\] {
  margin-top: 26px;
}

.mt-\[27px\] {
  margin-top: 27px;
}

.mt-\[28px\] {
  margin-top: 28px;
}

.mt-\[29px\] {
  margin-top: 29px;
}

.mt-\[30px\] {
  margin-top: 30px;
}

.mt-\[316px\] {
  margin-top: 316px;
}

.mt-\[31px\] {
  margin-top: 31px;
}

.mt-\[32px\] {
  margin-top: 32px;
}

.mt-\[33px\] {
  margin-top: 33px;
}

.mt-\[34px\] {
  margin-top: 34px;
}

.mt-\[356px\] {
  margin-top: 356px;
}

.mt-\[35px\] {
  margin-top: 35px;
}

.mt-\[3px\] {
  margin-top: 3px;
}

.mt-\[42px\] {
  margin-top: 42px;
}

.mt-\[47px\] {
  margin-top: 47px;
}

.mt-\[49px\] {
  margin-top: 49px;
}

.mt-\[50px\] {
  margin-top: 50px;
}

.mt-\[51px\] {
  margin-top: 51px;
}

.mt-\[52px\] {
  margin-top: 52px;
}

.mt-\[5px\] {
  margin-top: 5px;
}

.mt-\[60px\] {
  margin-top: 60px;
}

.mt-\[62px\] {
  margin-top: 62px;
}

.mt-\[63px\] {
  margin-top: 63px;
}

.mt-\[64px\] {
  margin-top: 64px;
}

.mt-\[6px\] {
  margin-top: 6px;
}

.mt-\[70px\] {
  margin-top: 70px;
}

.mt-\[71px\] {
  margin-top: 71px;
}

.mt-\[73px\] {
  margin-top: 73px;
}

.mt-\[77px\] {
  margin-top: 77px;
}

.mt-\[80px\] {
  margin-top: 80px;
}

.mt-\[83px\] {
  margin-top: 83px;
}

.mt-\[87px\] {
  margin-top: 87px;
}

.mt-\[9px\] {
  margin-top: 9px;
}

.mt-auto {
  margin-top: auto;
}

.ml-\[-19px\] {
  margin-left: -19px;
}

.ml-\[19px\] {
  margin-left: 19px;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-cell {
  display: table-cell;
}

.table-row {
  display: table-row;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.h-0 {
  height: 0px;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-\[100px\] {
  height: 100px;
}

.h-\[109px\] {
  height: 109px;
}

.h-\[10px\] {
  height: 10px;
}

.h-\[1191px\] {
  height: 1191px;
}

.h-\[122px\] {
  height: 122px;
}

.h-\[124px\] {
  height: 124px;
}

.h-\[129px\] {
  height: 129px;
}

.h-\[130px\] {
  height: 130px;
}

.h-\[139px\] {
  height: 139px;
}

.h-\[140px\] {
  height: 140px;
}

.h-\[146px\] {
  height: 146px;
}

.h-\[151px\] {
  height: 151px;
}

.h-\[15px\] {
  height: 15px;
}

.h-\[17\.79px\] {
  height: 17.79px;
}

.h-\[172px\] {
  height: 172px;
}

.h-\[180px\] {
  height: 180px;
}

.h-\[186px\] {
  height: 186px;
}

.h-\[187px\] {
  height: 187px;
}

.h-\[188px\] {
  height: 188px;
}

.h-\[19px\] {
  height: 19px;
}

.h-\[209px\] {
  height: 209px;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[21px\] {
  height: 21px;
}

.h-\[220px\] {
  height: 220px;
}

.h-\[227px\] {
  height: 227px;
}

.h-\[228px\] {
  height: 228px;
}

.h-\[23px\] {
  height: 23px;
}

.h-\[24px\] {
  height: 24px;
}

.h-\[25px\] {
  height: 25px;
}

.h-\[27px\] {
  height: 27px;
}

.h-\[293px\] {
  height: 293px;
}

.h-\[29px\] {
  height: 29px;
}

.h-\[30px\] {
  height: 30px;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[331px\] {
  height: 331px;
}

.h-\[347px\] {
  height: 347px;
}

.h-\[348px\] {
  height: 348px;
}

.h-\[36px\] {
  height: 36px;
}

.h-\[381px\] {
  height: 381px;
}

.h-\[38px\] {
  height: 38px;
}

.h-\[39px\] {
  height: 39px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[41px\] {
  height: 41px;
}

.h-\[422px\] {
  height: 422px;
}

.h-\[42px\] {
  height: 42px;
}

.h-\[43px\] {
  height: 43px;
}

.h-\[45px\] {
  height: 45px;
}

.h-\[468px\] {
  height: 468px;
}

.h-\[47px\] {
  height: 47px;
}

.h-\[49px\] {
  height: 49px;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[536px\] {
  height: 536px;
}

.h-\[53px\] {
  height: 53px;
}

.h-\[540px\] {
  height: 540px;
}

.h-\[54px\] {
  height: 54px;
}

.h-\[55px\] {
  height: 55px;
}

.h-\[560px\] {
  height: 560px;
}

.h-\[569px\] {
  height: 569px;
}

.h-\[56px\] {
  height: 56px;
}

.h-\[579px\] {
  height: 579px;
}

.h-\[57px\] {
  height: 57px;
}

.h-\[58px\] {
  height: 58px;
}

.h-\[59px\] {
  height: 59px;
}

.h-\[5px\] {
  height: 5px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[61px\] {
  height: 61px;
}

.h-\[620px\] {
  height: 620px;
}

.h-\[63px\] {
  height: 63px;
}

.h-\[64px\] {
  height: 64px;
}

.h-\[65px\] {
  height: 65px;
}

.h-\[70px\] {
  height: 70px;
}

.h-\[72px\] {
  height: 72px;
}

.h-\[740px\] {
  height: 740px;
}

.h-\[77px\] {
  height: 77px;
}

.h-\[79px\] {
  height: 79px;
}

.h-\[80px\] {
  height: 80px;
}

.h-\[81px\] {
  height: 81px;
}

.h-\[83px\] {
  height: 83px;
}

.h-\[85px\] {
  height: 85px;
}

.h-\[88px\] {
  height: 88px;
}

.h-\[90px\] {
  height: 90px;
}

.h-\[91px\] {
  height: 91px;
}

.h-\[92px\] {
  height: 92px;
}

.h-\[96px\] {
  height: 96px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-max {
  height: -moz-max-content;
  height: max-content;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.h-svh {
  height: 100svh;
}

.max-h-10 {
  max-height: 2.5rem;
}

.max-h-\[100px\] {
  max-height: 100px;
}

.max-h-\[130px\] {
  max-height: 130px;
}

.max-h-\[1440px\] {
  max-height: 1440px;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[20px\] {
  max-height: 20px;
}

.max-h-\[22px\] {
  max-height: 22px;
}

.max-h-\[23px\] {
  max-height: 23px;
}

.max-h-\[27px\] {
  max-height: 27px;
}

.max-h-\[28px\] {
  max-height: 28px;
}

.max-h-\[297\.66px\] {
  max-height: 297.66px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[30px\] {
  max-height: 30px;
}

.max-h-\[344px\] {
  max-height: 344px;
}

.max-h-\[350px\] {
  max-height: 350px;
}

.max-h-\[35px\] {
  max-height: 35px;
}

.max-h-\[36px\] {
  max-height: 36px;
}

.max-h-\[37px\] {
  max-height: 37px;
}

.max-h-\[38px\] {
  max-height: 38px;
}

.max-h-\[39px\] {
  max-height: 39px;
}

.max-h-\[40px\] {
  max-height: 40px;
}

.max-h-\[42px\] {
  max-height: 42px;
}

.max-h-\[45px\] {
  max-height: 45px;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.max-h-\[50px\] {
  max-height: 50px;
}

.max-h-\[60px\] {
  max-height: 60px;
}

.max-h-\[64px\] {
  max-height: 64px;
}

.max-h-\[802px\] {
  max-height: 802px;
}

.max-h-\[80px\] {
  max-height: 80px;
}

.max-h-full {
  max-height: 100%;
}

.min-h-10 {
  min-height: 2.5rem;
}

.min-h-12 {
  min-height: 3rem;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-7 {
  min-height: 1.75rem;
}

.min-h-\[114px\] {
  min-height: 114px;
}

.min-h-\[1277px\] {
  min-height: 1277px;
}

.min-h-\[1em\] {
  min-height: 1em;
}

.min-h-\[276px\] {
  min-height: 276px;
}

.min-h-\[42px\] {
  min-height: 42px;
}

.min-h-\[48px\] {
  min-height: 48px;
}

.min-h-\[49px\] {
  min-height: 49px;
}

.min-h-\[53px\] {
  min-height: 53px;
}

.min-h-\[54px\] {
  min-height: 54px;
}

.min-h-\[57px\] {
  min-height: 57px;
}

.min-h-\[70px\] {
  min-height: 70px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-1 {
  width: 0.25rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-\[10\%\] {
  width: 10%;
}

.w-\[100\%\] {
  width: 100%;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[109px\] {
  width: 109px;
}

.w-\[1105px\] {
  width: 1105px;
}

.w-\[110px\] {
  width: 110px;
}

.w-\[112px\] {
  width: 112px;
}

.w-\[115px\] {
  width: 115px;
}

.w-\[116px\] {
  width: 116px;
}

.w-\[1200px\] {
  width: 1200px;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[124px\] {
  width: 124px;
}

.w-\[125px\] {
  width: 125px;
}

.w-\[126px\] {
  width: 126px;
}

.w-\[1280px\] {
  width: 1280px;
}

.w-\[128px\] {
  width: 128px;
}

.w-\[129px\] {
  width: 129px;
}

.w-\[130px\] {
  width: 130px;
}

.w-\[131px\] {
  width: 131px;
}

.w-\[138px\] {
  width: 138px;
}

.w-\[143px\] {
  width: 143px;
}

.w-\[1470px\] {
  width: 1470px;
}

.w-\[147px\] {
  width: 147px;
}

.w-\[153px\] {
  width: 153px;
}

.w-\[160px\] {
  width: 160px;
}

.w-\[163px\] {
  width: 163px;
}

.w-\[169px\] {
  width: 169px;
}

.w-\[171px\] {
  width: 171px;
}

.w-\[174px\] {
  width: 174px;
}

.w-\[175px\] {
  width: 175px;
}

.w-\[177px\] {
  width: 177px;
}

.w-\[178px\] {
  width: 178px;
}

.w-\[180px\] {
  width: 180px;
}

.w-\[181px\] {
  width: 181px;
}

.w-\[186px\] {
  width: 186px;
}

.w-\[187px\] {
  width: 187px;
}

.w-\[189px\] {
  width: 189px;
}

.w-\[19px\] {
  width: 19px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[200\%\] {
  width: 200%;
}

.w-\[204px\] {
  width: 204px;
}

.w-\[207px\] {
  width: 207px;
}

.w-\[208px\] {
  width: 208px;
}

.w-\[210px\] {
  width: 210px;
}

.w-\[218px\] {
  width: 218px;
}

.w-\[22\%\] {
  width: 22%;
}

.w-\[231px\] {
  width: 231px;
}

.w-\[232px\] {
  width: 232px;
}

.w-\[236px\] {
  width: 236px;
}

.w-\[245px\] {
  width: 245px;
}

.w-\[252px\] {
  width: 252px;
}

.w-\[253px\] {
  width: 253px;
}

.w-\[271px\] {
  width: 271px;
}

.w-\[278px\] {
  width: 278px;
}

.w-\[279px\] {
  width: 279px;
}

.w-\[295px\] {
  width: 295px;
}

.w-\[298px\] {
  width: 298px;
}

.w-\[3\.25rem\] {
  width: 3.25rem;
}

.w-\[307px\] {
  width: 307px;
}

.w-\[30px\] {
  width: 30px;
}

.w-\[313px\] {
  width: 313px;
}

.w-\[315px\] {
  width: 315px;
}

.w-\[320px\] {
  width: 320px;
}

.w-\[330px\] {
  width: 330px;
}

.w-\[335px\] {
  width: 335px;
}

.w-\[365px\] {
  width: 365px;
}

.w-\[36px\] {
  width: 36px;
}

.w-\[372px\] {
  width: 372px;
}

.w-\[378px\] {
  width: 378px;
}

.w-\[37px\] {
  width: 37px;
}

.w-\[383px\] {
  width: 383px;
}

.w-\[38px\] {
  width: 38px;
}

.w-\[401px\] {
  width: 401px;
}

.w-\[41px\] {
  width: 41px;
}

.w-\[42px\] {
  width: 42px;
}

.w-\[456px\] {
  width: 456px;
}

.w-\[463px\] {
  width: 463px;
}

.w-\[464px\] {
  width: 464px;
}

.w-\[477px\] {
  width: 477px;
}

.w-\[47px\] {
  width: 47px;
}

.w-\[490px\] {
  width: 490px;
}

.w-\[491px\] {
  width: 491px;
}

.w-\[50\%\] {
  width: 50%;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[511px\] {
  width: 511px;
}

.w-\[51px\] {
  width: 51px;
}

.w-\[524px\] {
  width: 524px;
}

.w-\[52px\] {
  width: 52px;
}

.w-\[546px\] {
  width: 546px;
}

.w-\[548px\] {
  width: 548px;
}

.w-\[564px\] {
  width: 564px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[617px\] {
  width: 617px;
}

.w-\[61px\] {
  width: 61px;
}

.w-\[690px\] {
  width: 690px;
}

.w-\[69px\] {
  width: 69px;
}

.w-\[70px\] {
  width: 70px;
}

.w-\[720px\] {
  width: 720px;
}

.w-\[75px\] {
  width: 75px;
}

.w-\[76\.65px\] {
  width: 76.65px;
}

.w-\[781px\] {
  width: 781px;
}

.w-\[811px\] {
  width: 811px;
}

.w-\[828px\] {
  width: 828px;
}

.w-\[83px\] {
  width: 83px;
}

.w-\[85px\] {
  width: 85px;
}

.w-\[86px\] {
  width: 86px;
}

.w-\[878px\] {
  width: 878px;
}

.w-\[88px\] {
  width: 88px;
}

.w-\[895px\] {
  width: 895px;
}

.w-\[90px\] {
  width: 90px;
}

.w-\[913px\] {
  width: 913px;
}

.w-\[91px\] {
  width: 91px;
}

.w-\[92px\] {
  width: 92px;
}

.w-\[93px\] {
  width: 93px;
}

.w-\[951px\] {
  width: 951px;
}

.w-\[952px\] {
  width: 952px;
}

.w-\[963px\] {
  width: 963px;
}

.w-\[97px\] {
  width: 97px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-1 {
  min-width: 0.25rem;
}

.min-w-\[113px\] {
  min-width: 113px;
}

.min-w-\[115px\] {
  min-width: 115px;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[187px\] {
  min-width: 187px;
}

.min-w-\[248px\] {
  min-width: 248px;
}

.min-w-\[279px\] {
  min-width: 279px;
}

.min-w-\[289px\] {
  min-width: 289px;
}

.min-w-\[294px\] {
  min-width: 294px;
}

.min-w-\[295px\] {
  min-width: 295px;
}

.min-w-\[30px\] {
  min-width: 30px;
}

.min-w-\[310px\] {
  min-width: 310px;
}

.min-w-\[316px\] {
  min-width: 316px;
}

.min-w-\[415px\] {
  min-width: 415px;
}

.min-w-\[560px\] {
  min-width: 560px;
}

.min-w-\[65px\] {
  min-width: 65px;
}

.min-w-\[69px\] {
  min-width: 69px;
}

.min-w-\[76px\] {
  min-width: 76px;
}

.min-w-\[78px\] {
  min-width: 78px;
}

.min-w-\[85px\] {
  min-width: 85px;
}

.min-w-\[90px\] {
  min-width: 90px;
}

.max-w-16 {
  max-width: 4rem;
}

.max-w-20 {
  max-width: 5rem;
}

.max-w-60 {
  max-width: 15rem;
}

.max-w-\[1008px\] {
  max-width: 1008px;
}

.max-w-\[100px\] {
  max-width: 100px;
}

.max-w-\[110px\] {
  max-width: 110px;
}

.max-w-\[112px\] {
  max-width: 112px;
}

.max-w-\[115px\] {
  max-width: 115px;
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-\[1240px\] {
  max-width: 1240px;
}

.max-w-\[130px\] {
  max-width: 130px;
}

.max-w-\[140px\] {
  max-width: 140px;
}

.max-w-\[141px\] {
  max-width: 141px;
}

.max-w-\[1470px\] {
  max-width: 1470px;
}

.max-w-\[164px\] {
  max-width: 164px;
}

.max-w-\[168px\] {
  max-width: 168px;
}

.max-w-\[169px\] {
  max-width: 169px;
}

.max-w-\[170px\] {
  max-width: 170px;
}

.max-w-\[173px\] {
  max-width: 173px;
}

.max-w-\[187px\] {
  max-width: 187px;
}

.max-w-\[189px\] {
  max-width: 189px;
}

.max-w-\[194px\] {
  max-width: 194px;
}

.max-w-\[196px\] {
  max-width: 196px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[213px\] {
  max-width: 213px;
}

.max-w-\[216px\] {
  max-width: 216px;
}

.max-w-\[220px\] {
  max-width: 220px;
}

.max-w-\[223px\] {
  max-width: 223px;
}

.max-w-\[225px\] {
  max-width: 225px;
}

.max-w-\[236px\] {
  max-width: 236px;
}

.max-w-\[241px\] {
  max-width: 241px;
}

.max-w-\[243px\] {
  max-width: 243px;
}

.max-w-\[245px\] {
  max-width: 245px;
}

.max-w-\[247px\] {
  max-width: 247px;
}

.max-w-\[248px\] {
  max-width: 248px;
}

.max-w-\[24px\] {
  max-width: 24px;
}

.max-w-\[251px\] {
  max-width: 251px;
}

.max-w-\[265px\] {
  max-width: 265px;
}

.max-w-\[275px\] {
  max-width: 275px;
}

.max-w-\[277px\] {
  max-width: 277px;
}

.max-w-\[293px\] {
  max-width: 293px;
}

.max-w-\[294px\] {
  max-width: 294px;
}

.max-w-\[297px\] {
  max-width: 297px;
}

.max-w-\[300px\] {
  max-width: 300px;
}

.max-w-\[302px\] {
  max-width: 302px;
}

.max-w-\[303px\] {
  max-width: 303px;
}

.max-w-\[304px\] {
  max-width: 304px;
}

.max-w-\[306px\] {
  max-width: 306px;
}

.max-w-\[30px\] {
  max-width: 30px;
}

.max-w-\[312px\] {
  max-width: 312px;
}

.max-w-\[314px\] {
  max-width: 314px;
}

.max-w-\[325px\] {
  max-width: 325px;
}

.max-w-\[335px\] {
  max-width: 335px;
}

.max-w-\[344px\] {
  max-width: 344px;
}

.max-w-\[354px\] {
  max-width: 354px;
}

.max-w-\[366px\] {
  max-width: 366px;
}

.max-w-\[36px\] {
  max-width: 36px;
}

.max-w-\[371px\] {
  max-width: 371px;
}

.max-w-\[372px\] {
  max-width: 372px;
}

.max-w-\[374px\] {
  max-width: 374px;
}

.max-w-\[386px\] {
  max-width: 386px;
}

.max-w-\[387px\] {
  max-width: 387px;
}

.max-w-\[390px\] {
  max-width: 390px;
}

.max-w-\[409px\] {
  max-width: 409px;
}

.max-w-\[415px\] {
  max-width: 415px;
}

.max-w-\[42px\] {
  max-width: 42px;
}

.max-w-\[436px\] {
  max-width: 436px;
}

.max-w-\[440px\] {
  max-width: 440px;
}

.max-w-\[450px\] {
  max-width: 450px;
}

.max-w-\[454px\] {
  max-width: 454px;
}

.max-w-\[455px\] {
  max-width: 455px;
}

.max-w-\[45px\] {
  max-width: 45px;
}

.max-w-\[461px\] {
  max-width: 461px;
}

.max-w-\[463px\] {
  max-width: 463px;
}

.max-w-\[500px\] {
  max-width: 500px;
}

.max-w-\[50px\] {
  max-width: 50px;
}

.max-w-\[511px\] {
  max-width: 511px;
}

.max-w-\[52\%\] {
  max-width: 52%;
}

.max-w-\[554px\] {
  max-width: 554px;
}

.max-w-\[564px\] {
  max-width: 564px;
}

.max-w-\[584px\] {
  max-width: 584px;
}

.max-w-\[58px\] {
  max-width: 58px;
}

.max-w-\[594px\] {
  max-width: 594px;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[60px\] {
  max-width: 60px;
}

.max-w-\[68px\] {
  max-width: 68px;
}

.max-w-\[690px\] {
  max-width: 690px;
}

.max-w-\[70px\] {
  max-width: 70px;
}

.max-w-\[737px\] {
  max-width: 737px;
}

.max-w-\[770px\] {
  max-width: 770px;
}

.max-w-\[773px\] {
  max-width: 773px;
}

.max-w-\[78px\] {
  max-width: 78px;
}

.max-w-\[800px\] {
  max-width: 800px;
}

.max-w-\[80px\] {
  max-width: 80px;
}

.max-w-\[85px\] {
  max-width: 85px;
}

.max-w-\[952px\] {
  max-width: 952px;
}

.max-w-\[968px\] {
  max-width: 968px;
}

.max-w-\[99\%\] {
  max-width: 99%;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-none {
  max-width: none;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.table-auto {
  table-layout: auto;
}

.border-collapse {
  border-collapse: collapse;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-2\/4 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[137\%\] {
  --tw-translate-x: -137%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[152\%\] {
  --tw-translate-x: -152%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[191\%\] {
  --tw-translate-x: -191%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[55\%\] {
  --tw-translate-x: -55%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2\/4 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[77\%\] {
  --tw-translate-y: -77%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-133\%\] {
  --tw-translate-x: -133%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-165px\] {
  --tw-translate-x: -165px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[147\%\] {
  --tw-translate-x: 147%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[49\%\] {
  --tw-translate-x: 49%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[59\%\] {
  --tw-translate-x: 59%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[85px\] {
  --tw-translate-x: 85px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-105px\] {
  --tw-translate-y: -105px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-128px\] {
  --tw-translate-y: -128px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-135\%\] {
  --tw-translate-y: -135%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-155px\] {
  --tw-translate-y: -155px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-166px\] {
  --tw-translate-y: -166px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-167px\] {
  --tw-translate-y: -167px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-173px\] {
  --tw-translate-y: -173px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-91px\] {
  --tw-translate-y: -91px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-96px\] {
  --tw-translate-y: -96px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[12px\] {
  --tw-translate-y: 12px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[145\%\] {
  --tw-translate-y: 145%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[344\%\] {
  --tw-translate-y: 344%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-12 {
  --tw-rotate: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-3 {
  --tw-rotate: -3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[87deg\] {
  --tw-rotate: 87deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[90deg\] {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[92deg\] {
  --tw-rotate: 92deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 10s linear infinite;
}

@keyframes marquee2 {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0%);
  }
}

.animate-marquee2 {
  animation: marquee2 10s linear infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-outside {
  list-style-position: outside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.columns-1 {
  -moz-columns: 1;
       columns: 1;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-evenly {
  justify-content: space-evenly;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-14 {
  gap: 3.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-\[10px\] {
  gap: 10px;
}

.gap-\[11px\] {
  gap: 11px;
}

.gap-\[13px\] {
  gap: 13px;
}

.gap-\[14px\] {
  gap: 14px;
}

.gap-\[15px\] {
  gap: 15px;
}

.gap-\[170px\] {
  gap: 170px;
}

.gap-\[17px\] {
  gap: 17px;
}

.gap-\[18px\] {
  gap: 18px;
}

.gap-\[190px\] {
  gap: 190px;
}

.gap-\[19px\] {
  gap: 19px;
}

.gap-\[23px\] {
  gap: 23px;
}

.gap-\[25px\] {
  gap: 25px;
}

.gap-\[26px\] {
  gap: 26px;
}

.gap-\[28px\] {
  gap: 28px;
}

.gap-\[30px\] {
  gap: 30px;
}

.gap-\[35px\] {
  gap: 35px;
}

.gap-\[38px\] {
  gap: 38px;
}

.gap-\[40px\] {
  gap: 40px;
}

.gap-\[44px\] {
  gap: 44px;
}

.gap-\[49px\] {
  gap: 49px;
}

.gap-\[51px\] {
  gap: 51px;
}

.gap-\[57px\] {
  gap: 57px;
}

.gap-\[5px\] {
  gap: 5px;
}

.gap-\[6px\] {
  gap: 6px;
}

.gap-\[7px\] {
  gap: 7px;
}

.gap-\[80px\] {
  gap: 80px;
}

.gap-\[8px\] {
  gap: 8px;
}

.gap-\[90px\] {
  gap: 90px;
}

.gap-\[9px\] {
  gap: 9px;
}

.gap-x-\[26px\] {
  -moz-column-gap: 26px;
       column-gap: 26px;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-\[14px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(14px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(14px * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}

.divide-light-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(244 244 246 / var(--tw-divide-opacity));
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.self-baseline {
  align-self: baseline;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-\[100px\] {
  border-radius: 100px;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[13px\] {
  border-radius: 13px;
}

.rounded-\[14\.5px\] {
  border-radius: 14.5px;
}

.rounded-\[14px\] {
  border-radius: 14px;
}

.rounded-\[15px\] {
  border-radius: 15px;
}

.rounded-\[20px\] {
  border-radius: 20px;
}

.rounded-\[26px\] {
  border-radius: 26px;
}

.rounded-\[300px\] {
  border-radius: 300px;
}

.rounded-\[30px\] {
  border-radius: 30px;
}

.rounded-\[6px\] {
  border-radius: 6px;
}

.rounded-\[7px\] {
  border-radius: 7px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}

.rounded-b-\[14px\] {
  border-bottom-right-radius: 14px;
  border-bottom-left-radius: 14px;
}

.rounded-b-\[25px\] {
  border-bottom-right-radius: 25px;
  border-bottom-left-radius: 25px;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-l-2xl {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}

.rounded-l-\[10px\] {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.rounded-l-\[300px\] {
  border-top-left-radius: 300px;
  border-bottom-left-radius: 300px;
}

.rounded-l-\[3px\] {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-2xl {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}

.rounded-r-\[10px\] {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.rounded-r-\[15px\] {
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}

.rounded-r-\[300px\] {
  border-top-right-radius: 300px;
  border-bottom-right-radius: 300px;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-s-lg {
  border-start-start-radius: 0.5rem;
  border-end-start-radius: 0.5rem;
}

.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}

.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.rounded-bl-2xl {
  border-bottom-left-radius: 1rem;
}

.rounded-br-2xl {
  border-bottom-right-radius: 1rem;
}

.rounded-tl-2xl {
  border-top-left-radius: 1rem;
}

.rounded-tl-\[3px\] {
  border-top-left-radius: 3px;
}

.rounded-tr-2xl {
  border-top-right-radius: 1rem;
}

.rounded-tr-\[3px\] {
  border-top-right-radius: 3px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-\[15px\] {
  border-width: 15px;
}

.border-x-8 {
  border-left-width: 8px;
  border-right-width: 8px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-8 {
  border-bottom-width: 8px;
}

.border-l {
  border-left-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-\[\#46556E\] {
  --tw-border-opacity: 1;
  border-color: rgb(70 85 110 / var(--tw-border-opacity));
}

.border-\[\#FDBB47\] {
  --tw-border-opacity: 1;
  border-color: rgb(253 187 71 / var(--tw-border-opacity));
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.border-dark-1\/20 {
  border-color: rgb(8 11 16 / 0.2);
}

.border-dark-4 {
  --tw-border-opacity: 1;
  border-color: rgb(173 179 191 / var(--tw-border-opacity));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-light-1 {
  --tw-border-opacity: 1;
  border-color: rgb(189 194 204 / var(--tw-border-opacity));
}

.border-light-2 {
  --tw-border-opacity: 1;
  border-color: rgb(204 208 215 / var(--tw-border-opacity));
}

.border-light-4 {
  --tw-border-opacity: 1;
  border-color: rgb(225 228 232 / var(--tw-border-opacity));
}

.border-light-5 {
  --tw-border-opacity: 1;
  border-color: rgb(236 237 240 / var(--tw-border-opacity));
}

.border-pastel-green-light {
  --tw-border-opacity: 1;
  border-color: rgb(240 248 236 / var(--tw-border-opacity));
}

.border-primary-blue-dark {
  --tw-border-opacity: 1;
  border-color: rgb(24 43 74 / var(--tw-border-opacity));
}

.border-primary-orange {
  --tw-border-opacity: 1;
  border-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.border-primary-orange\/20 {
  border-color: rgb(239 127 26 / 0.2);
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.border-secondary-green {
  --tw-border-opacity: 1;
  border-color: rgb(102 185 64 / var(--tw-border-opacity));
}

.border-secondary-green\/30 {
  border-color: rgb(102 185 64 / 0.3);
}

.border-secondary-red {
  --tw-border-opacity: 1;
  border-color: rgb(247 47 73 / var(--tw-border-opacity));
}

.border-secondary-red\/30 {
  border-color: rgb(247 47 73 / 0.3);
}

.border-slate-700 {
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/15 {
  border-color: rgb(255 255 255 / 0.15);
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/50 {
  border-color: rgb(255 255 255 / 0.5);
}

.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity));
}

.border-secondary-green\/30 {
  border-color: rgb(102 185 64 / 0.3);
}

.border-x-transparent {
  border-left-color: transparent;
  border-right-color: transparent;
}

.border-b-light-4 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(225 228 232 / var(--tw-border-opacity));
}

.border-b-primary-orange {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.border-b-secondary-green {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(102 185 64 / var(--tw-border-opacity));
}

.bg-\[\#060300\] {
  --tw-bg-opacity: 1;
  background-color: rgb(6 3 0 / var(--tw-bg-opacity));
}

.bg-\[\#182B4AE5\] {
  background-color: #182B4AE5;
}

.bg-\[\#182B4A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(24 43 74 / var(--tw-bg-opacity));
}

.bg-\[\#30415D\] {
  --tw-bg-opacity: 1;
  background-color: rgb(48 65 93 / var(--tw-bg-opacity));
}

.bg-\[\#66b94080\] {
  background-color: #66b94080;
}

.bg-\[\#F4F4F6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 246 / var(--tw-bg-opacity));
}

.bg-\[\#F8F3EE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 243 238 / var(--tw-bg-opacity));
}

.bg-\[\#F8F8F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 248 249 / var(--tw-bg-opacity));
}

.bg-\[\#FAFAFB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 251 / var(--tw-bg-opacity));
}

.bg-\[\#FDFDFD\] {
  --tw-bg-opacity: 1;
  background-color: rgb(253 253 253 / var(--tw-bg-opacity));
}

.bg-\[\#FFEBED\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 237 / var(--tw-bg-opacity));
}

.bg-\[\#ccd0d7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(204 208 215 / var(--tw-bg-opacity));
}

.bg-\[\#ef7f1a1a\] {
  background-color: #ef7f1a1a;
}

.bg-\[\#f5faf2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 250 242 / var(--tw-bg-opacity));
}

.bg-\[\#fffaf6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 250 246 / var(--tw-bg-opacity));
}

.bg-\[\#ffffff26\] {
  background-color: #ffffff26;
}

.bg-\[rgba\(24\2c 43\2c 74\2c 0\.70\)\] {
  background-color: rgba(24,43,74,0.70);
}

.bg-amber-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/15 {
  background-color: rgb(0 0 0 / 0.15);
}

.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-blue-950\/70 {
  background-color: rgb(23 37 84 / 0.7);
}

.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-light-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 208 215 / var(--tw-bg-opacity));
}

.bg-light-4 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 228 232 / var(--tw-bg-opacity));
}

.bg-light-5 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 237 240 / var(--tw-bg-opacity));
}

.bg-light-6 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 246 / var(--tw-bg-opacity));
}

.bg-pastel-green-light {
  --tw-bg-opacity: 1;
  background-color: rgb(240 248 236 / var(--tw-bg-opacity));
}

.bg-pastel-green-light\/50 {
  background-color: rgb(240 248 236 / 0.5);
}

.bg-pastel-orange-light {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 233 / var(--tw-bg-opacity));
}

.bg-pastel-red-light {
  background-color: #FFEBED;;;
}

.bg-primary-blue-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(24 43 74 / var(--tw-bg-opacity));
}

.bg-primary-blue-dark\/90 {
  background-color: rgb(24 43 74 / 0.9);
}

.bg-primary-orange {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.bg-primary-orange\/10 {
  background-color: rgb(239 127 26 / 0.1);
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-secondary-green {
  --tw-bg-opacity: 1;
  background-color: rgb(102 185 64 / var(--tw-bg-opacity));
}

.bg-secondary-green\/10 {
  background-color: rgb(102 185 64 / 0.1);
}

.bg-secondary-green\/20 {
  background-color: rgb(102 185 64 / 0.2);
}

.bg-secondary-light-green {
  background-color: rgba(240, 248, 236, 0.50);
}

.bg-secondary-red {
  --tw-bg-opacity: 1;
  background-color: rgb(247 47 73 / var(--tw-bg-opacity));
}

.bg-secondary-red\/10 {
  background-color: rgb(247 47 73 / 0.1);
}

.bg-secondary-red\/30 {
  background-color: rgb(247 47 73 / 0.3);
}

.bg-slate-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.bg-zinc-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity));
}

.bg-zinc-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}

.bg-\[\#66B940\]\/10 {
  background-color: rgb(102 185 64 / 0.1);
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}

.bg-\[image\:linear-gradient\(to_right\2c white_0\%\2c white_50\%\2c \#f4f4f5_50\%\2c \#f4f4f5_100\%\)\] {
  background-image: linear-gradient(to right,white 0%,white 50%,#f4f4f5 50%,#f4f4f5 100%);
}

.bg-orange-gradient {
  background-image: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-\[\#ECEDF0\] {
  fill: #ECEDF0;
}

.fill-\[\#FDBB47\] {
  fill: #FDBB47;
}

.stroke-primary-orange {
  stroke: #EF7F1A;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-top {
  -o-object-position: top;
     object-position: top;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-3\.5 {
  padding: 0.875rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-\[13px\] {
  padding: 13px;
}

.p-\[14px\] {
  padding: 14px;
}

.p-\[19px\] {
  padding: 19px;
}

.p-\[2px\] {
  padding: 2px;
}

.p-\[30px\] {
  padding: 30px;
}

.p-\[5px\] {
  padding: 5px;
}

.p-px {
  padding: 1px;
}

.\!py-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-20 {
  padding-left: 5rem;
  padding-right: 5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}

.px-\[100px\] {
  padding-left: 100px;
  padding-right: 100px;
}

.px-\[11px\] {
  padding-left: 11px;
  padding-right: 11px;
}

.px-\[14px\] {
  padding-left: 14px;
  padding-right: 14px;
}

.px-\[15px\] {
  padding-left: 15px;
  padding-right: 15px;
}

.px-\[17px\] {
  padding-left: 17px;
  padding-right: 17px;
}

.px-\[18px\] {
  padding-left: 18px;
  padding-right: 18px;
}

.px-\[19px\] {
  padding-left: 19px;
  padding-right: 19px;
}

.px-\[22px\] {
  padding-left: 22px;
  padding-right: 22px;
}

.px-\[23px\] {
  padding-left: 23px;
  padding-right: 23px;
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px;
}

.px-\[25px\] {
  padding-left: 25px;
  padding-right: 25px;
}

.px-\[26px\] {
  padding-left: 26px;
  padding-right: 26px;
}

.px-\[27px\] {
  padding-left: 27px;
  padding-right: 27px;
}

.px-\[28px\] {
  padding-left: 28px;
  padding-right: 28px;
}

.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}

.px-\[31px\] {
  padding-left: 31px;
  padding-right: 31px;
}

.px-\[33px\] {
  padding-left: 33px;
  padding-right: 33px;
}

.px-\[35px\] {
  padding-left: 35px;
  padding-right: 35px;
}

.px-\[36px\] {
  padding-left: 36px;
  padding-right: 36px;
}

.px-\[41px\] {
  padding-left: 41px;
  padding-right: 41px;
}

.px-\[44px\] {
  padding-left: 44px;
  padding-right: 44px;
}

.px-\[50px\] {
  padding-left: 50px;
  padding-right: 50px;
}

.px-\[54px\] {
  padding-left: 54px;
  padding-right: 54px;
}

.px-\[55px\] {
  padding-left: 55px;
  padding-right: 55px;
}

.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}

.px-\[76px\] {
  padding-left: 76px;
  padding-right: 76px;
}

.px-\[81px\] {
  padding-left: 81px;
  padding-right: 81px;
}

.px-\[86px\] {
  padding-left: 86px;
  padding-right: 86px;
}

.px-\[88px\] {
  padding-left: 88px;
  padding-right: 88px;
}

.px-\[92px\] {
  padding-left: 92px;
  padding-right: 92px;
}

.px-\[95px\] {
  padding-left: 95px;
  padding-right: 95px;
}

.px-\[9px\] {
  padding-left: 9px;
  padding-right: 9px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[13px\] {
  padding-top: 13px;
  padding-bottom: 13px;
}

.py-\[14px\] {
  padding-top: 14px;
  padding-bottom: 14px;
}

.py-\[15px\] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.py-\[18px\] {
  padding-top: 18px;
  padding-bottom: 18px;
}

.py-\[22px\] {
  padding-top: 22px;
  padding-bottom: 22px;
}

.py-\[23px\] {
  padding-top: 23px;
  padding-bottom: 23px;
}

.py-\[25px\] {
  padding-top: 25px;
  padding-bottom: 25px;
}

.py-\[26px\] {
  padding-top: 26px;
  padding-bottom: 26px;
}

.py-\[28px\] {
  padding-top: 28px;
  padding-bottom: 28px;
}

.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px;
}

.py-\[30px\] {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-\[33px\] {
  padding-top: 33px;
  padding-bottom: 33px;
}

.py-\[34px\] {
  padding-top: 34px;
  padding-bottom: 34px;
}

.py-\[35px\] {
  padding-top: 35px;
  padding-bottom: 35px;
}

.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}

.py-\[43px\] {
  padding-top: 43px;
  padding-bottom: 43px;
}

.py-\[46px\] {
  padding-top: 46px;
  padding-bottom: 46px;
}

.py-\[58px\] {
  padding-top: 58px;
  padding-bottom: 58px;
}

.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px;
}

.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}

.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.\!pl-5 {
  padding-left: 1.25rem !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-1\.5 {
  padding-bottom: 0.375rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-2\.5 {
  padding-bottom: 0.625rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-32 {
  padding-bottom: 8rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-48 {
  padding-bottom: 12rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-9 {
  padding-bottom: 2.25rem;
}

.pb-\[100px\] {
  padding-bottom: 100px;
}

.pb-\[105px\] {
  padding-bottom: 105px;
}

.pb-\[10px\] {
  padding-bottom: 10px;
}

.pb-\[11px\] {
  padding-bottom: 11px;
}

.pb-\[12px\] {
  padding-bottom: 12px;
}

.pb-\[13px\] {
  padding-bottom: 13px;
}

.pb-\[143px\] {
  padding-bottom: 143px;
}

.pb-\[14px\] {
  padding-bottom: 14px;
}

.pb-\[150px\] {
  padding-bottom: 150px;
}

.pb-\[15px\] {
  padding-bottom: 15px;
}

.pb-\[16px\] {
  padding-bottom: 16px;
}

.pb-\[188px\] {
  padding-bottom: 188px;
}

.pb-\[18px\] {
  padding-bottom: 18px;
}

.pb-\[19px\] {
  padding-bottom: 19px;
}

.pb-\[20px\] {
  padding-bottom: 20px;
}

.pb-\[21px\] {
  padding-bottom: 21px;
}

.pb-\[22px\] {
  padding-bottom: 22px;
}

.pb-\[23px\] {
  padding-bottom: 23px;
}

.pb-\[25px\] {
  padding-bottom: 25px;
}

.pb-\[26px\] {
  padding-bottom: 26px;
}

.pb-\[27px\] {
  padding-bottom: 27px;
}

.pb-\[28px\] {
  padding-bottom: 28px;
}

.pb-\[30px\] {
  padding-bottom: 30px;
}

.pb-\[32px\] {
  padding-bottom: 32px;
}

.pb-\[34px\] {
  padding-bottom: 34px;
}

.pb-\[35px\] {
  padding-bottom: 35px;
}

.pb-\[37px\] {
  padding-bottom: 37px;
}

.pb-\[45px\] {
  padding-bottom: 45px;
}

.pb-\[49px\] {
  padding-bottom: 49px;
}

.pb-\[50px\] {
  padding-bottom: 50px;
}

.pb-\[55px\] {
  padding-bottom: 55px;
}

.pb-\[58px\] {
  padding-bottom: 58px;
}

.pb-\[5px\] {
  padding-bottom: 5px;
}

.pb-\[60px\] {
  padding-bottom: 60px;
}

.pb-\[61px\] {
  padding-bottom: 61px;
}

.pb-\[7px\] {
  padding-bottom: 7px;
}

.pb-\[83px\] {
  padding-bottom: 83px;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-2\.5 {
  padding-left: 0.625rem;
}

.pl-20 {
  padding-left: 5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-\[110px\] {
  padding-left: 110px;
}

.pl-\[11px\] {
  padding-left: 11px;
}

.pl-\[14px\] {
  padding-left: 14px;
}

.pl-\[15px\] {
  padding-left: 15px;
}

.pl-\[18px\] {
  padding-left: 18px;
}

.pl-\[19px\] {
  padding-left: 19px;
}

.pl-\[21px\] {
  padding-left: 21px;
}

.pl-\[22px\] {
  padding-left: 22px;
}

.pl-\[23px\] {
  padding-left: 23px;
}

.pl-\[26px\] {
  padding-left: 26px;
}

.pl-\[2px\] {
  padding-left: 2px;
}

.pl-\[30px\] {
  padding-left: 30px;
}

.pl-\[32px\] {
  padding-left: 32px;
}

.pl-\[41px\] {
  padding-left: 41px;
}

.pl-\[44px\] {
  padding-left: 44px;
}

.pl-\[47px\] {
  padding-left: 47px;
}

.pl-\[49px\] {
  padding-left: 49px;
}

.pl-\[50px\] {
  padding-left: 50px;
}

.pl-\[53px\] {
  padding-left: 53px;
}

.pl-\[58px\] {
  padding-left: 58px;
}

.pl-\[59px\] {
  padding-left: 59px;
}

.pl-\[73px\] {
  padding-left: 73px;
}

.pl-\[7px\] {
  padding-left: 7px;
}

.pr-0 {
  padding-right: 0px;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-11 {
  padding-right: 2.75rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-2\.5 {
  padding-right: 0.625rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-5 {
  padding-right: 1.25rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-7 {
  padding-right: 1.75rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pr-\[11px\] {
  padding-right: 11px;
}

.pr-\[15px\] {
  padding-right: 15px;
}

.pr-\[19px\] {
  padding-right: 19px;
}

.pr-\[21px\] {
  padding-right: 21px;
}

.pr-\[24px\] {
  padding-right: 24px;
}

.pr-\[26px\] {
  padding-right: 26px;
}

.pr-\[32px\] {
  padding-right: 32px;
}

.pr-\[37px\] {
  padding-right: 37px;
}

.pr-\[39px\] {
  padding-right: 39px;
}

.pr-\[3px\] {
  padding-right: 3px;
}

.pr-\[49px\] {
  padding-right: 49px;
}

.pr-\[4px\] {
  padding-right: 4px;
}

.pr-\[50px\] {
  padding-right: 50px;
}

.pr-\[7px\] {
  padding-right: 7px;
}

.pr-\[97px\] {
  padding-right: 97px;
}

.pr-\[9px\] {
  padding-right: 9px;
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-11 {
  padding-top: 2.75rem;
}

.pt-14 {
  padding-top: 3.5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-20 {
  padding-top: 5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-7 {
  padding-top: 1.75rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-\[101px\] {
  padding-top: 101px;
}

.pt-\[10px\] {
  padding-top: 10px;
}

.pt-\[110px\] {
  padding-top: 110px;
}

.pt-\[11px\] {
  padding-top: 11px;
}

.pt-\[13px\] {
  padding-top: 13px;
}

.pt-\[14px\] {
  padding-top: 14px;
}

.pt-\[15px\] {
  padding-top: 15px;
}

.pt-\[166px\] {
  padding-top: 166px;
}

.pt-\[16px\] {
  padding-top: 16px;
}

.pt-\[17px\] {
  padding-top: 17px;
}

.pt-\[18px\] {
  padding-top: 18px;
}

.pt-\[19px\] {
  padding-top: 19px;
}

.pt-\[21px\] {
  padding-top: 21px;
}

.pt-\[25px\] {
  padding-top: 25px;
}

.pt-\[26px\] {
  padding-top: 26px;
}

.pt-\[272px\] {
  padding-top: 272px;
}

.pt-\[29px\] {
  padding-top: 29px;
}

.pt-\[30px\] {
  padding-top: 30px;
}

.pt-\[33px\] {
  padding-top: 33px;
}

.pt-\[34px\] {
  padding-top: 34px;
}

.pt-\[35px\] {
  padding-top: 35px;
}

.pt-\[36px\] {
  padding-top: 36px;
}

.pt-\[37px\] {
  padding-top: 37px;
}

.pt-\[39px\] {
  padding-top: 39px;
}

.pt-\[41px\] {
  padding-top: 41px;
}

.pt-\[42px\] {
  padding-top: 42px;
}

.pt-\[45px\] {
  padding-top: 45px;
}

.pt-\[46px\] {
  padding-top: 46px;
}

.pt-\[49px\] {
  padding-top: 49px;
}

.pt-\[50px\] {
  padding-top: 50px;
}

.pt-\[51px\] {
  padding-top: 51px;
}

.pt-\[58px\] {
  padding-top: 58px;
}

.pt-\[60px\] {
  padding-top: 60px;
}

.pt-\[61px\] {
  padding-top: 61px;
}

.pt-\[62px\] {
  padding-top: 62px;
}

.pt-\[63px\] {
  padding-top: 63px;
}

.pt-\[66px\] {
  padding-top: 66px;
}

.pt-\[70px\] {
  padding-top: 70px;
}

.pt-\[73px\] {
  padding-top: 73px;
}

.pt-\[74px\] {
  padding-top: 74px;
}

.pt-\[77px\] {
  padding-top: 77px;
}

.pt-\[78px\] {
  padding-top: 78px;
}

.pt-\[7px\] {
  padding-top: 7px;
}

.pt-\[83px\] {
  padding-top: 83px;
}

.pt-\[84px\] {
  padding-top: 84px;
}

.pt-\[88px\] {
  padding-top: 88px;
}

.pt-\[9px\] {
  padding-top: 9px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-middle {
  vertical-align: middle;
}

.font-\[\'DS-Digital\'\] {
  font-family: 'DS-Digital';
}

.font-\[\'Roboto\'\] {
  font-family: 'Roboto';
}

.font-consolas {
  font-family: Consolas, monospace;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[160px\] {
  font-size: 160px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[17px\] {
  font-size: 17px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[19px\] {
  font-size: 19px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[21px\] {
  font-size: 21px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[25px\] {
  font-size: 25px;
}

.text-\[26px\] {
  font-size: 26px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-\[30px\] {
  font-size: 30px;
}

.text-\[32px\] {
  font-size: 32px;
}

.text-\[33px\] {
  font-size: 33px;
}

.text-\[34px\] {
  font-size: 34px;
}

.text-\[35px\] {
  font-size: 35px;
}

.text-\[38px\] {
  font-size: 38px;
}

.text-\[39px\] {
  font-size: 39px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-\[44px\] {
  font-size: 44px;
}

.text-\[45px\] {
  font-size: 45px;
}

.text-\[50px\] {
  font-size: 50px;
}

.text-\[55px\] {
  font-size: 55px;
}

.text-\[56px\] {
  font-size: 56px;
}

.text-\[6px\] {
  font-size: 6px;
}

.text-\[7px\] {
  font-size: 7px;
}

.text-\[82px\] {
  font-size: 82px;
}

.text-\[8px\] {
  font-size: 8px;
}

.text-\[9px\] {
  font-size: 9px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-\[10px\] {
  line-height: 10px;
}

.leading-\[12px\] {
  line-height: 12px;
}

.leading-\[13px\] {
  line-height: 13px;
}

.leading-\[15px\] {
  line-height: 15px;
}

.leading-\[16px\] {
  line-height: 16px;
}

.leading-\[17\.50px\] {
  line-height: 17.50px;
}

.leading-\[17\.5px\] {
  line-height: 17.5px;
}

.leading-\[178px\] {
  line-height: 178px;
}

.leading-\[18\.75px\] {
  line-height: 18.75px;
}

.leading-\[18px\] {
  line-height: 18px;
}

.leading-\[19px\] {
  line-height: 19px;
}

.leading-\[20px\] {
  line-height: 20px;
}

.leading-\[21\.5px\] {
  line-height: 21.5px;
}

.leading-\[21px\] {
  line-height: 21px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[22x\] {
  line-height: 22x;
}

.leading-\[23px\] {
  line-height: 23px;
}

.leading-\[24\.5px\] {
  line-height: 24.5px;
}

.leading-\[24px\] {
  line-height: 24px;
}

.leading-\[25px\] {
  line-height: 25px;
}

.leading-\[26px\] {
  line-height: 26px;
}

.leading-\[27px\] {
  line-height: 27px;
}

.leading-\[28px\] {
  line-height: 28px;
}

.leading-\[30px\] {
  line-height: 30px;
}

.leading-\[31\.5px\] {
  line-height: 31.5px;
}

.leading-\[31\.px\] {
  line-height: 31.px;
}

.leading-\[31px\] {
  line-height: 31px;
}

.leading-\[32px\] {
  line-height: 32px;
}

.leading-\[35px\] {
  line-height: 35px;
}

.leading-\[37px\] {
  line-height: 37px;
}

.leading-\[38px\] {
  line-height: 38px;
}

.leading-\[39\.5px\] {
  line-height: 39.5px;
}

.leading-\[39px\] {
  line-height: 39px;
}

.leading-\[40px\] {
  line-height: 40px;
}

.leading-\[40x\] {
  line-height: 40x;
}

.leading-\[41px\] {
  line-height: 41px;
}

.leading-\[45px\] {
  line-height: 45px;
}

.leading-\[49\.5px\] {
  line-height: 49.5px;
}

.leading-\[50px\] {
  line-height: 50px;
}

.leading-\[54px\] {
  line-height: 54px;
}

.leading-\[58px\] {
  line-height: 58px;
}

.leading-\[59px\] {
  line-height: 59px;
}

.leading-\[60px\] {
  line-height: 60px;
}

.leading-\[67px\] {
  line-height: 67px;
}

.leading-\[72px\] {
  line-height: 72px;
}

.leading-\[75px\] {
  line-height: 75px;
}

.leading-\[86px\] {
  line-height: 86px;
}

.leading-loose {
  line-height: 2;
}

.leading-normal {
  line-height: 1.5;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-\[-4px\] {
  letter-spacing: -4px;
}

.tracking-\[-5px\] {
  letter-spacing: -5px;
}

.tracking-\[-8px\] {
  letter-spacing: -8px;
}

.tracking-\[2\.28px\] {
  letter-spacing: 2.28px;
}

.tracking-\[2px\] {
  letter-spacing: 2px;
}

.tracking-\[5px\] {
  letter-spacing: 5px;
}

.tracking-normal {
  letter-spacing: 0em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-\[\#182B4A\] {
  --tw-text-opacity: 1;
  color: rgb(24 43 74 / var(--tw-text-opacity));
}

.text-\[\#2F67C2\] {
  --tw-text-opacity: 1;
  color: rgb(47 103 194 / var(--tw-text-opacity));
}

.text-\[\#646C7C\] {
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity));
}

.text-\[\#6B5E62\] {
  --tw-text-opacity: 1;
  color: rgb(107 94 98 / var(--tw-text-opacity));
}

.text-\[\#9099A8\] {
  --tw-text-opacity: 1;
  color: rgb(144 153 168 / var(--tw-text-opacity));
}

.text-\[\#B9BFC8\] {
  --tw-text-opacity: 1;
  color: rgb(185 191 200 / var(--tw-text-opacity));
}

.text-\[20xl\] {
  color: 20xl;
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-current {
  color: currentColor;
}

.text-dark-1 {
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.text-dark-1\/40 {
  color: rgb(8 11 16 / 0.4);
}

.text-dark-2 {
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity));
}

.text-dark-3 {
  --tw-text-opacity: 1;
  color: rgb(128 137 156 / var(--tw-text-opacity));
}

.text-dark-4 {
  --tw-text-opacity: 1;
  color: rgb(173 179 191 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-light-2 {
  --tw-text-opacity: 1;
  color: rgb(204 208 215 / var(--tw-text-opacity));
}

.text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

.text-pastel-green-light {
  --tw-text-opacity: 1;
  color: rgb(240 248 236 / var(--tw-text-opacity));
}

.text-pastel-orange-light {
  --tw-text-opacity: 1;
  color: rgb(254 243 233 / var(--tw-text-opacity));
}

.text-primary-blue-dark {
  --tw-text-opacity: 1;
  color: rgb(24 43 74 / var(--tw-text-opacity));
}

.text-primary-orange {
  --tw-text-opacity: 1;
  color: rgb(239 127 26 / var(--tw-text-opacity));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-secondary-green {
  --tw-text-opacity: 1;
  color: rgb(102 185 64 / var(--tw-text-opacity));
}

.text-secondary-red {
  --tw-text-opacity: 1;
  color: rgb(247 47 73 / var(--tw-text-opacity));
}

.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
}

.text-transparent {
  color: transparent;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-white\/40 {
  color: rgb(255 255 255 / 0.4);
}

.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.text-zinc-950 {
  --tw-text-opacity: 1;
  color: rgb(9 9 11 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.accent-primary-orange {
  accent-color: #EF7F1A;
}

.opacity-0 {
  opacity: 0;
}

.opacity-10 {
  opacity: 0.1;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-70 {
  opacity: 0.7;
}

.shadow-custom {
  --tw-shadow: 0px 15px 83px 0px rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0px 15px 83px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
  --tw-ring-inset: inset;
}

.ring-secondary-green\/30 {
  --tw-ring-color: rgb(102 185 64 / 0.3);
}

.ring-secondary-red\/30 {
  --tw-ring-color: rgb(247 47 73 / 0.3);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[2px\] {
  --tw-blur: blur(2px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(40px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[11px\] {
  --tw-backdrop-blur: blur(11px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[15px\] {
  --tw-backdrop-blur: blur(15px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[1px\] {
  --tw-backdrop-blur: blur(1px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[3px\] {
  --tw-backdrop-blur: blur(3px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[6px\] {
  --tw-backdrop-blur: blur(6px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-\[7\.5px\] {
  --tw-backdrop-blur: blur(7.5px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* roboto-300 - latin_latin-ext */

@font-face {
  font-display: swap;

  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */

  font-family: 'Roboto';

  font-style: normal;

  font-weight: 300;

  src: url('../fonts/roboto-v32-latin_latin-ext-300.woff2') format('woff2');

  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* roboto-regular - latin_latin-ext */

@font-face {
  font-display: swap;

  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */

  font-family: 'Roboto';

  font-style: normal;

  font-weight: 400;

  src: url('../fonts/roboto-v32-latin_latin-ext-regular.woff2')
		format('woff2');

  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* roboto-500 - latin_latin-ext */

@font-face {
  font-display: swap;

  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */

  font-family: 'Roboto';

  font-style: normal;

  font-weight: 500;

  src: url('../fonts/roboto-v32-latin_latin-ext-500.woff2') format('woff2');

  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* roboto-700 - latin_latin-ext */

@font-face {
  font-display: swap;

  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */

  font-family: 'Roboto';

  font-style: normal;

  font-weight: 700;

  src: url('../fonts/roboto-v32-latin_latin-ext-700.woff2') format('woff2');

  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

/* roboto-900 - latin_latin-ext */

@font-face {
  font-display: swap;

  /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */

  font-family: 'Roboto';

  font-style: normal;

  font-weight: 900;

  src: url('../fonts/roboto-v32-latin_latin-ext-900.woff2') format('woff2');

  /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

@font-face {
  font-display: swap;

  font-family: 'DS-Digital';

  font-style: normal;

  font-weight: 400;

  src: url("../fonts/ds-digital/DS-DIGI.woff2") format("woff2");
}

body {
  font-family: Roboto, sans-serif;
  font-weight: 400;
}

.shadow-hover {
  border-radius: 0.75rem;
  transition-duration: 200ms;
}

.shadow-hover:hover {
  cursor: pointer;
  border-color: transparent;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

._value {
  font-size: 26px;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(102 185 64 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  ._value {
    font-size: 33px;
  }
}

._symbol {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(102 185 64 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  ._symbol {
    font-size: 22px;
  }
}

._value.minimal {
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  ._value.minimal {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

._symbol.minimal {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(100 108 124 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  ._symbol.minimal {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

._suffix {
  display: block;
}

.reward-box--small ._suffix {
  display: inline-block;
}

@media (min-width: 768px) {
  .reward-box--small ._suffix {
    display: block;
  }
}

.shop-offers ._symbol,
.shop-offers ._value {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.search-reward ._symbol {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.search-reward ._value {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.search-reward ._suffix {
  display: inline;
}

:root {
  --aa-detached-media-query: none;
}

#autocomplete {
  margin-left: 2.25rem;
  height: 47px;
  width: 365px;
  align-items: center;
  border-radius: 14px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  #autocomplete {
    display: flex;
  }
}

.aa-Autocomplete {
  width: 100%;
}

.aa-Form {
  display: flex;
  width: 100%;
  align-items: center;
  padding-left: 1rem;
  padding-right: 3px;
}

.aa-Panel {
  position: absolute;
  left: 0px;
  right: 0px;
  z-index: 50;
  margin-left: -63px;
  margin-top: 0.875rem;
  height: auto;
  width: 100% !important;
  overflow: hidden;
  border-top-left-radius: 3px;
  border-top-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  .aa-Panel {
    margin-left: 0px;
    margin-top: 22px;
    width: 477px !important;
  }
}

.aa-InputWrapperPrefix {
  order: 1;
}

.aa-Label {
  display: block;
}

.aa-SubmitButton {
  display: flex;
  height: 41px;
  width: 41px;
  align-items: center;
  justify-content: center;
}

.aa-InputWrapper {
  flex-grow: 1;
}

input.aa-Input {
  display: block;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 0px;
  font-size: 1rem;
  line-height: 1.5rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

input[type='search']:focus {
  --tw-ring-color: transparent;
}

.aa-InputWrapperSuffix {
  height: 18px;
}

input[type='search']::-webkit-search-cancel-button {
  display: none;
}

.similar-shop__value ._value {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  line-height: 18.75px;
}

.similar-shop__value ._symbol {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  line-height: 18.75px;
}

.similar-shop__value ._suffix,
.similar-shop__value ._upTo {
  display: inline;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 18.75px;
}

.shop-description p {
  margin-bottom: 1rem;
}

.content-block h2 {
  margin-bottom: 0.625rem;
  margin-top: 1.25rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  line-height: 28px;
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.content-block h3 {
  margin-bottom: 0.625rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  line-height: 28px;
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.content-block p {
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 24.5px;
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.content-block a {
  text-decoration-line: underline;
}

.content-block a:hover {
  text-decoration-line: none;
}

.content-block img.img-responsive {
  margin-bottom: 1.25rem;
}

.content-block ul,
.content-block ol {
  list-style-type: disc;
  padding-left: 0.75rem;
}

.content-block ol {
  list-style-type: decimal;
}

.content-block li {
  margin-bottom: 0.875rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 24.5px;
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.content-block table {
  margin-top: 2rem;
  margin-bottom: 2rem;
  width: 100%;
  border-collapse: collapse;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.content-block th {
  text-align: left;
}

.content-block th,
.content-block td {
  border-width: 1px;
  padding: 0.5rem;
}

@media (min-width: 768px) {
  .content-block th,
.content-block td {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.faq-content p,
.faq-content li {
  margin-bottom: 1.25rem;
}

.faq-content ul {
  list-style-type: disc;
  padding-left: 0.75rem;
}

.shop-item-dropdown__reward ._value {
  font-size: 1.5rem;
  line-height: 2rem;
}

.shop-item-dropdown__reward ._symbol {
  font-size: 1rem;
  line-height: 1.5rem;
}

/* FORM CONTENT */

.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
  margin: auto;
  margin-bottom: 0.5rem;
  font-size: 25px;
  font-weight: 500;
  line-height: 39px;
  --tw-text-opacity: 1;
  color: rgb(8 11 16 / var(--tw-text-opacity));
}

.content p,
.content div {
  margin: auto;
  margin-bottom: 1.25rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 31.5px;
}

@media (min-width: 768px) {
  .content p,
.content div {
    margin-bottom: 2.5rem;
  }
}

.content img {
  margin-bottom: 60px;
  border-radius: 0.5rem;
}

.content ul {
  margin-bottom: 1.25rem;
  list-style-type: disc;
  line-height: 31.5px;
}

.content ul ul {
  margin-left: 1rem;
}

.content ol {
  margin-bottom: 1.25rem;
  list-style-type: decimal;
  line-height: 31.5px;
}

.content ol ol {
  margin-bottom: 1.25rem;
  list-style-type: decimal;
  line-height: 31.5px;
  counter-reset: item;
}

.content li {
  margin-bottom: 0.5rem;
}

.content li a {
  text-decoration-line: underline;
}

.content__outline li {
  margin-bottom: 0px;
}

.content table {
  margin-bottom: 60px;
  table-layout: auto;
  border-collapse: collapse;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.content td {
  border-width: 1px;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.g-recaptcha {
  margin-top: 0.25rem;
  border-style: none;
  padding: 0px;
}

.search-shop-inner__active {
  --tw-border-opacity: 1;
  border-color: rgb(102 185 64 / var(--tw-border-opacity));
  background-color: rgb(102 185 64 / 0.1);
  --tw-backdrop-blur: blur(11px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

/* SWIPER - MOJE OBLIBENE */

.swiper-button-prev.swiper-coupon-button-prev.swiper-button-disabled {
  display: none;
}

.swiper-button-next.swiper-coupon-button-next.swiper-button-disabled {
  display: none;
}

.swiper-button-prev.swiper1-button-prev.swiper-button-disabled {
  display: none;
}

.swiper-button-next.swiper1-button-next.swiper-button-disabled {
  display: none;
}

.swiper-button-next.banner-swiper-button-next.swiper-button-disabled {
  display: none;
}

.swiper-button-prev.banner-swiper-button-prev.swiper-button-disabled {
  display: none;
}

.swiper-button-prev.swiper-tipli-extra-button-prev.swiper-button-disabled {
  display: none;
}

.swiper-button-next.swiper-tipli-extra-button-next.swiper-button-disabled {
  display: none;
}

/* TIPPY - TOOLTIPS */

.tippy-box[data-theme~='transaction-tooltip'] {
  background-color: #ecedf0;
  padding: 6px;
  border-radius: 6px;
  max-width: 288px;
  font-size: 12px;
  line-height: 21px;
  text-align: center;
  color: #000;
}

.tippy-box[data-theme~='transaction-tooltip'][data-placement^='bottom']
	> .tippy-arrow::before {
  border-bottom-color: #ecedf0;
}

.no-default-style {
  outline: none !important;
  box-shadow: none !important;
}

.no-default-style:checked {
  background-image: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.footer-sticker {
  position: absolute;
  top: 72.5%;
  left: 20%;
}

@media only screen and (min-width: 310px) {
  .footer-sticker {
    left: 15%;
  }
}

@media only screen and (min-width: 390px) {
  .footer-sticker {
    left: 21%;
  }
}

@media only screen and (min-width: 414px) {
  .footer-sticker {
    left: 23%;
  }
}

@media only screen and (min-width: 430px) {
  .footer-sticker {
    left: 24%;
    top: 73.5%;
  }
}

@media only screen and (min-width: 500px) {
  .footer-sticker {
    left: 25%;
    top: 78.5%;
  }
}

@media only screen and (min-width: 600px) {
  .footer-sticker {
    left: 26%;
    top: 87.5%;
  }
}

.placeholder\:leading-\[24\.5px\]::-moz-placeholder {
  line-height: 24.5px;
}

.placeholder\:leading-\[24\.5px\]::placeholder {
  line-height: 24.5px;
}

.placeholder\:text-white::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.placeholder\:text-white::placeholder {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:left-2\/4::before {
  content: var(--tw-content);
  left: 50%;
}

.before\:top-2\/4::before {
  content: var(--tw-content);
  top: 50%;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:inline-block::before {
  content: var(--tw-content);
  display: inline-block;
}

.before\:size-6::before {
  content: var(--tw-content);
  width: 1.5rem;
  height: 1.5rem;
}

.before\:h-12::before {
  content: var(--tw-content);
  height: 3rem;
}

.before\:w-12::before {
  content: var(--tw-content);
  width: 3rem;
}

.before\:-translate-x-2\/4::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-y-2\/4::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:translate-x-0::before {
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:transform::before {
  content: var(--tw-content);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:bg-white\/50::before {
  content: var(--tw-content);
  background-color: rgb(255 255 255 / 0.5);
}

.before\:opacity-0::before {
  content: var(--tw-content);
  opacity: 0;
}

.before\:ring-0::before {
  content: var(--tw-content);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.before\:transition::before {
  content: var(--tw-content);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:transition-opacity::before {
  content: var(--tw-content);
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:duration-200::before {
  content: var(--tw-content);
  transition-duration: 200ms;
}

.before\:ease-in-out::before {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}

.last\:border-none:last-child {
  border-style: none;
}

.checked\:border-primary-orange:checked {
  --tw-border-opacity: 1;
  border-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.checked\:bg-\[\#66B940\]:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(102 185 64 / var(--tw-bg-opacity));
}

.checked\:bg-primary-orange:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.checked\:text-\[\#66B940\]:checked {
  --tw-text-opacity: 1;
  color: rgb(102 185 64 / var(--tw-text-opacity));
}

.checked\:before\:translate-x-full:checked::before {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:before\:bg-primary-orange:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.checked\:before\:bg-white:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.focus-within\:text-gray-400:focus-within {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-95:hover {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.01\]:hover {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[102\%\]:hover {
  --tw-scale-x: 102%;
  --tw-scale-y: 102%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border:hover {
  border-width: 1px;
}

.hover\:border-solid:hover {
  border-style: solid;
}

.hover\:border-primary-orange:hover {
  --tw-border-opacity: 1;
  border-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.hover\:border-secondary-green:hover {
  --tw-border-opacity: 1;
  border-color: rgb(102 185 64 / var(--tw-border-opacity));
}

.hover\:border-white:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.hover\:bg-\[\#30415D\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(48 65 93 / var(--tw-bg-opacity));
}

.hover\:bg-black\/80:hover {
  background-color: rgb(0 0 0 / 0.8);
}

.hover\:bg-blue-500\/20:hover {
  background-color: rgb(59 130 246 / 0.2);
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity));
}

.hover\:bg-primary-orange\/20:hover {
  background-color: rgb(239 127 26 / 0.2);
}

.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity));
}

.hover\:bg-red-500\/20:hover {
  background-color: rgb(239 68 68 / 0.2);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.hover\:bg-secondary-green\/20:hover {
  background-color: rgb(102 185 64 / 0.2);
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-zinc-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.hover\:bg-zinc-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}

.hover\:bg-blue-dark-gradient-hover:hover {
  background-image: linear-gradient(90deg, #182B4A 10%, #304E73 90%);
}

.hover\:bg-gray-gradient-hover:hover {
  background-image: linear-gradient(90deg, #F3F4F6 0%, #E5E7EB 100%);
}

.hover\:bg-orange-gradient-hover:hover {
  background-image: linear-gradient(309deg, #EF7F1A 13.11%, #FFA439 96.21%);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:before\:opacity-10:hover::before {
  content: var(--tw-content);
  opacity: 0.1;
}

.checked\:hover\:bg-primary-orange:hover:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.focus\:border-primary-orange:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:bg-primary-orange:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary-orange:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 127 26 / var(--tw-ring-opacity));
}

.focus\:ring-transparent:focus {
  --tw-ring-color: transparent;
}

.focus\:checked\:border-\[\#66B940\]:checked:focus {
  --tw-border-opacity: 1;
  border-color: rgb(102 185 64 / var(--tw-border-opacity));
}

.checked\:focus\:bg-primary-orange:focus:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.checked\:active\:bg-primary-orange:active:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(239 127 26 / var(--tw-bg-opacity));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:z-20 {
  z-index: 20;
}

.group:hover .group-hover\:cursor-pointer {
  cursor: pointer;
}

.group:hover .group-hover\:border-solid {
  border-style: solid;
}

.group:hover .group-hover\:border-primary-orange {
  --tw-border-opacity: 1;
  border-color: rgb(239 127 26 / var(--tw-border-opacity));
}

.group:hover .group-hover\:border-transparent {
  border-color: transparent;
}

.group\/banner:hover .group-hover\/banner\:bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity));
}

.group\/banner:hover .group-hover\/banner\:stroke-white {
  stroke: #fff;
}

.group:hover .group-hover\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:checked ~ .peer-checked\:text-\[\#66B940\] {
  --tw-text-opacity: 1;
  color: rgb(102 185 64 / var(--tw-text-opacity));
}

.peer:checked ~ .peer-checked\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.peer:checked ~ .peer-checked\:opacity-100 {
  opacity: 1;
}

@media (min-width: 640px) {
  .sm\:absolute {
    position: absolute;
  }

  .sm\:left-\[80px\] {
    left: 80px;
  }

  .sm\:right-\[9px\] {
    right: 9px;
  }

  .sm\:top-\[97px\] {
    top: 97px;
  }

  .sm\:row-start-2 {
    grid-row-start: 2;
  }

  .sm\:mb-2\.5 {
    margin-bottom: 0.625rem;
  }

  .sm\:mb-\[25px\] {
    margin-bottom: 25px;
  }

  .sm\:mb-\[5px\] {
    margin-bottom: 5px;
  }

  .sm\:mt-\[17px\] {
    margin-top: 17px;
  }

  .sm\:mt-\[43px\] {
    margin-top: 43px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-\[295px\] {
    width: 295px;
  }

  .sm\:w-\[395px\] {
    width: 395px;
  }

  .sm\:w-\[425px\] {
    width: 425px;
  }

  .sm\:w-\[428px\] {
    width: 428px;
  }

  .sm\:w-\[499px\] {
    width: 499px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .sm\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:gap-\[148px\] {
    gap: 148px;
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:bg-transparent {
    background-color: transparent;
  }

  .sm\:pt-0 {
    padding-top: 0px;
  }

  .sm\:pt-\[102px\] {
    padding-top: 102px;
  }

  .sm\:pt-\[65px\] {
    padding-top: 65px;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-start {
    text-align: start;
  }

  .sm\:text-\[32px\] {
    font-size: 32px;
  }

  .sm\:text-\[40px\] {
    font-size: 40px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:leading-7 {
    line-height: 1.75rem;
  }

  .sm\:leading-\[35px\] {
    line-height: 35px;
  }

  .sm\:leading-\[58px\] {
    line-height: 58px;
  }

  .sm\:text-primary-orange {
    --tw-text-opacity: 1;
    color: rgb(239 127 26 / var(--tw-text-opacity));
  }

  .sm\:blur-\[2px\] {
    --tw-blur: blur(2px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
}

@media (min-width: 768px) {
  .md\:static {
    position: static;
  }

  .md\:absolute {
    position: absolute;
  }

  .md\:bottom-0 {
    bottom: 0px;
  }

  .md\:bottom-\[-70px\] {
    bottom: -70px;
  }

  .md\:bottom-\[-74px\] {
    bottom: -74px;
  }

  .md\:left-\[-23px\] {
    left: -23px;
  }

  .md\:left-\[150px\] {
    left: 150px;
  }

  .md\:left-\[50\.5\%\] {
    left: 50.5%;
  }

  .md\:left-\[900px\] {
    left: 900px;
  }

  .md\:left-auto {
    left: auto;
  }

  .md\:right-0 {
    right: 0px;
  }

  .md\:right-10 {
    right: 2.5rem;
  }

  .md\:right-\[-23px\] {
    right: -23px;
  }

  .md\:right-\[190px\] {
    right: 190px;
  }

  .md\:right-\[31px\] {
    right: 31px;
  }

  .md\:right-\[90px\] {
    right: 90px;
  }

  .md\:right-auto {
    right: auto;
  }

  .md\:top-\[-10px\] {
    top: -10px;
  }

  .md\:top-\[-200px\] {
    top: -200px;
  }

  .md\:top-\[-50px\] {
    top: -50px;
  }

  .md\:top-\[114px\] {
    top: 114px;
  }

  .md\:top-\[16px\] {
    top: 16px;
  }

  .md\:top-\[20px\] {
    top: 20px;
  }

  .md\:top-\[31px\] {
    top: 31px;
  }

  .md\:top-\[47\%\] {
    top: 47%;
  }

  .md\:top-\[56px\] {
    top: 56px;
  }

  .md\:z-20 {
    z-index: 20;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:m-auto {
    margin: auto;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .md\:my-\[11px\] {
    margin-top: 11px;
    margin-bottom: 11px;
  }

  .md\:my-\[34px\] {
    margin-top: 34px;
    margin-bottom: 34px;
  }

  .md\:my-\[35px\] {
    margin-top: 35px;
    margin-bottom: 35px;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-1\.5 {
    margin-bottom: 0.375rem;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:mb-2\.5 {
    margin-bottom: 0.625rem;
  }

  .md\:mb-20 {
    margin-bottom: 5rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-9 {
    margin-bottom: 2.25rem;
  }

  .md\:mb-\[100px\] {
    margin-bottom: 100px;
  }

  .md\:mb-\[110px\] {
    margin-bottom: 110px;
  }

  .md\:mb-\[130px\] {
    margin-bottom: 130px;
  }

  .md\:mb-\[133px\] {
    margin-bottom: 133px;
  }

  .md\:mb-\[13px\] {
    margin-bottom: 13px;
  }

  .md\:mb-\[14px\] {
    margin-bottom: 14px;
  }

  .md\:mb-\[17px\] {
    margin-bottom: 17px;
  }

  .md\:mb-\[18px\] {
    margin-bottom: 18px;
  }

  .md\:mb-\[22px\] {
    margin-bottom: 22px;
  }

  .md\:mb-\[23px\] {
    margin-bottom: 23px;
  }

  .md\:mb-\[25px\] {
    margin-bottom: 25px;
  }

  .md\:mb-\[27px\] {
    margin-bottom: 27px;
  }

  .md\:mb-\[28px\] {
    margin-bottom: 28px;
  }

  .md\:mb-\[29px\] {
    margin-bottom: 29px;
  }

  .md\:mb-\[30px\] {
    margin-bottom: 30px;
  }

  .md\:mb-\[31px\] {
    margin-bottom: 31px;
  }

  .md\:mb-\[32px\] {
    margin-bottom: 32px;
  }

  .md\:mb-\[38px\] {
    margin-bottom: 38px;
  }

  .md\:mb-\[45px\] {
    margin-bottom: 45px;
  }

  .md\:mb-\[5px\] {
    margin-bottom: 5px;
  }

  .md\:mb-\[70px\] {
    margin-bottom: 70px;
  }

  .md\:mb-\[74px\] {
    margin-bottom: 74px;
  }

  .md\:mb-\[77px\] {
    margin-bottom: 77px;
  }

  .md\:mb-\[87px\] {
    margin-bottom: 87px;
  }

  .md\:ml-0 {
    margin-left: 0px;
  }

  .md\:ml-2\.5 {
    margin-left: 0.625rem;
  }

  .md\:ml-5 {
    margin-left: 1.25rem;
  }

  .md\:ml-\[-30px\] {
    margin-left: -30px;
  }

  .md\:ml-\[28px\] {
    margin-left: 28px;
  }

  .md\:ml-\[30px\] {
    margin-left: 30px;
  }

  .md\:ml-\[51px\] {
    margin-left: 51px;
  }

  .md\:ml-\[62px\] {
    margin-left: 62px;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mr-5 {
    margin-right: 1.25rem;
  }

  .md\:mr-\[45px\] {
    margin-right: 45px;
  }

  .md\:mr-\[48px\] {
    margin-right: 48px;
  }

  .md\:mr-\[51px\] {
    margin-right: 51px;
  }

  .md\:mr-\[54px\] {
    margin-right: 54px;
  }

  .md\:mr-\[62px\] {
    margin-right: 62px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:mt-\[-129px\] {
    margin-top: -129px;
  }

  .md\:mt-\[100px\] {
    margin-top: 100px;
  }

  .md\:mt-\[121px\] {
    margin-top: 121px;
  }

  .md\:mt-\[157px\] {
    margin-top: 157px;
  }

  .md\:mt-\[19px\] {
    margin-top: 19px;
  }

  .md\:mt-\[205px\] {
    margin-top: 205px;
  }

  .md\:mt-\[214px\] {
    margin-top: 214px;
  }

  .md\:mt-\[21px\] {
    margin-top: 21px;
  }

  .md\:mt-\[33px\] {
    margin-top: 33px;
  }

  .md\:mt-\[35px\] {
    margin-top: 35px;
  }

  .md\:mt-\[45px\] {
    margin-top: 45px;
  }

  .md\:mt-\[49px\] {
    margin-top: 49px;
  }

  .md\:mt-\[50px\] {
    margin-top: 50px;
  }

  .md\:mt-\[56px\] {
    margin-top: 56px;
  }

  .md\:mt-\[61px\] {
    margin-top: 61px;
  }

  .md\:mt-\[62px\] {
    margin-top: 62px;
  }

  .md\:mt-\[64px\] {
    margin-top: 64px;
  }

  .md\:mt-\[70px\] {
    margin-top: 70px;
  }

  .md\:ml-\[0\] {
    margin-left: 0;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-table {
    display: inline-table;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[120px\] {
    height: 120px;
  }

  .md\:h-\[129px\] {
    height: 129px;
  }

  .md\:h-\[212px\] {
    height: 212px;
  }

  .md\:h-\[233px\] {
    height: 233px;
  }

  .md\:h-\[260px\] {
    height: 260px;
  }

  .md\:h-\[32px\] {
    height: 32px;
  }

  .md\:h-\[39px\] {
    height: 39px;
  }

  .md\:h-\[50px\] {
    height: 50px;
  }

  .md\:h-\[54px\] {
    height: 54px;
  }

  .md\:h-\[56px\] {
    height: 56px;
  }

  .md\:h-\[61px\] {
    height: 61px;
  }

  .md\:h-\[65px\] {
    height: 65px;
  }

  .md\:h-\[90px\] {
    height: 90px;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:max-h-\[30px\] {
    max-height: 30px;
  }

  .md\:max-h-\[500px\] {
    max-height: 500px;
  }

  .md\:max-h-\[50px\] {
    max-height: 50px;
  }

  .md\:min-h-14 {
    min-height: 3.5rem;
  }

  .md\:min-h-\[43px\] {
    min-height: 43px;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-\[120px\] {
    width: 120px;
  }

  .md\:w-\[129px\] {
    width: 129px;
  }

  .md\:w-\[143px\] {
    width: 143px;
  }

  .md\:w-\[144px\] {
    width: 144px;
  }

  .md\:w-\[177px\] {
    width: 177px;
  }

  .md\:w-\[220px\] {
    width: 220px;
  }

  .md\:w-\[247px\] {
    width: 247px;
  }

  .md\:w-\[250px\] {
    width: 250px;
  }

  .md\:w-\[278px\] {
    width: 278px;
  }

  .md\:w-\[285px\] {
    width: 285px;
  }

  .md\:w-\[32px\] {
    width: 32px;
  }

  .md\:w-\[335px\] {
    width: 335px;
  }

  .md\:w-\[35\%\] {
    width: 35%;
  }

  .md\:w-\[353px\] {
    width: 353px;
  }

  .md\:w-\[482px\] {
    width: 482px;
  }

  .md\:w-\[537px\] {
    width: 537px;
  }

  .md\:w-\[54px\] {
    width: 54px;
  }

  .md\:w-\[65\%\] {
    width: 65%;
  }

  .md\:w-\[75px\] {
    width: 75px;
  }

  .md\:w-\[78px\] {
    width: 78px;
  }

  .md\:w-\[90px\] {
    width: 90px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[285px\] {
    min-width: 285px;
  }

  .md\:min-w-\[44px\] {
    min-width: 44px;
  }

  .md\:min-w-\[451px\] {
    min-width: 451px;
  }

  .md\:min-w-\[729px\] {
    min-width: 729px;
  }

  .md\:max-w-\[1200px\] {
    max-width: 1200px;
  }

  .md\:max-w-\[150px\] {
    max-width: 150px;
  }

  .md\:max-w-\[160px\] {
    max-width: 160px;
  }

  .md\:max-w-\[294px\] {
    max-width: 294px;
  }

  .md\:max-w-\[372px\] {
    max-width: 372px;
  }

  .md\:max-w-\[522px\] {
    max-width: 522px;
  }

  .md\:max-w-\[614px\] {
    max-width: 614px;
  }

  .md\:max-w-\[665px\] {
    max-width: 665px;
  }

  .md\:max-w-\[75px\] {
    max-width: 75px;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:max-w-none {
    max-width: none;
  }

  .md\:-translate-x-\[125\%\] {
    --tw-translate-x: -125%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-x-\[132\%\] {
    --tw-translate-x: -132%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[-114\%\] {
    --tw-translate-x: -114%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[-126\%\] {
    --tw-translate-x: -126%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[-170px\] {
    --tw-translate-x: -170px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[-279px\] {
    --tw-translate-x: -279px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[31\%\] {
    --tw-translate-x: 31%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[41\%\] {
    --tw-translate-x: 41%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[50\%\] {
    --tw-translate-x: 50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[73px\] {
    --tw-translate-x: 73px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-105px\] {
    --tw-translate-y: -105px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-108px\] {
    --tw-translate-y: -108px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-110px\] {
    --tw-translate-y: -110px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-115px\] {
    --tw-translate-y: -115px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-135px\] {
    --tw-translate-y: -135px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-155px\] {
    --tw-translate-y: -155px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-165px\] {
    --tw-translate-y: -165px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-75px\] {
    --tw-translate-y: -75px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-\[-95px\] {
    --tw-translate-y: -95px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:items-stretch {
    align-items: stretch;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-7 {
    gap: 1.75rem;
  }

  .md\:gap-\[15px\] {
    gap: 15px;
  }

  .md\:gap-\[19px\] {
    gap: 19px;
  }

  .md\:gap-\[20px\] {
    gap: 20px;
  }

  .md\:gap-\[25px\] {
    gap: 25px;
  }

  .md\:gap-\[26px\] {
    gap: 26px;
  }

  .md\:gap-\[30px\] {
    gap: 30px;
  }

  .md\:gap-\[49px\] {
    gap: 49px;
  }

  .md\:gap-\[60px\] {
    gap: 60px;
  }

  .md\:gap-\[81px\] {
    gap: 81px;
  }

  .md\:overflow-auto {
    overflow: auto;
  }

  .md\:overflow-hidden {
    overflow: hidden;
  }

  .md\:overflow-visible {
    overflow: visible;
  }

  .md\:overflow-x-auto {
    overflow-x: auto;
  }

  .md\:overflow-x-visible {
    overflow-x: visible;
  }

  .md\:rounded-2xl {
    border-radius: 1rem;
  }

  .md\:rounded-\[14px\] {
    border-radius: 14px;
  }

  .md\:rounded-\[30px\] {
    border-radius: 30px;
  }

  .md\:rounded-none {
    border-radius: 0px;
  }

  .md\:rounded-tl-2xl {
    border-top-left-radius: 1rem;
  }

  .md\:rounded-tr-\[3px\] {
    border-top-right-radius: 3px;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:border-0 {
    border-width: 0px;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:border-light-4 {
    --tw-border-opacity: 1;
    border-color: rgb(225 228 232 / var(--tw-border-opacity));
  }

  .md\:border-primary-orange {
    --tw-border-opacity: 1;
    border-color: rgb(239 127 26 / var(--tw-border-opacity));
  }

  .md\:border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity));
  }

  .md\:bg-light-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(244 244 246 / var(--tw-bg-opacity));
  }

  .md\:bg-pastel-orange-light {
    --tw-bg-opacity: 1;
    background-color: rgb(254 243 233 / var(--tw-bg-opacity));
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-14 {
    padding: 3.5rem;
  }

  .md\:p-2 {
    padding: 0.5rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-9 {
    padding: 2.25rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-\[26px\] {
    padding-left: 26px;
    padding-right: 26px;
  }

  .md\:px-\[44px\] {
    padding-left: 44px;
    padding-right: 44px;
  }

  .md\:px-\[49px\] {
    padding-left: 49px;
    padding-right: 49px;
  }

  .md\:px-\[55px\] {
    padding-left: 55px;
    padding-right: 55px;
  }

  .md\:px-\[56px\] {
    padding-left: 56px;
    padding-right: 56px;
  }

  .md\:px-\[59px\] {
    padding-left: 59px;
    padding-right: 59px;
  }

  .md\:px-\[60px\] {
    padding-left: 60px;
    padding-right: 60px;
  }

  .md\:px-\[64px\] {
    padding-left: 64px;
    padding-right: 64px;
  }

  .md\:px-\[73px\] {
    padding-left: 73px;
    padding-right: 73px;
  }

  .md\:px-\[76px\] {
    padding-left: 76px;
    padding-right: 76px;
  }

  .md\:px-\[78px\] {
    padding-left: 78px;
    padding-right: 78px;
  }

  .md\:px-\[79px\] {
    padding-left: 79px;
    padding-right: 79px;
  }

  .md\:px-\[84px\] {
    padding-left: 84px;
    padding-right: 84px;
  }

  .md\:px-\[88px\] {
    padding-left: 88px;
    padding-right: 88px;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
  }

  .md\:py-\[14px\] {
    padding-top: 14px;
    padding-bottom: 14px;
  }

  .md\:py-\[30px\] {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .md\:py-\[36px\] {
    padding-top: 36px;
    padding-bottom: 36px;
  }

  .md\:py-\[38px\] {
    padding-top: 38px;
    padding-bottom: 38px;
  }

  .md\:py-\[39px\] {
    padding-top: 39px;
    padding-bottom: 39px;
  }

  .md\:\!pl-0 {
    padding-left: 0px !important;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .md\:pb-12 {
    padding-bottom: 3rem;
  }

  .md\:pb-20 {
    padding-bottom: 5rem;
  }

  .md\:pb-24 {
    padding-bottom: 6rem;
  }

  .md\:pb-36 {
    padding-bottom: 9rem;
  }

  .md\:pb-48 {
    padding-bottom: 12rem;
  }

  .md\:pb-\[100px\] {
    padding-bottom: 100px;
  }

  .md\:pb-\[103px\] {
    padding-bottom: 103px;
  }

  .md\:pb-\[25px\] {
    padding-bottom: 25px;
  }

  .md\:pb-\[34px\] {
    padding-bottom: 34px;
  }

  .md\:pb-\[35px\] {
    padding-bottom: 35px;
  }

  .md\:pb-\[45px\] {
    padding-bottom: 45px;
  }

  .md\:pb-\[50px\] {
    padding-bottom: 50px;
  }

  .md\:pb-\[85px\] {
    padding-bottom: 85px;
  }

  .md\:pb-\[9px\] {
    padding-bottom: 9px;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-10 {
    padding-left: 2.5rem;
  }

  .md\:pl-2 {
    padding-left: 0.5rem;
  }

  .md\:pl-5 {
    padding-left: 1.25rem;
  }

  .md\:pl-\[22px\] {
    padding-left: 22px;
  }

  .md\:pl-\[32px\] {
    padding-left: 32px;
  }

  .md\:pl-\[78px\] {
    padding-left: 78px;
  }

  .md\:pr-0 {
    padding-right: 0px;
  }

  .md\:pr-10 {
    padding-right: 2.5rem;
  }

  .md\:pr-20 {
    padding-right: 5rem;
  }

  .md\:pr-\[109px\] {
    padding-right: 109px;
  }

  .md\:pr-\[15px\] {
    padding-right: 15px;
  }

  .md\:pr-\[258px\] {
    padding-right: 258px;
  }

  .md\:pr-\[66px\] {
    padding-right: 66px;
  }

  .md\:pr-\[86px\] {
    padding-right: 86px;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-14 {
    padding-top: 3.5rem;
  }

  .md\:pt-2 {
    padding-top: 0.5rem;
  }

  .md\:pt-\[100px\] {
    padding-top: 100px;
  }

  .md\:pt-\[102px\] {
    padding-top: 102px;
  }

  .md\:pt-\[109px\] {
    padding-top: 109px;
  }

  .md\:pt-\[117px\] {
    padding-top: 117px;
  }

  .md\:pt-\[121px\] {
    padding-top: 121px;
  }

  .md\:pt-\[150px\] {
    padding-top: 150px;
  }

  .md\:pt-\[30px\] {
    padding-top: 30px;
  }

  .md\:pt-\[32px\] {
    padding-top: 32px;
  }

  .md\:pt-\[41px\] {
    padding-top: 41px;
  }

  .md\:pt-\[58px\] {
    padding-top: 58px;
  }

  .md\:pt-\[63px\] {
    padding-top: 63px;
  }

  .md\:pt-\[66px\] {
    padding-top: 66px;
  }

  .md\:pt-\[74px\] {
    padding-top: 74px;
  }

  .md\:pt-\[81px\] {
    padding-top: 81px;
  }

  .md\:pt-\[83px\] {
    padding-top: 83px;
  }

  .md\:pt-\[88px\] {
    padding-top: 88px;
  }

  .md\:pt-\[96px\] {
    padding-top: 96px;
  }

  .md\:text-center {
    text-align: center;
  }

  .md\:text-start {
    text-align: start;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-\[20px\] {
    font-size: 20px;
  }

  .md\:text-\[22px\] {
    font-size: 22px;
  }

  .md\:text-\[26px\] {
    font-size: 26px;
  }

  .md\:text-\[30px\] {
    font-size: 30px;
  }

  .md\:text-\[32px\] {
    font-size: 32px;
  }

  .md\:text-\[33px\] {
    font-size: 33px;
  }

  .md\:text-\[35px\] {
    font-size: 35px;
  }

  .md\:text-\[40px\] {
    font-size: 40px;
  }

  .md\:text-\[45px\] {
    font-size: 45px;
  }

  .md\:text-\[55px\] {
    font-size: 55px;
  }

  .md\:text-\[60px\] {
    font-size: 60px;
  }

  .md\:text-\[80px\] {
    font-size: 80px;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .md\:font-normal {
    font-weight: 400;
  }

  .md\:italic {
    font-style: italic;
  }

  .md\:leading-7 {
    line-height: 1.75rem;
  }

  .md\:leading-\[21px\] {
    line-height: 21px;
  }

  .md\:leading-\[24\.5px\] {
    line-height: 24.5px;
  }

  .md\:leading-\[28px\] {
    line-height: 28px;
  }

  .md\:leading-\[31\.5px\] {
    line-height: 31.5px;
  }

  .md\:leading-\[35px\] {
    line-height: 35px;
  }

  .md\:leading-\[37px\] {
    line-height: 37px;
  }

  .md\:leading-\[39px\] {
    line-height: 39px;
  }

  .md\:leading-\[41px\] {
    line-height: 41px;
  }

  .md\:leading-\[49\.5px\] {
    line-height: 49.5px;
  }

  .md\:leading-\[50px\] {
    line-height: 50px;
  }

  .md\:leading-\[58px\] {
    line-height: 58px;
  }

  .md\:leading-\[60px\] {
    line-height: 60px;
  }

  .md\:leading-\[72px\] {
    line-height: 72px;
  }

  .md\:leading-\[81px\] {
    line-height: 81px;
  }

  .md\:tracking-\[5px\] {
    letter-spacing: 5px;
  }

  .md\:text-dark-1 {
    --tw-text-opacity: 1;
    color: rgb(8 11 16 / var(--tw-text-opacity));
  }

  .md\:text-dark-4 {
    --tw-text-opacity: 1;
    color: rgb(173 179 191 / var(--tw-text-opacity));
  }

  .md\:text-primary-orange {
    --tw-text-opacity: 1;
    color: rgb(239 127 26 / var(--tw-text-opacity));
  }

  .md\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }

  .md\:blur-\[2px\] {
    --tw-blur: blur(2px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }

  .md\:hover\:no-underline:hover {
    text-decoration-line: none;
  }
}

@media (min-width: 1024px) {
  .lg\:absolute {
    position: absolute;
  }

  .lg\:bottom-\[-2px\] {
    bottom: -2px;
  }

  .lg\:bottom-\[-40px\] {
    bottom: -40px;
  }

  .lg\:left-1\/2 {
    left: 50%;
  }

  .lg\:left-\[-42px\] {
    left: -42px;
  }

  .lg\:left-\[432px\] {
    left: 432px;
  }

  .lg\:left-\[598px\] {
    left: 598px;
  }

  .lg\:left-\[71\%\] {
    left: 71%;
  }

  .lg\:right-\[-35px\] {
    right: -35px;
  }

  .lg\:right-\[387px\] {
    right: 387px;
  }

  .lg\:right-\[52px\] {
    right: 52px;
  }

  .lg\:right-\[71\%\] {
    right: 71%;
  }

  .lg\:right-auto {
    right: auto;
  }

  .lg\:top-\[158px\] {
    top: 158px;
  }

  .lg\:top-\[1px\] {
    top: 1px;
  }

  .lg\:top-\[26px\] {
    top: 26px;
  }

  .lg\:top-\[389px\] {
    top: 389px;
  }

  .lg\:top-\[3px\] {
    top: 3px;
  }

  .lg\:top-\[43px\] {
    top: 43px;
  }

  .lg\:z-10 {
    z-index: 10;
  }

  .lg\:z-50 {
    z-index: 50;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:\!mr-0 {
    margin-right: 0px !important;
  }

  .lg\:\!mr-\[-30px\] {
    margin-right: -30px !important;
  }

  .lg\:-mt-\[46px\] {
    margin-top: -46px;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .lg\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-\[100px\] {
    margin-bottom: 100px;
  }

  .lg\:mb-\[15px\] {
    margin-bottom: 15px;
  }

  .lg\:mb-\[19px\] {
    margin-bottom: 19px;
  }

  .lg\:mb-\[23px\] {
    margin-bottom: 23px;
  }

  .lg\:mb-\[25px\] {
    margin-bottom: 25px;
  }

  .lg\:mb-\[3px\] {
    margin-bottom: 3px;
  }

  .lg\:mb-\[43px\] {
    margin-bottom: 43px;
  }

  .lg\:mb-\[50px\] {
    margin-bottom: 50px;
  }

  .lg\:ml-0 {
    margin-left: 0px;
  }

  .lg\:ml-10 {
    margin-left: 2.5rem;
  }

  .lg\:ml-4 {
    margin-left: 1rem;
  }

  .lg\:ml-5 {
    margin-left: 1.25rem;
  }

  .lg\:ml-\[-30px\] {
    margin-left: -30px;
  }

  .lg\:ml-\[25px\] {
    margin-left: 25px;
  }

  .lg\:ml-\[87px\] {
    margin-left: 87px;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:mr-0 {
    margin-right: 0px;
  }

  .lg\:mr-5 {
    margin-right: 1.25rem;
  }

  .lg\:mr-\[102px\] {
    margin-right: 102px;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-10 {
    margin-top: 2.5rem;
  }

  .lg\:mt-2 {
    margin-top: 0.5rem;
  }

  .lg\:mt-5 {
    margin-top: 1.25rem;
  }

  .lg\:mt-\[-27px\] {
    margin-top: -27px;
  }

  .lg\:mt-\[-50px\] {
    margin-top: -50px;
  }

  .lg\:mt-\[-92px\] {
    margin-top: -92px;
  }

  .lg\:mt-\[107px\] {
    margin-top: 107px;
  }

  .lg\:mt-\[119px\] {
    margin-top: 119px;
  }

  .lg\:mt-\[134px\] {
    margin-top: 134px;
  }

  .lg\:mt-\[13px\] {
    margin-top: 13px;
  }

  .lg\:mt-\[14px\] {
    margin-top: 14px;
  }

  .lg\:mt-\[19px\] {
    margin-top: 19px;
  }

  .lg\:mt-\[35px\] {
    margin-top: 35px;
  }

  .lg\:mt-\[368px\] {
    margin-top: 368px;
  }

  .lg\:mt-\[51px\] {
    margin-top: 51px;
  }

  .lg\:mt-\[60px\] {
    margin-top: 60px;
  }

  .lg\:mt-\[79px\] {
    margin-top: 79px;
  }

  .lg\:mt-\[88px\] {
    margin-top: 88px;
  }

  .lg\:mb-2\.5 {
    margin-bottom: 0.625rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:\!flex {
    display: flex !important;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[24px\] {
    height: 24px;
  }

  .lg\:h-\[278px\] {
    height: 278px;
  }

  .lg\:h-\[350px\] {
    height: 350px;
  }

  .lg\:h-\[381px\] {
    height: 381px;
  }

  .lg\:h-\[428px\] {
    height: 428px;
  }

  .lg\:h-\[61px\] {
    height: 61px;
  }

  .lg\:h-\[657px\] {
    height: 657px;
  }

  .lg\:h-\[72px\] {
    height: 72px;
  }

  .lg\:h-px {
    height: 1px;
  }

  .lg\:max-h-\[200px\] {
    max-height: 200px;
  }

  .lg\:max-h-\[850px\] {
    max-height: 850px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-\[181px\] {
    width: 181px;
  }

  .lg\:w-\[260px\] {
    width: 260px;
  }

  .lg\:w-\[285px\] {
    width: 285px;
  }

  .lg\:w-\[395px\] {
    width: 395px;
  }

  .lg\:w-\[425px\] {
    width: 425px;
  }

  .lg\:w-\[455px\] {
    width: 455px;
  }

  .lg\:w-\[542px\] {
    width: 542px;
  }

  .lg\:w-\[567\] {
    width: 567;
  }

  .lg\:w-\[61px\] {
    width: 61px;
  }

  .lg\:w-\[72px\] {
    width: 72px;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:min-w-\[285px\] {
    min-width: 285px;
  }

  .lg\:max-w-\[126px\] {
    max-width: 126px;
  }

  .lg\:max-w-\[169px\] {
    max-width: 169px;
  }

  .lg\:max-w-\[277px\] {
    max-width: 277px;
  }

  .lg\:max-w-\[326px\] {
    max-width: 326px;
  }

  .lg\:max-w-\[335px\] {
    max-width: 335px;
  }

  .lg\:max-w-\[341px\] {
    max-width: 341px;
  }

  .lg\:max-w-\[455px\] {
    max-width: 455px;
  }

  .lg\:max-w-full {
    max-width: 100%;
  }

  .lg\:shrink-0 {
    flex-shrink: 0;
  }

  .lg\:-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-0 {
    gap: 0px;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-2\.5 {
    gap: 0.625rem;
  }

  .lg\:gap-5 {
    gap: 1.25rem;
  }

  .lg\:gap-\[106px\] {
    gap: 106px;
  }

  .lg\:gap-\[110px\] {
    gap: 110px;
  }

  .lg\:gap-\[276px\] {
    gap: 276px;
  }

  .lg\:gap-\[309px\] {
    gap: 309px;
  }

  .lg\:gap-\[30px\] {
    gap: 30px;
  }

  .lg\:gap-\[35px\] {
    gap: 35px;
  }

  .lg\:gap-\[60px\] {
    gap: 60px;
  }

  .lg\:gap-\[70px\] {
    gap: 70px;
  }

  .lg\:gap-\[93px\] {
    gap: 93px;
  }

  .lg\:overflow-x-hidden {
    overflow-x: hidden;
  }

  .lg\:rounded-2xl {
    border-radius: 1rem;
  }

  .lg\:rounded-\[13px\] {
    border-radius: 13px;
  }

  .lg\:rounded-lg {
    border-radius: 0.5rem;
  }

  .lg\:rounded-b-\[0px\] {
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
  }

  .lg\:rounded-t-\[30px\] {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
  }

  .lg\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .lg\:border-0 {
    border-width: 0px;
  }

  .lg\:bg-\[\#30415D\]\/20 {
    background-color: rgb(48 65 93 / 0.2);
  }

  .lg\:bg-\[\#30415D\]\/40 {
    background-color: rgb(48 65 93 / 0.4);
  }

  .lg\:bg-\[\#30415D\]\/50 {
    background-color: rgb(48 65 93 / 0.5);
  }

  .lg\:bg-light-6 {
    --tw-bg-opacity: 1;
    background-color: rgb(244 244 246 / var(--tw-bg-opacity));
  }

  .lg\:bg-transparent {
    background-color: transparent;
  }

  .lg\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  }

  .lg\:bg-white\/10 {
    background-color: rgb(255 255 255 / 0.1);
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:p-5 {
    padding: 1.25rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:px-\[17px\] {
    padding-left: 17px;
    padding-right: 17px;
  }

  .lg\:px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px;
  }

  .lg\:px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px;
  }

  .lg\:px-\[90px\] {
    padding-left: 90px;
    padding-right: 90px;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .lg\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .lg\:py-\[18px\] {
    padding-top: 18px;
    padding-bottom: 18px;
  }

  .lg\:py-\[19px\] {
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .lg\:py-\[28px\] {
    padding-top: 28px;
    padding-bottom: 28px;
  }

  .lg\:py-\[50px\] {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-2\.5 {
    padding-bottom: 0.625rem;
  }

  .lg\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .lg\:pb-\[100px\] {
    padding-bottom: 100px;
  }

  .lg\:pb-\[14px\] {
    padding-bottom: 14px;
  }

  .lg\:pb-\[368px\] {
    padding-bottom: 368px;
  }

  .lg\:pl-0 {
    padding-left: 0px;
  }

  .lg\:pl-3 {
    padding-left: 0.75rem;
  }

  .lg\:pl-5 {
    padding-left: 1.25rem;
  }

  .lg\:pr-0 {
    padding-right: 0px;
  }

  .lg\:pr-2\.5 {
    padding-right: 0.625rem;
  }

  .lg\:pr-\[171px\] {
    padding-right: 171px;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:pt-\[100px\] {
    padding-top: 100px;
  }

  .lg\:pt-\[14px\] {
    padding-top: 14px;
  }

  .lg\:pt-\[21px\] {
    padding-top: 21px;
  }

  .lg\:pt-\[25px\] {
    padding-top: 25px;
  }

  .lg\:pt-\[70px\] {
    padding-top: 70px;
  }

  .lg\:pt-\[78px\] {
    padding-top: 78px;
  }

  .lg\:pt-\[81px\] {
    padding-top: 81px;
  }

  .lg\:pt-\[84px\] {
    padding-top: 84px;
  }

  .lg\:pl-7 {
    padding-left: 1.75rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-\[22px\] {
    font-size: 22px;
  }

  .lg\:text-\[26px\] {
    font-size: 26px;
  }

  .lg\:text-\[30px\] {
    font-size: 30px;
  }

  .lg\:text-\[33px\] {
    font-size: 33px;
  }

  .lg\:text-\[35px\] {
    font-size: 35px;
  }

  .lg\:text-\[40px\] {
    font-size: 40px;
  }

  .lg\:text-\[45px\] {
    font-size: 45px;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .lg\:font-bold {
    font-weight: 700;
  }

  .lg\:font-medium {
    font-weight: 500;
  }

  .lg\:leading-\[21px\] {
    line-height: 21px;
  }

  .lg\:leading-\[24\.5px\] {
    line-height: 24.5px;
  }

  .lg\:leading-\[28px\] {
    line-height: 28px;
  }

  .lg\:leading-\[31\.5px\] {
    line-height: 31.5px;
  }

  .lg\:leading-\[35px\] {
    line-height: 35px;
  }

  .lg\:leading-\[37px\] {
    line-height: 37px;
  }

  .lg\:leading-\[39px\] {
    line-height: 39px;
  }

  .lg\:leading-\[58px\] {
    line-height: 58px;
  }

  .lg\:leading-\[60px\] {
    line-height: 60px;
  }

  .lg\:text-dark-1 {
    --tw-text-opacity: 1;
    color: rgb(8 11 16 / var(--tw-text-opacity));
  }

  .lg\:text-primary-orange {
    --tw-text-opacity: 1;
    color: rgb(239 127 26 / var(--tw-text-opacity));
  }

  .lg\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }

  .lg\:blur-\[2px\] {
    --tw-blur: blur(2px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }

  .lg\:backdrop-blur-\[11px\] {
    --tw-backdrop-blur: blur(11px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }
}

@media (min-width: 1280px) {
  .xl\:absolute {
    position: absolute;
  }

  .xl\:bottom-\[-27px\] {
    bottom: -27px;
  }

  .xl\:bottom-\[34px\] {
    bottom: 34px;
  }

  .xl\:left-\[230px\] {
    left: 230px;
  }

  .xl\:left-\[270px\] {
    left: 270px;
  }

  .xl\:left-\[393px\] {
    left: 393px;
  }

  .xl\:left-auto {
    left: auto;
  }

  .xl\:right-\[-177px\] {
    right: -177px;
  }

  .xl\:right-\[246px\] {
    right: 246px;
  }

  .xl\:right-\[266px\] {
    right: 266px;
  }

  .xl\:right-\[51px\] {
    right: 51px;
  }

  .xl\:top-\[26px\] {
    top: 26px;
  }

  .xl\:top-\[286px\] {
    top: 286px;
  }

  .xl\:top-\[95px\] {
    top: 95px;
  }

  .xl\:m-0 {
    margin: 0px;
  }

  .xl\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .xl\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .xl\:mb-\[0\] {
    margin-bottom: 0;
  }

  .xl\:mt-0 {
    margin-top: 0px;
  }

  .xl\:mt-5 {
    margin-top: 1.25rem;
  }

  .xl\:mt-\[-7px\] {
    margin-top: -7px;
  }

  .xl\:mt-\[25px\] {
    margin-top: 25px;
  }

  .xl\:block {
    display: block;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:h-\[155px\] {
    height: 155px;
  }

  .xl\:h-\[265px\] {
    height: 265px;
  }

  .xl\:w-\[1240px\] {
    width: 1240px;
  }

  .xl\:w-\[128px\] {
    width: 128px;
  }

  .xl\:w-\[310px\] {
    width: 310px;
  }

  .xl\:w-\[75px\] {
    width: 75px;
  }

  .xl\:w-auto {
    width: auto;
  }

  .xl\:min-w-\[310px\] {
    min-width: 310px;
  }

  .xl\:max-w-\[235px\] {
    max-width: 235px;
  }

  .xl\:max-w-\[62\%\] {
    max-width: 62%;
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:flex-col {
    flex-direction: column;
  }

  .xl\:justify-end {
    justify-content: flex-end;
  }

  .xl\:justify-between {
    justify-content: space-between;
  }

  .xl\:gap-0 {
    gap: 0px;
  }

  .xl\:gap-5 {
    gap: 1.25rem;
  }

  .xl\:p-0 {
    padding: 0px;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .xl\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .xl\:pl-0 {
    padding-left: 0px;
  }

  .xl\:pl-5 {
    padding-left: 1.25rem;
  }

  .xl\:pl-\[3px\] {
    padding-left: 3px;
  }

  .xl\:pl-\[41px\] {
    padding-left: 41px;
  }

  .xl\:pl-\[44px\] {
    padding-left: 44px;
  }

  .xl\:pr-2\.5 {
    padding-right: 0.625rem;
  }

  .xl\:pr-\[27px\] {
    padding-right: 27px;
  }

  .xl\:text-start {
    text-align: start;
  }

  .xl\:text-\[20px\] {
    font-size: 20px;
  }

  .xl\:text-\[40px\] {
    font-size: 40px;
  }

  .xl\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .xl\:leading-\[35px\] {
    line-height: 35px;
  }

  .xl\:leading-\[58px\] {
    line-height: 58px;
  }

  .xl\:hover\:border-primary-orange:hover {
    --tw-border-opacity: 1;
    border-color: rgb(239 127 26 / var(--tw-border-opacity));
  }

  .xl\:hover\:border-white:hover {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity));
  }

  .xl\:hover\:bg-light-4:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(225 228 232 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-light-6:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(244 244 246 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-pastel-green-light:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(240 248 236 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-pastel-orange-light:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(254 243 233 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-primary-blue-second-dark:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(16 27 45 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-primary-orange:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(239 127 26 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-primary-orange\/15:hover {
    background-color: rgb(239 127 26 / 0.15);
  }

  .xl\:hover\:bg-transparent:hover {
    background-color: transparent;
  }

  .xl\:hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  }

  .xl\:hover\:bg-gray-gradient-hover:hover {
    background-image: linear-gradient(90deg, #F3F4F6 0%, #E5E7EB 100%);
  }

  .xl\:hover\:bg-green-gradient:hover {
    background-image: linear-gradient(90deg, #FFF 24.84%, #F0F8EC 52.65%);;
  }

  .xl\:hover\:bg-orange-gradient:hover {
    background-image: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
  }

  .xl\:hover\:bg-orange-gradient-hover:hover {
    background-image: linear-gradient(309deg, #EF7F1A 13.11%, #FFA439 96.21%);
  }

  .xl\:hover\:text-dark-1:hover {
    --tw-text-opacity: 1;
    color: rgb(8 11 16 / var(--tw-text-opacity));
  }

  .xl\:hover\:text-primary-orange:hover {
    --tw-text-opacity: 1;
    color: rgb(239 127 26 / var(--tw-text-opacity));
  }

  .xl\:hover\:text-secondary-green:hover {
    --tw-text-opacity: 1;
    color: rgb(102 185 64 / var(--tw-text-opacity));
  }

  .xl\:hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }

  .xl\:hover\:underline:hover {
    text-decoration-line: underline;
  }

  .xl\:hover\:no-underline:hover {
    text-decoration-line: none;
  }
}

@media (min-width: 1536px) {
  .\32xl\:mt-10 {
    margin-top: 2.5rem;
  }
}
