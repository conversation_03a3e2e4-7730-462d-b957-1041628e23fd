// Show more top text
var showMoreBtn = document.getElementById('show-more')
var showMoreBtnMobile = document.getElementById('show-more-mobile')
var descriptionDiv = document.getElementById('description')

function showMore() {
	if (descriptionDiv.classList.contains('line-clamp-2')) {
		descriptionDiv.classList.remove('line-clamp-2')
		showMoreBtnMobile.classList.add('hidden')
		descriptionDiv.classList.remove('description-transparency')
		if (showMoreBtn) {
			showMoreBtn.style.display = 'none'
		}
	}
}

showMoreBtn.addEventListener('click', showMore)

// Show more deals
var showMoreBtnD = document.getElementById('show-more-deals')
var moreDealsDiv = document.getElementById('js-show-more-deals')

function showMoreD() {
	// moreDealsDiv.classList.remove('max-h-[1440px]', 'lg:max-h-[850px]')
	// showMoreBtnD.classList.add('hidden')
	// if (showMoreBtnD) {
	// 	showMoreBtnD.style.display = 'none'
	// }

	console.log(
		'display more deals by remove class from .deal-item-wrapper elements'
	)

	const dealItemWrappers = document.querySelectorAll('.deal-item-wrapper')

	dealItemWrappers.forEach(function (dealItemWrapper) {
		dealItemWrapper.classList.remove('hidden')
	})

	showMoreBtnD.style.display = 'none'
}

if (showMoreBtnD) {
	showMoreBtnD.addEventListener('click', showMoreD)
}

if (showMoreBtnMobile) {
	showMoreBtnMobile.addEventListener('click', showMore)
}

if (showMoreBtnMobile) {
	showMoreBtnMobile.addEventListener('click', showMore)
}

// Funkce pro přesun elementů
function moveSidebarContent() {
	var desktopSidebar = document.getElementById('desktop-sidebar')
	var mobileSidebar = document.getElementById('mobile-sidebar')

	if (window.innerWidth < 1280) {
		// Pokud je okno menší než 1280px, přesune obsah do mobile-sidebar
		if (
			desktopSidebar &&
			mobileSidebar &&
			desktopSidebar.childElementCount > 0
		) {
			while (desktopSidebar.childNodes.length > 0) {
				var child = desktopSidebar.childNodes[0]

				// Odstraní třídu 'hidden' ze všech potomků přesouvaného elementu
				if (child.classList && child.classList.contains('hidden')) {
					child.classList.remove('hidden')
				}

				mobileSidebar.appendChild(child)
			}
		}
	} else {
		// Pokud je okno větší nebo rovno 1280px, přesune obsah zpět do desktop-sidebar
		if (
			desktopSidebar &&
			mobileSidebar &&
			mobileSidebar.childElementCount > 0
		) {
			while (mobileSidebar.childNodes.length > 0) {
				desktopSidebar.appendChild(mobileSidebar.childNodes[0])
			}
		}
	}
}

// Inicializace při načtení stránky
window.onload = moveSidebarContent

// Sleduje změny velikosti okna
window.onresize = moveSidebarContent

// SHOW MORE OFFERS
if (document.getElementById('show-more-offers')) {
	document
		.getElementById('show-more-offers')
		.addEventListener('click', function () {
			var extraOffers = document.querySelectorAll('.extra-offers')
			for (var i = 0; i < extraOffers.length; i++) {
				extraOffers[i].classList.remove('hidden')
			}
			var sixthOffer = document.querySelector('.opacity-10')
			if (sixthOffer) {
				sixthOffer.classList.remove('opacity-10')
			}
			this.style.display = 'none'
		})
}

// CONDITIONS POPUP
var conditionsPopup = document.getElementById('conditions-popup')
var openConditionsPopup = document.getElementById('open-conditions-popup')
var closeConditionsPopup = document.querySelectorAll('.conditions-popup-close')

openConditionsPopup.onclick = function () {
	conditionsPopup.style.display = 'flex'
}

closeConditionsPopup.forEach(function (button) {
	button.addEventListener('click', function () {
		conditionsPopup.style.display = 'none'
	})
})

const modal = document.getElementById('conditions-modal')
const openBtn = document.getElementById('open-conditions-modal')
const closeBtn = document.getElementById('close-conditions-modal')

openBtn.onclick = function () {
	modal.style.display = 'flex'
	document.body.classList.add('no-scroll')
}

closeBtn.onclick = function () {
	modal.style.display = 'none'
	document.body.classList.remove('no-scroll')
}

window.onclick = function (event) {
	//console.log(event.target)

	if (event.target === modal) {
		modal.style.display = 'none'
		document.body.classList.remove('no-scroll')
	} else if (event.target == conditionsPopup) {
		conditionsPopup.style.display = 'none'
	} else if (event.target == conditionsPopup) {
		conditionsPopup.style.display = 'none'
	} else if (registerPopup && event.target == registerPopup) {
		registerPopup.style.display = 'none'
	}
}
