@use "tools";
@use "vars";

.k-footer {
    position: relative;
    display: block;    
}

.k-footer .container {
    padding-top: 5rem;
    padding-bottom: 3rem;
    border-top: 1px solid #D9D9D9;    
}

.k-footer svg {
    width: 200px;
    max-width: 100%;
}

.k-footer__wrapper {
    position: relative;
    display: flex;  
    width: 100%;  
    flex-direction: row;
    flex-wrap: wrap;
    text-align: left;
    
    @include tools.breakpoint(vars.$desktop) {
        flex-wrap: nowrap;
    }
}

.k-footer__column {
    flex: 1 1 50%; 
    margin-bottom: 2rem;   

    @include tools.breakpoint(vars.$tablet--land) {
        flex: 1 1 auto; 
        margin-bottom: 0;   
    }
}

.k-footer__column--first {
    flex: 1 1 auto;
    margin-bottom: 1rem;
    order: 1;

    @include tools.breakpoint(vars.$tablet) {
        flex: 0 0 230px;
        order: 0;
        padding-right: 2rem;
    }

    @include tools.breakpoint(vars.$tablet--land) {
        flex: 0 0 320px;
    }
}