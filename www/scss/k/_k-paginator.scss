@use "tools";
@use "vars";

.k-paginator__wrapper {
	position: relative;
	display: flex;
	min-height: 50px;	
	align-items: center;
    justify-content: center;
    flex-direction: row;	
}

.k-paginator__item {
    position: relative;
    display: flex;
    width: 30px;
    height: 30px;
    text-align: center;
    @include tools.light();
    color: #323232; /* text color */
    font-size: 14px;
    text-decoration: none;
    margin-right: 5px;
    line-height: 30px;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;

    &:hover {
        border-color: vars.$color-primary;
        color: vars.$color-primary;
        font-weight: 700;
    }

    &.active {
        border-color: vars.$color-primary;
        color: vars.$color-primary;
        font-weight: 700;
    }
}

.k-paginator__separator {
    position: relative;
    display: block;
    width: 30px;
    height: 30px;
    text-align: center;
    @include tools.light();
    color: #323232; /* text color */
    font-size: 14px;
    text-decoration: none;
    margin-right: 5px;
    line-height: 30px;    
}
 
.k-paginator__button {
    position: relative;
    display: flex;    
    align-items: center;
    justify-content: center;    
    font-size: 12px;    
    background-color: transparent;
    border: 1px solid vars.$color-primary;
    color: vars.$color-primary;
    font-size: 16px;
    font-weight: 700;
    width: auto;
    height: 30px;
    padding: 0 1rem;
    margin: 1rem;
    //text-decoration: underline;

    &:hover {
        background-color: #dddddd;
    }
}

// New Paginator
.k-paginator__wrapper--new {
    display: flex;
    justify-content: space-between;

    @include tools.breakpoint(vars.$tablet) {
        justify-content: center;
    }
}

.k-paginator__wrapper--new .k-paginator__button {    
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 40px;
    flex-shrink: 0;
    background-color: vars.$color-primary;
    border: 0;
}

.k-paginator__pages {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    flex-grow: 1;
    height: 40px;
    border: 1px solid #CCC;
    background: rgba(217, 217, 217, 0.00);
    color: #000;
    text-align: center;    
    font-weight: 400;
    line-height: normal;

    @include tools.breakpoint(vars.$tablet) {
        max-width: 150px;
    }
}

.k-paginator__wrapper--new .k-paginator__button--disable {
    background-color: #ccc;
}

// New paginator SK
.k-paginator__wrapper--new-sk {
    display: flex;
    justify-content: space-between;

    @include tools.breakpoint(vars.$tablet) {
        justify-content: center;
    }
}

.k-paginator__wrapper--new-sk .k-paginator__button {    
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 110px;
    height: 40px;
    flex-shrink: 0;
    background-color: vars.$color-primary;
    border-radius: 100px;
    margin: 0;
    font-size: 14px;
    font-weight: 700;
    color: #fff;
    text-decoration: none;
    border: 0;
}

.k-paginator__wrapper--new-sk .k-paginator__pages {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    flex-grow: 1;
    height: 40px;
    border: 1px solid #CCC;
    background: rgba(217, 217, 217, 0.00);
    color: #000;
    text-align: center;    
    font-weight: 400;
    line-height: normal;
    border-radius: 100px;
    margin: 0 10px;

    @include tools.breakpoint(vars.$tablet) {
        max-width: 150px;
    }
}

.k-paginator__wrapper--new-sk .k-paginator__button--disable {
    background-color: #ccc;
}

.k-paginator__item--contest {
    position: relative;
    background-color: vars.$color-primary;
    cursor: default;
    border: 1px solid transparent;
}

.k-paginator__item--contest:hover {    
    color: #000;
    border: 1px solid transparent;
    font-weight: normal;
}

.k-paginator__item--contest span {    
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    top: -29px;
    font-size: 12px;
    font-weight: 700;
    line-height: 33px;
    text-align: center;
    width: 52px;
    height: 22px;    
    border-radius: 50px;
    background-color: #FFC600;
}

.k-paginator__button-desktop-text {
    display: none;

    @include tools.breakpoint(vars.$tablet) {
        display: block;
    }    
}