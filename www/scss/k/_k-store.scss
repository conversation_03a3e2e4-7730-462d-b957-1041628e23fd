@use "tools";
@use "vars";

.storeContent {    
    display: flex;
    flex-direction: column;
    //border-top: 1px solid #e4e4ea;
    margin-top: 2rem;
    //padding: 1rem;

    @include tools.breakpoint(vars.$desktop) {
        flex-direction: row;
        padding: 0;
    }

}

.storeContent__main {    
    @include tools.breakpoint(vars.$desktop) {
        flex: 0 0 65%;
        max-width: 65%;
        width: 65%;
        padding-right: 1rem;
        border-right: 1px solid #e4e4ea;
    }    
}

.storeContent__storeList {  
    @include tools.breakpoint(vars.$desktop) {  
        flex: 0 0 35%;
        max-width: 35%;
        width: 35%;
        padding-left: 1rem;
        padding-top: 0;
        margin-top: 0;
        border-top-width: 0;
    }
}

.k-store__address {
    font-size: 1.25em;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.storeInfo {    
    display: flex;    
    flex-direction: column;
    margin-bottom: 3rem;
}

.storeInfo__storeHours .storeHours {
    width: 100%
}

.storeInfo__storeHours .storeHours__header {    
    align-items: center;
    font-size: 14px;
}

.storeInfo__storeHours .storeHours__toggle {
    padding-top: 0;
    padding-bottom: 0;    
    transition: transform .15s;    
    display: none
}

.storeInfo__storeHours .storeHours__item:not(.storeHours__header) {    
    justify-content: flex-start;    
    align-items: center;
    font-size: 14px
}

.storeInfo__storeHours .storeHours__item:not(.storeHours__header) .storeHours__itemDay {
    padding-right: 1rem;    
    flex: none;
    width: 100px;
}

.storeInfo__storeHours .storeHours__item:not(.storeHours__header) .storeHours__itemTime {    
    flex: 1;
    white-space: normal
}

.storeInfo__storeHours .storeHours__item--active {
    color: #000;
    font-weight: 700
}


.storeInfo__row {    
    display: flex;
    margin-bottom: 1.5rem;
    text-decoration: none
}

.storeInfo__row:last-child {
    margin-bottom: 0
}

.storeInfo__icon {    
    flex: 0 0 auto;
    height: 24px;
    margin-right: 1rem;
    width: 24px;
}

.storeInfo__hours {
    flex: 1 1 auto
}

.storeInfo__storeDetails {    
    display: flex;    
    flex-direction: row;
    font-size: 14px;
}

.storeInfo__storeDetails>* {
    margin-right: 1rem
}

.storeInfo__container-hours {
    display: flex;
    width: 50%;
}

.storeInfo__additional {
    display: flex;
    width: 50%;
    flex-direction: column;
    justify-content: flex-start;

    img {
        background-color: #a1cb5b;
        padding: 11px;
        border-radius: 50%;
        margin-bottom: 4px;
        box-shadow: 0 0 11px 0 rgba(0,0,0,0.3);
    }

    .storeInfo__additional-phone,
    .storeInfo__additional-email,
    .storeInfo__additional-address {
        cursor: pointer;
        font-size: 14px;
        transition: transform 0.2s ease-in-out;
        transform: scale(1);
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    
        a, span {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        &:hover {
            transform: scale(1.05);
        }
    }
}

.storeHours {
    font-size: 14px;
    list-style: none;
    margin: 0;
    padding: 0
}

.storeHours__item {    
    display: flex;    
    justify-content: space-between;
    line-height: 1.5;    
}

.storeHours__itemDay {    
    flex: 1 1 auto
}

.storeHours__itemTime {    
    flex: 0 0 auto
}

.storeHours__item--active {
    color: #a1cb5b
}

.color--green {
    color: #a1cb5b;
}

.storeList {
    list-style: none;
    margin: 0;
    padding: 0
}

.storeList__item {
    border-bottom: 1px solid #e4e4ea;
    padding: 12px 0 12px .25rem
}

.storeList__item:first-child {
    border-top: 1px solid #e4e4ea
}

.storeItem {    
    align-items: center;    
    display: flex;
    justify-content: space-between;
}

.storeItem__wrapper {
    display: flex;
    align-items: center;
}

.storeItem__icon {
    flex: 0 0 auto;
    width: 24px;
    height: 24px;
}

.storeItem__content {
    flex: 1 1 auto;
    font-weight: 500;
    margin-left: 1.5rem;
}

.storeItem__distance {
    margin-left: 1.5rem;
    margin-top: 4px;
    color: #bc2026;
}

.icon,
.icon svg {
    display: block;
    height: 100%;
    width: 100%;
    fill: #bc2026;
}

.icon svg {
    fill: #bc2026;
}

.storeItem__image {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;    
}

.storeItem__image img {
    position: relative;
    display: block;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 40px;
    margin: auto;
    border-radius: 50%;
}


@media screen and (max-width: 1179px) {
    .storeContent__map {
        margin-bottom: 30px;
    }  
}

@media only screen and (max-width: 505px) {
    .storeInfo__container-hours {
        width: 100%;
    }
    .storeInfo__storeHours {
        padding: 0 10px;
        flex-direction: column;
        gap: 24px;
    }
    .storeInfo__additional {
        width: 100%;
        gap: 12px
    }

    .storeList__item:last-child {
        border-bottom: 0
    }
  }
  


