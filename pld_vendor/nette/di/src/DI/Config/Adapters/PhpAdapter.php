<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

declare(strict_types=1);

namespace Nette\DI\Config\Adapters;

use Nette;


/**
 * Reading and generating PHP files.
 */
final class PhpAdapter implements Nette\DI\Config\Adapter
{
	use Nette\SmartObject;

	/**
	 * Reads configuration from PHP file.
	 */
	public function load(string $file): array
	{
		return require $file;
	}


	/**
	 * Generates configuration in PHP format.
	 */
	public function dump(array $data): string
	{
		return "<?php // generated by Nette \nreturn " . (new Nette\PhpGenerator\Dumper)->dump($data) . ';';
	}
}
