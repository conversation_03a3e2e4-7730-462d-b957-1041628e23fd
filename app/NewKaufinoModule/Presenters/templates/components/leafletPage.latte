<div class="k-leaflets__item mb-5">
    {var $leaflet = $leafletPage->getLeaflet()}
    <a href="{link Leaflet:leaflet shop => $leaflet->getShop(), leaflet => $leaflet}#p-{$leafletPage->getPageNumber()}" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
        <picture>
            <source 
                srcset="
                    {$leafletPage->getImageUrl() |image:230,288,'exactTop','webp'} 1x,
                    {$leafletPage->getImageUrl() |image:460,576,'exactTop','webp'} 2x
                " 
                type="image/webp"
            >            
            <img 
                src="{$basePath}/images/placeholder-230x288.png"                             
                srcset="
                    {$leafletPage->getImageUrl() |image:230,288,'exactTop'} 1x,
                    {$leafletPage->getImageUrl() |image:460,576,'exactTop'} 2x
                "
                data-sizes="auto"
                width="230" 
                height="288" 
                alt="{$leaflet->getName()}" 
                class="k-leaflets__image"
                loading="lazy"
            >
        </picture>                    
    </a>
    <div class="k-leaflets__title mt-0 mb-0">
        <a href="{link Leaflet:leaflet shop => $leaflet->getShop(), leaflet => $leaflet}#p-{$leafletPage->getPageNumber()}" class="color-black">
            {$leaflet->getName()}
        </a>
    </div>                
    <p class="k-leaflets__date mt-0 mb-0">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
</div>  