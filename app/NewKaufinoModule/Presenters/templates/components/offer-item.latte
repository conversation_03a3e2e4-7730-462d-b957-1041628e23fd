{var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
<div class="k-offers__item {if $offer->isExpired()}expired{/if} {isset($cssClass) ? $cssClass}">
    <div class="k-offers__inner" data-line="{_kaufino.shop.expired}">
        {if !$offer->isExpired()}
            <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}" class="k-offers__image-wrapper">
        {else}
            <span class="k-offers__image-wrapper">
        {/if}

            <picture>
                <source 
                    srcset="
                        {$offer->getImageUrl()|image:150,150,'fit','webp'} 1x,
                        {$offer->getImageUrl()|image:300,300,'fit','webp'} 2x                            
                    " 
                    type="image/webp"
                >            
                <img
                    loading="lazy" 
                    src="{$offer->getImageUrl()|image:150,150,'fit'}"                             
                    srcset="
                        {$offer->getImageUrl()|image:150,150,'fit'} 1x,
                        {$offer->getImageUrl()|image:300,300,'fit'} 2x
                    "
                    data-sizes="auto"
                    width="150" 
                    height="150" 
                    alt="{$offer->getName()}" 
                    class="k-offers__image"                    
                >
            </picture>                 

        {if !$offer->isExpired()}
            </a>
        {else}
            </span>
        {/if}                        

        <div class="k-offers__content">
            <small class="k-offers__small">
                {if !isset($hideShop)}
                    <a n:href="Shop:shop $offerLeaflet->getShop()">
                        {$offer->getShop()->getName()}
                    </a>
                {/if}
                {if !isset($hideTags)}
                    {foreach $offer->getTags() as $tag}
                        {if !isset($hideShop) && !isset($hideTags)}|{/if} <a n:href="Tag:tag $tag">
                            {$tag->getName()}
                        </a>
                        {breakIf $iterator->getCounter() > 1}
                    {/foreach}
                {/if}
            </small>
            <p class="k-offers__title">            
                {if !$offer->isExpired()}    
                    <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}">{$offer->getName()}</a>
                {else}
                    {$offer->getName()}
                {/if}
            </p>
            <p class="k-offers__text">{$offer->getDescription()}</p>
            <div class="k-offers__price">
                {*                
                {if !$offer->isExpired()}
                    <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}" class="k-offers__link">{_kaufino.shop.link, [brand => $offer->getShop()->getName(), page => $offer->getLeafletPage()->getPageNumber()]|noescape}</a>
                {else}
                    <span class="k-offers__link">{_kaufino.shop.link, [brand => $offer->getShop()->getName(), page => $offer->getLeafletPage()->getPageNumber()]|noescape}</span>
                {/if}
                *}

                <div class="k-offers__right">                    
                    <small n:if="$offer->getCommonPrice() > 0">{$offer->getCommonPrice()|price:$offer->getLocalization()}</small>
                    <strong>{$offer->getCurrentPrice()|price:$offer->getLocalization()}</strong>
                </div>
            </div>
        </div>
    </div>
</div>