{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block title}{if $pageExtension && $pageExtension->getTitle()}{$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}{else}{$metaTitle}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'ShopEshop',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container mt-4">
        <p class="k-breadcrumb">            
            <a n:href="Shops:shops" class="link">{_kaufino.navbar.shops}</a> |
            {*<a n:href="Tag:tag $shop->getTag()" class="link">{$shop->getTag()->getName()}</a> |*}
            <span class="color-grey">{$shop->getName()}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">
                
                <div class="k-profile-header">                    
                    <div class="k-profile-header__logo-wrapper">
                        <picture>
                            <source 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >                            
                            <img 
                                src="{$basePath}/images/placeholder-80x70.png" 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                " 
                                width="80" 
                                height="80" 
                                alt="{$shop->getName()}" 
                                class="k-profile-header__logo"                                
                            >
                        </picture>
                    </div>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {$heading1}
                        </h1>
                        
                        <p class="k-profile-header__text">                                                     
                            {$metaDescription|noescape}
                        </p>

                        <div class="k-profile-header__button-wrapper">
                            <a n:href="Exit:shop $shop" target="_blank" class="k-profile-header__button">{_kaufino.shop.button, [brand => $shop->getName()]} »</a>
                        </div> 
                    </div>                                       
                </div>        

                {* Letáky *}
                {if count($leafletsInTop) > 0}
                    <div class="k-leaflets__wrapper mt-3">                        
                        {foreach $leafletsInTop as $leaflet}
                            {include '../components/leaflet-newsletter.latte', leaflet => $leaflet}          
                        {/foreach}                        
                    </div>                
                {/if}

                {* Kupony *}
                {if false && count($coupons) > 0}
                    {foreach $coupons as $coupon}                        
                        <div class="k-coupon">
                            <div class="k-coupon__box {if false}k-coupon__box--sale{/if}">
                                <strong class="k-coupon__box-value">{$coupon->getDiscountAmount()}{if $coupon->getDiscountType() == relative}%{/if}</strong>
                                <small class="k-coupon__box-type">{_kaufino.coupon.type.sale}</small>
                            </div>                    

                            <div class="k-coupon__content">
                                <small class="d-block mb-2">{_kaufino.coupon.valid}: {$coupon->getValidTill()|localDate:'long'}</small>
                                <h3 class="mt-0 mb-2"><a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="color-black td-hover-underline">{$coupon->getShop()->getName()}: {$coupon->getName()}</a></h3>
                                {$coupon->getDescription()|noescape}                                
                            </div>
                            
                            <a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="k-coupon__button k-coupon__button--code js-click-link">
                                <span class="k-coupon__button-label">{_kaufino.coupon.showCode}</span>
                                <span class="k-coupon__button-code">{$coupon->getCode()}</span>
                            </a>                    
                        </div>                                                                    
                    {/foreach}
                {/if}

                {if $contentBlocksAllowed}
                    {foreach $contentBlocks as $contentBlock}
                        {continueIf $contentBlock->getType() === 'legacy'}

                        <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                            <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                            {$contentBlock->getContent() |content|noescape}
                        </div>
                    {/foreach}
                {else}
                    <div class="k-content">
                        {*$shop->getDescription()|noescape*}

                        {if $pageExtension && $pageExtension->getLongDescription()}
                            {$pageExtension->getLongDescription() |content|noescape}
                        {else}
                            {cache md5($shop->getDescription()), expire => '20 minutes'}
                                {$shop->getDescription()|content|noescape}
                            {/cache}
                        {/if}
                    </div>
                {/if}
                
                <!-- Vypis brandu - Responsive - 1 -->
                <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="9065144021" data-ad-format="auto" data-full-width-responsive="true"></ins>
                
                <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                </script>

                <div n:if="count($similarShops)-1 > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.otherShops}</h2>
                    <div class="k-shop">    
                        {foreach $similarShops as $similarShop}
                            {continueIf $similarShop->getId() == $shop->getId()}
                            {include '../components/shop-logo.latte', shop => $similarShop}
                        {/foreach}
                    </div>
                </div>                

                <div n:if="count($offers) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offers, [brand => $shop->getName()]}</h2>

                    <div class="k-offers">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {include '../components/offer-item.latte', offer => $offer}
                        {/foreach}
                    </div>
                </div>                

                {if $popupCoupon}
                    {include popup.latte, coupon => $popupCoupon}
                {/if}

                <p class="d-flex" n:if="count($expiredLeaflets) > 0">
                    <a n:href="Archive:archive, $shop" class="link ml-auto">{_'kaufino.leaflets.expiredMetaTitle', [brand => $shop->getName()]} »</a>
                </p>

                {* Pobočky *}
                {*
                <div class="row">
                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Praha</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>

                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Brno</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>

                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Ostrava</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>
                </div>
                *}  
            </div>
        </div>                      
                
    </div>	    

	<div class="float-wrapper__stop"></div>	
</div>
