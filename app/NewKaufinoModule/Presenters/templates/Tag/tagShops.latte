{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.tag.metaTitle, [brand => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.tag.text, [brand => $tag->getName()]}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container mt-4">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a>
        </p>
    </div>
{/block}

{block content}

<!--<div class="leaflet k-lf-layout k-lf-layout&#45;&#45;fixed-container">
    <div class="container">
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header&#45;&#45;mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.tag.title, [brand => $tag->getName()]}
                            {/if}
                        </h1>
						<p class="page-header__text ml-0">
                            {if $pageExtension && $pageExtension->getShortDescription()}
                                {$pageExtension->getShortDescription()}
                            {else}
                                {_kaufino.tag.text, [brand => $tag->getName()]}
                            {/if}
                        </p>
					</div>
				</div>

                <div n:if="count($shops) > 0" class="">
                    <div class="k-shop">
                        {foreach $shops as $shop}
                            {include '../components/shop-logo.latte', shop => $shop, cssClass => $iterator->counter > 12 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 11" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">
                    {_kaufino.tag.titleWithTag, [tag => $tag->getName()]}
                </h2>

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">
                        {foreach $leaflets as $leaflet}

                            {if $iterator->counter == 1}
                                <div class="k-leaflets__item k-leaflets__item&#45;&#45;first mb-3">
                                    &lt;!&ndash; Vypis tagu - Responsive - 2 &ndash;&gt;
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="7746820391" data-ad-format="auto" data-full-width-responsive="true"></ins>

                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($leaflets) > 17" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
                    </p>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                <div n:if="count($cities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.tag.citiesWithTag, [tag => $tag->getName()]}
                    </h2>

                    <div class="k-tag mb-5">
                        {foreach $cities as $city}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                            </span>
                        {/foreach}

                        <p n:if="count($cities) > 11" class="d-flex w100">
                            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                        </p>
                    </div>
                </div>

                &lt;!&ndash; Vypis tagu - Responsive - 1 &ndash;&gt;
                <ins
                    class="adsbygoogle"
                    style="display:block"
                    data-ad-client="ca-pub-4233432057183172"
                    data-ad-slot="1635165628"
                    data-ad-format="auto"
                    data-full-width-responsive="true">
                </ins>

                <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                </script>

                <div class="k-content">
                    {cache md5($tag->getDescription()), expire => '20 minutes'}
                            {$tag->getDescription()|content|noescape}
                    {/cache}
                </div>
            </div>
        </div>

    </div>

	<div class="float-wrapper__stop"></div>
</div>-->

<div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-[45px]">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>

    <div class="swiper k-hp-swiper mt-[27px] mb-[22px] md:mt-0 md:mb-[50px]" style="mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 101%)" >
        <div class="swiper-wrapper">
            {for $i = 1; $i <= 20; $i++}
                <a title="Akční letáky Billa" class="swiper-slide" href="/cz/billa" style="width: 94.2px; margin-right: 16px;" role="group" aria-label="6 / 18">
                <picture class="flex justify-center items-center rounded-full w-[64px] h-[64px] md:w-[90px] md:h-[90px] shadow-md transition-shadow duration-200 ease-in-out border-2 border-primary overflow-hidden">
                    <source srcset="https://n.klmcdn.com/zoh4eiLi/IMG/7200/VeIV62vZbj_gqWb_fgWDu_uO1jnU2aFA4V-dNg59VmU/resize:fit:80:70:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.webp 1x,
                                https://n.klmcdn.com/zoh4eiLi/IMG/7200/vVpF0ei85Zi0U9rxYprjnXy2xIJQwNlNZAfhUncGa3U/resize:fit:160:140:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.webp 2x
                            " type="image/webp">
                    <img
                        src="/images/placeholder-80x70.png"
                        srcset="https://n.klmcdn.com/zoh4eiLi/IMG/7200/T6OoNWEunzq05fp__4bF6R5pn196MC3J5u7JqmIDS7s/resize:fit:80:70:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.png 1x,
                            https://n.klmcdn.com/zoh4eiLi/IMG/7200/VvR89-IAYXwqgrvzVHLWDBfaojuHf-J-L-X18iaJNII/resize:fit:160:140:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.png 2x"
                        data-sizes="auto"
                        width="80"
                        height="70"
                        alt="Billa"
                        class="max-h-[40px] max-w-[40px] md:max-w-[50px]"
                        loading="lazy"
                    >
                </picture>

            </a>
            {/for}
        </div>
    </div>

		<div class="mb-6">
				<h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">Akční ceny a aktuální letáky v obchodech online</h1>
				<div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
							<path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
							<path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
						</svg>
						Obchody
						<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
							<path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
						</svg>
						Bydlení a zahrada
				</div>
				<p class="text-xs md:text-sm font-light leading-[21px] md:leading-[22px] text-[#646C7C] mb-4 md:mb-8">Prohlédněte si nabídky těch nejoblíbenějších supermarketů a hypermarketů, pro které vám každý den přinášíme online aktuální letáky s bezkonkurenčními cenami.</p>
		</div>

		<div class="grid grid-cols-3 md:grid-cols-5 gap-x-[7px] gap-y-[13px] md:gap-x-3 md:gap-y-5 mb-10">
				{for $i = 1; $i <= 10; $i++}
						<div class="bg-light-6 pt-2 pb-[9px] md:pb-[17px] px-2 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
								<div class="flex justify-center items-center bg-white px-2 py-10 md:py-20 mb-[7px] md:mb-[17px] rounded-lg">
										<img src="https://www.tipli.cz/upload/images/shops-shop-logo/788597.svg" alt="">
								</div>
								<div class="md:px-2">
										<div class="hidden md:inline text-xs font-light px-2 py-1 border border-light-3 rounded">Obchod</div>
										<div class="text-sm md:text-base uppercase leading-7 md:mt-2.5">pepco</div>
								</div>
						</div>
				{/for}
		</div>
		<div class="text-center text-sm leading-[24.5px] underline mb-10">Načíst ďalší obchody</div>

		<div class="md:hidden">
				<div class="text-[20px] leading-[35px] font-medium">Akční letáky</div>
				<div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
				{for $i = 1; $i <= 6 ; $i++}
				<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
					<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
						<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
							<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
							<div class="w-full relative">
								<img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
							</div>
							<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
								<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
									Otevřít
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
										<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									</svg>
								</button>
							</div>
						</div>
						<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
							<img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
							<div class="leading-[21px]">
								<div class="text-xs md:text-lg font-medium">Lidl</div>
								<div class="text-xs font-light">04.04. - 07.04.2024</div>
							</div>
						</div>
					</div>
				</div>
				{/for}
			</div>
				<div class="text-center text-sm leading-[24.5px] underline mt-5 mb-10">Načíst ďalší Letáky</div>
		</div>

		<div class="hidden md:block mb-10">
				<h2 class="text-[26px] leading-[39px] font-medium mb-8">Letáky v kategorii Bydlení a zahrada</h2>

				<div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">
				<div class="col-span-2 p-1.5 md:p-2 bg-green-light rounded-xl">
					<div class="flex mb-[13px] mb-[17px] relative" style="position: relative;">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full relative">
							<img class="w-full border rounded-l-lg max-h-[270px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/kbfrh3M8jRHgFUfQSBaxI3CSf9fhrfrgMP6SDWPdgag/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU1LzI1NTYxMS8wenJhN3RnODA3Zjk1YjZ0cjZlNnYwOHYuanBn.webp" alt="">
						</div>
						<div class="w-full relative">
							<img class="w-full border rounded-r-lg max-h-[270px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/dGMHy1WRxt5crdYsfdnhMC8XUsQ057p76w0KNfUhR1w/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQyNC81MTMzNjIxMzZmOTMxNTFkLmhybTE1c3ZucmYzMS5qcGc.webp" alt="">
						</div>
						<div class="flex gap-1 absolute bottom-1 right-1 z-10">
							<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-green py-[3px] px-[7px] md:py-1 md:px-2.5">Aktuální leták</button>
							<button class="rounded-md flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] py-[3px] px-[7px] md:py-1 md:px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="flex items-center gap-[15px] pl-2 pb-[9px]">
						<img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
						<div>
							<div class="text-sm md:text-lg font-medium">Lidl</div>
							<div class="text-xs font-light">04.04. - 07.04.2024</div>
						</div>
					</div>
				</div>

				{for $i = 1; $i <= 3; $i++}
				<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
					<div class="p-1.5 md:p-2 bg-orange-light rounded-xl h-full">
						<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
							<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
							<div class="w-full relative">
								<img class="rounded-lg w-full" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/kbfrh3M8jRHgFUfQSBaxI3CSf9fhrfrgMP6SDWPdgag/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU1LzI1NTYxMS8wenJhN3RnODA3Zjk1YjZ0cjZlNnYwOHYuanBn.webp" alt="letak">
							</div>
							<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
								<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-orange py-1 px-2.5">Nový leták</button>
								<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
									Otevřít
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
										<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									</svg>
								</button>
							</div>
						</div>
						<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
							<img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
							<div class="leading-[21px]">
								<div class="text-xs md:text-lg font-medium">Lidl</div>
								<div class="text-xs font-light">04.04. - 07.04.2024</div>
							</div>
						</div>
					</div>
				</div>
				{/for}
			</div>

				<div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
				{for $i = 1; $i <= 5; $i++}
				<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
					<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
						<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
							<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
							<div class="w-full relative">
								<img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
							</div>
							<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
								<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
									Otevřít
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
										<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									</svg>
								</button>
							</div>
						</div>
						<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
							<img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
							<div class="leading-[21px]">
								<div class="text-xs md:text-lg font-medium">Lidl</div>
								<div class="text-xs font-light">04.04. - 07.04.2024</div>
							</div>
						</div>
					</div>
				</div>
				{/for}
			</div>
		</div>
</div>

<div class="bg-light-6">
		<div class="container py-10">
				<h2 class="text-[26px] leading-[39px] font-medium mb-8">Letáky v kategorii Bydlení a zahrada</h2>

				<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 mb-10">
						{for $i = 1; $i <= 9; $i++}
								<div class="flex items-center justify-between font-light leading-7 pt-[14px] pb-[15px] px-4 bg-white rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
										Ostrava
										<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
											<path d="M3.53538 11.3891L10.6064 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
											<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
								</div>
						{/for}
				</div>

				<h2 class="text-[26px] leading-[39px] font-medium mb-8">Bydlení a zahrada</h2>
				<div class="content text-light leading-7">
					Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností, jak během nákupů ušetřit, v případě eshopu se několikrát do roka koná Lidl shop mega výprodej, během kterého lze ušetřit například na doplňcích do domácnosti. Prolistovat si mohou zákazníci leták hned na čtyřech místech. Dostupný je akční leták Lidl zde, na Kaufino.com, na e-shopu společnosti nebo v kamenných prodejnách a mobilní aplikaci. Podobný sortiment jako Lidl leták aktuální nabízí také leták Tesco, kde je opět dostupné nejen potravinové, ale také nepotravinové zboží. Spousta kupujících také ráda využívá Lidl Polsko leták, dající se uplatnit v polských prodejnách. Mnohdy Lidl Polsko leták nabízí ještě výhodnější akce, než které zahrnuje český leták Lidl. Obsažené Lidl akce umožňují vyzkoušení novinek bez velké finanční náročnosti. Při plánování nákupů je ideální sledovat i Lidl leták příští týden, který společnost vydává několik dnů před koncem platnosti aktuálního letáku. V Lidl letáku na příští týden opět nechybí desítky slev na nejen potravinový sortiment. Atraktivní nabídka obvykle zahrnuje jak běžné značky, tak i vlastní produkty řetězce Lidl.
				</div>
		</div>
</div>

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    const FavoriteSwiper = new Swiper('.swiper.favorite-shops', {
        direction: 'horizontal',
        slidesPerView: 3.5,
        breakpoints: {
            1024: {
                slidesPerView: 7,
                spaceBetween: 36,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 36,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.favorite-shops',
            prevEl: '.swiper-button-prev.favorite-shops',
        },
    });

    var swipperElm = document.querySelector(".k-hp-swiper");

    if (swipperElm) {
        const swiper = new Swiper(swipperElm, {
            slidesPerView: 4.5,
            spaceBetween: 12,

            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
                disabledClass: "swiper-button-disabled",
            },
            breakpoints: {
                1050: {
                    slidesPerView: 10.5,
                    sliedesPerGroup: 5,
                    spaceBetween: 16,
                },
                675: {
                    slidesPerView: 6,
                },
                850: {
                    slidesPerView: 8,
                },
                420: {
                    slidesPerView: 5.3,
                },
            },
        });
    }
</script>
