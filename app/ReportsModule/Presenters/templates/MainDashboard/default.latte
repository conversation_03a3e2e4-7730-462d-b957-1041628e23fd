{block content}
<script>
    var linksQueue = [];
</script>

<div class="row">
    <div class="col-md-4">
        <h1 n:block=title style="float:left">Dashboard</h1>
    </div>
    <div class="col-md-8">
        {control calendar}
		{if in_array($user->getId(), [64017, 796091, 1])}
			<a n:href="this!, newDesign => 1" class="btn btn-success" style="float:right;margin-right:5px">Vyzkoušet nový vzhled</a>
		{/if}
	</div>
</div>

<div class="row" n:snippet="totals">
    {ifset $showTotals}
        {var $metrics = $getMetrics()}
        {var $colMdSize = !$isMultipleMetrics ? 2 : 2}
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfUsers |amount}</h3>
            <small>Number of registrations</small>
        </div>
        <div class="col-md-{$colMdSize}">
            {var $activationRate = $metrics->countOfUsers > 0 ? $metrics->countOfActivations / $metrics->countOfUsers : 0}
            <h3>{$activationRate*100 |amount} %</h3>
            <small>Activations rate</small>
        </div>
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfActivations |amount}</h3>
            <small>Number of activations</small>
        </div>
        <div class="col-md-{$colMdSize}" n:if="!$isMultipleMetrics">
            <h3>{$metrics->countOfReactivations |amount}</h3>
            <small>Number of reactivations</small>
        </div>
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->turnover |amount} Kč</h3>
            <small>Turnover</small>
        </div>
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->originalTurnover |amount} Kč</h3>
            <small>Original turnover (turnover before confirmation)</small>
        </div>
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfTransactions |amount}</h3>
            <small>Count of transactions</small>
        </div>
        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfPurchases |amount}</h3>
            <small>Count of purchases</small>
        </div>
		{if !$isMultipleMetrics}
			<div class="col-md-{$colMdSize}">
				<h3>{$metrics->countOfActiveUsers |amount}</h3>
				<small>Number of transaction active users</small>
			</div>
		{else}
			<div class="col-md-{$colMdSize}">
				<h3>{$getCountOfTransactionActiveUsers($calendar->getFrom(), $calendar->getTo()) |amount}</h3>
				<small>Number of transaction active users</small>
			</div>
		{/if}

        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfAddonInstalls |amount}</h3>
            <small>Number of addon installs</small>
        </div>

        <div class="col-md-{$colMdSize}" n:if="!$isMultipleMetrics">
            <h3>{$metrics->countOfUsersUsingAddon |amount}</h3>
            <small>Number of active users (using addon)</small>
        </div>

        <div class="col-md-{$colMdSize}">
            <h3>{$metrics->countOfMobileAppInstalls |amount}</h3>
            <small>Number of mobile app installs</small>
        </div>

        <div class="col-md-{$colMdSize}" n:if="!$isMultipleMetrics">
            <h3>{$metrics->countOfUsersUsingMobileApp |amount}</h3>
            <small>Number of active users (using mobile app)</small>
        </div>

        <div class="col-md-{$colMdSize}" n:if="!$isMultipleMetrics">
            <h3>{$metrics->countOfActiveUsersMobileApp |amount}</h3>
            <small>Number of transaction active users (using mobile app)</small>
        </div>

		<div class="col-md-{$colMdSize}">
			<h3>{$metrics->confirmedTransactionsRate * 100 |amount} %</h3>
			<small>Confirmed turnover</small>
		</div>
    {else}
        <div class="col-md-12">
            <div class="loading"><span></span></div>
        </div>
    {/ifset}
</div>
<script>linksQueue.push({link //showTotals!, from => $calendar->getFrom()->format('Y-m-d'), to => $calendar->getTo()->format('Y-m-d'), shortcut => $calendar->getShortcut()});</script>

<div class="row">
    {foreach $localizations as $localization}
        {var $localizationId = $localization->getId()}
        <div class="col-md-6">
            {control "country-$localizationId"}
        </div>

        {if $iterator->counter % 2 == 0}
            </div><div class="row">
        {/if}
    {/foreach}
</div>

<style>
    @-webkit-keyframes moving-gradient {
        0% { background-position: -250px 0; }
        100% { background-position: 250px 0; }
    }

    .loading span {
        margin-top: 20px;
        margin-bottom: 10px;
        height: 26px;
        display:block;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 500px 100px;
        animation-name: moving-gradient;
        animation-duration: 1s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
    }
</style>



<script>
    var limit = 5, i = 0;
    var linksQueues = [], link;

    linksQueues[0] = [];
    linksQueues[1] = [];
    linksQueues[2] = [];
    linksQueues[3] = [];
    linksQueues[4] = [];
    linksQueues[5] = [];

    for (var queueIndex in linksQueue) {
        if (i > limit) {
            i = 0;
        }

        link = linksQueue[queueIndex];
        linksQueues[i].push(link);
        i++;
    }

    console.log(linksQueues);

    queueIndex = 0;
    for (queueIndex in linksQueues) {
        processQueue(linksQueues[queueIndex]);
    }

    function processQueue(queue) {
        for (var itemIndex in queue) {
            var link = queue[itemIndex];

            console.log("spouštím:" + link);
            $.nette.ajax({
                url: link,
                off: ['unique']
            }).done(function() {
                queue.splice(itemIndex, 1);
                processQueue(queue);
            });

            break;
        }
    }

//    for (var queueIndex in linksQueue) {
//            var link = linksQueue[queueIndex];
//
//            $.nette.ajax({
//                url: link,
//                off: ['unique']
//            }).done(function() {
//    //                queue.splice(itemIndex, 1);
//    //                processQueue(queue);
//            });
//        }
</script>
