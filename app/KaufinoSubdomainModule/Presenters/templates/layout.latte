{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
	<meta charset="utf-8">
	{capture $headTitle|spaceless|stripHtml}{ifset title}{include title}{/ifset}{/capture}
	<title>{if strlen($headTitle) > 50}{$headTitle}{else}{$headTitle} | Kaufino{/if}</title>
	<meta name="keywords" content="">
    <meta name="description" content="{ifset description}{include description|stripHtml}{/ifset}">
	<meta name="author" content="Kaufino">
	<meta name="robots" content="noindex,nofollow">
	<link rel="canonical" href="{$canonicalUrl}">

	<meta property="og:title" content="{if strlen($headTitle) > 50}{$headTitle}{else}{$headTitle} | Kaufino{/if}" />
    <meta property="og:site_name" content="Kauf<PERSON>"/>
    <meta property="og:url" content="{link //this}" />
    <meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="{$basePath}/images/1200x627-og_kaufino.png" />
    <meta property="fb:app_id" content="" />

	<meta name="twitter:card" content="summary" />

	<!-- Viewport for mobile devices -->    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

	<link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/favicon-16x16.png">
	<link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="384x384"  href="{$basePath}/images/favicon/android-chrome-384x384.png">
{*	<link rel="manifest" href="{$basePath}/images/favicon/site.webmanifest">*}	
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">
	<link rel="stylesheet" href="/css/nice-select2.css">
	{ifset hreflang}{include hreflang}{/ifset}
	
	{var $version = 2.24}

	<link rel="stylesheet" href="{$basePath}/js/swiper/swiper-bundle.min.css" />
	<link rel="stylesheet" href="{$basePath}/css/main.front.css?v={$version}">			

	<script>
		dataLayer = [{}];
	</script>	
	
	<script n:if="isset($googleOptimizeExperiment)">
		dataLayer.push({
			'expId': {$googleOptimizeExperiment->getExperimentId()},
			'expVar': {$googleOptimizeExperiment->getVariant()}
		});
	</script>

	{if in_array($website->getId(), [99, 100, 109])}
		<script n:syntax="off">
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}

			if(localStorage.getItem('consentMode') === null){
				gtag('consent', 'default', {
					'ad_storage': 'denied',
					'analytics_storage': 'denied',
					'personalization_storage': 'denied',
					'functionality_storage': 'denied',
					'security_storage': 'denied',
					'ad_user_data': 'denied',
					'ad_personalization': 'denied',
				});
			} else {
				gtag('consent', 'default', JSON.parse(localStorage.getItem('consentMode')));
			}
		</script>
	{/if}

	{if in_array($website->getId(), [134, 146])}
		<script n:syntax="off">
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}

			if(localStorage.getItem('consentMode') === null){
				gtag('consent', 'default', {
					'ad_storage': 'granted',
					'analytics_storage': 'granted',
					'personalization_storage': 'granted',
					'functionality_storage': 'granted',
					'security_storage': 'granted',
					'ad_user_data': 'granted',
					'ad_personalization': 'granted',
				});
			} else {
				gtag('consent', 'default', JSON.parse(localStorage.getItem('consentMode')));
			}
		</script>
	{/if}

	{if in_array($website->getId(), [127, 128, 129])}
		{if $localization->isSpaian() || $localization->isJar() || $localization->isFrancian()}
			<script async src=“https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3454721603118795” crossorigin=“anonymous”></script>
		{/if}
	{/if}

	<!-- Google Tag Manager -->
	<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
	new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
	j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
	'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-T8M6QHZ');</script>
	<!-- End Google Tag Manager -->

	{if $channel === 'k'}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
	{elseif ($channel === 'l' || $channel === 'm')}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
		<style>
		/* CSS pro aktivní branding */
		.active-branding {
			position: fixed !important;
			top: 0;
			left: 50%;
			transform: translate(-50%, 0);
			z-index: 0;			
		}
		
		.container {
			background: #fff;
		}
		
		.k-leaflets__wrapper--xs-mx {
			margin: 0;
		}		

		@media (min-width: 1200px) {
			.k-header,
			.k-header__category,
			.k-breadcrumb__container,
			.k-lf-layout--fixed-container,
			.k-footer {
				position: relative;
				width: 1170px;
				margin: auto;
				z-index: 1;
			}

			.k-header {
				z-index: 2;
			}
		}
		</style>


		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>

		<script>
			window.googletag = window.googletag || { cmd: [] };
			var desktopSlotDefined = false;
			var mobileSlotDefined = false;
			var currentDevice = window.innerWidth >= 1179 ? 'desktop' : 'mobile'; // Inicializace na základě aktuální šířky okna

			const desktopSlots = [
				{foreach $adUnits as $adUnit}
				{continueIf $adUnit->isMobile()}
				{ path: "/23037269705/{$adUnit->getCode() |noescape}", sizes: {$adUnit->getAdUnitSizesAsArray()}, divId: {$adUnit->getElementId()} },
				{/foreach}
			];

			const mobileSlots = [
				{foreach $adUnits as $adUnit}
				{continueIf $adUnit->isMobile() === false}
				{ path: "/23037269705/{$adUnit->getCode() |noescape}", sizes: {$adUnit->getAdUnitSizesAsArray()}, divId: {$adUnit->getElementId()} },
				{/foreach}
			];

			// Funkce pro inicializaci slotů podle viewportu
			function initializeSlots() {
				var isDesktop = window.innerWidth >= 1179;
			
				if (isDesktop && !desktopSlotDefined) {
				googletag.cmd.push(() => {
					console.log("define slots - desktopSlots");
					desktopSlots.forEach(slot => {
					googletag.defineSlot(slot.path, slot.sizes, slot.divId).addService(googletag.pubads());
					});
					googletag.enableServices(); // Definujeme desktop sloty
				});
				desktopSlotDefined = true;
				} else if (!isDesktop && !mobileSlotDefined) {
				googletag.cmd.push(() => {
					console.log("define slots - mobileSlots");
					mobileSlots.forEach(slot => {
					googletag.defineSlot(slot.path, slot.sizes, slot.divId).addService(googletag.pubads());
					});
					googletag.enableServices(); // Definujeme mobilní sloty
				});
				mobileSlotDefined = true;
				}
			}
			
			// Funkce pro refresh slotů pouze při změně zařízení (desktop/mobile)
			function refreshSlotsOnResize() {
				var isDesktop = window.innerWidth >= 1179;
				var newDevice = isDesktop ? 'desktop' : 'mobile';
			
				// Pokud došlo ke změně zařízení (překročení breakpointu)
				if (newDevice !== currentDevice) {
				currentDevice = newDevice; // Aktualizujeme stav zařízení
			
				if (isDesktop && !desktopSlotDefined) {
					// Definice desktop slotů, pokud nejsou ještě definovány
					googletag.cmd.push(() => {
					desktopSlots.forEach(slot => {
						googletag.defineSlot(slot.path, slot.sizes, slot.divId).addService(googletag.pubads());
					});
					desktopSlotDefined = true;
					googletag.enableServices(); // Aktivujeme služby
					var slotsToRefresh = desktopSlots.map(slot => googletag.pubads().getSlots().find(s => s.getSlotElementId() === slot.divId));
					googletag.pubads().refresh(slotsToRefresh); // Aktualizace pouze desktop slotů
					});
				} else if (!isDesktop && !mobileSlotDefined) {
					// Definice mobilních slotů, pokud nejsou ještě definovány
					googletag.cmd.push(() => {
					mobileSlots.forEach(slot => {
						googletag.defineSlot(slot.path, slot.sizes, slot.divId).addService(googletag.pubads());
					});
					mobileSlotDefined = true;
					googletag.enableServices(); // Aktivujeme služby
					var slotsToRefresh = mobileSlots.map(slot => googletag.pubads().getSlots().find(s => s.getSlotElementId() === slot.divId));
					googletag.pubads().refresh(slotsToRefresh); // Aktualizace pouze mobilních slotů
					});
				} else if (isDesktop && desktopSlotDefined) {
					// Aktualizace desktop slotů, pokud jsou definovány
					googletag.cmd.push(() => {
					var slotsToRefresh = desktopSlots.map(slot => googletag.pubads().getSlots().find(s => s.getSlotElementId() === slot.divId));
					googletag.pubads().refresh(slotsToRefresh); // Aktualizace pouze desktop slotů
					});
				} else if (!isDesktop && mobileSlotDefined) {
					// Aktualizace mobilních slotů, pokud jsou definovány
					googletag.cmd.push(() => {
					var slotsToRefresh = mobileSlots.map(slot => googletag.pubads().getSlots().find(s => s.getSlotElementId() === slot.divId));
					googletag.pubads().refresh(slotsToRefresh); // Aktualizace pouze mobilních slotů
					});
				}
				}
			}
			
			// Funkce pro aktivaci brandingu
			function activateBranding() {
				console.log("Přidání active-branding třídy pro velikost 2000x1400 nebo 2560x1440");
				const brandingElement = document.getElementById('branding');
				if (brandingElement) {
				brandingElement.classList.add('active-branding');
				} else {
				console.error('Branding element nebyl nalezen.');
				}
			}
			
			// Nastavení posluchače pro slotRenderEnded
			function setupSlotRenderListener() {
				googletag.pubads().addEventListener('slotRenderEnded', (event) => {
				if (event.slot.getSlotElementId() === 'branding') {
					const size = event.size;
					if (size && ((size[0] === 2000 && size[1] === 1400) || (size[0] === 2560 && size[1] === 1440))) {
					activateBranding();
					}
				}
				});
			}
			
			// Inicializace slotů při načtení stránky
			initializeSlots();
			
			// Přidání inicializační logiky pro refresh při změně velikosti
			window.addEventListener('resize', refreshSlotsOnResize);
			
			// Nastavení slotRenderEnded posluchače
			googletag.cmd.push(() => {
				setupSlotRenderListener();
			});
		</script>
	{elseif ($channel === 'n')}
		<style>
		/* CSS pro aktivní branding */
		.active-branding {
			position: fixed !important;
			top: 0;
			left: 50%;
			transform: translate(-50%, 0);
			z-index: 0;			
		}
		
		.container {
			background: #fff;
		}
		
		.k-leaflets__wrapper--xs-mx {
			margin: 0;
		}		

		@media (min-width: 1200px) {
			.k-header,
			.k-header__category,
			.k-breadcrumb__container,
			.k-lf-layout--fixed-container,
			.k-footer {
				position: relative;
				width: 1170px;
				margin: auto;
				z-index: 1;
			}

			.k-header {
				z-index: 2;
			}
		}
		</style>

		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
	{else}
	{if false && $localization->isSlovak()}
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>

		<script  n:syntax="off">
			window.googletag = window.googletag || {cmd: []};
			googletag.cmd.push(function() {
				googletag.defineSlot('/23037269705/kaufino-shopdetail1', [[250, 250], [250, 360], [336, 280], [300, 250]], 'div-gpt-ad-1720005679399-0').addService(googletag.pubads());
				googletag.pubads().enableSingleRequest();
				googletag.pubads().collapseEmptyDivs();
				googletag.enableServices();
			});
		</script>

		<script n:syntax="off">
			window.googletag = window.googletag || {cmd: []};
			googletag.cmd.push(function() {
				googletag.defineSlot('/23037269705/kaufino', [[480, 320], [300, 250], [320, 480], [336, 280], [250, 250]], 'div-gpt-ad-1719839381877-0').addService(googletag.pubads());
				googletag.pubads().enableSingleRequest();
				googletag.pubads().collapseEmptyDivs();
				googletag.enableServices();
			});
		</script>

		<script n:syntax="off">
			window.googletag = window.googletag || {cmd: []};
			googletag.cmd.push(function() {
				googletag.defineSlot('/23037269705/kaufino/leafletdetail_1', [[300, 250], [336, 280], [250, 250], [250, 360]], 'div-gpt-ad-1718185569181-0').addService(googletag.pubads());
				googletag.pubads().enableSingleRequest();
				googletag.pubads().collapseEmptyDivs();
				googletag.enableServices();
			});
		</script>

		<script n:syntax="off">
			window.googletag = window.googletag || {cmd: []};
			googletag.cmd.push(function() {
				googletag.defineSlot('/23037269705/kaufino/leafletdetail_2', [[300, 250], [250, 250], [250, 360], [336, 280]], 'div-gpt-ad-1718185642329-0').addService(googletag.pubads());
				googletag.pubads().enableSingleRequest();
				googletag.pubads().collapseEmptyDivs();
				googletag.enableServices();
			});
		</script>

		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3454721603118795" crossorigin="anonymous"></script>
	{else}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
	{/if}
	{/if}

	{if true}
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Montagu+Slab:opsz,wght@16..144,700&display=swap" rel="stylesheet">
	{/if}

	{* History api JS *}
	{if $user->isLoggedIn() && $website->getId() === 100}
		<script n:syntax="off">
			// --- Obsluha události popstate (stisknutí tlačítka Zpět/Vpřed) ---
			window.addEventListener('popstate', function(event) {
				console.log('Událost popstate:', event.state, 'Aktuální URL:', window.location.href);

				if (event.state && event.state.type === 'backButtonFunnel' && event.state.url) {
					// Uživatel stiskl Zpět a dostal se na jeden z našich vložených stavů
					console.log(`Tlačítko Zpět pro vložený leták. Přesměrování na: ${event.state.url}`);

					// Zde můžete přidat kód pro sledování této konkrétní navigace,
					// např. pomocí vašeho 'PromoElementHistoryStorage.addItem(...)', pokud jej implementujete.
					// if (typeof PromoElementHistoryStorage !== 'undefined' && event.state.leafletId) {
					// PromoElementHistoryStorage.addItem(event.state.leafletId, backButtonFunnelConfig.backButtonParamName);
					// }

					// Přesměrování na URL letáku uloženou ve stavu historie (způsobí plné načtení stránky)
					window.location.replace(event.state.url);

				} else if (event.state && event.state.type === 'originalLandingPage') {
					// Uživatel se vrátil zpět na "původní" vstupní stránku (např. Lidl) z jednoho z vložených letáků.
					// Obsah by měl být již správný, protože jsme na tuto URL nikdy skutečně nepřešli,
					// pouze jsme ji vrátili na vrchol zásobníku historie.
					console.log('Navigace zpět na zástupný stav původní vstupní stránky.');
				} else {
					// Standardní navigace tlačítkem Zpět/Vpřed, která nesouvisí s naším trychtýřem,
					// nebo uživatel stiskl Zpět tolikrát, že se dostal před náš trychtýř (např. zpět na Google).
					console.log('Standardní událost popstate nebo návrat na původní odkazující stránku.');
				}
			});
		</script>	
	{/if}

	{dump $adUnits}

	{block head}{/block}
</head>

<body>
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T8M6QHZ"
	height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->

	{include adUnit, 'branding', 'branding'}

	{if $channel === 'n'}
		<div class="ads-container--branding">
		<div class="px-ads px-ads--leaderboard">
			<!-- letaky.kaufino.com / leaderboard1-direct -->
			<script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
			<div id="PX_35661_470646047657075"></div>
			<script>
				window.px2 = window.px2 || { conf: {},queue: [] };
				px2.queue.push(function () {
					px2.render({
						slot: {
							id: 35661
						},
						elem: "PX_35661_470646047657075"
					})
				});
			</script>	 
	  	</div>
		</div>     
	{/if}

	{if $channel === 'n'}
	<div class="px-main-content">
	{/if}	

	<header class="k-header k-header--city-picker">
		<div class="container container--flex">
			<a n:href="Homepage:default" class="k-header__logo-link k-header__logo-link--flex">			
				{if true}
					<svg width="40" height="33" viewBox="0 0 40 33" fill="none" xmlns="http://www.w3.org/2000/svg" n:syntax="off" class="k-header__logo-link__logo">
						<path d="M2.20058 16.7799C2.20473 13.1589 3.64504 9.68745 6.20552 7.12707C8.766 4.56669 12.2375 3.12652 15.8585 3.12251H24.6486V0.921925H15.8583C11.6524 0.921925 7.61887 2.59267 4.64488 5.56661C1.67088 8.54056 7.0486e-05 12.5741 0 16.7799C0 20.9858 1.67078 25.0194 4.64478 27.9934C7.61879 30.9674 11.6524 32.6382 15.8583 32.6382H24.6484V30.4376H15.8583C12.2372 30.4335 8.76571 28.9931 6.20532 26.4326C3.64493 23.8721 2.20479 20.4004 2.20085 16.7794L2.20058 16.7799Z" fill="#FEDB2D"/>
						<path d="M21.7493 30.4374H19.6505C16.0283 30.4374 12.5545 28.9985 9.99323 26.4373C7.43197 23.876 5.99306 20.4022 5.99306 16.78C5.99306 13.1578 7.43197 9.684 9.99323 7.12274C12.5545 4.56147 16.0283 3.12257 19.6505 3.12257H21.7493V0.921986H19.6502C15.4445 0.922127 11.4111 2.59293 8.43717 5.56686C5.46329 8.54079 3.79255 12.5743 3.79248 16.78C3.79248 20.9858 5.46322 25.0194 8.43717 27.9934C11.4111 30.9674 15.4447 32.6382 19.6505 32.6383H21.7496L21.7493 30.4374Z" fill="#F8992A"/>
						<path d="M23.4983 32.7775C32.3121 32.7775 39.457 25.6325 39.457 16.8187C39.457 8.00494 32.3121 0.859974 23.4983 0.859974C14.6845 0.859974 7.53955 8.00494 7.53955 16.8187C7.53955 25.6325 14.6845 32.7775 23.4983 32.7775Z" fill="#FEDB2D"/>
						<path d="M28.627 4.43381C28.7022 4.46491 28.7766 4.4976 28.851 4.53002C28.7766 4.49733 28.7022 4.46491 28.627 4.43381ZM29.166 4.67221C29.2428 4.70835 29.3196 4.74397 29.3956 4.78118C29.3196 4.74397 29.2428 4.70809 29.166 4.67221ZM34.0814 8.59924C34.1707 8.71352 34.2584 8.82913 34.3437 8.94687C34.2583 8.82922 34.1709 8.71325 34.0814 8.59897V8.59924ZM28.0811 4.22013C28.1332 4.23874 28.1837 4.26 28.2352 4.27913C28.1837 4.25973 28.1332 4.23874 28.0811 4.22013ZM30.372 5.31192C30.4555 5.36188 30.5376 5.41398 30.6197 5.46554C30.5373 5.41398 30.4555 5.36188 30.372 5.31192ZM10.5846 20.4066C10.5458 20.2674 10.5093 20.1276 10.4751 19.9873C10.5093 20.1279 10.5458 20.2677 10.5846 20.4066Z" fill="#F8992A"/>
						<path d="M35.4119 10.6792C35.6382 11.117 35.84 11.5671 36.0165 12.0272C34.5156 8.91124 31.8684 6.49597 28.6282 5.28626C25.3881 4.07655 21.8057 4.16602 18.63 5.53597C15.4542 6.90592 12.9309 9.45031 11.5874 12.6373C10.2439 15.8243 10.1842 19.4073 11.4208 22.6373C11.3191 22.4263 11.2229 22.2122 11.1324 21.995C11.8291 23.6604 12.855 25.168 14.1487 26.4272C15.4423 27.6864 16.977 28.6714 18.6606 29.3229C20.3443 29.9745 22.1422 30.2792 23.9465 30.2188C25.7508 30.1585 27.5243 29.7342 29.1606 28.9716C30.7969 28.209 32.2623 27.1237 33.4689 25.7808C34.6755 24.4379 35.5984 22.8651 36.1822 21.1568C36.766 19.4485 36.9987 17.6399 36.8663 15.8394C36.7339 14.039 36.2392 12.2838 35.4119 10.6792Z" fill="#F8992A"/>
						<path d="M24.1235 3.43102C24.1871 3.43394 24.25 3.43846 24.3133 3.44218C24.25 3.43846 24.1871 3.4342 24.1235 3.43102Z" fill="#F8992A"/>
						<path d="M10.5353 17.8457C10.5358 14.8094 11.5671 11.8632 13.4603 9.48941C15.3535 7.1156 17.9964 5.4548 20.9566 4.77888C23.9167 4.10295 27.0186 4.45194 29.7545 5.76872C32.4904 7.0855 34.6983 9.29206 36.0166 12.0272C35.8401 11.5671 35.6383 11.117 35.412 10.6792L35.3899 10.6362C35.3184 10.4988 35.2446 10.3629 35.1686 10.2285L35.1412 10.1809C34.9046 9.76663 34.6461 9.36528 34.3667 8.97855L34.3441 8.94666C34.2586 8.82936 34.1711 8.71347 34.0818 8.59903C34.0588 8.56926 34.0355 8.53958 34.0119 8.50999C33.9234 8.39753 33.833 8.28651 33.7408 8.17698L33.7148 8.14695C33.5151 7.91183 33.3076 7.68389 33.0924 7.46312L33.0647 7.43389C32.9736 7.34087 32.8803 7.25051 32.7865 7.16014C32.7443 7.11948 32.7018 7.07908 32.6592 7.03895C32.5699 6.95497 32.4794 6.87223 32.3878 6.79072C32.34 6.7482 32.2908 6.70647 32.2422 6.66475C31.9449 6.40819 31.6365 6.16492 31.317 5.93494C31.2487 5.88604 31.1807 5.83661 31.1116 5.78877C31.0265 5.72994 30.9407 5.67218 30.854 5.61548C30.7766 5.56461 30.6986 5.51456 30.6202 5.46532C30.5378 5.41376 30.4559 5.36167 30.3725 5.31171C30.2592 5.24394 30.1444 5.17882 30.0291 5.11424C29.9274 5.05754 29.8249 5.00208 29.7216 4.94787C29.6138 4.89084 29.5053 4.83529 29.396 4.78123C29.32 4.74402 29.2429 4.70814 29.1661 4.67226C29.0619 4.62336 28.9571 4.57588 28.8515 4.52981C28.777 4.49739 28.7026 4.4647 28.6271 4.4336C28.4977 4.37992 28.367 4.32889 28.2354 4.27919C28.1841 4.25952 28.1333 4.23879 28.0815 4.21992C27.9023 4.15496 27.7217 4.09382 27.5399 4.03654C27.4761 4.01634 27.412 3.99827 27.348 3.97913C27.2185 3.94015 27.0882 3.90312 26.9573 3.86804C26.8873 3.84944 26.8172 3.83128 26.7468 3.81356C26.6076 3.77901 26.4675 3.74712 26.3266 3.71682C26.27 3.70459 26.2137 3.6913 26.1565 3.67961C25.9613 3.64028 25.7646 3.60519 25.5665 3.57436C25.5227 3.56745 25.4783 3.56267 25.4344 3.55629C25.2757 3.53326 25.116 3.51288 24.9555 3.49516C24.8885 3.48799 24.8213 3.48188 24.7543 3.4755C24.608 3.46203 24.4609 3.45087 24.3132 3.44201C24.2502 3.43829 24.1869 3.43404 24.1234 3.43085C23.9161 3.42128 23.7083 3.4149 23.4986 3.4149C21.7384 3.4149 19.9955 3.7616 18.3693 4.4352C16.7431 5.1088 15.2655 6.0961 14.0209 7.34075C12.7763 8.58539 11.789 10.063 11.1154 11.6892C10.4418 13.3154 10.0952 15.0583 10.0952 16.8185C10.0952 17.0657 10.1032 17.311 10.1162 17.5547C10.1205 17.6344 10.1284 17.7128 10.1343 17.792C10.146 17.956 10.1585 18.1195 10.1763 18.2816C10.1864 18.3743 10.1994 18.466 10.2114 18.558C10.2305 18.7058 10.2507 18.853 10.2746 18.9992C10.29 19.0943 10.3078 19.1889 10.3251 19.2835C10.3517 19.4255 10.3799 19.5669 10.4107 19.7072C10.4314 19.8009 10.4529 19.8942 10.4753 19.987C10.5096 20.1279 10.5462 20.2674 10.5848 20.4064C10.6095 20.4957 10.6342 20.5848 10.6608 20.6733C10.7038 20.8173 10.7506 20.9598 10.7984 21.1017C10.8256 21.1819 10.8513 21.2627 10.88 21.3425C10.9372 21.5019 10.9988 21.6595 11.0621 21.8161C11.086 21.8753 11.1078 21.9357 11.1323 21.9947C11.2233 22.2115 11.3195 22.4257 11.4209 22.637C10.8346 21.1076 10.5344 19.4837 10.5353 17.8457Z" fill="#F57F20"/>
					</svg>
					<span class="k-header__logo-text montagu-slab">{_kaufino.navbar.leaflets}</span>
				{else}
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 704 126" n:syntax="off">				
						<path fill="#fedb2d" d="M8.28 59.902A51.447 51.447 0 0159.67 8.514h33.074V.234H59.669A59.669 59.669 0 000 59.902a59.669 59.669 0 0059.669 59.669h33.074v-8.28H59.669A51.447 51.447 0 018.281 59.9z"/>
						<path fill="#f8992a" d="M81.835 111.29h-7.897a51.388 51.388 0 110-102.776h7.897V.234h-7.898A59.669 59.669 0 0014.27 59.902a59.669 59.669 0 0059.668 59.669h7.898z"/>
						<circle fill="#fedb2d" cx="88.416" cy="60.047" r="60.047"/>
						<path fill="#f8992a" d="M117.834 19.096q1.802 1.297 3.482 2.745-1.679-1.447-3.482-2.745zM132.326 35.25q.43.759.833 1.534-.404-.775-.833-1.535zM116.093 17.894q.487.321.968.652-.48-.332-.968-.652zM41.622 78.852zM107.714 13.447c.283.117.563.24.843.362-.28-.123-.56-.245-.843-.362zM109.742 14.344c.289.136.578.27.864.41-.286-.14-.575-.275-.864-.41zM111.832 15.382q.582.306 1.156.626-.573-.32-1.156-.626zM128.237 29.12c.336.43.666.865.987 1.308q-.482-.664-.987-1.309zM105.66 12.643c.196.07.386.15.58.222-.194-.073-.384-.152-.58-.222zM129.31 30.547a50.5 50.5 0 012.913 4.524 50.441 50.441 0 00-2.914-4.524zM126.955 27.532zM123.364 23.706zM121.863 22.315q.518.46 1.022.934-.505-.473-1.022-.934zM124.515 24.845q1.214 1.246 2.342 2.573-1.128-1.326-2.342-2.573zM114.28 16.751c.314.188.623.384.932.578-.31-.194-.618-.39-.932-.578zM39.172 70.916zM38.66 68.252zM38.063 62.818zM38.29 65.552zM40.63 76.163zM39.827 73.547a49.94 49.94 0 01-.412-1.578q.193.794.412 1.578zM95.7 10.146c.166.024.333.042.497.068-.164-.026-.331-.044-.496-.068z"/>
						<path fill="#f8992a" d="M133.242 36.946a50.214 50.214 0 012.275 5.072A50.438 50.438 0 0042.972 81.94q-.574-1.191-1.085-2.417a50.435 50.435 0 1091.355-42.577z"/>
						<path fill="#f8992a" d="M98.418 10.61c.215.044.426.094.64.14-.214-.046-.425-.096-.64-.14zM90.768 9.674c.239.011.476.028.714.042-.238-.014-.475-.03-.714-.042zM102.9 11.737c.241.072.483.14.722.216-.239-.076-.48-.144-.721-.216zM100.638 11.114q.397.1.792.205-.395-.105-.792-.205zM93.142 9.842c.252.024.505.047.757.075-.252-.028-.505-.051-.757-.075z"/>
						<path d="M39.64 63.911a50.44 50.44 0 0195.877-21.893 50.214 50.214 0 00-2.275-5.072l-.083-.162q-.404-.775-.833-1.534l-.103-.179a50.5 50.5 0 00-2.914-4.524l-.085-.12a50.355 50.355 0 00-.987-1.308q-.13-.168-.263-.335a49.735 49.735 0 00-1.02-1.253l-.098-.113q-1.127-1.327-2.342-2.573l-.104-.11c-.343-.35-.694-.69-1.047-1.03a51.586 51.586 0 00-.479-.456q-.504-.474-1.021-.934c-.18-.16-.365-.317-.548-.474q-1.678-1.448-3.481-2.746c-.257-.184-.513-.37-.773-.55q-.48-.332-.969-.652a49.865 49.865 0 00-.88-.565c-.31-.194-.618-.39-.932-.578-.426-.255-.858-.5-1.292-.743q-.574-.32-1.157-.626a50.209 50.209 0 00-1.225-.627c-.286-.14-.576-.275-.865-.41q-.588-.276-1.184-.536c-.28-.122-.56-.245-.844-.362-.487-.202-.979-.394-1.474-.581-.193-.074-.384-.152-.579-.223a50.561 50.561 0 00-2.038-.69c-.24-.076-.481-.144-.722-.216q-.731-.22-1.47-.418-.395-.105-.792-.205c-.524-.13-1.051-.25-1.581-.364-.213-.046-.425-.096-.64-.14q-1.102-.222-2.22-.396c-.165-.026-.332-.044-.497-.068q-.896-.13-1.802-.23c-.252-.027-.505-.05-.757-.074q-.826-.076-1.66-.126c-.237-.014-.475-.03-.714-.042-.78-.036-1.562-.06-2.351-.06a50.432 50.432 0 00-50.432 50.433c0 .93.03 1.853.079 2.77.016.3.046.595.068.893.044.617.091 1.232.158 1.842.038.349.087.694.132 1.04.072.556.148 1.11.238 1.66.058.358.125.714.19 1.07.1.534.206 1.066.322 1.594q.117.529.243 1.053c.129.53.267 1.055.412 1.578.093.336.186.671.286 1.004.162.542.338 1.078.518 1.612.102.302.199.606.307.906.215.6.447 1.193.685 1.782.09.223.172.45.264.672q.514 1.224 1.086 2.417A50.29 50.29 0 0139.64 63.91z" fill="#f57f20"/>
						<path fill="#bc2026" d="M198.54 71.981a129.4 129.4 0 0014.102-14.422 133.04 133.04 0 0011.537-15.703h23.234q-5.61 8.495-12.419 16.986-6.813 8.494-14.662 16.505l18.428 26.92a11.847 11.847 0 003.125 3.205 6.907 6.907 0 003.766.962 10.455 10.455 0 006.25-2.083l-.641 12.659a16.159 16.159 0 01-5.449 2.563 25.56 25.56 0 01-7.05.962 19.927 19.927 0 01-5.93-.802 15.75 15.75 0 01-4.726-2.403 22.874 22.874 0 01-4.167-4.086q-2.005-2.483-4.246-5.85l-12.819-19.709q-2.085 1.765-4.167 3.284-2.084 1.525-4.166 2.965v25.639h-21.312V25.03h-9.295V9.968h25.64q2.722 0 3.845 1.282a5.715 5.715 0 011.121 3.846zM335.064 116.528a20.026 20.026 0 01-6.17 2.885 28.284 28.284 0 01-8.091 1.122 18.32 18.32 0 01-8.734-2.004 10.046 10.046 0 01-5.048-6.17 20.342 20.342 0 01-8.813 6.41 32.529 32.529 0 01-12.178 2.244q-12.499 0-20.03-6.65-7.534-6.647-7.531-19.148 0-12.98 8.332-19.63 8.33-6.648 22.113-6.65a50.213 50.213 0 018.413.721 41.483 41.483 0 017.291 1.843v-2.083a27.048 27.048 0 00-.641-6.41 11.433 11.433 0 00-2.403-4.646q-3.689-4.166-12.98-4.167a49.75 49.75 0 00-11.618 1.442 51.684 51.684 0 00-11.617 4.327V45.542a53.59 53.59 0 0112.499-3.846 76.93 76.93 0 0114.1-1.282q18.426 0 27.082 8.172a18.993 18.993 0 015.287 8.253 37.495 37.495 0 011.603 11.617v33.17q0 4.808 3.686 4.808a10.822 10.822 0 003.365-.642 8.941 8.941 0 002.884-1.441zm-30.446-33.49a21.479 21.479 0 00-4.887-1.362 34.965 34.965 0 00-6.17-.56q-7.053 0-10.336 3.765a13.748 13.748 0 00-3.285 9.374q0 6.412 3.285 9.694a12.006 12.006 0 008.893 3.285q5.927 0 9.295-3.685 3.203-3.523 3.205-9.775zM424.958 116.368a27.377 27.377 0 01-6.41 2.965 25.2 25.2 0 01-7.852 1.202 18.213 18.213 0 01-8.412-1.924 10.973 10.973 0 01-5.368-6.089 26.444 26.444 0 01-9.534 6.25 34.97 34.97 0 01-12.9 2.243q-14.103 0-21.312-7.53a23.03 23.03 0 01-5.208-9.055 40.87 40.87 0 01-1.522-11.778V41.856h21.312v47.432a37.059 37.059 0 00.48 6.49 12.013 12.013 0 002.083 4.887q3.366 4.326 10.095 4.326a14.034 14.034 0 006.49-1.362 13.19 13.19 0 004.407-3.606 14.093 14.093 0 002.563-5.848 41.194 41.194 0 00.642-7.772V41.856h21.311v59.77q0 4.808 3.846 4.808a10.118 10.118 0 003.285-.642 9.027 9.027 0 002.805-1.441zM485.848 24.87a40.982 40.982 0 00-6.25-1.602 37.816 37.816 0 00-6.73-.641q-5.449 0-7.53 2.885-2.085 2.564-2.084 8.492v7.852h18.908V56.92h-18.908v62.654H441.94V56.92h-12.177V44.9l12.177-3.044v-7.05a46.368 46.368 0 011.203-11.297 19.417 19.417 0 014.566-8.413q6.57-7.05 20.832-7.051a56.358 56.358 0 019.054.801 43.194 43.194 0 018.252 2.084zm3.845 32.049V41.856h25.639q2.723 0 3.845 1.282a5.715 5.715 0 011.122 3.845v54.643q0 4.807 3.846 4.807a10.118 10.118 0 003.285-.641 9.01 9.01 0 002.804-1.442l-.64 12.178a21.813 21.813 0 01-6.01 2.805 26.491 26.491 0 01-8.092 1.202q-9.614 0-13.62-5.448a13.91 13.91 0 01-2.243-5.449 35.905 35.905 0 01-.642-7.05v-45.67zm31.087-36.215a11.74 11.74 0 01-3.285 8.413 10.923 10.923 0 01-8.252 3.445 11.705 11.705 0 01-8.493-3.445 11.311 11.311 0 01-3.525-8.413 11.764 11.764 0 0111.858-11.858 11.187 11.187 0 018.332 3.446 11.586 11.586 0 013.365 8.412zM562.763 51.79a28.053 28.053 0 0110.095-8.251 32.582 32.582 0 0114.742-3.125q14.1 0 21.313 7.531A23.117 23.117 0 01614.12 57a40.934 40.934 0 011.522 11.777v50.797h-21.31V72.142a36.95 36.95 0 00-.482-6.49 12.05 12.05 0 00-2.083-4.887q-3.365-4.327-10.095-4.326a14.075 14.075 0 00-6.49 1.361 13.24 13.24 0 00-4.406 3.605 14.108 14.108 0 00-2.564 5.85 41.071 41.071 0 00-.641 7.772v44.547h-21.313V56.919h-9.294V41.856h19.39a6.02 6.02 0 013.845 1.041 6.084 6.084 0 011.763 3.766zM703.614 80.795q0 18.91-9.694 29.564-9.697 10.658-28.122 10.656-18.43 0-28.122-10.656-9.698-10.655-9.696-29.564 0-18.907 9.696-29.645 9.692-10.734 28.122-10.736 18.425 0 28.122 10.736 9.694 10.738 9.694 29.645zm-21.632 0a55.515 55.515 0 00-1.122-12.019 23.283 23.283 0 00-3.205-8.012 12.27 12.27 0 00-5.127-4.406 16.317 16.317 0 00-6.73-1.362 13.951 13.951 0 00-11.859 5.849q-4.325 5.852-4.326 19.95a54.158 54.158 0 001.122 11.937 23.108 23.108 0 003.204 7.933 12.215 12.215 0 005.128 4.406 17.307 17.307 0 0013.461 0 12.242 12.242 0 005.127-4.406 23.21 23.21 0 003.205-7.933 54.348 54.348 0 001.122-11.937z"/>
					</svg>
				{/if}
			</a>

			<div class="k-header__search k-header__search--city-picker">
				<form>
					<input required type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_kaufino.navbar.search.placeholder}">
					<input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_kaufino.navbar.search.submit}">

					<div class="k-header__search-wrapper">
					</div>
				</form>

				<form n:name="cityPickerControl-form" n:if="$localization->hasGeolocation()" class="order-first order-sm-1">
					<select n:name="cityId" style="display: none"></select>

					{* <input n:name="submit" value="{_kaufino.cityPicker.submit}" class="k-header__search-submit"> *}
				</form>
			</div>

			<div class="k-header__nav">				
				<a href="{link Shops:shops}" class="k-header__nav-item">{_kaufino.navbar.shops}</a>

				<span class="k-header__nav-separator">|</span>

				{foreach $headerShops() as $headerShop}					
					{if $iterator->first}
					<div class="k-header__nav-dropdown-wrapper">
						<a href="{link Leaflets:leaflets}" class="k-header__nav-item k-header__nav-item--more">{_kaufino.navbar.leaflets}</a>

						<div class="k-header__nav-dropdown">
					{/if}
						<a n:href="Shop:shop $headerShop" class="k-header__nav-dropdown-item">{$headerShop->getName()}</a>
					
					{if $iterator->last}
						</div>
					</div>
					{/if}
				{/foreach}
			</div>

			<button class="k-header__menu-icon">
				<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg>
			</button>			
		</div>		
	</header>

	{var $footerShopsTags = $footerShopsTags()}
	<div n:if="$footerShopsTags && count($footerShopsTags) >= 7" class="k-header__category">
		<div class="container container--flex">
			<a n:foreach="$footerShopsTags as $footerTag" n:href="Tag:tag $footerTag" class="k-header__category-item d-block fz-m color-black td-none td-hover-underline {if $footerTag->isSeasonal() === true}fw-bold{/if}">{$footerTag->getName()}</a>
		</div>
	</div>
		
	<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>
	
	{block breadcrumb}{/block}	

	{include content}

	{if in_array($presenterName, ['KaufinoSubdomain:Shop', 'KaufinoSubdomain:Tag', 'KaufinoSubdomain:City', 'KaufinoSubdomain:Article']) && $user->isLoggedIn()}
		<div class="k-page-extension">
			<span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
			<span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>			
			<span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>
			<span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>			

			{if $presenterName == 'KaufinoSubdomain:Shop'}
				{var $shopAlternativeNames = $shop->getAlternativeNames()}
						
				<span n:class="$shopAlternativeNames && count($shopAlternativeNames|explode) ? k-page-extension__tag--green ,k-page-extension__tag">AN</span>
				<a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
			{/if}					

			{if $presenterName == 'KaufinoSubdomain:Tag'}
				<a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
			{/if}

			{if $presenterName == 'KaufinoSubdomain:Article'}
				<a n:href=":Admin:Article:article $article->getId()" class="k-page-extension__btn" target="_blank">Edit article</a>
			{/if}

			{if $pageExtension}
				<a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>	

				{var $shopKeywords = $pageExtension->getKeywords()}							
				<span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>				
			{else}
				<a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
			{/if}

			<a n:href=":Admin:Translations:Dictionary:dictionary dictionary => 'kaufino', localizationId => $localization->getId()" class="k-page-extension__btn" target="_blank">Translations</a>
		</div>
	{/if}	

	<footer class="k-footer">		
		<div class="container">
			<div class="k-footer__wrapper">
				<div class="k-footer__column k-footer__column--first">
					<a n:href="Homepage:default region => 'no'" class="d-block mb-4">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 703.614 121.016" n:syntax="off">				
							<path fill="#fedb2d" d="M8.28 59.902A51.447 51.447 0 0159.67 8.514h33.074V.234H59.669A59.669 59.669 0 000 59.902a59.669 59.669 0 0059.669 59.669h33.074v-8.28H59.669A51.447 51.447 0 018.281 59.9z"/>
							<path fill="#f8992a" d="M81.835 111.29h-7.897a51.388 51.388 0 110-102.776h7.897V.234h-7.898A59.669 59.669 0 0014.27 59.902a59.669 59.669 0 0059.668 59.669h7.898z"/>
							<circle fill="#fedb2d" cx="88.416" cy="60.047" r="60.047"/>
							<path fill="#f8992a" d="M117.834 19.096q1.802 1.297 3.482 2.745-1.679-1.447-3.482-2.745zM132.326 35.25q.43.759.833 1.534-.404-.775-.833-1.535zM116.093 17.894q.487.321.968.652-.48-.332-.968-.652zM41.622 78.852zM107.714 13.447c.283.117.563.24.843.362-.28-.123-.56-.245-.843-.362zM109.742 14.344c.289.136.578.27.864.41-.286-.14-.575-.275-.864-.41zM111.832 15.382q.582.306 1.156.626-.573-.32-1.156-.626zM128.237 29.12c.336.43.666.865.987 1.308q-.482-.664-.987-1.309zM105.66 12.643c.196.07.386.15.58.222-.194-.073-.384-.152-.58-.222zM129.31 30.547a50.5 50.5 0 012.913 4.524 50.441 50.441 0 00-2.914-4.524zM126.955 27.532zM123.364 23.706zM121.863 22.315q.518.46 1.022.934-.505-.473-1.022-.934zM124.515 24.845q1.214 1.246 2.342 2.573-1.128-1.326-2.342-2.573zM114.28 16.751c.314.188.623.384.932.578-.31-.194-.618-.39-.932-.578zM39.172 70.916zM38.66 68.252zM38.063 62.818zM38.29 65.552zM40.63 76.163zM39.827 73.547a49.94 49.94 0 01-.412-1.578q.193.794.412 1.578zM95.7 10.146c.166.024.333.042.497.068-.164-.026-.331-.044-.496-.068z"/>
							<path fill="#f8992a" d="M133.242 36.946a50.214 50.214 0 012.275 5.072A50.438 50.438 0 0042.972 81.94q-.574-1.191-1.085-2.417a50.435 50.435 0 1091.355-42.577z"/>
							<path fill="#f8992a" d="M98.418 10.61c.215.044.426.094.64.14-.214-.046-.425-.096-.64-.14zM90.768 9.674c.239.011.476.028.714.042-.238-.014-.475-.03-.714-.042zM102.9 11.737c.241.072.483.14.722.216-.239-.076-.48-.144-.721-.216zM100.638 11.114q.397.1.792.205-.395-.105-.792-.205zM93.142 9.842c.252.024.505.047.757.075-.252-.028-.505-.051-.757-.075z"/>
							<path d="M39.64 63.911a50.44 50.44 0 0195.877-21.893 50.214 50.214 0 00-2.275-5.072l-.083-.162q-.404-.775-.833-1.534l-.103-.179a50.5 50.5 0 00-2.914-4.524l-.085-.12a50.355 50.355 0 00-.987-1.308q-.13-.168-.263-.335a49.735 49.735 0 00-1.02-1.253l-.098-.113q-1.127-1.327-2.342-2.573l-.104-.11c-.343-.35-.694-.69-1.047-1.03a51.586 51.586 0 00-.479-.456q-.504-.474-1.021-.934c-.18-.16-.365-.317-.548-.474q-1.678-1.448-3.481-2.746c-.257-.184-.513-.37-.773-.55q-.48-.332-.969-.652a49.865 49.865 0 00-.88-.565c-.31-.194-.618-.39-.932-.578-.426-.255-.858-.5-1.292-.743q-.574-.32-1.157-.626a50.209 50.209 0 00-1.225-.627c-.286-.14-.576-.275-.865-.41q-.588-.276-1.184-.536c-.28-.122-.56-.245-.844-.362-.487-.202-.979-.394-1.474-.581-.193-.074-.384-.152-.579-.223a50.561 50.561 0 00-2.038-.69c-.24-.076-.481-.144-.722-.216q-.731-.22-1.47-.418-.395-.105-.792-.205c-.524-.13-1.051-.25-1.581-.364-.213-.046-.425-.096-.64-.14q-1.102-.222-2.22-.396c-.165-.026-.332-.044-.497-.068q-.896-.13-1.802-.23c-.252-.027-.505-.05-.757-.074q-.826-.076-1.66-.126c-.237-.014-.475-.03-.714-.042-.78-.036-1.562-.06-2.351-.06a50.432 50.432 0 00-50.432 50.433c0 .93.03 1.853.079 2.77.016.3.046.595.068.893.044.617.091 1.232.158 1.842.038.349.087.694.132 1.04.072.556.148 1.11.238 1.66.058.358.125.714.19 1.07.1.534.206 1.066.322 1.594q.117.529.243 1.053c.129.53.267 1.055.412 1.578.093.336.186.671.286 1.004.162.542.338 1.078.518 1.612.102.302.199.606.307.906.215.6.447 1.193.685 1.782.09.223.172.45.264.672q.514 1.224 1.086 2.417A50.29 50.29 0 0139.64 63.91z" fill="#f57f20"/>
							<path fill="#bc2026" d="M198.54 71.981a129.4 129.4 0 0014.102-14.422 133.04 133.04 0 0011.537-15.703h23.234q-5.61 8.495-12.419 16.986-6.813 8.494-14.662 16.505l18.428 26.92a11.847 11.847 0 003.125 3.205 6.907 6.907 0 003.766.962 10.455 10.455 0 006.25-2.083l-.641 12.659a16.159 16.159 0 01-5.449 2.563 25.56 25.56 0 01-7.05.962 19.927 19.927 0 01-5.93-.802 15.75 15.75 0 01-4.726-2.403 22.874 22.874 0 01-4.167-4.086q-2.005-2.483-4.246-5.85l-12.819-19.709q-2.085 1.765-4.167 3.284-2.084 1.525-4.166 2.965v25.639h-21.312V25.03h-9.295V9.968h25.64q2.722 0 3.845 1.282a5.715 5.715 0 011.121 3.846zM335.064 116.528a20.026 20.026 0 01-6.17 2.885 28.284 28.284 0 01-8.091 1.122 18.32 18.32 0 01-8.734-2.004 10.046 10.046 0 01-5.048-6.17 20.342 20.342 0 01-8.813 6.41 32.529 32.529 0 01-12.178 2.244q-12.499 0-20.03-6.65-7.534-6.647-7.531-19.148 0-12.98 8.332-19.63 8.33-6.648 22.113-6.65a50.213 50.213 0 018.413.721 41.483 41.483 0 017.291 1.843v-2.083a27.048 27.048 0 00-.641-6.41 11.433 11.433 0 00-2.403-4.646q-3.689-4.166-12.98-4.167a49.75 49.75 0 00-11.618 1.442 51.684 51.684 0 00-11.617 4.327V45.542a53.59 53.59 0 0112.499-3.846 76.93 76.93 0 0114.1-1.282q18.426 0 27.082 8.172a18.993 18.993 0 015.287 8.253 37.495 37.495 0 011.603 11.617v33.17q0 4.808 3.686 4.808a10.822 10.822 0 003.365-.642 8.941 8.941 0 002.884-1.441zm-30.446-33.49a21.479 21.479 0 00-4.887-1.362 34.965 34.965 0 00-6.17-.56q-7.053 0-10.336 3.765a13.748 13.748 0 00-3.285 9.374q0 6.412 3.285 9.694a12.006 12.006 0 008.893 3.285q5.927 0 9.295-3.685 3.203-3.523 3.205-9.775zM424.958 116.368a27.377 27.377 0 01-6.41 2.965 25.2 25.2 0 01-7.852 1.202 18.213 18.213 0 01-8.412-1.924 10.973 10.973 0 01-5.368-6.089 26.444 26.444 0 01-9.534 6.25 34.97 34.97 0 01-12.9 2.243q-14.103 0-21.312-7.53a23.03 23.03 0 01-5.208-9.055 40.87 40.87 0 01-1.522-11.778V41.856h21.312v47.432a37.059 37.059 0 00.48 6.49 12.013 12.013 0 002.083 4.887q3.366 4.326 10.095 4.326a14.034 14.034 0 006.49-1.362 13.19 13.19 0 004.407-3.606 14.093 14.093 0 002.563-5.848 41.194 41.194 0 00.642-7.772V41.856h21.311v59.77q0 4.808 3.846 4.808a10.118 10.118 0 003.285-.642 9.027 9.027 0 002.805-1.441zM485.848 24.87a40.982 40.982 0 00-6.25-1.602 37.816 37.816 0 00-6.73-.641q-5.449 0-7.53 2.885-2.085 2.564-2.084 8.492v7.852h18.908V56.92h-18.908v62.654H441.94V56.92h-12.177V44.9l12.177-3.044v-7.05a46.368 46.368 0 011.203-11.297 19.417 19.417 0 014.566-8.413q6.57-7.05 20.832-7.051a56.358 56.358 0 019.054.801 43.194 43.194 0 018.252 2.084zm3.845 32.049V41.856h25.639q2.723 0 3.845 1.282a5.715 5.715 0 011.122 3.845v54.643q0 4.807 3.846 4.807a10.118 10.118 0 003.285-.641 9.01 9.01 0 002.804-1.442l-.64 12.178a21.813 21.813 0 01-6.01 2.805 26.491 26.491 0 01-8.092 1.202q-9.614 0-13.62-5.448a13.91 13.91 0 01-2.243-5.449 35.905 35.905 0 01-.642-7.05v-45.67zm31.087-36.215a11.74 11.74 0 01-3.285 8.413 10.923 10.923 0 01-8.252 3.445 11.705 11.705 0 01-8.493-3.445 11.311 11.311 0 01-3.525-8.413 11.764 11.764 0 0111.858-11.858 11.187 11.187 0 018.332 3.446 11.586 11.586 0 013.365 8.412zM562.763 51.79a28.053 28.053 0 0110.095-8.251 32.582 32.582 0 0114.742-3.125q14.1 0 21.313 7.531A23.117 23.117 0 01614.12 57a40.934 40.934 0 011.522 11.777v50.797h-21.31V72.142a36.95 36.95 0 00-.482-6.49 12.05 12.05 0 00-2.083-4.887q-3.365-4.327-10.095-4.326a14.075 14.075 0 00-6.49 1.361 13.24 13.24 0 00-4.406 3.605 14.108 14.108 0 00-2.564 5.85 41.071 41.071 0 00-.641 7.772v44.547h-21.313V56.919h-9.294V41.856h19.39a6.02 6.02 0 013.845 1.041 6.084 6.084 0 011.763 3.766zM703.614 80.795q0 18.91-9.694 29.564-9.697 10.658-28.122 10.656-18.43 0-28.122-10.656-9.698-10.655-9.696-29.564 0-18.907 9.696-29.645 9.692-10.734 28.122-10.736 18.425 0 28.122 10.736 9.694 10.738 9.694 29.645zm-21.632 0a55.515 55.515 0 00-1.122-12.019 23.283 23.283 0 00-3.205-8.012 12.27 12.27 0 00-5.127-4.406 16.317 16.317 0 00-6.73-1.362 13.951 13.951 0 00-11.859 5.849q-4.325 5.852-4.326 19.95a54.158 54.158 0 001.122 11.937 23.108 23.108 0 003.204 7.933 12.215 12.215 0 005.128 4.406 17.307 17.307 0 0013.461 0 12.242 12.242 0 005.127-4.406 23.21 23.21 0 003.205-7.933 54.348 54.348 0 001.122-11.937z"/>
						</svg>
					</a>
					<p class="fz-s color-grey mb-2">Copyright © {date('Y')} {_kaufino.footer.copyright}
						{if $user->isLoggedIn()}
							({$geoCountry})
						{/if}
					</p>					
				</div>
				<div class="k-footer__column">
					<div class="k-footer__wrapper">
						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.shops}:</strong>
								<a n:foreach="$footerShops() as $footerShop" n:href="Shop:shop $footerShop" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerShop->getName()}</a>
							</p>
						</div>		

						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.category}:</strong>
								<a n:foreach="$footerShopsTags as $footerTag" n:href="Tag:tag $footerTag" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerTag->getName()}</a>
							</p>
						</div>

						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.aboutKaufino}:</strong>
								<a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.leaflets}</a>
								<a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.shops}</a>
								
								{if count($cities) > 0}
									<a n:href="Cities:cities" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_'kaufino.navbar.cities'}</a>
								{/if}

								{if $website->isKaufino() && $localization->hasArticles()}					
									<a href="{link Articles:articles}" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.navbar.articles}</a>
								{/if}

								<a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.aboutUs}</a>

								{if $conditions}
									<a n:href="Conditions:default, $conditions" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$conditions->getName()}</a>
								{else}
									<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.cookies}</a>
								{/if}
								
								{* Skryto
									<a n:href="Static:privacy" class="d-block fz-m color-black td-none td-hover-underline mb-4">Nastavení ochrany soukromí</a>
									<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">Zásady užívání cookies</a>
									<a n:href="Static:termsOfUse" class="d-block fz-m color-black td-none td-hover-underline mb-4">Podmínky užívání</a>
								*}
							</p>
						</div>
					</div>	
				</div>
			</div>
		</div>
	</footer>

	{if $channel === 'n'}
	</div> 
	{/if}

	{block scripts}
		{*<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>*}
		<script src="{$basePath}/js/main.js?v={$version}" async></script>

		{if isset($userLoggedIn) && $pageExtension && $pageExtension->getKeywords()}
			<script src="{$basePath}/js/page-extension.js?v={$version}" async></script>
		{/if}		

		{if $localization->isCzech()}
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				// Najde prvek s classou 'contest-promo__cross'
				var crossElement = document.querySelector('.contest-promo__cross');

				if (crossElement) {
					// Přidá event listener na kliknutí
					crossElement.addEventListener('click', function() {
						// Najde prvek s classou 'contest-promo'
						var promoElement = document.querySelector('.contest-promo');

						if (promoElement) {
							// Přidá classu 'hidden'
							promoElement.classList.add('hidden');
						}
					});
				}
			});
		</script>
		{/if}

		<script src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>

	{if $localization->hasGeolocation()}
		<script src="/js/nice-select2.js"></script>
		<script>
			const cityPicker = document.getElementById('frm-cityPickerControl-form-cityId');
			const form = document.getElementById('frm-cityPickerControl-form');
			const niceSelect = NiceSelect.bind(cityPicker, { searchable: true });

			cityPicker.addEventListener('change', function(event) {
				const selectedCityId = event.target.value;

				if (selectedCityId === 'currentLocation') {
					getLocationFromBrowser();
				} else {
					form.submit();
				}
			});

			function checkCookie(name) {
				var cookies = document.cookie.split(';');
				for (var i = 0; i < cookies.length; i++) {
					var cookie = cookies[i].trim();
					if (cookie.indexOf(name + '=') === 0) {
						return true;
					}
				}
				return false;
			}

			function getCookie(name) {
				let cookieArr = document.cookie.split(";");

				for (let i = 0; i < cookieArr.length; i++) {
					let cookie = cookieArr[i].trim();

					if (cookie.indexOf(name + "=") === 0) {
						return cookie.substring(name.length + 1);
					}
				}

				return null;
			}

			function resolveCityIdFromUserIp() {
				const xhr = new XMLHttpRequest();
				xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromIp, websiteId: $presenter->website->getId()}, true);
				xhr.setRequestHeader('Content-Type', 'application/json');

				xhr.onload = function() {
					if (xhr.status === 200) {
						const response = JSON.parse(xhr.responseText);
						setUserCityId(response.cityId);

						return response;
					} else {
						console.error('Error:', xhr.status, xhr.statusText);
					}
				};

				xhr.onerror = function() {
					console.error('Request failed.');
				};

				xhr.send();
			}

			function getLocationFromBrowser()
			{
				navigator.geolocation.getCurrentPosition(function(position) {
					let latitude = position.coords.latitude;
					let longitude = position.coords.longitude;

					const xhr = new XMLHttpRequest();
					xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromPosition, websiteId: $presenter->website->getId()}, true);
					xhr.setRequestHeader('Content-Type', 'application/json');

					xhr.onload = function() {
						if (xhr.status === 200) {
							const response = JSON.parse(xhr.responseText);
							setUserCityId(response.cityId);

							return response;
						} else {
							console.error('Error:', xhr.status, xhr.statusText);
						}
					};

					xhr.onerror = function() {
						console.error('Request failed.');
					};

					xhr.send(JSON.stringify({ latitude: latitude, longitude: longitude }));
				}, function(error) {
					alert({_kaufino.cityPicker.error});

					userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
					setUserCityId(userLocation.cityId);
				});
			}

			function setUserCityId(cityId)
			{
				cityPicker.value = cityId
				niceSelect.update();
			}

			let userLocation;
			if (checkCookie('userLocation') === false) {
				userLocation = resolveCityIdFromUserIp();
			} else {
				userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
				setUserCityId(userLocation.cityId);
			}
		</script>
	{/if}
	{/block}
</body>
</html>

{define adUnit $id, $class='desktop'}
	{ifset $adUnits[$id]}
		<div class="ads-container ads-container--{$class}">
			<div class="ads-label">{_'kaufino.leaflet.ads'}</div>
			{*$adUnits[$id]->getCode()*}
			<div id="{$id}"></div>
		</div>

		<script>			
			{if $adUnits[$id]->isMobile()}
			if(window.innerWidth < 1179) {
				googletag.cmd.push(() => {
					googletag.display({$id});
				});
			}
			{/if}

			{if $adUnits[$id]->isMobile() === false}
			if (window.innerWidth >= 1179) {
				googletag.cmd.push(() => {
					googletag.display({$id});
				});
			}
			{/if}
		</script>
	{/ifset}
{/define}