<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\ShopFacade;
use Kaufino\Model\Tags\Entities\Tag;

final class OffersPresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	public function actionOffers(): void
	{
        $offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 100);

		$this->template->offersTags = $offerTags;

        $offers = $this->offerFacade->findTopOffersByTags($this->localization, $offerTags);

        $offerTagsAlphabetically = [];

        foreach ($offerTags as $tag) {
            $offerTagsAlphabetically[mb_substr($tag->getName(), 0, 1)][] = $tag;
        }

        ksort($offerTagsAlphabetically);

        bdump($offerTagsAlphabetically);

        $this->template->offerTagsAlphabetically = $offerTagsAlphabetically;
        $this->template->offers = $offers;
	}

    public function actionTag(Tag $tag)
    {

    }
}
