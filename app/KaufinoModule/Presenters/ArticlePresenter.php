<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON>ino\Model\Articles\ArticleFacade;
use <PERSON><PERSON>ino\Model\Articles\Entities\Article;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Users\UserFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class ArticlePresenter extends BasePresenter
{
	/** @var ArticleFacade @inject */
	public $articleFacade;

    /** @var LeafletFacade @inject */
    public $leafletFacade;

    /** @var UserFacade @inject */
    public $userFacade;

	public function actionArticle(Article $article): void
	{
		if (!$article->isActive()) {
			$this->redirectPermanent(':Ka<PERSON>ino:Homepage:default');
		}

		$this->responseCacheTags[] = 'articles';

		$this->template->article = $article;
		$this->template->articles = $this->articleFacade->findArticles($this->website, 10, $article);

        $this->template->leaflets = $this->leafletFacade->findLeafletsByShops($article->getShops()->toArray(), 10, true, null, Website::MODULE_KAUFINO);

        $articlesByShop = $this->articleFacade->findArticlesByWebsiteAndShops($this->website, $article->getShops()->toArray(), 4, $article);

        $author = $this->userFacade->find($article->getAuthor()->getId());
        $this->template->author = $author;

        bdump($author);
        bdump($author->getSlug());

        if (count($articlesByShop) < 4) {
            $articlesByShop = array_merge($articlesByShop, $this->articleFacade->findArticles($this->website, 10, $article));
        }

        $this->template->articles = array_slice(
            array_unique($articlesByShop, SORT_REGULAR),
            0,
            4
        );
	}
}
