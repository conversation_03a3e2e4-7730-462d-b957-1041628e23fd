<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Sitemap\Item;
use <PERSON><PERSON><PERSON>\Model\Sitemap\SitemapFeed;
use <PERSON><PERSON><PERSON>\Model\Sitemap\SitemapGenerator;
use <PERSON><PERSON><PERSON>\Model\Users\UserFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class SitemapPresenter extends BasePresenter
{
    public const TYPE_SHOPS = 'shops';
    public const TYPE_LEAFLETS = 'leaflets';
    public const TYPE_OFFERS = 'offers';
    public const TYPE_TAGS = 'tags';
    public const TYPE_CITIES = 'cities';
    public const TYPE_STORES = 'stores';
    public const TYPE_OTHER = 'other';
    public const TYPE_ARTICLES = 'articles';

	public SitemapGenerator $sitemapGenerator;
    private ArticleFacade $articleFacade;
    private UserFacade $userFacade;

    public function __construct(SitemapGenerator $sitemapGenerator, ArticleFacade $articleFacade, UserFacade $userFacade)
	{
		$this->sitemapGenerator = $sitemapGenerator;
        $this->articleFacade = $articleFacade;
        $this->userFacade = $userFacade;
    }

    public function renderType(string $type): void
    {
        $this->disableCachedResponse = true;

        switch ($type) {
            case self::TYPE_SHOPS:
                $feed = $this->sitemapGenerator->generateShopsFeed($this->website);
                break;
            case self::TYPE_LEAFLETS:
                $feed = $this->sitemapGenerator->generateLeafletsFeed($this->website);
                break;
            case self::TYPE_TAGS:
                $feed = $this->sitemapGenerator->generateTagsFeed($this->website);
                break;
            case self::TYPE_CITIES:
                $feed = $this->sitemapGenerator->generateCityFeed($this->website);
                $feed->addItem(new Item($this->link('//Cities:cities'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                break;
            case self::TYPE_STORES:
                $feed = $this->sitemapGenerator->generateStoreFeed($this->website);
                break;
            case self::TYPE_ARTICLES:
                $feed = $this->sitemapGenerator->generateArticlesFeed($this->website);
                break;
            case self::TYPE_OTHER:
                $feed = new SitemapFeed();
                $feed->addItem(new Item($this->link('//Homepage:default'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Shops:shops'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Leaflets:leaflets'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Offers:offers'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Static:aboutUs'), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));
                break;
            default:
                $this->error('Invalid type.');
        }

        $this->template->feed = $feed;
    }

	public function renderSitemap(): void
	{
        $this->template->hasCities = count($this->geoFacade->findCities($this->website->getLocalization(), 1, Website::MODULE_KAUFINO)) > 0;
        $this->template->hasStores = count($this->geoFacade->findStores($this->website->getLocalization(), 1, Website::MODULE_KAUFINO)) > 0;

        $this->template->hasArticles = $this->website->getLocalization()->hasArticles();

		$this->disableCachedResponse = true;
	}
}
