{capture $stores}
    {var $uniqueStores = []}
    {var $i = 0}
    {foreach $shops as $shop}
        {continueIf !$shop->hasCity($city)}
        {var $uniqueStores[] = $shop}
        {var $i = $i + 1}
        {breakIf $i > 3}
    {/foreach}


    {var $countOfStores = count($uniqueStores)}
    {foreach $uniqueStores as $store}
        {if $iterator->isLast() && $countOfStores > 1} {_kaufino.city.city.generatedText.or} {/if}
        {if $city->isActiveBrandsKaufino()}
            <a n:href="City:shop $city, $store">{_kaufino.city.shop.leafletStores.title, [brand => $store->getName(), city => $city->getName()] |spaceless}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
        {else}
            <a n:href="Shop:shop $shop">{_kaufino.city.city.leafletStores.title, [brand => $store->getName()] |spaceless}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
        {/if}
    {/foreach}
{/capture}

{capture $shopsLink |spaceless|trim}
<a n:href="Shops:shops">{_kaufino.navbar.shops}</a>
{/capture}

{block robots}{if $city->isNoIndex()}noindex, nofollow{else}index,follow{/if}{/block}

{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'City',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.city.metaTitle, [city => $city->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.city.city.metaDescription, [city => $city->getName(), stores => trim($stores)]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Shops:shops" class="link">{_kaufino.navbar.shops}</a> |
            <span class="color-grey">{$city->getName()}</span>      
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container d-block">	
        <div class="leaflet__content w100">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">                 
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.city.city.title, [city => $city->getName()]}
                            {/if}
                        </h1>						
					</div>					
				</div> 

                <div n:if="count($shops) > 0" class="">
                    {*<h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.city.otherShops, [city => $city->getName()]}</h2>*}
                    <div class="k-shop">    
                        {foreach $shops as $shop}
                            {include '../components/shop-logo.latte', shop => $shop, city => $city->isActiveBrandsKaufino() ? $city : null, cssClass => $iterator->counter > 12 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 11" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>               

                {if count($leaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.city.actualLeaflet}</h2>                                        

                    <div class="k-leaflets__wrapper">                        
                        {foreach $leaflets as $leaflet}                            
                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 10 ? 'hidden' : ''}
                            {breakIf $iterator->counter > 4}
                        {/foreach}                        
                    </div>

                    <p n:if="count($leaflets) > 9" class="d-flex mt-3 mb-5">
                        <a n:href="Shops:shops" class="link ml-auto k-show-more-button">{_'kaufino.showMore.leaflets'} »</a>
                    </p>                
                {/if}
                
                
                <div n:if="count($offers) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offersAll}</h2>

                    <div class="k-offers k-offers--4 mb-3">
                        {foreach $offersByTag as $offer}
                            {continueIf !$offer->getLeafletPage()}

                            {include '../components/offer-tag.latte', offer => $offer}
                        {/foreach}
                    </div>

                    <div class="d-flex mt-3 mb-5">            
                        <a n:href="Offers:offers" class="link ml-auto k-show-more-button">
                        {_'kaufino.showMore.tags'} »
                        </a>
                    </div>
                </div>
                

                <div class="k-content mt-5">
                    {var $month = ((new DateTime())|monthName|lower)}

                    {capture $categoriesInText}
                        {var $items = ($footerShopsTags()|slice: 0, 3)}
                        {foreach $items as $_category}{last}{if count($items) > 1} {_kaufino.city.city.generatedText.and} {/if}{/last}<a n:href="Tag:tag $_category" title="{_kaufino.city.city.categoriesInText, [category => $_category->getName()]}">{$_category->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}

                    {capture $citiesInText}
                        {var $items = ($nearestCities|slice: 0, 3)}
                        {foreach $items as $_city}{last}{if count($items) > 1} {_kaufino.city.city.generatedText.and} {/if}{/last}<a n:href="City:city $_city" title="{_kaufino.city.city.citiesInText, [city => $_city->getName()]}">{$_city->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}

                    {capture $storesInText}
                        {var $uniqueStores = []}
                        {foreach $leaflets as $leaflet}
                            {skipIf !$leaflet->getShop()->hasCity($city)}
                            {skipIf $leaflet->getShop()->isHidden()}
                            {var $uniqueStores[$leaflet->getShop()->getId()] = $leaflet->getShop()}
                            {breakIf $iterator->counter > 5}
                        {/foreach}

                        {foreach $uniqueStores as $store}
                            <a n:href="Shop:shop $store" title="{_kaufino.city.city.leafletStores.title, [brand => $store->getName()]}">{$store->getName()}</a>{sep}, {/sep}
                        {/foreach}
                    {/capture}                    

                    <p>{_kaufino.city.city.generatedText.1, [city => $city->getName(), cities => $citiesInText, population => (number_format($city->getPopulation(), 0, ',', ' ')), month => $month, category => $categoriesInText]|noescape}</p>
                    <p>{_kaufino.city.city.generatedText.2, [city => $city->getName(), stores => trim($storesInText)]|noescape}</p>
                    <ul>
                        <li n:foreach="$leaflets as $leaflet">
                            {capture $validSince}{$leaflet->getValidSince()|localDate} {/capture}
                            {capture $validTill}{$leaflet->getValidTill()|localDate}{/capture}
                            
                            <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet">{_kaufino.city.city.generatedText.leaflet, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validTill => $validTill]}</a>
                            {breakIf $iterator->getCounter() > 2}
                        </li>
                    </ul>
                    {*<p n:if="count($nearestCities)">{_kaufino.city.city.generatedText.3, [city => $city->getName(), cities => $citiesInText, stores => $storesInText, shopsLink => $shopsLink,  month => $localization->isHungarian() ? ($month|firstUpper) : $month]|noescape}</p>*}
                </div>
            </div>        			            
        </div>	

    </div>    

	<div class="float-wrapper__stop"></div>	
</div>
