{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.tag.metaTitle, [tag => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.tag.text, [tag => $tag->getName()]}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a>            
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_kaufino.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$tag->getName()},
                "item": {link //Tag:tag $tag}
        }
  ]
}
    </script>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.tag.title, [tag => $tag->getName()]}
                            {/if}
                        </h1>
						<p class="page-header__text ml-0">
                            {if $pageExtension && $pageExtension->getShortDescription()}
                                {$pageExtension->getShortDescription()}
                            {else}
                                {_kaufino.tag.text, [tag => $tag->getName()]}
                            {/if}
                        </p>
					</div>					
				</div>

                <div n:if="count($shops) > 0" class="">
                    <div class="k-shop">
                        {foreach $shops as $shop}
                            {include '../components/shop-logo.latte', shop => $shop, cssClass => $iterator->counter > 12 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 11" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">
                    {_kaufino.tag.titleWithTag, [tag => $tag->getName()]}
                </h2>

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">                        
                        {foreach $leaflets as $leaflet}

                            {*if $iterator->counter == 1}
                                <div class="k-leaflets__item k-leaflets__item--first mb-3">                                    
                                    <div class="ads-container">
                                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                        <!-- Vypis tagu - Responsive - 2 -->
                                        <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="7746820391" data-ad-format="auto" data-full-width-responsive="true"></ins>
                                        
                                        <script>
                                            (adsbygoogle = window.adsbygoogle || []).push({});
                                        </script>
                                    </div>
                                </div>
                            {/if*}                            

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}                        
                    </div>

                    <p n:if="count($leaflets) > 17" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
                    </p>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                {*
                <div n:if="count($cities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.tag.citiesWithTag, [tag => $tag->getName()]}
                    </h2>

                    <div class="k-tag mb-5">
                        {foreach $cities as $city}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                            </span>
                        {/foreach}

                        <p n:if="count($cities) > 11" class="d-flex w100">
                            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                        </p>
                    </div>
                </div>

                <!-- Vypis tagu - Responsive - 1 -->
                <ins 
                    class="adsbygoogle"
                    style="display:block"
                    data-ad-client="ca-pub-4233432057183172"
                    data-ad-slot="1635165628"
                    data-ad-format="auto"
                    data-full-width-responsive="true">
                </ins>

                <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                </script>
                *}

                <div class="k-content">
                    {cache md5($tag->getDescription()), expire => '20 minutes'}
                            {$tag->getDescription()|content|noescape}
                    {/cache}
                </div>
            </div>        		            
        </div>
        
    </div>    

	<div class="float-wrapper__stop"></div>	
</div>
