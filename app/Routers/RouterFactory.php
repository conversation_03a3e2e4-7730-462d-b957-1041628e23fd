<?php

namespace tipli\Routers;

use Nette;
use Nette\Application\Routers\Route;
use Nette\Application\Routers\RouteList;
use Nette\Utils\Strings;
use tipli\FrontModule\Presenters\SitemapDataPresenter;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Repositories\UserRepository;
use tipli\Model\Articles\Repositories\ArticleRepository;
use tipli\Model\Configuration;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\Repositories\DealRepository;
use tipli\Model\Inbox\Repositories\NotificationRepository;
use tipli\Model\Jobs\Repositories\JobRepository;
use tipli\Model\LandingPages\Entities\LandingPage;
use tipli\Model\LandingPages\LandingPageFacade;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\Repositories\LeafletRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Marketing\Repositories\BannerRepository;
use tipli\Model\PartnerOrganizations\Repositories\PartnerOrganizationRepository;
use tipli\Model\Popups\Entities\PopupInteraction;
use tipli\Model\Popups\Repositories\PopupInteractionRepository;
use tipli\Model\Products\Repositories\ProductRepository;
use tipli\Model\Redirections\Repositories\RedirectionRepository;
use tipli\Model\Regions\Entities\Region;
use tipli\Model\Regions\Repositories\RegionRepository;
use tipli\Model\Reviews\Repositories\ReviewRequestRepository;
use tipli\Model\Seo\SeoFacade;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Repositories\OfferRepository;
use tipli\Model\Shops\Repositories\ShopRepository;
use tipli\Model\Shops\Repositories\StoreRepository;
use tipli\Model\Shortener\Repositories\ShortcutRepository;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\Repositories\TagRepository;
use tipli\Model\Products2\Repositories\ProductRepository as Product2Repository;

class RouterFactory
{
	/** @var Localization */
	private $localization;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var RedirectionRepository */
	private $redirectionRepository;

	/** @var ShopRepository */
	private $shopRepository;

	/** @var OfferRepository */
	private $offerRepository;

	/** @var TagRepository */
	private $tagRepository;

	/** @var UserRepository */
	private $userRepository;

	/** @var ArticleRepository */
	private $articleRepository;

	/** @var JobRepository */
	private $jobRepository;

	/** @var PartnerOrganizationRepository */
	private $partnerOrganizationRepository;

	/** @var ShortcutRepository */
	private $shortcutRepository;

	/** @var BannerRepository */
	private $bannerRepository;

	/** @var LeafletRepository */
	private $leafletRepository;

	/** @var StoreRepository */
	private $storeRepository;

	/** @var NotificationRepository */
	private $notificationRepository;

	/** @var RegionRepository */
	private $regionRepository;

	/** @var DealRepository */
	private $dealRepository;

	/** @var ProductRepository */
	private $productRepository;

	/** @var Product2Repository */
	private $product2Repository;

	/** @var PopupInteractionRepository */
	private $popupInteractionRepository;

	/** @var LandingPageFacade */
	private $landingPageFacade;

	/** @var \tipli\Model\Shops\Repositories\RedirectionRepository */
	private $shopRedirectionRepository;

	public function __construct(
		LocalizationFacade $localizationFacade,
		RedirectionRepository $redirectionRepository,
		ShopRepository $shopRepository,
		OfferRepository $offerRepository,
		TagRepository $tagRepository,
		UserRepository $userRepository,
		ArticleRepository $articleRepository,
		JobRepository $jobRepository,
		PartnerOrganizationRepository $partnerOrganizationRepository,
		ShortcutRepository $shortcutRepository,
		BannerRepository $bannerRepository,
		LeafletRepository $leafletRepository,
		StoreRepository $storeRepository,
		NotificationRepository $notificationRepository,
		RegionRepository $regionRepository,
		DealRepository $dealRepository,
		PopupInteractionRepository $popupInteractionRepository,
		LandingPageFacade $landingPageFacade,
		ProductRepository $productRepository,
		\tipli\Model\Shops\Repositories\RedirectionRepository $shopRedirectionRepository,
		Product2Repository $product2Repository,
		private ReviewRequestRepository $reviewRequestRepository,
		private Configuration $configuration
	) {
		$this->localizationFacade = $localizationFacade;
		$this->redirectionRepository = $redirectionRepository;
		$this->shopRepository = $shopRepository;
		$this->offerRepository = $offerRepository;
		$this->tagRepository = $tagRepository;
		$this->userRepository = $userRepository;
		$this->articleRepository = $articleRepository;
		$this->jobRepository = $jobRepository;
		$this->partnerOrganizationRepository = $partnerOrganizationRepository;
		$this->shortcutRepository = $shortcutRepository;
		$this->leafletRepository = $leafletRepository;
		$this->notificationRepository = $notificationRepository;
		$this->regionRepository = $regionRepository;

		$sapi = php_sapi_name();
		if ($sapi != 'cli') {
			$this->localization = $this->localizationFacade->getCurrentLocalization();
		}

		$this->bannerRepository = $bannerRepository;
		$this->storeRepository = $storeRepository;
		$this->dealRepository = $dealRepository;
		$this->popupInteractionRepository = $popupInteractionRepository;
		$this->landingPageFacade = $landingPageFacade;
		$this->productRepository = $productRepository;
		$this->shopRedirectionRepository = $shopRedirectionRepository;
		$this->product2Repository = $product2Repository;
	}

	/**
	 * @return Nette\Application\IRouter
	 */
	public function createRouter()
	{
		$trans = $this->localization ? self::getTranslations($this->localization) : null;

		if (!$trans && PHP_SAPI === 'cli') {
			$consoleArguments = isset($_SERVER['argv']) ? implode(' ', $_SERVER['argv']) : null;
			if (
				$consoleArguments !== null &&
				(Strings::contains($consoleArguments, 'orm:') || Strings::contains($consoleArguments, 'dbal:') || Strings::contains($consoleArguments, 'import-sql-data'))
			) {
				return new RouteList(); /** @phpstan-ignore-line */
			}

			$trans = self::getTranslations(
				$this->localizationFacade->findOneByLocale(Localization::LOCALE_CZECH)
			);
		}

		$router = new RouteList();

		$globalRoutes = [
			'Api:Webhook:affiliateWindow' => 'api/v1/webhook/affiliate-window',
			'Api:Webhook:admitad' => 'api/v1/webhook/admitad',
			'Api:Webhook:rakuten' => 'api/v1/webhook/rakuten',
			'Api:Webhook:tradedoubler' => 'api/v1/webhook/tradedoubler',
			'Api:Webhook:affilbox' => 'api/v1/webhook/affilbox/<at>',
			'Api:Webhook:datadog' => 'api/v1/datadog-webhook',
			'Api:Webhook:apiMetrics' => 'api/v1/apimetrics-webhook',
			'Api:Webhook:webPageTestResult' => 'api/v1/webpagetest-result',
			'Api:Webhook:competitiveDeals' => 'api/v1/webhook/competitive-deals',
			'Api:Webhook:opcacheInvalidate' => 'api/v1/webhook/opcache-invalidate',
			'Api:Webhook:apifyShopChecker' => 'api/v1/webhook/apify-shop-checker',
			'Api:ApifyWebhook:productShopWebhook' => 'api/v1/apify-webhook/product-shop-webhook',
			'Api:ApifyWebhook:transactionsWebhook' => 'api/v1/apify-webhook/transactions-webhook',
			'Api:ApifyWebhook:bookingTransactionsWebhook' => 'api/v1/apify-webhook/booking-transactions-webhook',
			'Api:Interaction:interactions' => 'api/v1/interactions',
			'Api:AddonData:newShops' => 'api/v1/addon/shops-new',
			'Api:AddonData:menu' => 'api/v1/addon/menu',
			'Api:AddonData:user' => 'api/v1/addon/user',
			'Api:Addon:Pages:default' => 'api/v2/addon/pages',
			'Api:Addon:Page:default' => 'api/v2/addon/page',
			'Api:Addon:Google:default' => 'api/v2/addon/google',
			'Api:Addon:Seznam:default' => 'api/v2/addon/seznam',
			'Api:Addon:Heureka:default' => 'api/v2/addon/heureka',
			'Api:Addon:Zbozi:default' => 'api/v2/addon/zbozi',
			'Api:Addon:User:default' => 'api/v2/addon/user',
			'Api:Addon:User:settings' => 'api/v2/addon/user/settings',
//			'Api:Addon:User:settings' => 'api/v2/addon/omg/user/settings',
			'Api:Addon:Search:default' => 'api/v2/addon/search',
			'Api:Addon:Addon:default' => 'api/v2/addon/addon',
			'Api:Addon:Coupons:default' => 'api/v2/addon/coupons',
			'Api:Addon:AlternativeOffers:default' => 'api/v2/addon/alternative-offers',
			'Api:Me:recommendedUsers' => 'api/v1/me/recommended-users',
			'Api:StartSearch:startSearch' => 'api/v1/search/start-search',
			'Api:UserWebhook:open' => 'api/v1/user-webhook/open',
			'Api:UserWebhook:click' => 'api/v1/user-webhook/click',
			'Api:EmailEventWebhook:mailgun' => 'api/v1/email-event-webhook/mailgun',
			'Api:EmailEventWebhook:mandrill' => 'api/v1/email-event-webhook/mandrill',
			'Api:EmailEventWebhook:mailchimp' => 'api/v1/email-event-webhook/mailchimp',
			'Api:User:todayUsers' => 'api/v1/users/today',
			'Api:User:todayActiveUsers' => 'api/v1/users/today-active',
			'Api:User:checkUsers' => 'api/v1/users/check',
			'Api:Shop:shops' => 'api/v1/shops',
			'Api:Leaflets:default' => 'api/v1/leaflets',
			'Api:Deal:inbox' => 'api/v1/deals/inbox',
			'Api:Deal:emailInbox' => 'api/v1/deals/email-inbox',
			'Api:Deals:coupons' => 'api/v1/deals/coupons',
			'Api:Deals:default' => 'api/v1/deals',
			'Api:Deals:activeDeals' => 'api/v1/active-deals',
			'Api:CompetitionDeals:default' => 'api/v1/competition-deals',
			'Api:Competitor:competitorOfferWebhook' => 'api/v1/competitor-offer-webhook',
			'Api:Popup:interaction' => 'api/v1/popup/interaction',
			'Api:AdminSearch:users' => 'api/v1/admin-search/users',
			'Api:Slack:message' => 'api/v1/workplace/message',
			'Api:Slack:afterDeploySuccessMessage' => 'api/v1/workplace/after-deploy-success-message',
			'Api:Slack:afterDeployFailMessage' => 'api/v1/workplace/after-deploy-fail-message',
			'Api:AssertibleWebhook:assertibleWebhook' => 'api/v1/assertible-webhook',
			'Api:Trifft:default' => 'api/v1/trifft',
			'Api:FrontendWarmup:default' => 'api/v1/frontend-warmup',
			'Api:PartnerSystems:default' => 'api/v1/partner-systems',
			'Api:Image:adminRedactorUpload' => 'api/v1/image/admin-redactor-upload',
			'Api:ShopFeed:default' => 'api/v1/shop-feed',
			'Api:Zasilkovna:unsubscribe' => 'api/v1/zasilkovna/unsubscribe',
			'Api:Edenred:users' => 'api/v1/edenred/users',
			'Front:Edenred:default' => 'edenred/vip',
			'Front:Edenred:thankYou' => 'edenred/vip/dakujeme',
			'Front:Edenred:conditions' => 'edenred/vip/podmienky',
		];

		$oldStaticRoutes = [
			'LandingPage:Homepage:pestryJidelnicek' => $trans['pestryJidelnicek'],
			'LandingPage:Homepage:pestryJidelnicekLandingPage' => $trans['pestryJidelnicekLandingPage'],
			'Front:Sign:in' => $trans['signIn'],
			'Front:Sign:out' => $trans['signOut'],
			'Front:Sign:forgottenPassword' => $trans['forgottenPassword'],
			'Front:Sign:forgottenPasswordSent' => $trans['forgottenPasswordSent'],
			'Front:Sign:fbLogin' => $trans['signFacebook'],
			'Front:Account:Review:default' => $trans['myReviews'],
			'Front:Account:Review:share' => $trans['shareReview'],
			'Front:Account:User:changePassword' => $trans['changePassword'],
			'Front:Account:User:settings' => $trans['settings'],
			'Front:Account:User:guarantee' => $trans['userGuarantee'],
			'Front:Account:User:tellFriend' => $trans['userTellFriend'],
			'Front:Account:User:phoneNumberVerification' => $trans['phoneNumberVerification'],
			'Front:Account:User:accountNumberChangeVerification' => $trans['accountNumberChangeVerification'],
			'Front:Account:User:sessionVerification' => $trans['sessionVerification'],
			'Front:Account:Transaction:default' => $trans['transactions'],
			'Front:Account:Payout:default' => $trans['payouts'],
			'Front:Homepage:defaultLogged' => $trans['myFavorites'],
			'Front:Homepage:defaultLoggedInactive' => $trans['myFavoritesInactive'],
			'Front:Homepage:subscribeCampaign' => $trans['subscribeCampaign'],
			'Front:Account:Notification:default' => $trans['notification'],
			'Front:Static:guide' => 'guide',
			'Front:Static:contact' => $trans['contact'],
			'Front:Static:accountDeletionRequest' => $trans['accountDeletionRequest'],
			'Front:Static:conditions' => $trans['conditions'],
			'Front:Static:zasilkovnaConditions' => $trans['zasilkovnaConditions'],
			'Front:Static:forMedia' => $trans['forMedia'],
			'Front:Static:addon' => $trans['addon'],
			'Front:Static:addonInstalled' => $trans['addonInstalled'],
			'Front:Static:howItWorks' => $trans['howItWorks'],
			'Front:Static:howWorksTransactions' => $trans['howWorksTransactions'],
			'Front:Static:guarantee' => $trans['guarantee'],
//          'Front:Static:guaranteeHighestRewards' => $trans['guaranteeHighestRewards'],
//			'Front:Static:cashbackRules' => 'cashback-rules',
			'Front:Static:complaints' => $trans['complaints'],
			'Front:Static:workWithUs' => $trans['workWithUs'],
			'Front:Static:bloggers' => $trans['bloggers'],
			'Front:Static:ads' => 'ads.txt',
			'Front:Zasilkovna:default' => $trans['zasilkovnaDefault'],
			'Front:Zasilkovna:extension' => $trans['zasilkovnaExtension'],
			'Front:Zasilkovna:thankYou' => $trans['zasilkovnaThankYou'],
			'Front:SocialShare:facebook' => $trans['socialShareFacebook'],
			'Front:SocialShare:twitter' => $trans['socialShareTwitter'],
			'Front:SitemapData:web' => $trans['sitemap'],
			'Front:SitemapData:bot' => 'sitemap.xml',
			'Front:Robots:default' => 'robots.txt',
			'Front:OpenSearch:xml' => 'opensearch.xml',
			'Front:Articles:Rss:articles' => 'articles.xml',
			'Front:Articles:Rss:instantArticles' => 'instant-articles.xml',
			'Front:SurveyBonus:default' => 'survey/ISgts2o7xi',
			'Front:Static:greyson' => 'greyson',
			'Front:Static:o2Benefity' => 'o2-benefity',
			'Front:Static:b2b' => 'b2b',
			'Front:Static:asyncPopup' => 'exit-async',
			'Front:Static:leafletList' => 'letaky-static',
			'Front:Static:leaflet' => 'letaky-detail',
			'Front:Static:leafletBrand' => 'letaky-obchod',
			'Front:Static:leafletBranch' => 'letaky-pobocka',
			'Front:Static:privacyPolicy' => $trans['privacy'],
			'Front:Static:learnLaw' => $trans['learnLaw'],
			'Front:Static:privacyInfo' => $trans['privacyInfo'],
			'Front:Static:cookies' => 'cookies',
			'Front:Static:adminSms' => 'admin-sms',
			'Front:Static:faq' => $trans['faq'],
//			'Front:Static:topOffers' => $trans['topOffers'],
			'Front:Account:FreshdeskFeedback:default' => $trans['feedback'],
			'Front:Account:FreshdeskFeedback:thankYou' => $trans['feedbackThankYou'],
			'Front:Static:onBoard' => 'onboard',
			'Front:Static:leafletLayout' => 'letaky-layout',
			'Front:Static:dziekujemy' => $trans['dziekujemy'],
			'Front:SpeedTest:default' => 'speed-test',
			'Front:Static:phoneApp' => $trans['phoneApp'],
			'Front:Static:adtest' => 'adtest',
			'Front:Account:Refund:default' => $trans['refunds'],
			'Front:Account:Refund:thankYou' => $trans['refundsThankYou'],
			'Front:Account:Refunds:default' => $trans['refundsList'],
			'Front:Shops:Redirection:setAdblockUsed' => 'redirection/set-adblock-used',
			'Front:Redirection:thanks' => $trans['redirectionFeedbackThankYou'],
			'Front:Redirection:redirectionFeedback' => 'redirection-feedback/<uniqueId>/<type>',
//			'Front:Static:newHp' => 'new-hp',
			'Front:Static:afterSignUp' => 'addon-bonus',
			'Front:Static:speedTest' => 'page-speed-test',
			'Front:Static:newDesignHp' => 'new-design-hp',
			'Front:Static:newDesignWelcome' => 'new-design-welcome',
			'Front:Static:newDesignSales' => 'new-design-sales',
			'Front:Static:newDesignShopProfile' => 'new-design-shop-profile',
			'Front:Static:newDesignShops' => 'new-design-shops',
			'Front:Static:newDesignFavorite' => 'new-design-favorite',
			'Front:Static:newDesignModals' => 'new-design-modals',
			'Front:Static:newDesignSettings' => 'new-design-settings',
			'Front:Static:newDesignSettings1' => 'new-design-settings-1',
			'Front:Static:newDesignSettings2' => 'new-design-settings-2',
			'Front:Static:newDesignSettings3' => 'new-design-settings-3',
			'Front:Static:newDesignSettings4' => 'new-design-settings-4',
			'Front:Static:newDesignSettings5' => 'new-design-settings-5',
			'Front:Static:newDesignSettings6' => 'new-design-settings-6',
			'Front:Static:newDesignSettings7' => 'new-design-settings-7',
			'Front:Static:newDesignSettings8' => 'new-design-settings-8',
			'Front:Static:newDesignSettings9' => 'new-design-settings-9',
			'Front:Static:newDesignSatisfactionGuarantee1' => 'new-design-satisfaction-guarantee-1',
			'Front:Static:newDesignForgotPassword' => 'new-design-forgot-password',
			'Front:Static:newDesignRequestForgotPassword' => 'new-design-request-forgot-password',
			'Front:Static:newDesignNewPassword' => 'new-design-new-password',
			'Front:Static:newDesignLogin' => 'new-design-login',
			'Front:Static:newDesignNewAccount' => 'new-design-new-account',
			'Front:Static:newDesignApplication' => 'new-design-application',
			'Front:Static:newDesignContact' => 'new-design-contact',
			'Front:Static:newDesignTipliToBrowser' => 'new-design-tipli-to-browser',
			'Front:Static:newDesignRedirect' => 'new-design-redirect',
			'Front:Static:newDesignRedirect1' => 'new-design-redirect-1',
			'Front:Static:newDesignRedirect2' => 'new-design-redirect-2',
			'Front:Static:newDesignRecommendation' => 'new-design-recommendation',
			'Front:Static:newDesignHowItsWorking' => 'new-design-how-its-working',
			'Front:Static:newDesignFAQ' => 'new-design-faq',
			'Front:Static:newDesignVOP' => 'new-design-vop',
			'Front:Static:newDesignBlog' => 'new-design-blog',
			'Front:Static:newDesignBlogDetail' => 'new-design-blog-detail',
			'Front:Static:newDesignCareer' => 'new-design-career',
			'Front:Static:newDesignCareerDetail' => 'new-design-career-detail',
			'Front:Static:newDesign404' => 'new-design-404',
			'Front:Static:newDesignBenefits' => 'new-design-benefits',
		];

		$hasNewRouter = true;
		$staticRoutes = array_merge($globalRoutes, $this->getNewFrontModuleRoutes($trans, true));
		$moduleName = 'NewFront';

		/** @var string $staticRoute */
		/** @var string|null $path */
		foreach ($staticRoutes as $staticRoute => $path) {
			if ($path === null) {
				continue;
			}
			$router->addRoute($path, $staticRoute);
		}

		$router->addRoute(
			'api/v1/campaign-image/<action>',
			[
				'module' => 'Api',
				'presenter' => 'CampaignImage',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'api/v1/shop-image/<shopId>[/<width>][/<height>]',
			[
				'module' => 'Api',
				'presenter' => 'ShopImage',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'admin/<presenter>/<action>[/<id>]',
			[
				'module' => 'Admin',
				'presenter' => 'Introduce',
				'action' => 'default',
				'id' => null,
			]
		);

		$router->addRoute('api/v1/mobile/static', 'Api:Mobile:Static:default');
		$router->addRoute('api/v1/mobile/auth/register', 'Api:Mobile:Auth:register');
		$router->addRoute('api/v1/mobile/auth/login', 'Api:Mobile:Auth:login');
		$router->addRoute('api/v1/mobile/auth/logout', 'Api:Mobile:Auth:logout');
		$router->addRoute('api/v1/mobile/auth/apple', 'Api:Mobile:Auth:apple');
		$router->addRoute('api/v1/mobile/auth/facebook', 'Api:Mobile:Auth:facebook');
		$router->addRoute('api/v1/mobile/auth/google', 'Api:Mobile:Auth:google');

		$router->addRoute('api/v1/mobile/check-version', 'Api:Mobile:Version:version');
		$router->addRoute('api/v1/mobile/user/device-token', 'Api:Mobile:User:deviceToken');
		$router->addRoute('api/v1/mobile/user/me', 'Api:Mobile:User:me');
		$router->addRoute('api/v1/mobile/user/name', 'Api:Mobile:User:name');
		$router->addRoute('api/v1/mobile/user/birthdate', 'Api:Mobile:User:birthdate');
		$router->addRoute('api/v1/mobile/user/gender', 'Api:Mobile:User:gender');
		$router->addRoute('api/v1/mobile/user/phone-number', 'Api:Mobile:User:phoneNumber');
		$router->addRoute('api/v1/mobile/user/phone-number/verify-code', 'Api:Mobile:User:phoneNumberVerifyCode');
		$router->addRoute('api/v1/mobile/user/account-number', 'Api:Mobile:User:accountNumber');
		$router->addRoute('api/v1/mobile/user/password', 'Api:Mobile:User:password');
		$router->addRoute('api/v1/mobile/user/forgotten-password', 'Api:Mobile:ForgottenPassword:default');

		$router->addRoute('api/v1/mobile/shop/home', 'Api:Mobile:Shop:home');
		$router->addRoute('api/v1/mobile/shop/detail/<id>', 'Api:Mobile:Shop:detail');
		$router->addRoute('api/v1/mobile/shop/detail/<shopId>/coupons', 'Api:Mobile:Shop:coupons');
		$router->addRoute('api/v1/mobile/shop/detail/<shopId>/sales', 'Api:Mobile:Shop:sales');
		$router->addRoute('api/v1/mobile/shop/search[/<query>]', 'Api:Mobile:Shop:search');
		$router->addRoute('api/v1/mobile/shop', 'Api:Mobile:Shop:default');
		$router->addRoute('api/v1/mobile/shop/favorites', 'Api:Mobile:Shop:favorites');
		$router->addRoute('api/v1/mobile/shop/favorite/<id>', 'Api:Mobile:Shop:favorite');

		$router->addRoute('api/v1/mobile/deal/tags', 'Api:Mobile:Deal:tags');
		$router->addRoute('api/v1/mobile/deal/home/<USER>', 'Api:Mobile:Deal:home');
		$router->addRoute('api/v1/mobile/deal/coupons/[<tagId>]', 'Api:Mobile:Deal:coupons');
		$router->addRoute('api/v1/mobile/deal/sales/[<tagId>]', 'Api:Mobile:Deal:sales');

		$router->addRoute('api/v1/mobile/referral', 'Api:Mobile:Referral:default');

		$router->addRoute('api/v1/mobile/notification', 'Api:Mobile:Notification:default');
		$router->addRoute('api/v1/mobile/notification/open', 'Api:Mobile:Notification:open');
		$router->addRoute('api/v1/mobile/notification/push-test', 'Api:Mobile:Notification:pushTest');

		$router->addRoute('api/v1/mobile/tag/<parentId>/tags', 'Api:Mobile:Tag:tags');
		$router->addRoute('api/v1/mobile/tag/<tagId>/shops', 'Api:Mobile:Tag:shops');

		$router->addRoute('api/v1/mobile/account/home', 'Api:Mobile:Account:home');
		$router->addRoute('api/v1/mobile/account/transactions', 'Api:Mobile:Account:transactions');
		$router->addRoute('api/v1/mobile/account/transactions/registered', 'Api:Mobile:Account:transactionsRegistered');
		$router->addRoute('api/v1/mobile/account/transactions/confirmed', 'Api:Mobile:Account:transactionsConfirmed');

		$router->addRoute('api/v1/mobile/account/payout/check', 'Api:Mobile:Account:payoutCheck');
		$router->addRoute('api/v1/mobile/account/payout/request', 'Api:Mobile:Account:payoutRequest');
		$router->addRoute('api/v1/mobile/account/payouts', 'Api:Mobile:Account:payouts');
		$router->addRoute('api/v1/mobile/account/delete', 'Api:Mobile:Account:delete');

		$router->addRoute('api/v1/mobile/sandbox/<action>', 'Api:Mobile:Sandbox:default');

		$router->addRoute('api/v1/mobile/review', 'Api:Mobile:Review:default');

		$router->addRoute('new-homepage', 'Front:Homepage:new');

		$router->addRoute(
			'api/v1/mobile/[<url .+>]',
			[
				'module' => 'Api:Mobile',
				'presenter' => 'Error4xx',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'api/v1/addon/shops/<shopId>',
			[
				'module' => 'Api',
				'presenter' => 'Addon',
				'action' => 'Shops',
			]
		);

		$router->addRoute(
			'api/v2/addon/alternative-offer/<id>',
			[
				'module' => 'Api:Addon',
				'presenter' => 'AlternativeOffer',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'api/v1/reward/<shop>',
			[
				'module' => 'Api',
				'presenter' => 'Reward',
				'action' => 'reward',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug($shop, $this->localization);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization twisto|twisto-test>/users/<userId>/transactions',
			[
				'module' => 'Api',
				'presenter' => 'Twisto',
				'action' => 'UserTransactions',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization twisto|twisto-test>/shops',
			[
				'module' => 'Api',
				'presenter' => 'Twisto',
				'action' => 'shops',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization twisto|twisto-test>/users/search',
			[
				'module' => 'Api',
				'presenter' => 'Twisto',
				'action' => 'usersSearch',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization twisto|twisto-test>/users',
			[
				'module' => 'Api',
				'presenter' => 'Twisto',
				'action' => 'users',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization rondo|rondo-test>/transactions',
			[
				'module' => 'Api',
				'presenter' => 'Rondo',
				'action' => 'transactions',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization rondo|rondo-test>/shops',
			[
				'module' => 'Api',
				'presenter' => 'Rondo',
				'action' => 'shops',
			]
		);

		$router->addRoute(
			'api/v1/<partnerOrganization rondo|rondo-test>/users',
			[
				'module' => 'Api',
				'presenter' => 'Rondo',
				'action' => 'users',
			]
		);

		$router->addRoute(
			'api/v1/homecredit/transactions',
			[
				'module' => 'Api',
				'presenter' => 'HomeCredit',
				'action' => 'transactions',
			]
		);

		$router->addRoute(
			'api/v1/homecredit/shops',
			[
				'module' => 'Api',
				'presenter' => 'HomeCredit',
				'action' => 'shops',
			]
		);

		$router->addRoute(
			'api/v1/homecredit/tags',
			[
				'module' => 'Api',
				'presenter' => 'HomeCredit',
				'action' => 'tags',
			]
		);

		$router->addRoute(
			'reports/<presenter>/<action>[/<id>]',
			[
				'module' => 'Reports',
				'presenter' => 'Homepage',
				'action' => 'default',
				'id' => null,
			]
		);

		$router->addRoute(
			'tick/<presenter>/<action>[/<id>]',
			[
				'module' => 'Tick',
				'presenter' => 'Dashboard',
				'action' => 'default',
				'id' => null,
			]
		);

		$router->addRoute(
			'o/<partnerOrganization>',
			[
				'presenter' => 'Front:Account:TellFriend',
				'action' => 'recommendByPartnerOrganization',
				'partnerOrganization' => [
					Route::FILTER_IN => function ($partnerOrganization) {
						return $this->partnerOrganizationRepository->findBySlug($partnerOrganization, $this->localization);
					},
					Route::FILTER_OUT => static function ($partnerOrganization) {
						return $partnerOrganization->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['emailVerified'] . '/<at>',
			[
				'presenter' => $moduleName . ':Account:User',
				'action' => 'emailVerified',
			]
		);

		$router->addRoute(
			$trans['signUp'] . '[/<slug>]',
			[
				'module' => $moduleName,
				'presenter' => 'Sign',
				'action' => 'up',
				'slug' => null,
			]
		);
		$router->addRoute(
			'new/' . $trans['signUp'] . '[/<slug>]',
			[
				'module' => $moduleName,
				'presenter' => 'Sign',
				'action' => 'up',
				'slug' => null,
			]
		);

		$router->addRoute(
			$trans['accountChangeVerification'] . '/<hash>',
			[
				'presenter' => $moduleName . ':Account:User',
				'action' => 'changeVerification',
			]
		);

		$router->addRoute($trans['unsubscribeEmail'] . '[/<contentType>]', $moduleName . ':Static:unsubscribeEmail');

		if ($hasNewRouter) {
			$router->addRoute(
				$trans['shop'] . '/<shop>',
				[
					'presenter' => 'NewFront:Shops:Shop',
					'action' => 'default',
					'shop' => [
						Route::FILTER_IN => function ($shop) {
							return $this->shopRepository->findBySlug($shop, $this->localization);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]
			);
		} else {
			$router->addRoute(
				$trans['shop'] . '/<shop>',
				[
					'presenter' => 'Front:Shops:Shop',
					'action' => 'shop',
					'shop' => [
						Route::FILTER_IN => function ($shop) {
							return $this->shopRepository->findBySlug($shop, $this->localization);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]
			);

			$router->addRoute(
				'new/' . $trans['shop'] . '/<shop>',
				[
					'presenter' => 'NewFront:Shops:Shop',
					'action' => 'default',
					'shop' => [
						Route::FILTER_IN => function ($shop) {
							return $this->shopRepository->findBySlug($shop, $this->localization);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]
			);
		}


		if ($hasNewRouter) {
			$router->addRoute(
				$trans['shops'] . '/' . $trans['newest']  . '[/<tag>]',
				[
					'presenter' => 'NewFront:Shops:Shops',
					'action' => 'newest',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_SHOP);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isShopTag() ? $tag->getSlug() : null;
						},
					],
				]
			);
		} else {
			$router->addRoute(
				$trans['shops'] . '/' . $trans['newest'],
				[
					'presenter' => 'Front:Shops:Shop',
					'action' => 'newest',
				]
			);
		}


		$router->addRoute(
			$trans['newShop'] . '/<shop>',
			[
				'presenter' => 'Front:Shops:Shop',
				'action' => 'newShop',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug($shop, $this->localization);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		if ($hasNewRouter) {
			$router->addRoute($trans['search'] . '[/<searchQuery>]', 'NewFront:Shops:Shops:search');
		} else {
			$router->addRoute($trans['search'] . '/<searchQuery>', 'Front:Shops:Shop:shops');
			$router->addRoute('new/' . $trans['search'] . '[/<searchQuery>]', 'NewFront:Shops:Shops:search');
		}

		if ($hasNewRouter) {
			$router->addRoute(
				$trans['shops'] . '/' . $trans['nonCashback'] . '[/<tag>]',
				[
					'presenter' => 'NewFront:Shops:Shops',
					'action' => 'list',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_SHOP);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isShopTag() ? $tag->getSlug() : null;
						},
					],
				]
			);

			$router->addRoute(
				$trans['shops'] . '[/<tag>]',
				[
					'presenter' => 'NewFront:Shops:Shops',
					'action' => 'default',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_SHOP);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isShopTag() ? $tag->getSlug() : null;
						},
					],
				]
			);
		} else {
			$router->addRoute(
				$trans['shops'] . '/<tag>',
				[
					'presenter' => 'Front:Shops:Shop',
					'action' => 'shops',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_SHOP);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isShopTag() ? $tag->getSlug() : null;
						},
					],
				]
			);

			$router->addRoute(
				'new/' . $trans['shops'] . '[/<tag>]',
				[
					'presenter' => 'NewFront:Shops:Shops',
					'action' => 'default',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_SHOP);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isShopTag() ? $tag->getSlug() : null;
						},
					],
				]
			);

			$router->addRoute($trans['shops'], 'Front:Shops:Shop:shops');
		}

		$router->addRoute(
			'<type>' . '.xml',
			[
				'presenter' => $moduleName . ':SitemapData',
				'action' => 'urls',
				'type' => [
					Route::FILTER_IN => static function ($type) {
						if (SitemapDataPresenter::isValidSitemapType($type)) {
							return $type;
						} else {
							return null;
						}
					},
					Route::FILTER_OUT => static function ($type) {
						if (SitemapDataPresenter::isValidSitemapType($type)) {
							return $type;
						} else {
							return null;
						}
					},
				],
			]
		);

		$router->addRoute($trans['search'], 'Front:Shops:Shop:shops', Route::ONE_WAY);

		$router->addRoute($trans['guaranteeHighestRewards'], 'Front:Homepage:default', Route::ONE_WAY);

		$router->addRoute(
			$trans['redirectShop'] . '/<shop>/<source rondo>/<subId>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shopRondoDirect',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectShop'] . '/<shop>/<source homecredit>/<subId>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shopHomeCreditDirect',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectShop'] . '/<shop>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['redirectShop'] . '/<shop>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectDeal'] . '/<deal>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'deal',
				'deal' => [
					Route::FILTER_IN => function ($deal) {
						return $this->dealRepository->findBySlug($this->localization, $deal);
					},
					Route::FILTER_OUT => static function (Deal $deal) {
						return $deal->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['redirectDeal'] . '/<deal>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'deal',
				'deal' => [
					Route::FILTER_IN => function ($deal) {
						return $this->dealRepository->findBySlug($this->localization, $deal);
					},
					Route::FILTER_OUT => static function (Deal $deal) {
						return $deal->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectProduct'] . '/<product>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'product',
				'product' => [
					Route::FILTER_IN => function ($id) {
						return $this->productRepository->find($id);
					},
					Route::FILTER_OUT => static function ($product) {
						return $product->getId();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['redirectProduct'] . '/<product>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'product',
				'product' => [
					Route::FILTER_IN => function ($id) {
						return $this->productRepository->find($id);
					},
					Route::FILTER_OUT => static function ($product) {
						return $product->getId();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectProduct'] . '/2/<product2>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'product2',
				'product2' => [
					Route::FILTER_IN => function ($id) {
						return $this->product2Repository->find($id);
					},
					Route::FILTER_OUT => static function ($product) {
						return $product->getId();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['redirectProduct'] . '/2/<product2>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'product2',
				'product2' => [
					Route::FILTER_IN => function ($id) {
						return $this->product2Repository->find($id);
					},
					Route::FILTER_OUT => static function ($product) {
						return $product->getId();
					},
				],
			]
		);

		$router->addRoute(
			'exit/<redirection>/<secureHash>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'exit',
				'redirection' => [
					Route::FILTER_IN => function ($uniqueId) {
						return $this->shopRedirectionRepository->findByUniqueId($uniqueId);
					},
					Route::FILTER_OUT => static function (Redirection $redirection) {
						return $redirection->getUniqueId();
					},
				],
			]
		);

		$router->addRoute(
			'sync-exit/<syncId>/<secureHash>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'syncExit',
			]
		);

		$router->addRoute(
			$trans['redirectShop'] . '/recaptcha/<token>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'recaptcha',
			]
		);

		$router->addRoute(
			'new/' . $trans['redirectShop'] . '/recaptcha/<token>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'recaptcha',
			]
		);

		// old redirection 301 to deal
		$router->addRoute(
			$trans['redirectDealOld'] . '/<deal>',
			[
				'presenter' => 'Front:Shops:Redirection',
				'action' => 'dealOld',
				'deal' => [
					Route::FILTER_IN => function ($deal) {
						return $this->dealRepository->findBySlug($this->localization, $deal);
					},
					Route::FILTER_OUT => static function (Deal $deal) {
						return $deal->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectShopDirect'] . '/<shop>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shopDirect',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectShopPartner'] . '/<shop>/<partner twisto|rondo|homecredit|homecredit_test>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shopPartner',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectShopSpinner'] . '/<shop>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'shopSpinner',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$entity = $this->shopRepository->findBySlug($shop, $this->localization);
						return $entity;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectOffer'] . '/<offer>',
			[
				'presenter' => $moduleName .  ':Shops:Redirection',
				'action' => 'offer',
				'offer' => [
					Route::FILTER_IN => function ($offer) {
						return $this->offerRepository->find($offer);
					},
					Route::FILTER_OUT => static function ($offer) {
						return $offer->getId();
					},
				],
			]
		);

		$router->addRoute(
			$trans['banner'] . '/<banner>',
			[
				'presenter' => $moduleName . ':Shops:Redirection',
				'action' => 'banner',
				'banner' => [
					Route::FILTER_IN => function ($banner) {
						return $this->bannerRepository->find($banner);
					},
					Route::FILTER_OUT => static function ($banner) {
						return $banner->getId();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['banner'] . '/<banner>',
			[
				'presenter' => 'NewFront:Shops:Redirection',
				'action' => 'banner',
				'banner' => [
					Route::FILTER_IN => function ($banner) {
						return $this->bannerRepository->find($banner);
					},
					Route::FILTER_OUT => static function ($banner) {
						return $banner->getId();
					},
				],
			]
		);

		$router->addRoute(
			$trans['notification'] . '/<notification>',
			[
				'presenter' => $moduleName . ':Redirection',
				'action' => 'notification',
				'notification' => [
					Route::FILTER_IN => function ($notification) {
						return $this->notificationRepository->find($notification);
					},
					Route::FILTER_OUT => static function ($notification) {
						return $notification->getId();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['notification'] . '/<notification>',
			[
				'presenter' => 'NewFront:Redirection',
				'action' => 'notification',
				'notification' => [
					Route::FILTER_IN => function ($notification) {
						return $this->notificationRepository->find($notification);
					},
					Route::FILTER_OUT => static function ($notification) {
						return $notification->getId();
					},
				],
			]
		);

		$router->addRoute(
			$trans['redirectPopup'] . '/<popupInteraction>',
			[
				'presenter' => $moduleName . ':Redirection',
				'action' => 'popup',
				'popupInteraction' => [
					Route::FILTER_IN => function ($popupInteraction) {
						return $this->popupInteractionRepository->find($popupInteraction);
					},
					Route::FILTER_OUT => static function (PopupInteraction $popupInteraction) {
						return $popupInteraction->getId();
					},
				],
			]
		);

		$router->addRoute(
			$trans['sales'] . '[/<entity>]',
			[
				'presenter' => $moduleName . ':Shops:Sale',
				'action' => 'sales',
				'entity' => [
					Route::FILTER_IN => function ($entity) {
						return $this->tagRepository->findBySlug($entity, $this->localization, Tag::TYPE_SALE) ? : $this->shopRepository->findBySlug($entity, $this->localization);
					},
					Route::FILTER_OUT => static function ($entity) {
						if (($entity instanceof Tag && $entity->isSaleTag()) || $entity instanceof Shop) {
							return $entity->getSlug();
						}
					},
				],
			]
		);

		if ($hasNewRouter) {
			$router->addRoute(
				$trans['deals'] . '[/<entity>]',
				[
					'presenter' => 'NewFront:Deals:Deals',
					'action' => 'default',
					'entity' => [
						Route::FILTER_IN => function ($entity) {
							return $this->tagRepository->findBySlug($entity, $this->localization, [Tag::TYPE_SALE]) ? : $this->shopRepository->findBySlug($entity, $this->localization);
						},
						Route::FILTER_OUT => static function ($entity) {
							if (($entity instanceof Tag && $entity->isSaleTag()) || $entity instanceof Shop) {
								return $entity->getSlug();
							}
						},
					],
				]
			);
		} else {
			$router->addRoute(
				$trans['deals'] . '[/<entity>]',
				[
					'presenter' => $moduleName . ':Deals:Deal',
					'action' => 'deals',
					'entity' => [
						Route::FILTER_IN => function ($entity) {
							return $this->tagRepository->findBySlug($entity, $this->localization, [Tag::TYPE_SALE]) ? : $this->shopRepository->findBySlug($entity, $this->localization);
						},
						Route::FILTER_OUT => static function ($entity) {
							if (($entity instanceof Tag && $entity->isSaleTag()) || $entity instanceof Shop) {
								return $entity->getSlug();
							}
						},
					],
				]
			);

			$router->addRoute(
				'new/' . $trans['deals'] . '[/<entity>]',
				[
					'presenter' => 'NewFront:Deals:Deals',
					'action' => 'default',
					'entity' => [
						Route::FILTER_IN => function ($entity) {
							return $this->tagRepository->findBySlug($entity, $this->localization, [Tag::TYPE_SALE]) ? : $this->shopRepository->findBySlug($entity, $this->localization);
						},
						Route::FILTER_OUT => static function ($entity) {
							if (($entity instanceof Tag && $entity->isSaleTag()) || $entity instanceof Shop) {
								return $entity->getSlug();
							}
						},
					],
				]
			);
		}

		$router->addRoute(
			$trans['rewardCampaigns'],
			[
				'presenter' => 'Front:Rewards:Reward',
				'action' => 'rewardCampaigns',
			]
		);

		$router->addRoute(
			$trans['newPassword'] . '/<user>',
			[
				'presenter' => $moduleName . ':Sign',
				'action' => 'newPassword',
				'user' => [
					Route::FILTER_IN => function ($user) {
						return $this->userRepository->findOneByNewPasswordRequestHash($user);
					},
					Route::FILTER_OUT => static function ($user) {
						return $user->getNewPasswordRequestHash();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['newPassword'] . '/<user>',
			[
				'presenter' => 'NewFront:Sign',
				'action' => 'newPassword',
				'user' => [
					Route::FILTER_IN => function ($user) {
						return $this->userRepository->findOneByNewPasswordRequestHash($user);
					},
					Route::FILTER_OUT => static function ($user) {
						return $user->getNewPasswordRequestHash();
					},
				],
			]
		);

		$router->addRoute(
			$trans['leaflet'] . '/<leaflet>',
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'leaflet',
				'leaflet' => [
					Route::FILTER_IN => function ($leaflet) {
						return $this->leafletRepository->findBySlug($leaflet, $this->localization);
					},
					Route::FILTER_OUT => static function (Leaflet $leaflet) {
						return $leaflet->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['leaflet'] . '/<leaflet>',
			[
				'presenter' => 'NewFront:Leaflets:Leaflet',
				'action' => 'leaflet',
				'leaflet' => [
					Route::FILTER_IN => function ($leaflet) {
						return $this->leafletRepository->findBySlug($leaflet, $this->localization);
					},
					Route::FILTER_OUT => static function (Leaflet $leaflet) {
						return $leaflet->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['leaflets'],
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'new/' . $trans['leaflets'],
			[
				'presenter' => 'NewFront:Leaflets:Leaflet',
				'action' => 'default',
			]
		);

		$router->addRoute(
			$trans['leaflets-shops'],
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'shops',
			]
		);

		$router->addRoute(
			$trans['leaflets-all-valid'],
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'allValidLeaflets',
			]
		);

		$router->addRoute(
			$trans['leaflets-all-scheduled'],
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'allScheduledLeaflets',
			]
		);

		$router->addRoute(
			$trans['leaflets'] . '/<tag>',
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'leaflets',
				'tag' => [
					Route::FILTER_IN => function ($tag) {
						return $this->tagRepository->findBySlug($tag, $this->localization, Tag::TYPE_LEAFLET);
					},
					Route::FILTER_OUT => static function (Tag $tag) {
						return $tag->isLeafletTag() ? $tag->getSlug() : null;
					},
				],
			]
		);

		$router->addRoute(
			$trans['leaflets'] . '/<shop>',
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'leaflets',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug($shop, $this->localization);
					},
					Route::FILTER_OUT => static function (Shop $shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['leaflets'] . '/<region>',
			[
				'presenter' => $moduleName . ':Leaflets:Leaflet',
				'action' => 'leaflets',
				'region' => [
					Route::FILTER_IN => function ($region) {
						return $this->regionRepository->findOneBySlug($region, $this->localization);
					},
					Route::FILTER_OUT => static function (Region $region) {
						return $region->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['jobs'],
			[
				'presenter' => $moduleName . ':Jobs:Job',
				'action' => 'jobs',
			]
		);

		$router->addRoute(
			'new/' . $trans['jobs'],
			[
				'presenter' => 'NewFront:Jobs:Job',
				'action' => 'jobs',
			]
		);

		$router->addRoute(
			$trans['jobs'] . '/<job>',
			[
				'presenter' => $moduleName . ':Jobs:Job',
				'action' => 'job',
				'job' => [
					Route::FILTER_IN => function ($job) {
						return $this->jobRepository->findBySlug($job, $this->localization);
					},
					Route::FILTER_OUT => static function ($job) {
						return $job->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['jobs'] . '/<job>',
			[
				'presenter' => 'NewFront:Jobs:Job',
				'action' => 'job',
				'job' => [
					Route::FILTER_IN => function ($job) {
						return $this->jobRepository->findBySlug($job, $this->localization);
					},
					Route::FILTER_OUT => static function ($job) {
						return $job->getSlug();
					},
				],
			]
		);

		if ($hasNewRouter) {
			$router->addRoute(
				$trans['article'] . '/<article>',
				[
					'presenter' => 'NewFront:Articles:Article',
					'action' => 'default',
					'article' => [
						Route::FILTER_IN => function ($article) {
							return $this->articleRepository->findBySlug(SeoFacade::cleanUrl($article), $this->localization);
						},
						Route::FILTER_OUT => static function ($article) {
							return $article->getSlug();
						},
					],
				]
			);

			$router->addRoute(
				$trans['articles'] . '[/<tag>]',
				[
					'presenter' => 'NewFront:Articles:Articles',
					'action' => 'default',
					'tag' => [
						Route::FILTER_IN => function ($tag) {
							return $this->tagRepository->findBySlug(SeoFacade::cleanUrl($tag), $this->localization, Tag::TYPE_ARTICLE);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->isArticleTag() ? $tag->getSlug() : null;
						},
					],
				]
			);
		} else {
			$router->addRoute(
				$trans['article'] . '/<article>',
				[
					'presenter' => $moduleName . ':Articles:Article',
					'action' => 'article',
					'article' => [
						Route::FILTER_IN => function ($article) {
							return $this->articleRepository->findBySlug(SeoFacade::cleanUrl($article), $this->localization);
						},
						Route::FILTER_OUT => static function ($article) {
							return $article->getSlug();
						},
					],
				]
			);

			$router->addRoute(
				'new/' . $trans['article'] . '/<article>',
				[
					'presenter' => 'NewFront:Articles:Article',
					'action' => 'default',
					'article' => [
						Route::FILTER_IN => function ($article) {
							return $this->articleRepository->findBySlug(SeoFacade::cleanUrl($article), $this->localization);
						},
						Route::FILTER_OUT => static function ($article) {
							return $article->getSlug();
						},
					],
				]
			);
		}

		$router->addRoute(
			'preview',
			[
				'presenter' => $moduleName . ':Articles:Article',
				'action' => 'previewArticle',
			]
		);

		$router->addRoute(
			$trans['addReview'] . '[/<shop>]',
			[
				'presenter' => $moduleName . ':Account:Review',
				'action' => 'review',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$shop = $this->shopRepository->findBySlug($shop, $this->localization);
						return $shop && $shop->isActive() ? $shop : null;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'new/' . $trans['addReview'] . '[/<shop>]',
			[
				'presenter' => 'NewFront:Account:Review',
				'action' => 'review',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						$shop = $this->shopRepository->findBySlug($shop, $this->localization);
						return $shop && $shop->isActive() ? $shop : null;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			$trans['addTipliReview'] . '/<uniqueId>/<rating>',
			[
				'presenter' => $moduleName . ':Account:Review',
				'action' => 'reviewRequest',
			]
		);

		$router->addRoute(
			$trans['reviewsList'] . '[/<shop>]',
			[
				'presenter' => $moduleName . ':Reviews',
				'action' => 'default',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug($shop, $this->localization);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

//		$router->addRoute(
//			$trans['articles'] . '/<tag>',
//			[
//				'presenter' => $moduleName . ':Articles:Articles',
//				'action' => 'default',
//				'tag' => [
//					Route::FILTER_IN => function ($tag) {
//						return $this->tagRepository->findBySlug(SeoFacade::cleanUrl($tag), $this->localization, Tag::TYPE_ARTICLE);
//					},
//					Route::FILTER_OUT => static function ($tag) {
//						return $tag->isArticleTag() ? $tag->getSlug() : null;
//					},
//				],
//			]
//		);

		$router->addRoute(
			'clanky-test/<tag>',
			[
				'presenter' => $moduleName . ':Articles:Article',
				'action' => 'articlesTest',
				'tag' => [
					Route::FILTER_IN => function ($tag) {
						return $this->tagRepository->findBySlug(SeoFacade::cleanUrl($tag), $this->localization, Tag::TYPE_ARTICLE);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->isArticleTag() ? $tag->getSlug() : null;
					},
				],
			]
		);

		$router->addRoute(
			$trans['articles'] . '/<shop>',
			[
				'presenter' => $moduleName . ':Articles:Article',
				'action' => 'articles',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug(SeoFacade::cleanUrl($shop), $this->localization);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			],
			Route::ONE_WAY
		);
		$router->addRoute(
			'tipli-extra/activate/<id>', // NewFront:Vouchers:Vouchers:activateVoucher
			[
				'presenter' => $moduleName . ':Vouchers:Vouchers',
				'action' => 'activateVoucher',
			]
		);

		$router->addRoute($trans['articles'], $moduleName . ':Articles:Article:articles');
		$router->addRoute('clanky-test', $moduleName . ':Articles:Article:articlesTest');

		// Redirect Company Zonky to Shop Zonky
		$router->addRoute($trans['company'] . '/zonky', static function () use ($trans) {
			return new Nette\Application\Responses\RedirectResponse('/' . $trans['shop'] . '/zonky');
		});

		$router->addRoute(
			'p[-<entranceType>]/<p>/<shop>',
			[
				'presenter' => $moduleName . ':Account:TellFriend',
				'action' => 'recommendShop',
				'shop' => [
					Route::FILTER_IN => function ($shop) {
						return $this->shopRepository->findBySlug(SeoFacade::cleanUrl($shop), $this->localization);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]
		);

		$router->addRoute('p[-<entranceType>]/<p>', $moduleName . ':Account:TellFriend:recommend');

		$router->addRoute($trans['external-exit-popup'], 'Front:ExternalExitPopup:default');
		$router->addRoute($trans['external-exit-popup-facebook'], 'Front:ExternalExitPopup:facebook');

		$router->addRoute(
			'<shortcutPrefix>/<shortcutSlug .+>',
			[
				'presenter' => $moduleName . ':Shortener',
				'action' => 'shortcut',
				null => [
					Route::FILTER_IN => function ($params) {
						$shortcut = $this->shortcutRepository->findOneBy([
							'localization' => $this->localization,
							'prefix' => $params['shortcutPrefix'],
							'slug' => SeoFacade::cleanUrl($params['shortcutSlug']),
						]);

						if ($shortcut) {
							$params['shortcut'] = $shortcut;
							return $params;
						}

						return null;
					},
					Route::FILTER_OUT => static function ($params) {
						return $params;
					},
				],
			]
		);

		$router->addRoute(
			$trans['myApprovals'],
			[
				'presenter' => $moduleName . ':Account:Condition',
				'action' => 'default',
			]
		);

		$router->addRoute(
			'new/' . $trans['myApprovals'],
			[
				'presenter' => 'NewFront:Account:Condition',
				'action' => 'default',
			]
		);

		$router->addRoute(
			$trans['myApprovals'] . '/<id>',
			[
				'presenter' => $moduleName . ':Account:Condition',
				'action' => 'approval',
			]
		);

		$router->addRoute(
			'new/' . $trans['myApprovals'] . '/<id>',
			[
				'presenter' => 'NewFront:Account:Condition',
				'action' => 'approval',
			]
		);

		$router->addRoute(
			$trans['landingPage'] . '/<landingPage>',
			[
				'presenter' => $moduleName . ':LandingPages:LandingPage',
				'action' => 'landingPage',
				'landingPage' => [
					Route::FILTER_IN => function ($landingPage) {
						return $this->landingPageFacade->findBySlug($this->localization, $landingPage);
					},
					Route::FILTER_OUT => static function (LandingPage $landingPage) {
						return $landingPage->getSlug();
					},
				],
			]
		);

		$router->addRoute(
			'uc/<id>',
			[
				'presenter' => $moduleName . ':Shortener',
				'action' => 'userCard',
			]
		);

		$router->addRoute(
			'upload/thumbnails/<modulo>/<id>/<name>',
			[
				'presenter' => 'Front:Redirection',
				'action' => 'proxyImage',
			]
		);

		$router->addRoute(
			'upload/thumbnails/<modulo>/<id>/o/<name>',
			[
				'presenter' => 'Front:Redirection',
				'action' => 'proxyImage',
			]
		);

		$router->addRoute(
			'<redirection .+>',
			[
				'presenter' => $moduleName . ':Redirection',
				'action' => 'redirection',
				'redirection' => [
					Route::FILTER_IN => function ($url) {
						return $this->redirectionRepository->findOneBy(['localization' => $this->localization, 'oldUrl' => SeoFacade::cleanUrl($url)]);
					},
					Route::FILTER_OUT => static function ($url) {
						return $url->oldUrl;
					},
				],
			]
		);

		$router->addRoute('<presenter>/<action>[/<id>]', $moduleName . ':Homepage:default');

		return $router; /** @phpstan-ignore-line */
	}

	public static function getTranslation($key, Localization $localization)
	{
		$translations = self::getTranslations($localization);

		return $translations[$key];
	}

	private static function getTranslations(Localization $localization)
	{
		$dictionary = [
			'sitemap' => [
				'cs' => 'mapa-webu',
				'si' => 'spletni-zemljevid',
				'hr' => 'web-karta',
				'rs' => 'mapa-webu-todo',
				'bg' => 'ueb-karta',
				'sk' => 'mapa-webu',
				'pl' => 'mapa-webu',
				'ro' => 'harta-webului',
				'hu' => 'webterkep',
			],
			'signUp' => [
				'cs' => 'registrace',
				'si' => 'registracija',
				'hr' => 'registracija',
				'rs' => 'registrace-todo',
				'bg' => 'registratsiya',
				'sk' => 'registracia',
				'pl' => 'rejestracja',
				'ro' => 'inregistrare',
				'hu' => 'regisztracio',
			],
			'signIn' => [
				'cs' => 'prihlaseni',
				'si' => 'vpisi-se',
				'hr' => 'prijaviti-se',
				'rs' => 'prihlaseni-todo',
				'bg' => 'vlizam',
				'sk' => 'prihlasenie',
				'pl' => 'zaloguj-sie',
				'ro' => 'logheaza-te',
				'hu' => 'bejelentkezes',
			],
			'signOut' => [
				'cs' => 'odhlaseni',
				'si' => 'odjava',
				'hr' => 'odjavite-se',
				'rs' => 'odhlaseni-todo',
				'bg' => 'izlez-ot-profila-si',
				'sk' => 'odhlasenie',
				'pl' => 'wyloguj-sie',
				'ro' => 'delogheaza-te',
				'hu' => 'kijelentkezes',
			],
			'signFacebook' => [
				'cs' => 'prihlaseni-pres-facebook',
				'si' => 'prijava-preko-facebook',
				'hr' => 'prijavite-se-putem-facebooka',
				'rs' => 'prihlaseni-pres-facebook-todo',
				'bg' => 'vlezte-prez-facebook',
				'sk' => 'prihlasenie-cez-facebook',
				'pl' => 'zalogowanie-przez-facebook',
				'ro' => 'logheaza-te-cu-facebook',
				'hu' => 'bejelentkezes-facebookkal',
			],
			'forgottenPassword' => [
				'cs' => 'zapomenute-heslo',
				'si' => 'pozabljeno-geslo',
				'hr' => 'zaboravljena-lozinka',
				'rs' => 'zapomenute-heslo-todo',
				'bg' => 'zabravena-parola',
				'sk' => 'zabudnute-heslo',
				'pl' => 'zapomniane-haslo',
				'ro' => 'parola-uitata',
				'hu' => 'elfelejtett-jelszo',
			],
			'forgottenPasswordSent' => [
				'cs' => 'zapomenute-heslo-odeslano',
				'si' => 'pozabljeno-geslo-poslano',
				'hr' => 'zaboravljena-lozinka-poslana',
				'rs' => 'zapomenute-heslo-odeslano-todo',
				'bg' => 'izpratena-zabravena-parola',
				'sk' => 'zabudnute-heslo-odoslane',
				'pl' => 'zapomniane-haslo-wyslano',
				'ro' => 'parola-uitata-expediata',
				'hu' => 'elfelejtett-jelszo-elkuldve',
			],
			'newPassword' => [
				'cs' => 'nove-heslo',
				'si' => 'novo-geslo',
				'hr' => 'nova-lozinka',
				'rs' => 'nove-heslo-todo',
				'bg' => 'nova-parola',
				'sk' => 'nove-heslo',
				'pl' => 'nowe-haslo',
				'ro' => 'parola-noua',
				'hu' => 'uj-jelszo',
			],
			'myReviews' => [
				'cs' => 'hodnoceni/moje-hodnoceni',
				'si' => 'ocena/moja-ocena',
				'hr' => 'ocjena/moja-ocena',
				'rs' => 'hodnoceni/moje-hodnoceni-todo',
				'bg' => 'otsenka/moyata-otsenka',
				'sk' => 'hodnotenie/moje-hodnotenie',
				'pl' => 'ocena/moja-ocena',
				'ro' => 'aprecierile-mele',
				'hu' => 'ertekelesek/ertekeleseim',
			],
			'reviewsList' => [
				'cs' => 'hodnoceni',
				'si' => 'ocena',
				'hr' => 'ocjena',
				'rs' => 'hodnoceni-todo',
				'bg' => 'otsenyavane',
				'sk' => 'hodnotenie',
				'pl' => 'ocena',
				'ro' => 'apreciere',
				'hu' => 'ertekeles',
			],
			'addReview' => [
				'cs' => 'hodnoceni/pridat-hodnoceni',
				'si' => 'ocena/dodaj-oceno',
				'hr' => 'ocjena/dodaj-ocjenu',
				'rs' => 'hodnoceni/pridat-hodnoceni-todo',
				'bg' => 'otsenka/dobavete-otsenka',
				'sk' => 'hodnotenie/pridat-hodnotenie',
				'pl' => 'ocena/dodaj-ocene',
				'ro' => 'adauga-apreciere',
				'hu' => 'ertekeles/ertekeles-hozzaadasa',
			],
			'addTipliReview' => [
				'cs' => 'hodnoceni/pridat-hodnoceni-tipli',
				'si' => 'ocena/dodaj-oceno-tipli',
				'hr' => 'ocjena/dodaj-ocjenu-tipli',
				'rs' => 'hodnoceni/pridat-hodnoceni-tipli-todo',
				'bg' => 'otsenka/dobavete-otsenka-tipli',
				'sk' => 'hodnotenie/pridat-hodnotenie-tipli',
				'pl' => 'ocena/dodaj-ocene-tipli',
				'ro' => 'adauga-apreciere-tipli',
				'hu' => 'ertekeles/ertekeles-hozzaadasa-tipli',
			],
			'shareReview' => [
				'cs' => 'hodnoceni/sdilet',
				'si' => 'ocena/deliti',
				'hr' => 'ocjena/dijeliti',
				'rs' => 'hodnoceni/sdilet-todo',
				'bg' => 'tsenyavane/spodelyam',
				'sk' => 'hodnotenie/zdielat',
				'pl' => 'ocena/udostepnij',
				'ro' => 'apreciere/distribuie',
				'hu' => 'ertekeles/megosztas',
			],
			'myApprovals' => [
				'cs' => 'moje-souhlasy',
				'si' => 'moje-odobritve',
				'hr' => 'moja-odobrenja',
				'rs' => 'moje-souhlasy-todo',
				'bg' => 'moite-odobreniya',
				'sk' => 'moje-suhlasy',
				'pl' => 'moje-zgody',
				'ro' => 'aprobarile-mele',
				'hu' => 'beleegyezeseim',
			],
			'changePassword' => [
				'cs' => 'zmena-hesla',
				'si' => 'spreminjanje-gesla',
				'hr' => 'promjena-lozinke',
				'rs' => 'zmena-hesla-todo',
				'bg' => 'smyana-na-parolata',
				'sk' => 'zmena-hesla',
				'pl' => 'zmiana-hasla',
				'ro' => 'schimba-parola',
				'hu' => 'jelszo-megvaltoztatasa',
			],
			'settings' => [
				'cs' => 'nastaveni-uctu',
				'si' => 'nastavitve-racuna',
				'hr' => 'postavke-racuna',
				'rs' => 'nastaveni-uctu-todo',
				'bg' => 'nastroiiki-na-akaunta',
				'sk' => 'nastavenie-uctu',
				'pl' => 'nastawienie-konta',
				'ro' => 'setarile-contului',
				'hu' => 'fiok-beallitasai',
			],
			'emailVerified' => [
				'cs' => 'overeni-emailu',
				'si' => 'preverite-email',
				'hr' => 'cek-email',
				'rs' => 'overeni-emailu-todo',
				'bg' => 'proverka-email',
				'sk' => 'overenie-emailu',
				'pl' => 'zweryfikuj-email',
				'ro' => 'verifica-emailul',
				'hu' => 'email-hitelesitese',
			],
			'transactions' => [
				'cs' => 'odmeny',
				'si' => 'nagrade',
				'hr' => 'nagrade',
				'rs' => 'odmeny-todo',
				'bg' => 'nagradi',
				'sk' => 'odmeny',
				'pl' => 'nagrody',
				'ro' => 'recompense',
				'hu' => 'jutalmak',
			],
			'payouts' => [
				'cs' => 'vyplaty',
				'si' => 'izplacila',
				'hr' => 'isplate',
				'rs' => 'vyplaty-todo',
				'bg' => 'izplashtaniya',
				'sk' => 'vyplaty',
				'pl' => 'wyplata',
				'ro' => 'cheltuieli',
				'hu' => 'fizetesek',
			],
			'payoutsThankYou' => [
				'cs' => 'vyplaty/dekujeme',
				'sk' => 'vyplaty/dakujeme',
				'pl' => 'wyplata/dziekujemy',
				'ro' => 'cheltuieli/multumim',
				'hu' => 'fizetesek/koszonjuk',
				'si' => 'izplacila/hvala',
				'hr' => 'isplate/hvala',
				'rs' => 'vyplaty/dekujeme-todo',
				'bg' => 'izplashtaniya/blagodarya',
			],
			'myFavorites' => [
				'cs' => 'moje-oblibene',
				'si' => 'moji-najljubsi',
				'hr' => 'moji-omiljeni',
				'rs' => 'moje-oblibene-todo',
				'bg' => 'lyubimite-mi',
				'sk' => 'moje-oblubene',
				'pl' => 'moje-ulubione',
				'ro' => 'favoritele-mele',
				'hu' => 'kedvenceim',
			],
			'subscribeCampaign' => [
				'cs' => 'kampan',
				'si' => 'kampanja',
				'hr' => 'kampanja',
				'rs' => 'kampan-todo',
				'bg' => 'kampaniya',
				'sk' => 'kampan',
				'pl' => 'kampan',
				'ro' => 'kampan',
				'hu' => 'kampany',
			],
			'myFavoritesInactive' => [
				'cs' => 'vitejte',
				'si' => 'dobrodosli',
				'hr' => 'dobrodosli',
				'rs' => 'vitejte-todo',
				'bg' => 'dobre-doshli',
				'sk' => 'vitajte',
				'pl' => 'witajcie',
				'ro' => 'bine-ati-venit',
				'hu' => 'udvozoljuk',
			],
			'favouriteShops' => [
				'cs' => 'oblibene-obchody',
				'si' => 'najljubse-trgovine',
				'hr' => 'omiljene-trgovine',
				'rs' => 'oblibene-obchody-todo',
				'bg' => 'lyubimi-magazini',
				'sk' => 'oblubene-obchody',
				'pl' => 'ulubione-sklepy',
				'ro' => 'magazine-favorite',
				'hu' => 'kedvenc-webaruhazak',
			],
			'personalData' => [
				'cs' => 'osobni-udaje',
				'si' => 'osebni-podatki',
				'hr' => 'osobni-podaci',
				'rs' => 'osobni-udaje-todo',
				'bg' => 'lichni-danni',
				'sk' => 'osobne-udaje',
				'pl' => 'dane-osobowe',
				'ro' => 'date-personale',
				'hu' => 'szemelyes-adatok',
			],
			'bankDetails' => [
				'cs' => 'bankovni-udaje',
				'si' => 'bancne-podrobnosti',
				'hr' => 'bankovni-detalji',
				'rs' => 'bankovni-udaje-todo',
				'bg' => 'bankova-informatsiya',
				'sk' => 'bankove-udaje',
				'pl' => 'dane-bankowe',
				'ro' => 'informatii-bancare',
				'hu' => 'bank-informaciok',
			],
			'information' => [
				'cs' => 'informace',
				'si' => 'informacije',
				'hr' => 'informacija',
				'rs' => 'informace-todo',
				'bg' => 'informatsiya',
				'sk' => 'informacie',
				'pl' => 'informacje',
				'ro' => 'informatie',
				'hu' => 'informaciok',
			],
			'unsubscribeEmail' => [
				'cs' => 'odhlasit-email',
				'si' => 'odjaviti-email',
				'hr' => 'odjavite-email',
				'rs' => 'odhlasit-email-todo',
				'bg' => 'izlez-email',
				'sk' => 'odhlasit-email',
				'pl' => 'wylogowac-email',
				'ro' => 'dezabonare-email',
				'hu' => 'email-kijelentese',
			],
			'contact' => [
				'cs' => 'kontakt',
				'si' => 'kontaktirajte',
				'hr' => 'kontakt',
				'rs' => 'kontakt-todo',
				'bg' => 'kontakt',
				'sk' => 'kontakt',
				'pl' => 'kontakt',
				'ro' => 'contact',
				'hu' => 'kapcsolat',
			],
			'accountDeletionRequest' => [
				'cs' => 'zruseni-uctu',
				'si' => 'preklic-racuna',
				'hr' => 'otkazivanje-racuna',
				'rs' => 'zruseni-uctu-todo',
				'bg' => 'anulirane-na-akaunt',
				'sk' => 'zrusenie-uctu',
				'pl' => 'usuniecie-konta',
				'ro' => 'anularea-contului',
				'hu' => 'fiok-torlese',
			],
			'welcome' => [
				'cs' => 'vitejte',
				'si' => 'dobrodosli',
				'hr' => 'dobrodosli',
				'rs' => 'vitejte-todo',
				'bg' => 'dobre-doshli',
				'sk' => 'vitajte',
				'pl' => 'witajcie',
				'ro' => 'bine-ati-venit',
				'hu' => 'udvozoljuk',
			],
			'conditions' => [
				'cs' => 'podminky',
				'si' => 'pogoji',
				'hr' => 'uvjeti',
				'rs' => 'podminky-todo',
				'bg' => 'usloviya',
				'sk' => 'podmienky',
				'pl' => 'warunki',
				'ro' => 'conditii',
				'hu' => 'feltetelek',
			],
			'zasilkovnaConditions' => [
				'cs' => 'souhlas-zasilkovna',
				'si' => 'souhlas-zasilkovna',
				'hr' => 'souhlas-zasilkovna',
				'rs' => 'souhlas-zasilkovna',
				'bg' => 'souhlas-zasilkovna',
				'sk' => 'suhlas-zasielkovna',
				'pl' => 'souhlas-zasilkovna',
				'ro' => 'acceptare-coletarie',
				'hu' => 'beleegyezes-csomagkuldo',
			],
			'guarantee' => [
				'cs' => 'garance-vasi-spokojenosti',
				'si' => 'garancija-za-vase-zadovoljstvo',
				'hr' => 'jamstvo-vaseg-zadovoljstva',
				'rs' => 'garance-vasi-spokojenosti-todo',
				'bg' => 'garantsiya-za-vasheto-udovletvorenie',
				'sk' => 'garancia-vasej-spokojnosti',
				'pl' => 'gwarancja-waszego-zadowolenia',
				'ro' => 'garantia-multumirii-dvs',
				'hu' => 'az-on-elegedettsegi-garanciaja',
			],
			'lastRedirections' => [
				'cs' => 'presmerovani',
				'si' => 'preusmeritve',
				'hr' => 'preusmjerenja',
				'rs' => 'preusmerenja',
				'bg' => 'prenasochvania',
				'sk' => 'presmerovania',
				'pl' => 'przekierowania',
				'ro' => 'redirectionari',
				'hu' => 'atiranyitasok',
			],
			'userGuarantee' => [
				'cs' => 'garance-spokojenosti',
				'si' => 'garancija-na-zadovoljstvo',
				'hr' => 'garancija-zadovoljstva',
				'rs' => 'garance-spokojenosti-todo',
				'bg' => 'garantsiya-za-udovletvorenie',
				'sk' => 'garancia-spokojnosti',
				'pl' => 'satysfakcja-gwarantowana',
				'ro' => 'multumire-garantata',
				'hu' => 'elegedettsegi-garancia',
			],
			'guaranteeHighestRewards' => [
				'cs' => 'garance-nejvyssich-odmen',
				'si' => 'zagotovljene-najvisje-nagrade',
				'hr' => 'zajamcena-najveca-nagrada',
				'rs' => 'garance-nejvyssich-odmen-todo',
				'bg' => 'garantsiya-za-naii-visoki-nagradi',
				'sk' => 'garancia-najvyssich-odmien',
				'pl' => 'gwarancja-najwyzszych-wyplat',
				'ro' => 'garantia-celor-mai-mari-recompense',
				'hu' => 'legmagasabb-jutalmak-garanciaja',
			],
			'forMedia' => [
				'cs' => 'pro-media',
				'si' => 'za-medije',
				'hr' => 'za-medije',
				'rs' => 'pro-media-todo',
				'bg' => 'za-mediite',
				'sk' => 'pre-media',
				'pl' => 'dla-mediow',
				'ro' => 'pentru-media',
				'hu' => 'medianak',
			],
			'payout' => [
				'cs' => 'vyplata',
				'si' => 'placa',
				'hr' => 'placa',
				'rs' => 'vyplata-todo',
				'bg' => 'zaplata',
				'sk' => 'vyplata',
				'pl' => 'wyplata',
				'ro' => 'cheltuiala',
				'hu' => 'fizetes',
			],
			'userTellFriend' => [
				'cs' => 'doporucit-pratelum',
				'si' => 'priporoci-prijateljem',
				'hr' => 'preporuciti-prijateljima',
				'rs' => 'doporucit-pratelum-todo',
				'bg' => 'preporuchaiite-na-priyateli',
				'sk' => 'odporucit-priatelom',
				'pl' => 'polec-znajomym',
				'ro' => 'recomanda-prietenilor',
				'hu' => 'ajanlas-baratoknak',
			],
			'phoneNumberVerification' => [
				'cs' => 'overeni-telefonniho-cisla',
				'si' => 'preverjanje-telefonske-stevilke',
				'hr' => 'potvrda-telefonskog-broja',
				'rs' => 'overeni-telefonniho-cisla-todo',
				'bg' => 'proverka-na-telefonen-nomer',
				'sk' => 'overeni-telefonniho-cisla',
				'pl' => 'overeni-telefonniho-cisla',
				'ro' => 'verificarea-numarului-de-telefon',
				'hu' => 'telefonszam-hitelesitese',
			],
			'accountNumberChangeVerification' => [
				'cs' => 'nastaveni-uctu/potvrdte-zmenu-bankovniho-uctu',
				'si' => 'nastavitve-racuna/potrdite-spremembo-bancnega-racuna',
				'hr' => 'postavke-racuna/potvrdite-promjenu-bankovnog-racuna',
				'rs' => 'nastaveni-uctu/potvrdte-zmenu-bankovniho-uctu-todo',
				'bg' => 'nastroikite-na-akaunta/potvurdete-promyanata-na-bankovata-smetka',
				'sk' => 'nastavenie-uctu/potvrdte-zmenu-bankoveho-uctu',
				'pl' => 'nastawienie-konta/potwierdz-zmiane-rachunku-bankowego',
				'ro' => 'setarile-contului/confirmati-schimbarea-contului-bancar',
				'hu' => 'fiok-beallitasai/bankszamlaszam-valtoztatasanak-megerositese',
			],
			'accountChangeVerification' => [
				'cs' => 'nastaveni-uctu/potvrzeni-zmeny',
				'si' => 'nastavitve-racuna/potrditev-spremembe',
				'hr' => 'postavke-racuna/potvrda-promjene',
				'rs' => 'nastaveni-uctu/potvrzeni-zmeny-todo',
				'bg' => 'nastroiki-na-akaunta/potvurzhdenie-na-promyanata',
				'sk' => 'nastavenie-uctu/potvrzenie-zmeny',
				'pl' => 'nastawienie-konta/potwierdzenie-zmiany',
				'ro' => 'setarile-contului/confirmarea-modificarii',
				'hu' => 'fiok-beallitasai/jutalom-megerositese',
			],
			'sessionVerification' => [
				'cs' => 'overeni-prihlaseni',
				'si' => 'preverjanje-prijave',
				'hr' => 'potvrda-prijave',
				'rs' => 'overeni-prihlaseni-todo',
				'bg' => 'proverka-na-vlizane',
				'sk' => 'overenie-prihlasenia',
				'pl' => 'zweryfikuj-logowanie',
				'ro' => 'verifica-logarea',
				'hu' => 'bejelentkezes-hitelesitese',
			],
			'addon' => [
				'cs' => 'tipli-do-prohlizece',
				'si' => 'tipli-v-brskalnik',
				'hr' => 'tipli-pregledniku',
				'rs' => 'tipli-do-prohlizece-todo',
				'bg' => 'tipli-kum-brauzura',
				'sk' => 'tipli-do-prehliadaca',
				'pl' => 'tipli-do-przegladarki',
				'ro' => 'tipli-in-cautare',
				'hu' => 'tipli-a-bongeszobe',
			],
			'addonInstalled' => [
				'cs' => 'instalace-doplnku',
				'si' => 'namestitev-dodatka',
				'hr' => 'instaliranje-dodatka',
				'rs' => 'instalace-doplnku-todo',
				'bg' => 'instalirane-na-dobavkata',
				'sk' => 'instalacia-doplnku',
				'pl' => 'pobierz-wtyczke',
				'ro' => 'instalarea-suplimentelor',
				'hu' => 'kiegeszito-telepitese',
			],
			'howItWorks' => [
				'cs' => 'jak-to-funguje',
				'si' => 'kako-deluje',
				'hr' => 'kako-radi',
				'rs' => 'jak-to-funguje-todo',
				'bg' => 'kak-raboti',
				'sk' => 'ako-to-funguje',
				'pl' => 'jak-to-dziala',
				'ro' => 'cum-functioneaza',
				'hu' => 'hogyan-mukodik',
			],
			'howWorksTransactions' => [
				'cs' => 'jak-funguji-odmeny',
				'si' => 'kako-delujejo-nagrade',
				'hr' => 'kako-funkcioniraju-nagrade',
				'rs' => 'jak-funguji-odmeny-todo',
				'bg' => 'kak-rabotyat-nagradite',
				'sk' => 'ako-funguju-odmeny',
				'pl' => 'jak-dzialaja-nagrody',
				'ro' => 'cum-functioneaza-recompensele',
				'hu' => 'hogyan-mukodnek-a-jutalmak',
			],
			'whatIsCashback' => [
				'cs' => 'co-je-cashback',
				'si' => 'kaj-je-cashback',
				'hr' => 'sto-je-cashback',
				'rs' => 'co-je-cashback-todo',
				'bg' => 'kakvo-e-cashback',
				'sk' => 'co-je-cashback',
				'pl' => 'co-to-jest-cashback',
				'ro' => 'ce-este-cashback',
				'hu' => 'mi-a-cashback',
			],
			'cashbackRules' => [
				'cs' => 'pravidla-cashbacku',
				'si' => 'pravila-cashback',
				'hr' => 'pravila-cashback',
				'rs' => 'pravidla-cashbacku-todo',
				'bg' => 'pravila-cashback',
				'sk' => 'pravidla-cashbacku',
				'pl' => 'zasady-cashbacku',
				'ro' => 'regulile-cashbackului',
				'hu' => 'cashback-szabalyai',
			],
			'complaints' => [
				'cs' => 'reklamace-ztraty-odmen',
				'si' => 'zahtevki-za-izgubo-nagrade',
				'hr' => 'tuzbe-za-gubitak-nagrada',
				'rs' => 'reklamace-ztraty-odmen-todo',
				'bg' => 'iskove-za-zaguba-na-nagradi',
				'sk' => 'reklamacia-straty-odmien',
				'pl' => 'reklamacja-nienaliczenie-rabatu',
				'ro' => 'reclamarea-pierderii-recompensei',
				'hu' => 'reklamacio-jutalom-elvesztese',
			],
			'workWithUs' => [
				'cs' => 'spolupracujte-s-nami',
				'si' => 'sodelujte-z-nami',
				'hr' => 'suradivati ​-s-nama',
				'rs' => 'spolupracujte-s-nami-todo',
				'bg' => 'sutrudnichat-s-nas',
				'sk' => 'spolupracujte-s-nami',
				'pl' => 'wspolpracujcie-z-nami',
				'ro' => 'lucrati-impreuna-cu-noi',
				'hu' => 'mukodjon-velunk-egyutt',
			],
			'bloggers' => [
				'cs' => 'blogeri',
				'si' => 'blogerji',
				'hr' => 'blogeri',
				'rs' => 'blogeri-todo',
				'bg' => 'bloguri',
				'sk' => 'blogeri-sk',
				'pl' => 'blogerzy',
				'ro' => 'blogeri',
				'hu' => 'bloggerek',
			],
			'search' => [
				'cs' => 'hledat',
				'si' => 'iskanje',
				'hr' => 'trazi',
				'rs' => 'hledat-todo',
				'bg' => 'tursene',
				'sk' => 'hladat',
				'pl' => 'szukac',
				'ro' => 'cauta',
				'hu' => 'kereses',
			],
			'shops' => [
				'cs' => 'obchody',
				'si' => 'trgovine',
				'hr' => 'trgovine',
				'rs' => 'obchody-todo',
				'bg' => 'magazini',
				'sk' => 'obchody',
				'pl' => 'sklepy',
				'ro' => 'magazine',
				'hu' => 'webaruhazak',
			],
			'newest' => [
				'cs' => 'nejnovejsi',
				'si' => 'najnovejsi',
				'hr' => 'najnoviji',
				'rs' => 'nejnovejsi-todo',
				'bg' => 'posleden',
				'sk' => 'najnovsie',
				'pl' => 'najnowsze',
				'ro' => 'cele-mai-recente',
				'hu' => 'legujabb',
			],
			'nonCashback' => [
				'cs' => 'slevove-kupony',
				'si' => 'popusti-kuponi',
				'hr' => 'popust-kuponi',
				'rs' => 'slevovi-kuponi',
				'bg' => 'kuponi-za-otstapka',
				'sk' => 'zlavove-kupony',
				'pl' => 'kupony-rabatowe',
				'ro' => 'cupon-de-reducere',
				'hu' => 'kedvezmeny-kuponok',
			],
			'shop' => [
				'cs' => 'obchod',
				'si' => 'trgovina',
				'hr' => 'trgovina',
				'rs' => 'obchod-todo',
				'bg' => 'turgoviyna',
				'sk' => 'obchod',
				'pl' => 'sklep',
				'ro' => 'magazin',
				'hu' => 'webaruhaz',
			],
			'newShop' => [
				'cs' => 'obchod-new',
				'si' => 'trgovina-new',
				'hr' => 'trgovina-new',
				'rs' => 'obchod-new-todo',
				'bg' => 'turgoviyna-new',
				'sk' => 'obchod-new',
				'pl' => 'sklep-new',
				'ro' => 'magazin-new',
				'hu' => 'webaruhaz-hu-new',
			],
			'sales' => [
				'cs' => 'sales',
				'si' => 'sales',
				'hr' => 'sales',
				'rs' => 'sales',
				'bg' => 'sales',
				'sk' => 'sales',
				'pl' => 'sales',
				'ro' => 'sales',
				'hu' => 'sales',
			],
			'deals' => [
				'cs' => 'slevy',
				'si' => 'prodaja',
				'hr' => 'prodajni',
				'rs' => 'slevy-todo',
				'bg' => 'prodazhbi',
				'sk' => 'zlavy',
				'pl' => 'znizki',
				'ro' => 'reduceri',
				'hu' => 'kedvezmenyek',
			],
			'products' => [
				'cs' => 'produkty',
				'si' => 'izdelkov',
				'hr' => 'proizvoda',
				'rs' => 'produkty-todo',
				'bg' => 'produkti',
				'sk' => 'produkty',
				'pl' => 'produkty',
				'ro' => 'produse',
				'hu' => 'termekek',
			],
			'rewardCampaigns' => [
				'cs' => 'specialni-odmeny',
				'si' => 'posebne-nagrade',
				'hr' => 'posebne-nagrade',
				'rs' => 'specialni-odmeny-todo',
				'bg' => 'spetsialni-nagradi',
				'sk' => 'specialni-odmeny',
				'pl' => 'specialni-odmeny',
				'ro' => 'recompense-speciale',
				'hu' => 'specialis-jutalmak',
			],
			'sale' => [
				'cs' => 'sleva',
				'si' => 'popust',
				'hr' => 'popust',
				'rs' => 'sleva-todo',
				'bg' => 'ot-stupka',
				'sk' => 'zlava',
				'pl' => 'znizka',
				'ro' => 'reducere',
				'hu' => 'kedvezmeny',
			],
			'redirectShop' => [
				'cs' => 'prejit/obchod',
				'si' => 'prelaz/trgovina',
				'hr' => 'prolaz/trgovina',
				'rs' => 'prejit/obchod-todo',
				'bg' => 'pas/turgoviyna',
				'sk' => 'prejst/obchod',
				'pl' => 'przejsc/sklep',
				'ro' => 'redirectioneaza/magazin',
				'hu' => 'atteres/webaruhaz',
			],
			'redirectDealOld' => [
				'cs' => 'prejit/deal',
				'si' => 'prelaz/deal',
				'hr' => 'prolaz/deal',
				'rs' => 'prejit/deal-todo',
				'bg' => 'pas/deal',
				'sk' => 'prejst/deal',
				'pl' => 'przejsc/deal',
				'ro' => 'redirectioneaza/deal',
				'hu' => 'atteres/deal',
			],
			'redirectProduct' => [
				'cs' => 'prejit/produkt',
				'si' => 'prelaz/izdelek',
				'hr' => 'prolaz/proizvod',
				'rs' => 'prejit/produkt-todo',
				'bg' => 'pas/produkt',
				'sk' => 'prejst/produkt',
				'pl' => 'przejsc/produkt',
				'ro' => 'redirectioneaza/produkt',
				'hu' => 'atteres/produkt',
			],
			'redirectShopDirect' => [
				'cs' => 'prejit/obchod/hned',
				'si' => 'prelaz/trgovina/rjav',
				'hr' => 'prolaz/trgovina/sada',
				'rs' => 'prejit/obchod/hned-todo',
				'bg' => 'pas/turgoviyna/sega',
				'sk' => 'prejst/obchod/hned',
				'pl' => 'przejsc/sklep/teraz',
				'ro' => 'redirectioneaza/magazin/imediat',
				'hu' => 'atteres/webaruhaz/most',
			],
			'redirectShopPartner' => [
				'cs' => 'prejit/obchod/partner',
				'si' => 'prelaz/trgovina/partner',
				'hr' => 'prolaz/trgovina/partner',
				'rs' => 'prejit/obchod/partner-todo',
				'bg' => 'pas/turgoviyna/partner',
				'sk' => 'prejst/obchod/partner',
				'pl' => 'przejsc/sklep/partner',
				'ro' => 'redirectioneaza/magazin/partner',
				'hu' => 'atteres/webaruhaz/partner',
			],
			'redirectShopSpinner' => [
				'cs' => 'prejit/obchod/partner',
				'si' => 'prelaz/trgovina/partner',
				'hr' => 'prolaz/trgovina/partner',
				'rs' => 'prejit/obchod/partner-todo',
				'bg' => 'pas/turgoviyna/partner',
				'sk' => 'prejst/obchod/partner',
				'pl' => 'przejsc/sklep/partner',
				'ro' => 'redirectioneaza/magazin/partner',
				'hu' => 'atteres/webaruhaz/partner',
			],
			'redirectOffer' => [
				'cs' => 'prejit/nabidka',
				'si' => 'prelaz/ponudba',
				'hr' => 'prolaz/ponuditi',
				'rs' => 'prejit/nabidka-todo',
				'bg' => 'pas/oferta',
				'sk' => 'prejst/ponuka',
				'pl' => 'przejsc/oferta',
				'ro' => 'redirectioneaza/oferta',
				'hu' => 'atteres/kinalat',
			],
			'redirectDeal' => [
				'cs' => 'prejit/sleva',
				'si' => 'prelaz/popust',
				'hr' => 'prolaz/popust',
				'rs' => 'prejit/sleva-todo',
				'bg' => 'pas/ot-stupka',
				'sk' => 'prejst/zlava',
				'pl' => 'przejsc/znizka',
				'ro' => 'redirectioneaza/reducere',
				'hu' => 'atteres/kedvezmeny',
			],
			'redirectPopup' => [
				'cs' => 'prejit/popup',
				'si' => 'prelaz/popup',
				'hr' => 'prolaz/popup',
				'rs' => 'prejit/popup-todo',
				'bg' => 'pas/popup',
				'sk' => 'prejst/popup',
				'pl' => 'przejsc/popup',
				'ro' => 'redirectioneaza/popup',
				'hu' => 'atteres/popup',
			],
			'banner' => [
				'cs' => 'prejit/banner',
				'si' => 'prelaz/banner',
				'hr' => 'prolaz/banner',
				'rs' => 'prejit/banner',
				'bg' => 'pas/banner',
				'sk' => 'prejst/banner',
				'pl' => 'przejsc/banner',
				'ro' => 'redirectioneaza/banner',
				'hu' => 'atteres/banner',
			],
			'notification' => [
				'cs' => 'zpravy',
				'si' => 'novice',
				'hr' => 'vijesti',
				'rs' => 'zpravy-todo',
				'bg' => 'novini',
				'sk' => 'upozornenie',
				'pl' => 'powiadomienia',
				'ro' => 'notificare',
				'hu' => 'figyelmeztetes',
			],
			'articles' => [
				'cs' => 'clanky',
				'si' => 'clanki',
				'hr' => 'clanci',
				'rs' => 'clanky-todo',
				'bg' => 'statii',
				'sk' => 'clanky',
				'pl' => 'artykuly',
				'ro' => 'articole',
				'hu' => 'cikkek',
			],
			'article' => [
				'cs' => 'clanek',
				'si' => 'clanek',
				'hr' => 'clanak',
				'rs' => 'clanek-todo',
				'bg' => 'statiya',
				'sk' => 'clanok',
				'pl' => 'artykul',
				'ro' => 'articol',
				'hu' => 'cikk',
			],
			'socialShareFacebook' => [
				'cs' => 'sdilet-na-facebooku',
				'si' => 'deli-na-facebooku',
				'hr' => 'podijeli-na-facebooku',
				'rs' => 'sdilet-na-facebooku-todo',
				'bg' => 'spodelyam-vuv-facebook',
				'sk' => 'zdielat-na-facebooku',
				'pl' => 'udostepnic-na-facebooku',
				'ro' => 'distribuie-pe-facebook',
				'hu' => 'megosztas-facebookon',
			],
			'socialShareTwitter' => [
				'cs' => 'sdilet-na-twitteru',
				'si' => 'deli-na-twitterju',
				'hr' => 'podijeli-na-twitteru',
				'rs' => 'sdilet-na-twitteru-todo',
				'bg' => 'spodelyane-v-twitter',
				'sk' => 'zdielat-na-twitteri',
				'pl' => 'udostepnic-na-twitteru',
				'ro' => 'distribuie-pe-twitter',
				'hu' => 'megosztas-twitteren',
			],
			'pestryJidelnicek' => [
				'cs' => 'pestry-jidelnicek',
				'si' => 'pestry-jidelnicek',
				'hr' => 'pestry-jidelnicek',
				'rs' => 'pestry-jidelnicek',
				'bg' => 'pestry-jidelnicek-todo',
				'sk' => 'pestry-jedalnicek',
				'pl' => 'pestry-jidelnicek-pl',
				'ro' => 'pestry-jidelnicek',
				'hu' => 'valtozatos-taplalkozas',
			],
			'pestryJidelnicekLandingPage' => [
				'cs' => 'lp/pestry-jidelnicek',
				'si' => 'lp/pestry-jidelnicek',
				'hr' => 'lp/pestry-jidelnicek',
				'rs' => 'lp/pestry-jidelnicek',
				'bg' => 'lp/pestry-jidelnicek',
				'sk' => 'lp/pestry-jedalnicek',
				'pl' => 'lp/pestry-jidelnicek-pl',
				'ro' => 'lp/pestry-jidelnicek-pl',
				'hu' => 'lp/valtozatos-taplalkozas',
			],
			'external-exit-popup' => [
				'cs' => 'external-exit-popup',
				'si' => 'external-exit-popup',
				'hr' => 'external-exit-popup',
				'rs' => 'external-exit-popup',
				'bg' => 'external-exit-popup',
				'sk' => 'external-exit-popup',
				'pl' => 'external-exit-popup',
				'ro' => 'external-exit-popup',
				'hu' => 'external-exit-popup',
			],
			'external-exit-popup-facebook' => [
				'cs' => 'external-exit-popup-facebook',
				'si' => 'external-exit-popup-facebook',
				'hr' => 'external-exit-popup-facebook',
				'rs' => 'external-exit-popup-facebook',
				'bg' => 'external-exit-popup-facebook',
				'sk' => 'external-exit-popup-facebook',
				'pl' => 'external-exit-popup-facebook',
				'ro' => 'external-exit-popup-facebook',
				'hu' => 'external-exit-popup-facebook',
			],
			'leaflet' => [
				'cs' => 'letak',
				'si' => 'letak',
				'hr' => 'letak',
				'rs' => 'letak-todo',
				'bg' => 'broshura',
				'sk' => 'letak',
				'pl' => 'gazetka',
				'ro' => 'catalog',
				'hu' => 'akcios-ujsag',
			],
			'leaflets' => [
				'cs' => 'letaky',
				'si' => 'letaki',
				'hr' => 'letci',
				'rs' => 'letaky-todo',
				'bg' => 'listovki',
				'sk' => 'letaky',
				'pl' => 'gazetki',
				'ro' => 'cataloage',
				'hu' => 'akcios-ujsagok',
			],
			'leaflets-shops' => [
				'cs' => 'letaky/obchody',
				'si' => 'letaki/trgovine',
				'hr' => 'letci/trgovine',
				'rs' => 'letaky/obchody-todo',
				'bg' => 'listovki/magazini',
				'sk' => 'letaky/obchody',
				'pl' => 'gazetki/sklepy',
				'ro' => 'cataloage/magazine',
				'hu' => 'akcios-ujsagok/uzletek',
			],
			'leaflets-all-valid' => [
				'cs' => 'letaky/aktualni',
				'si' => 'letaki/aktual',
				'hr' => 'letci/aktualni',
				'rs' => 'letaky/aktualni-todo',
				'bg' => 'listovki/aktualni',
				'sk' => 'letaky/aktualne',
				'pl' => 'gazetki/aktualni',
				'ro' => 'cataloage/actuale',
				'hu' => 'akcios-ujsagok/aktualis',
			],
			'leaflets-all-scheduled' => [
				'cs' => 'letaky/pripravovane',
				'si' => 'letaki/v-pripravi',
				'hr' => 'letci/u-pripremi',
				'rs' => 'letaky/pripravovane-todo',
				'bg' => 'listovki/predstoyashti',
				'sk' => 'letaky/buduce',
				'pl' => 'gazetki/nadchodzace',
				'ro' => 'cataloage/pregatite',
				'hu' => 'akcios-ujsagok/jovobeli',
			],
			'privacy' => [
				'cs' => 'souhlas-se-zpracovanim-osobnich-udaju',
				'si' => 'soglasje-za-obdelavo-osebnih-podatkov',
				'hr' => 'suglasnost-za-obradu-osobnih-podataka',
				'rs' => 'souhlas-se-zpracovanim-osobnich-udaju-todo',
				'bg' => 'suglasie-za-obrabotka-na-lichni-danni',
				'sk' => 'suhlas-so-spracuvanim-osobnych-udajov',
				'pl' => 'zgoda-na-przetwarzanie-danych-osobowych',
				'ro' => 'acceptare-prelucrarea-datelor-cu-caracter-personal',
				'hu' => 'egyetertes-a-szemelyes-adatok-felhasznalasaval',
			],
			'learnLaw' => [
				'cs' => 'pouceni-o-pravech-subjektu-udaju',
				'si' => 'pouk-o-pravicah-posameznikov-na-katere-se-nanasajo-osebni podatki',
				'hr' => 'pouka-o-pravima-nositelja-podataka',
				'rs' => 'pouceni-o-pravech-subjektu-udaju-todo',
				'bg' => 'instruktsiya-za-pravata-na-subektite-na-danni',
				'sk' => 'poucenie-o-pravach-dotknutej-osoby',
				'pl' => 'pouczenie-o-prawach-osob-ktore-udostepniaja-dane-osobowe',
				'ro' => 'nota-informativa-despre-drepturile-persoanei-vizate',
				'hu' => 'felvilagositas-az-erintett-szemely-jogairol',
			],
			'privacyInfo' => [
				'cs' => 'informace-o-zpracovani-osobnich-udaju-a-pravech-subjektu-udaju',
				'si' => 'informacije-o-obdelavi-osebnih-podatkov-in-pravicah-posameznikov-na-katere-se-nanasajo-osebni-podatki',
				'hr' => 'informacije-o-obradi-osobnih-podataka-i-pravima-nositelja-podataka',
				'rs' => 'informace-o-zpracovani-osobnich-udaju-a-pravech-subjektu-udaju-todo',
				'bg' => 'informatsiya-otnosno-obrabotkata-na-lichni-danni-i-pravata-na-subektite-na-danni',
				'sk' => 'informacie-o-spracuvani-osobnych-udajov-a-pravach-subjektu',
				'pl' => 'informacje-o-przetwarzaniu-danych-osobowych-i-prawach-osob-ktore-udostepniaja-dane',
				'ro' => 'informatii-privind-prelucrarea-datelor-cu-caracter-personal',
				'hu' => 'informaciok-az-alany-jogairol-és-a-szemelyes-adatok-feldolgozasarol',
			],
			'faq' => [
				'cs' => 'casto-kladene-otazky',
				'si' => 'pogosta-vprasanja',
				'hr' => 'cesto-postavljana-pitanja',
				'rs' => 'casto-kladene-otazky-todo',
				'bg' => 'chesto-zadavani-vuprosi',
				'sk' => 'najcastejsie-otazky',
				'pl' => 'najczesciej-zadawane-pytania',
				'ro' => 'cele-mai-frecvente-intrebari',
				'hu' => 'gyakran-feltett-kerdesek',
			],
			'topOffers' => [
				'cs' => 'akce',
				'si' => 'ukrepanje',
				'hr' => 'akcijski',
				'rs' => 'akce-todo',
				'bg' => 'deistvie',
				'sk' => 'akcie',
				'pl' => 'promocje',
				'ro' => 'promotii',
				'hu' => 'akciok',
			],
			'landingPage' => [
				'cs' => 'akce',
				'si' => 'ukrepanje',
				'hr' => 'akcijski',
				'rs' => 'akce-todo',
				'bg' => 'deistvie',
				'sk' => 'akcie',
				'pl' => 'promocje',
				'ro' => 'promotii',
				'hu' => 'akciok',
			],
			'company' => [
				'cs' => 'firma',
				'si' => 'podjetje',
				'hr' => 'firma',
				'rs' => 'firma-todo',
				'bg' => 'tvurd',
				'sk' => 'firma',
				'pl' => 'firma',
				'ro' => 'firma',
				'hu' => 'ceg',
			],
			'form' => [
				'cs' => 'formular',
				'si' => 'oblika',
				'hr' => 'oblik',
				'rs' => 'formular-todo',
				'bg' => 'forma',
				'sk' => 'formular',
				'pl' => 'formularz',
				'ro' => 'formular',
				'hu' => 'urlap',
			],
			'dziekujemy' => [
				'cs' => 'dziekujemy',
				'si' => 'dziekujemy',
				'hr' => 'dziekujemy',
				'rs' => 'dziekujemy',
				'bg' => 'dziekujemy',
				'sk' => 'dziekujemy',
				'pl' => 'dziekujemy',
				'ro' => 'va-multumim',
				'hu' => 'koszonjuk',
			],
			'feedback' => [
				'cs' => 'podpora/zpetna-vazba',
				'si' => 'podpora/povratne-informacije',
				'hr' => 'podrska/povratna-informacija',
				'rs' => 'podpora/zpetna-vazba-todo',
				'bg' => 'poddruzhka/obratna-vruzka',
				'sk' => 'podpora/spatna-vazba',
				'pl' => 'obsluga-klienta/ocena',
				'ro' => 'suport-client/feedback',
				'hu' => 'ugyfelszolgalat/feedback',
			],
			'feedbackThankYou' => [
				'cs' => 'podpora/zpetna-vazba/dekujeme',
				'si' => 'podpora/povratne-informacije/hvala',
				'hr' => 'podrska/povratna-informacije/hvala',
				'rs' => 'podpora/zpetna-vazba/dekujeme-todo',
				'bg' => 'poddruzhka/obratna-vruzka/blagodarya',
				'sk' => 'podpora/spatna-vazba/dakujeme',
				'pl' => 'obsluga-klienta/ocena/dziekujemy',
				'ro' => 'suport-client/feedback/va-multumim',
				'hu' => 'ugyfelszolgalat/feedback/koszonjuk',
			],
			'jobs' => [
				'cs' => 'kariera',
				'si' => 'kariera',
				'hr' => 'karijera',
				'rs' => 'kariera-todo',
				'bg' => 'kariera',
				'sk' => 'kariera',
				'pl' => 'kariera',
				'ro' => 'cariera',
				'hu' => 'karrier',
			],
			'phoneApp' => [
				'cs' => 'mobilni-aplikace',
				'si' => 'mobilna-aplikacija',
				'hr' => 'mobilna-aplikacija',
				'rs' => 'mobilni-aplikace-todo',
				'bg' => 'mobilno-prilozhenie',
				'sk' => 'mobilna-aplikacia',
				'pl' => 'aplikacja-mobilna',
				'ro' => 'aplicatii-de-mobil',
				'hu' => 'mobilalkalmazas',
			],
			'zasilkovnaDefault' => [
				'cs' => 'zasilkovna/vip-odmena',
				'si' => 'zasilkovna/vip-odmena',
				'hr' => 'zasilkovna/vip-odmena',
				'rs' => 'zasilkovna/vip-odmena',
				'bg' => 'zasilkovna/vip-odmena',
				'sk' => 'zasielkovna/vip-odmena',
				'pl' => 'zasilkovna/vip-odmena',
				'ro' => 'zasilkovna/vip-odmena',
				'hu' => 'csomagkuldo/vip-jutalom',
			],
			'zasilkovnaExtension' => [
				'cs' => 'zasilkovna/prodlouzeni-vip',
				'si' => 'zasilkovna/prodlouzeni-vip',
				'hr' => 'zasilkovna/prodlouzeni-vip',
				'rs' => 'zasilkovna/prodlouzeni-vip',
				'bg' => 'zasilkovna/prodlouzeni-vip',
				'sk' => 'zasielkovna/predlzenie-vip',
				'pl' => 'zasilkovna/vip-odmena',
				'ro' => 'zasilkovna/vip-odmena',
				'hu' => 'csomagkuldo/vip-jutalom',
			],
			'zasilkovnaThankYou' => [
				'cs' => 'zasilkovna/dekujeme',
				'si' => 'zasilkovna/dekujeme',
				'hr' => 'zasilkovna/dekujeme',
				'rs' => 'zasilkovna/dekujeme',
				'bg' => 'zasilkovna/dekujeme',
				'sk' => 'zasielkovna/dakujeme',
				'pl' => 'zasilkovna/vip-odmena',
				'ro' => 'zasilkovna/vip-odmena',
				'hu' => 'csomagkuldo/vip-jutalom',
			],
			'refunds' => [
				'cs' => 'reklamace',
				'si' => 'pritozba',
				'hr' => 'prituzba',
				'rs' => 'reklamace-todo',
				'bg' => 'oplakvane',
				'sk' => 'reklamacia',
				'pl' => 'reklamacja',
				'ro' => 'reclamatie',
				'hu' => 'reklamacio',
			],
			'refundsThankYou' => [
				'cs' => 'reklamace/dekujeme',
				'sk' => 'reklamacie/dakujeme',
				'pl' => 'reklamacja/dziekujemy',
				'ro' => 'reclamatie/multumim',
				'hu' => 'reklamacio/koszonjuk',
				'si' => 'pritozba/hvala',
				'hr' => 'prituzba/hvala',
				'rs' => 'reklamace/dekujeme-todo',
				'bg' => 'oplakvane/blagodarya',
			],
			'refundsList' => [
				'cs' => 'moje-reklamace',
				'sk' => 'moje-reklamacie',
				'pl' => 'moje-reklamacje',
				'ro' => 'reclamațiile-mele',
				'hu' => 'reklamaciom-panaszkezeles',
				'si' => 'moja-pritozba',
				'hr' => 'moja-prituzba',
				'rs' => 'moje-reklamace-todo',
				'bg' => 'moeto-oplakvane',
			],
			'redirectionFeedbackThankYou' => [
				'cs' => 'feedback/dekujeme',
				'sk' => 'feedback/dakujeme',
				'pl' => 'feedback/dziekujemy',
				'ro' => 'feedback/multumim',
				'hu' => 'feedback/koszonjuk',
				'si' => 'feedback/hvala',
				'hr' => 'feedback/hvala',
				'rs' => 'feedback/dekujeme-todo',
				'bg' => 'feedback/blagodarya',
			],
			'emailVerificationBonus' => [
				'cs' => '1kc-za-overeni',
				'sk' => '1kc-za-overenie',
				'pl' => '1kc-za-overeni-todo',
				'ro' => '1kc-za-overeni-todo',
				'hu' => '1kc-za-overeni-todo',
				'si' => '1kc-za-overeni-todo',
				'hr' => '1kc-za-overeni-todo',
				'rs' => '1kc-za-overeni-todo',
				'bg' => '1kc-za-overeni-todo',
			],
		];

		$output = [];
		foreach ($dictionary as $key => $translations) {
			if (isset($translations[$localization->getLocale()])) {
				$output[$key] = $translations[$localization->getLocale()];
			} else {
				throw new InvalidArgumentException('Translation for key ' . $key . ' and locale ' . $localization->getLocale() . ' not found in RouterFactory.');
			}
		}

		return $output;
	}

	private function getNewFrontModuleRoutes(array $trans, bool $hasNewRouter): array
	{
		$routes = [
			'NewFront:Static:contact' => $trans['contact'],
			'NewFront:HomepageLogged:defaultActiveUser' => $trans['myFavorites'],
			'NewFront:HomepageLogged:default' => $trans['myFavoritesInactive'],
			'NewFront:Homepage:subscribeCampaign' => $trans['subscribeCampaign'],
			'NewFront:Sign:in' => $trans['signIn'],
			'NewFront:Sign:out' => $trans['signOut'],
			'NewFront:Account:Review:default' => $trans['myReviews'],
			'NewFront:Account:Review:share' => $trans['shareReview'],
			'NewFront:Account:User:changePassword' => $trans['changePassword'],
			'NewFront:Account:User:settings' => $trans['settings'],
			'NewFront:Account:LastRedirections:default' => $trans['lastRedirections'],
			'NewFront:Account:User:guarantee' => $trans['userGuarantee'],
			'NewFront:Account:User:tellFriend' => $trans['userTellFriend'],
			'NewFront:Account:User:phoneNumberVerification' => $trans['phoneNumberVerification'],
			'NewFront:Account:User:accountNumberChangeVerification' => $trans['accountNumberChangeVerification'],
			'NewFront:Account:User:sessionVerification' => $trans['sessionVerification'],
			'NewFront:Account:User:accountDeletionRequest' => $trans['accountDeletionRequest'],
			'NewFront:Account:Transaction:default' => $trans['transactions'],
			'NewFront:Account:Payout:default' => $trans['payouts'],
			'NewFront:Account:Payout:thankYou' => $trans['payoutsThankYou'],
			'NewFront:Account:Notification:default' => $trans['notification'],
			'NewFront:Static:phoneApp' => $trans['phoneApp'],
			'NewFront:Account:Refund:default' => $trans['refunds'],
			'NewFront:Account:Refunds:default' => $trans['refundsList'],
			'NewFront:Account:Refund:thankYou' => $trans['refundsThankYou'],
			'NewFront:Account:EmailVerificationBonus:default' => $trans['emailVerificationBonus'],
			'NewFront:Sign:forgottenPassword' => $trans['forgottenPassword'],
			'NewFront:Sign:forgottenPasswordSent' => $trans['forgottenPasswordSent'],
			'NewFront:Static:addon' => $trans['addon'],
			'NewFront:Static:addonInstalled' => $trans['addonInstalled'],
			'NewFront:Static:howItWorks' => $trans['howItWorks'],
			'NewFront:Static:faq' => $trans['faq'],
			//'NewFront:Articles:Articles:default' => $trans['articles'],
			'NewFront:Static:conditions' => $trans['conditions'],
			'NewFront:Static:privacyPolicy' => $trans['privacy'],
			'NewFront:Static:cookies' => 'cookies',
			'NewFront:SitemapData:web' => $trans['sitemap'],
			'NewFront:SitemapData:bot' => 'sitemap.xml',
			'NewFront:Robots:default' => 'robots.txt',
			'NewFront:Articles:Rss:articles' => 'articles.xml',
			'NewFront:Articles:Rss:instantArticles' => 'instant-articles.xml',
			'NewFront:Shops:Redirection:setAdblockUsed' => 'redirection/set-adblock-used',
			'NewFront:Zasilkovna:default' => $trans['zasilkovnaDefault'],
			'NewFront:Zasilkovna:extension' => $trans['zasilkovnaExtension'],
			'NewFront:Zasilkovna:thankYou' => $trans['zasilkovnaThankYou'],
			'NewFront:Static:newDesignBenefits' => 'new-design-benefits',
			'NewFront:Static:newDesignTipliGivesOut' => 'new-design-tipli-gives-out',
			'NewFront:Static:givesOutShopSelected' => 'gives-out-shop-selected',
			'NewFront:Static:givesOutIntroScreen' => 'gives-out-intro-screen',
			'NewFront:Static:newDesignSales' => 'new-design-sales',
			'NewFront:Static:newDesignSales1' => 'new-design-sales-1',
			'NewFront:Static:wheelOfFortune' => 'wheel-of-fortune',
			'NewFront:Static:conversionPageAfterRegistration' => 'conversion-page-after-registration',
			'NewFront:Static:newDesignCareer' => 'new-design-career',
			'NewFront:Static:newDesignCareerDetail' => 'new-design-career-detail',
			'NewFront:Static:lastRedirect' => 'last-redirect',
			'NewFront:Static:couponTutorial' => 'coupon-tutorial',
			'NewFront:Static:ads' => 'ads.txt',
			'NewFront:OpenSearch:xml' => 'opensearch.xml',
			'NewFront:LuckyShops:LuckyShops:default' => 'tipli-rozdava',
			'NewFront:LuckyShops:LuckyShops:test' => 'tipli-rozdava-test',
			'NewFront:LuckyShops:LuckyShops:setUserLuckyShop' => 'tipli-rozdava/nastavit-stastny-obchod',
			'NewFront:Static:afterSignUp' => 'addon-bonus',
			'NewFront:Static:afterSignIn' => 'addon-bonus-login',
			'NewFront:Redirection:redirectionFeedback' => 'redirection-feedback/<uniqueId>/<type>',
			'NewFront:Redirection:thanks' => $trans['redirectionFeedbackThankYou'],
			'NewFront:Static:tipliExtraPage' => 'tipli-extra-page',
			'NewFront:Static:test' => 'bot-test',
			'NewFront:Static:luckyShopsFinal' => 'lucky-shops-final',
			'NewFront:Vouchers:Vouchers:default' => 'tipli-extra',
		];

		$routesWithNewPrefixInUrl = [];
		foreach ($routes as $key => $route) {
			$routesWithNewPrefixInUrl[$key] = 'new/' . $route;
		}

		return $hasNewRouter ? $routes : $routesWithNewPrefixInUrl;
	}
}
