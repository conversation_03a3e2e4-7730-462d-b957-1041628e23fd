#
# WARNING: it is CRITICAL that this file & directory are NOT accessible directly via a web browser!
# https://nette.org/security-warning
#
includes:
	- samples.neon
	- rabbitmq.neon

parameters:
	mode: 'normal'
	rabbitMqAllowed: true
	locales:
		cs: [www.tipli.czlocal, www.tipli.cz, www.tiplicz.dev, atomic.tipli.cz, dev.tipli.cz]
		sk: [www.tipli.sklocal, www.tipli.sk, www.tiplisk.dev]
		pl: [www.tipli.pllocal, www.tipli.pl, www.tiplipl.dev]
		ro: [www.tipli.rolocal, www.tipli.ro, www.tipliro.dev]
		hu: [www.tiplino.hulocal, www.tiplino.hu, www.tiplino.hu]
		si: [www.tipli.silocal, www.tipli.si]
		hr: [www.tipli.hrlocal, www.tipli.hr]
		bg: [www.tipli.bglocal, www.tipli.bg]
	urls:
		cs: 'https://www.tipli.cz/'
		sk: 'https://www.tipli.sk/'
		pl: 'https://www.tipli.pl/'
		ro: 'https://www.tipli.ro/'
		hu: 'https://www.tiplino.hu/'
		si: 'https://www.tipli.si/'
		hr: 'https://www.tipli.hr/'
		rs: 'https://www.tipli.rs/'
		bg: 'https://www.tipli.bg/'
	upload:
		images: %wwwDir%/upload/images/
		thumbnails: %wwwDir%/upload/thumbnails/
		files: %wwwDir%/upload/files/
	facebook:
		pixelId: 1812881122303002
		app:
			default_graph_version: 'v3.3'
			app_id: '***************'
			app_secret: '84495be27f24cc86f3e4583e09f12c56'
			accessToken: 'EAACplXkwpRIBALjUSbQaUn4OGYGP35JSqwgVbJddZCsbKvA256SZAHP57ucJYsInaTa312WQPYsDY7ydMr2hS2u7GXt6CwsukSiVANGQ07dTalPKZA7ucA0yZAX1TqxxeST5q6AswpRfZA2Mv1ibKqFbCfQKEziQRZCElpveGWqQZDZD'
#            accessToken: 'EAACplXkwpRIBALwnZCMPA6ZC0qbOM6NgsZBnxvtM62fiaf8obZC3OvEFZCPEVdENVVMi56t1P5ykb8J75MgoX1z183CJ0tC3BxD6CZAzHdxBvPZBVFpOzQQOCe5xHZAd1JtRZBNDHkTOMWPyH4Ouuf3zs43zTszCe47AZD'
#            accessToken: 'EAACplXkwpRIBADtH8mGoxVWrqZCVvtW1uTxJDDD7R2wHZCakyVCZBJI5QWZATZBW0ELfxPixkq70OdH7WHoZCxLTPU3NZANMCQmgsPActdcQkToqqYmXJCePlycGqM3ZBJFme6O6sTIKgTFECpKIozesO7dWzZCrQXEE6GqqB0klBLUntKO1XrKHh'
			adAccountIds:
				cs: [***************, ***************]
				sk: [***************, ***************]
				pl: [***************, ***************]
		apps:
			cs: '***************'
			sk: '***************'
			pl: '***************'
			ro: '***************'
			hu: '****************'
			si: '****************' # @todo
			hr: '****************' # @todo
			rs: '****************' # @todo
			bg: '****************' # @todo
		pages:
			cs:
				id: '****************'
				accessToken: 'EAACplXkwpRIBAErYQCFWMZBdm50xngnFRUxjTm93AaA5tpiJ4dtM3xTZAzpeNpEt7BZAZBpUHfYg1BfyUjHWY01aZB037LUVBdZC5X22aJMOh7G1MFiWz3nYBNRL9mnzqpVcQCWZAmhskBxmmWTX6QthRUgRSmJt5R6c5L8xIug7bgtQhBnDEkv'
			sk:
				id: '****************'
				accessToken: 'EAACplXkwpRIBANBZBVnIbAVfQTlASskLq60ZA5rt9GSCmP5fMavbpZCHH4ZB18d3xV5GGZChWLT9bZAx7Mx2SD9Cq6SIyyLK83bJZBes4ZCttLgaIW9KfTrQ4kNFsNZCfIsFmi8K2DtEumfwPlqunGCl9gi5GixcT9pKIN8uLhmPguQZDZD'
			pl:
				id: '****************'
				accessToken: 'EAACplXkwpRIBAPLHakvTJTFScL0ZAgtz9fGvYvZAuPccpfjEPozwMwn9NdIfsJj94uQS7QPIEPhaM8ko5NtBCMbela9fXHgfygdCPnmbMLciaTYkNzVPL8MPq8q3q5xTThJJFUL1YjgomacvijgEFu0KQA2U7PIJMMKbHO9wZDZD'
	google:
#        appId: '447795909057-6mdul544c3au7re32hj6lbdri2a6m6es.apps.googleusercontent.com'
		webClientId: '179697024891-eqjtkln3k2v41olj0phjrksaftnjje3a.apps.googleusercontent.com'
		iOSClientId:
			cs: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			sk: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			pl: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			ro: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			hu: '179697024891-prhgg2se01c9nhb9j9h0kcudf13d3p31.apps.googleusercontent.com'
			si: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			hr: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			rs: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
			bg: '179697024891-ebbk8h71p0v2et48uimprriqmom7s6qg.apps.googleusercontent.com'
	apple:
		clientId: cz.tipli
		#clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: **********************************************************************************************************************************************************************************************************************************************************************************
#		clientSecret: eyJraWQiOiJQVEZONE0zNUo2IiwiYWxnIjoiRVMyNTYifQ.********************************************************************************************************************************************.fxliz8LsLgs0sZPV_X89ieJi_3ZETn3GhLZVBMSHVJGVvv0stboyS9iL6nNWl7ubVzMWfimR14kmu92rySvnlQ
		clientSecret: eyJraWQiOiJQVEZONE0zNUo2IiwiYWxnIjoiRVMyNTYifQ.********************************************************************************************************************************************.2k2lplxt0NT1Phtq9x0kfk2XWtA3Ie36RV6SGXNpVjFtIiRtYJntbZueKZznynY01mmRphYNe2BbAMIAD2BhEw
	smsbrana:
		login: 'tipli_h1'
		password: 'jBMVPK4gQje9qfs4'
	ga:
		accounts:
			cs: 'UA-********-1'
			sk: 'UA-********-2'
			pl: 'UA-********-3'
			ro: 'UA-********-4'
			hu: '*********'
			si: '*********' # @todo
			hr: '*********' # @todo
			rs: '*********' # @todo
			bg: '*********' # @todo
	freshdesk:
		groups:
			cs: ***********
			sk: ***********
			pl: ***********
			ro: ***********
			hu: ***********
			si: ***********
			hr: ***********
			bg: ***********
		emailConfigIds:
			cs: ***********
			sk: ***********
			pl: ***********
			ro: ***********
			hu: ***********
			si: ***********
			hr: ***********
			bg: ***********
		api:
			apiUrl: https://tipli.freshdesk.com/api/v2/
			key: ********************
		jobs:
			email: '<EMAIL>'
	mailgun:
#		apiKey: '************************************'
		apiKey: '**************************************************'
		domains:
			cz: 'mail.tipli.cz'
			sk: 'mail.tipli.sk'
			pl: 'mail.tipli.pl'
			ro: 'mail.tipli.ro'
			hu: 'mail.tiplino.hu'
			si: 'mail.tipli.si'
			hr: 'mail.tipli.hr'
			rs: 'mail.tipli.rs'
			bg: 'mail.tipli.bg'
	mandrill:
		apiKey: 'md-**********************'
	mailchimp:
		apiKey: *************************************
		server: us17
		lists:
			cs: 53141ad3c3
			sk: d30832fe51
			pl: ca29fcfd1b
			ro: 68bd3fff24
			hu: 7da9ed5a42
			si: 6aa11be7a4
			hr: d65c8dd21d
			bg: 6a8329a830
	mailkit:
		id: 217409611
		md5: 8f5ab918ebcc8c0bcfe9117631e411e7
		server: us17
		lists:
			cs: 118912
			sk: 118974
			pl: 118975
			ro: 118976
			hu: 118977
			si: 118980
			hr: 118979
			bg: 118978

	oneSignal:
		cs: [appId: '************************************', apiKey: 'ODIwZjk4MjYtMThiNS00ZTFmLTg0OWItNTNkZjJmMjVmY2Mz']
		sk: [appId: '50bed49b-4f48-4b0b-ad41-14ee1105dfe4', apiKey: 'NzIyMWMzYTctYTE1Mi00Y2Y0LTg4YTItMmZmMmZiZGZmNDI2']
		pl: [appId: '656eca67-c4b1-4e15-9494-f6f5114cc026', apiKey: 'MjJkYjllYjUtZWEyZC00MzAwLWIzMTMtZTgzZDQzMTRjN2Fk']
		ro: [appId: 'bd19707c-7bbd-4761-857c-533d2c558979', apiKey: 'YjQ5ZDExNTEtMjcyYy00MWE5LTg1YWQtN2MyMzA2Nzk1Y2Jk']
		hu: [appId: '8b6969bd-b3bf-4a03-b83c-c76515b68760', apiKey: 'NWVmZjZhNzUtZjdjMC00MzNiLThhZjgtNTkzMmNkOTZlMmQ3']
		si: [appId: '8b6969bd-b3bf-4a03-b83c-c76515b68760', apiKey: 'NWVmZjZhNzUtZjdjMC00MzNiLThhZjgtNTkzMmNkOTZlMmQ3'] # @todo
		hr: [appId: '8b6969bd-b3bf-4a03-b83c-c76515b68760', apiKey: 'NWVmZjZhNzUtZjdjMC00MzNiLThhZjgtNTkzMmNkOTZlMmQ3'] # @todo
		rs: [appId: '8b6969bd-b3bf-4a03-b83c-c76515b68760', apiKey: 'NWVmZjZhNzUtZjdjMC00MzNiLThhZjgtNTkzMmNkOTZlMmQ3'] # @todo
		bg: [appId: '8b6969bd-b3bf-4a03-b83c-c76515b68760', apiKey: 'NWVmZjZhNzUtZjdjMC00MzNiLThhZjgtNTkzMmNkOTZlMmQ3'] # @todo
	apify:
		token: 'LwY7R6KH8tTLhZKqLNA8EwHfz'
	bonusFriendRewardCampaigns:
		cs: 237
		sk: 238
		pl: 238 #todo
	minimalShareCoefficients:
		cs: 0.5
		sk: 0.5
		pl: 0.5
		ro: 0.5
		hu: 0.5
		si: 0.5
		hr: 0.5
		bg: 0.5
	mjml:
		appId: '1055e2de-0d91-11e7-981f-42010a101402'
		publicKey: 'c7108085-4bef-4c28-aa69-b16bbe10988b'
		secretKey: '68a1885f-1996-4156-9822-d565a3f180df'
	datadog:
#		apiKey: ********************************
#		appKey: 82ac3eda033c7cadce7cfa71a7f67a8a2d32052b
		apiKey: 143eb9dc4853b90995e098cb7914e240
		appKey: 31492f93f7d25fd1e2f514089e9921533c197810
	opsGenie:
		apiKey: ************************************
		integrationApiKey: 5f0fcc5f-aa5d-4461-a5e7-bd22af1263c8
	allowedPhoneCountryCodes:
		cs: ['+420', '+421']
		sk: ['+421', '+420']
		pl: ['+48']
		ro: ['+40']
		hu: ['+36']
		si: ['+386']
		hr: ['+385']
		bg: ['+359']
	smartsUpp:
		groups:
			cs: 36JonPXWV7
			sk: 36JonPXWV7
			pl: zJOTzChhtZ
			ro: B4Ea0szDXL
			hu: yVNUwftUUQ
			si: Ux8rMnT9rm
			hr: 11FyA0bbrf
			bg: 3Btaw54Lnt
		authToken: "ac59f01b5dda28544fda6216f6c2744a5aa85bef"

	unLoggedRedirectionUsers:
		cs: 128745
		sk: 128747
		pl: 128749
		ro: 1396120
		hu: 2010939
		si: 4035979
		hr: 4035980
		bg: 4035981
	translations:
		schemaLanguage: cs_CZ
		schemaLocalizationId: 1
		localeDir: %appDir%/locale/
		localesShortcuts:
			cs: 'cs_CZ'
			sk: 'sk_SK'
			pl: 'pl_PL'
			ro: 'ro_RO'
			en: 'en_US'
			hu: 'hu_HU'
			si: 'si_SI' # @todo
			hr: 'hr_HR' # @todo
			bg: 'bg_BG' # @todo
	googleAnalyticsViewIds:
		cs: 131241411
		sk: 135227349
		pl: 148679934
		ro: 207019066
		hu: ********* # @todo
		si: ********* # @todo
		hr: ********* # @todo
		rs: ********* # @todo
		bg: ********* # @todo
	leaflets:
		apiKey: 'steve2011'
	stores:
		apiKey: 'steve2011'
	ipData:
		apiKey: d4df5465bc0f682b426240f7b2dca073fbeaa9eb26e7dfd552da580e
	webtempDir: '%wwwDir%/webtemp'
	tick:
		apiKey: dfb31ad178ef43f76e693477ea2c694f
	collabim:
		apiKey: 3716c3e24ad8d98f2461adcc058d0193287754cd
	twisto:
		apiKeys:
			test: '0998cf4f-f375-4f10-b644-5f7cc86dc167'
			production: 'b10d3858-1e43-4b77-95fc-bbef71a4f6d9'
	zasilkovna:
		shareRewardCampaignIds:
			cs: 261
			sk: 283
		moneyRewardCampaignIds:
			cs: 583
			sk: 627
		partnerOrganizationIds:
			cs: 31
			sk: 37
	mobileAppStoreLinks:
		ios:
			cs: https://apps.apple.com/cz/app/tipli/id1492288796
			sk: https://apps.apple.com/sk/app/tipli/id1492288796
			pl: https://apps.apple.com/pl/app/tipli/id1492288796
			ro: https://apps.apple.com/ro/app/tipli/id1492288796
			hu: https://apps.apple.com/cz/app/tiplino/id1579873267
			si: https://apps.apple.com/ro/app/tipli/id1492288796 # @todo
			hr: https://apps.apple.com/ro/app/tipli/id1492288796 # @todo
			rs: https://apps.apple.com/ro/app/tipli/id1492288796 # @todo
			bg: https://apps.apple.com/ro/app/tipli/id1492288796 # @todo
		android:
			cs: "https://play.google.com/store/apps/details?id=cz.tipli.android.app"
			sk: "https://play.google.com/store/apps/details?id=cz.tipli.android.app"
			pl: "https://play.google.com/store/apps/details?id=cz.tipli.android.app"
			ro: "https://play.google.com/store/apps/details?id=cz.tipli.android.app"
			hu: "https://play.google.com/store/apps/details?id=cz.tiplino.hu.android.app"
			si: "https://play.google.com/store/apps/details?id=cz.tipli.android.app" # @todo
			hr: "https://play.google.com/store/apps/details?id=cz.tipli.android.app" # @todo
			rs: "https://play.google.com/store/apps/details?id=cz.tipli.android.app" # @todo
			bg: "https://play.google.com/store/apps/details?id=cz.tipli.android.app" # @todo
	sentry:
		dsn: "https://<EMAIL>/5680608"
	algolia:
		appId: D6VZ0E6F5F
		apiKey: ********************************
	fioAccounts:
		cs:
			account: ********/2010
			token: kx11cwB3qPSR6wVkgxEuQpvOK1lIHebpbxQRTHNof1Nye8O70rqu8GVcaSakFEnX
		sk:
			account: ********/2010
			token: 'eg2YFE4NQfG2f56K0XQScd5mTfvHsntvOJvqyNlQzeIaAmRX8NEwicQ5rHy2cKi4'

application:
	errorPresenter: NewFront:Error
	mapping:
		*: tipli\*Module\Presenters\*Presenter

extensions:
	console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)
	events: Contributte\EventDispatcher\DI\EventDispatcherExtension
	events2extra: Contributte\Events\Extra\DI\EventBridgesExtension
	events2application: Contributte\Events\Extra\DI\EventApplicationBridgeExtension
	events2security: Contributte\Events\Extra\DI\EventSecurityBridgeExtension
	events2latte: Contributte\Events\Extra\DI\EventLatteBridgeExtension
	translation: Contributte\Translation\DI\TranslationExtension
	autowired: Kdyby\Autowired\DI\AutowiredExtension
	honeypot: wodCZ\NetteHoneypot\HoneypotExtension
	recaptcha: Contributte\ReCaptcha\DI\ReCaptchaExtension

	# Common
	nettrine.annotations: Nettrine\Annotations\DI\AnnotationsExtension
	nettrine.cache: Nettrine\Cache\DI\CacheExtension
	# ORM
	nettrine.dbal: Nettrine\DBAL\DI\DbalExtension
	nettrine.dbal.console: Nettrine\DBAL\DI\DbalConsoleExtension(%consoleMode%)
	nettrine.orm: Nettrine\ORM\DI\OrmExtension
	nettrine.orm.cache: Nettrine\ORM\DI\OrmCacheExtension
	nettrine.orm.annotations: Nettrine\ORM\DI\OrmAnnotationsExtension
	nettrine.orm.console: Nettrine\ORM\DI\OrmConsoleExtension(%consoleMode%)

#	 register all console bridges
	console.extra: Contributte\Console\Extra\DI\ConsoleBridgesExtension(%consoleMode%)
	console.advancedCache: Contributte\Console\Extra\DI\AdvancedCacheConsoleExtension(%consoleMode%)
	rabbitMq: Contributte\RabbitMQ\DI\RabbitMQExtension24

console:
	name: Tipli
	version: '1.0'
	catchExceptions: false
	lazy: false

nettrine.orm:
	entityManagerDecoratorClass: tipli\Model\Doctrine\EntityManager
	configuration:
#		autoGenerateProxyClasses: %debugMode% # 2 => AUTOGENERATE_FILE_NOT_EXISTS
		autoGenerateProxyClasses: 2 # 2 => AUTOGENERATE_FILE_NOT_EXISTS AUTOGENERATE_FILE_NOT_EXISTS
		customStringFunctions:
			IFNULL: DoctrineExtensions\Query\Mysql\IfNull
			FIELD: DoctrineExtensions\Query\Mysql\Field
			IF: DoctrineExtensions\Query\Mysql\IfElse
			DATE: DoctrineExtensions\Query\Mysql\Date
			RAND: DoctrineExtensions\Query\Mysql\Rand
			ROUND: DoctrineExtensions\Query\Mysql\Round
			FLOOR: DoctrineExtensions\Query\Mysql\Floor
			UNIX_TIMESTAMP: DoctrineExtensions\Query\Mysql\UnixTimestamp

console.extra:
	# optionally disable these bridges
	router: false
	security: false
	utils: false

console.advancedCache:
		generators:
			di: Contributte\Console\Extra\Cache\Generators\DiContainersCacheGenerator(
					[
						consoleProduction: [debugMode: false, consoleMode: true],
						consoleDebug: [debugMode: true, consoleMode: true]
					]
				)
recaptcha:
	secretKey: '6LcUF98ZAAAAAKhm_FH3UOrUDE18UIk8rRE5F1bB'
	siteKey: '6LcUF98ZAAAAAP2vYiKpDneqsqX_w_t1SWpKNjnY'
	#secretKey: '6LeGs3kUAAAAAMdX1QR1z5zYsB1WOtVMde0sFF8M'
#	siteKey: '6LeGs3kUAAAAAKnnavozxLr98Pvb5X4rkw9b77Y1'

session:
	expiration: 30 days
	savePath: "%tempDir%/sessions"
	debugger: true

http:
	headers:
		X-Powered-By: 'tipli'
		X-XSS-Protection: "1"

tracy:
	bar:
		- tipli\Components\DevPanel
	#email: <EMAIL>  # for sending error logs

translation:
	locales:
		whitelist: [cs, sk, pl, ro, hu, en, si, hr, rs, bg]
		default: en
		fallback: [en]
	dirs:
		- %appDir%/locale
	returnOriginalMessage: true # to not translate undefined messages, default is true
	localeResolvers:
		- tipli\Model\Localization\WebsiteResolver
	logger: true


database:
	default:
		dsn: %db.dsn%
		user: %db.user%
		password: %db.password%

#doctrine:
#	host: %db.host%
#	user: %db.user%
#	password: %db.password%
#	dbname: %db.dbname%
#	metadataCache: %db.metadataCache%
#	queryCache: %db.metadataCache%
#	resultCache: %db.metadataCache%
#	hydrationCache: %db.metadataCache%
#	charset: UTF8MB4
#	metadata:
#		tipli: %appDir%
#	dql:
#		string:
#			field: DoctrineExtensions\Query\Mysql\Field
#			if: DoctrineExtensions\Query\Mysql\IfElse
#			ifnull: DoctrineExtensions\Query\Mysql\IfNull
#			DATE: DoctrineExtensions\Query\Mysql\Date
#		numeric:
#			rand: DoctrineExtensions\Query\Mysql\Rand
#	secondLevelCache:
#		enabled: true
#		factoryClass: Doctrine\ORM\Cache\DefaultCacheFactory
#		driver: default

nettrine.dbal:
	debug:
		panel: %debugMode%
	connection:
		driver: pdo_mysql
		host: %db.host%
		user: %db.user%
		password: %db.password%
		dbname: %db.dbname%
		charset: UTF8MB4
nettrine.orm.annotations:
	mapping:
		tipli\Model: %appDir%/Model


honeypot:
	inline: true

decorator:
	tipli\Commands\Job:
		inject: yes

	Symfony\Component\Console\Command\Command:
		tags: [kdyby.console.command]

	tipli\Model\RabbitMq\BaseConsumer:
		setup:
			- setEventDispatcher(@Symfony\Contracts\EventDispatcher\EventDispatcherInterface)

	tipli\Model\PartnerSystems\Networks\Network:
		setup:
			- setClientLayer(@tipli\Model\Layers\ClientLayer)
			- setCache(@Nette\Caching\IStorage)
		inject: true

services:
	configurator:
		type: Nette\Configurator
		imported: true

	# Configuration
	configuration:
		class: tipli\Model\Configuration
		setup:
			- setImagesPath(%upload.images%)
			- setThumbnailsPath(%upload.thumbnails%)
			- setTrackingAllowed(%trackingAllowed%)
			- setFacebookApps(%facebook.apps%)
			- setGaAccounts(%ga.accounts%)
			- setBonusFriendRewardCampaigns(%bonusFriendRewardCampaigns%)
			- setFacebookPages(%facebook.pages%)
			- setAllowedPhoneCountryCodes(%allowedPhoneCountryCodes%)
			- setSmartsUppGroups(%smartsUpp.groups%)
			- setUrls(%urls%)
			- setUnLoggedRedirectionUsers(%unLoggedRedirectionUsers%)
			- setSmsbranaCredentials(%smsbrana%)
			- setLocalesShortcuts(%translations.localesShortcuts%)
			- setLocaleDir(%translations.localeDir%)
			- setFilesPath(%upload.files%)
			- setFbAdAccountIds(%facebook.app.adAccountIds%)
			- setLeafletsApiKey(%leaflets.apiKey%)
			- setStoresApiKey(%stores.apiKey%)
			- setIpDataApiKey(%ipData.apiKey%)
			- setMode(%mode%)
			- setGoogleWebClientId(%google.webClientId%)
			- setGoogleIOSClientId(%google.iOSClientId%)
			- setTickApiKey(%tick.apiKey%)
			- setCollabimApiKey(%collabim.apiKey%)
			- setTwistoApiKeys(%twisto.apiKeys%)
			- setRabbitMqAllowed(%rabbitMqAllowed%)
			- setZasilkovnaShareRewardCampaignIds(%zasilkovna.shareRewardCampaignIds%)
			- setZasilkovnaMoneyRewardCampaignIds(%zasilkovna.moneyRewardCampaignIds%)
			- setZasilkovnaPartnerOrganizationIds(%zasilkovna.partnerOrganizationIds%)
			- setAppleClientId(%apple.clientId%)
			- setAppleClientSecret(%apple.clientSecret%)
			- setMobileAppStoreLinks(%mobileAppStoreLinks%)
			- setFreshdeskGroupsIds(%freshdesk.groups%)
			- setFreshdeskEmailConfigIds(%freshdesk.emailConfigIds%)
			- setFioAccounts(%fioAccounts%)
			- setTempDir(%tempDir%)
			- setWebsites(%locales%)
			- setMailchimpApiKey(%mailchimp.apiKey%)
			- setMailchimpServer(%mailchimp.server%)
			- setMailchimpLists(%mailchimp.lists%)
			- setMailkitId(%mailkit.id%)
			- setMailkitMd5(%mailkit.md5%)
			- setMailkitLists(%mailkit.lists%)
	- tipli\FrontModule\Components\IPaginatorFactory
	- tipli\NewFrontModule\Components\IPaginatorFactory
	- tipli\FrontModule\Components\ISorterFactory

	# Router
	- tipli\Routers\RouterFactory
	router: @tipli\Routers\RouterFactory::createRouter()

	# Admin
	- tipli\AdminModule\Components\IPaginatorFactory
	- tipli\AdminModule\Components\IDataGridFactory
	- tipli\AdminModule\Components\IKeywordsControlFactory
	- tipli\AdminModule\Components\ICalendarFactory
	- tipli\AdminModule\Components\INotesControlFactory

	# CustomInputs
	- tipli\Model\Shops\CustomInputsBinder

	# Webloader
	- tipli\Components\ICssBuilderControlFactory
	- tipli\Components\IJsBuilderControlFactory

	# Amazon
	- Aws\Sqs\SqsClient(%amazon.sqs%)

	# Freshdesk
	- tipli\Model\Freshdesk\FreshdeskFacade
	- tipli\Model\Freshdesk\FreshdeskClient(@freshdeskClient)
	- tipli\Model\Freshdesk\TicketManager
	- tipli\Model\Freshdesk\FeedbackManager
	- tipli\Model\Freshdesk\TicketResponderResolver
	- tipli\Model\Freshdesk\MessageManager
	- tipli\Model\Freshdesk\RequestManager
	- tipli\Model\Freshdesk\RequestProcessor
	- tipli\Model\Freshdesk\Subscriber\FreshdeskSubscriber

	TicketRepository:
		class: tipli\Model\Freshdesk\Repositories\TicketRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Freshdesk\Entities\Ticket')

	- tipli\Model\Freshdesk\Subscribers\TicketSubscriber

	FeedbackRepository:
		class: tipli\Model\Freshdesk\Repositories\FeedbackRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Freshdesk\Entities\Feedback')

	MessageRepository:
		class: tipli\Model\Freshdesk\Repositories\MessageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Freshdesk\Entities\Message')

	RequestRepository:
		class: tipli\Model\Freshdesk\Repositories\RequestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Freshdesk\Entities\Request')

	# Messages
	- tipli\AdminModule\MessagesModule\Forms\ISendGroupMessageControlFactory
	- tipli\AdminModule\MessagesModule\Forms\ISendMessageControlFactory


	- tipli\Model\Messages\MessageFacade
	- tipli\Model\Messages\MessagePreviewFacade
	- tipli\Model\Messages\EmailFactory
	- tipli\Model\Messages\EmailTemplateFactory
	- tipli\Model\Messages\EmailRenderer
	- tipli\Model\Messages\EmailManager
	- tipli\Model\Messages\EmailRouter(@amazonEmailSender, @mailgunEmailSender, @mandrillEmailSender)
	- tipli\Model\Messages\BlockedEmailFacade
	- tipli\Model\Messages\SendingPolicyManager
	- tipli\Model\Messages\MjmlConverter(%mjml.appId%, %mjml.secretKey%)
	- tipli\Model\Messages\EmailsInteractionManager
#    Production:
#    emailSender: tipli\Model\Messages\AmazonEmailSender(@mail.mailer)
#    emailSender: tipli\Model\Messages\MailgunEmailSender(%mailgun.apiKey%, %mailgun.domains%)
	- tipli\Model\Messages\OneSignalClient(%oneSignal%)
	- tipli\Model\Messages\MailgunClient(%mailgun.apiKey%)
	- tipli\Model\Messages\MailgunEventManager
	- tipli\Model\Messages\MailchimpEventManager
	- tipli\Model\Messages\MailkitEventManager
	- tipli\Model\Messages\MandrillManager
	- tipli\Model\Messages\MandrillEventManager
	- tipli\Model\Messages\MandrillClient(%mandrill.apiKey%)
	- tipli\Model\Messages\OneSignalNotificationManager
	- tipli\Model\Messages\ImageGenerator

	# Autoresponders
	- tipli\Model\Messages\UserAutoResponderFacade
	- tipli\Model\Messages\UserAutoResponderManager
	- tipli\Model\Messages\UserAutoResponderSender

	userAutoResponderRepository:
		class: tipli\Model\Messages\Repositories\UserAutoResponderRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\UserAutoResponder')

	#Mandrill
	MandrillRepository:
		class: tipli\Model\Messages\Repositories\MandrillRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\MandrillTag')

	# OneSignal
	OneSignalNotificationRepository:
		class: tipli\Model\Messages\Repositories\OneSignalNotificationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\OneSignalNotification')

	# Emails
	- tipli\Model\Messages\Emails\IRegistrationEmailFactory
	- tipli\Model\Messages\Emails\IRegistrationRecommenderEmailFactory
	- tipli\Model\Messages\Emails\IAfterRegistration1DayAutoResponderFactory
	- tipli\Model\Messages\Emails\IAfterRegistration9DaysAutoResponderFactory
	- tipli\Model\Messages\Emails\IAfterRegistration3DaysAutoResponderFactory
	- tipli\Model\Messages\Emails\INewPasswordEmailFactory
	- tipli\Model\Messages\Emails\ITellFriendEmailFactory
	- tipli\Model\Messages\Emails\ITransactionRegistrationEmailFactory
	- tipli\Model\Messages\Emails\IBonusTransactionRegistrationEmailFactory
	- tipli\Model\Messages\Emails\ITransactionRecommendationBonusRegistrationEmailFactory
	- tipli\Model\Messages\Emails\ITransactionConfirmationEmailFactory
	- tipli\Model\Messages\Emails\IPayoutCreationEmailFactory
	- tipli\Model\Messages\Emails\IPayoutConfirmationEmailFactory
	- tipli\Model\Messages\Emails\IAccountNumberChangeVerificationEmailFactory
	- tipli\Model\Messages\Emails\IAccountNumberVerificationEmailFactory
	- tipli\Model\Messages\Emails\IMoneyRewardExpirationEmailFactory
	- tipli\Model\Messages\Emails\IShareRewardExpirationEmailFactory
	- tipli\Model\Messages\Emails\IMoneyRewardReminderEmailFactory
	- tipli\Model\Messages\Emails\IVerificationEmailFactory
	- tipli\Model\Messages\Emails\IFreshdeskFeedbackEmailFactory
	- tipli\Model\Messages\FblGmailClient
	- tipli\Model\Messages\FblManager
	- tipli\Model\Messages\Emails\IUserRewardExpirationNotificationEmailFactory
	- tipli\Model\Messages\Emails\IUserRewardToNewUsersNotificationEmailFactory
	- tipli\Model\Messages\Emails\IAfterRedirectionAutoResponderEmailFactory
	- tipli\Model\Messages\Emails\ICampaignAutoResponderEmailFactory
	- tipli\Model\Messages\Emails\IHowToVideoAutoResponderFactory
	- tipli\Model\Messages\Emails\IMobileAppAutoResponderFactory
	- tipli\Model\Messages\Emails\IAddonAutoResponderAutoResponderFactory
	- tipli\Model\Messages\Emails\IHowToGetRewardsAutoResponderFactory
	- tipli\Model\Messages\Emails\IElectronicsAutoResponderFactory
	- tipli\Model\Messages\Emails\ITravelAutoResponderFactory
	- tipli\Model\Messages\Emails\IAliExpressAutoResponderFactory
	- tipli\Model\Messages\Emails\IShopsAutoResponderAutoResponderFactory
	- tipli\Model\Messages\Emails\IFashionAutoResponderFactory
	- tipli\Model\Messages\Emails\IHealthAutoResponderFactory
	- tipli\Model\Messages\Emails\Autoresponders\FeedbackAfterRegistration\FeedbackAfterRegistrationAutoresponderFactory
	- tipli\Model\Messages\Emails\Autoresponders\Deals\DealsAutoResponderFactory
	- tipli\Model\Messages\EmailEventsWebhookHandler
	- tipli\Model\Messages\Emails\IReviewRequestEmail
	- tipli\Model\Messages\Emails\IReviewRequestResponseEmail

	# Sms
	- tipli\Model\Messages\SmsManager(@smsSender)
	- tipli\Model\Messages\SmsFactory
	- tipli\Model\Messages\SmsScheduler
	- tipli\AdminModule\SmsModule\Forms\ISmsControlFactory

	# Reports
#	- tipli\Model\Reports\Producers\SqlQueriesProducer
	- tipli\Model\Reports\KeywordManager
	- tipli\Model\Reports\ApifyTaskFacade
	- tipli\Model\Reports\ApifyTaskManager
	- tipli\Model\Reports\ApifyClient(%apify.token%)

	apifyTaskRepository:
		class: tipli\Model\Reports\Repositories\ApifyTaskRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\ApifyTask')


	# Products
	- tipli\Model\Products\ProductFacade
	- tipli\Model\Products\ProductManager
	- tipli\Model\Products\ShopProductManager
	- tipli\Model\Products\BrandManager
	- tipli\Model\Products\ProductChecker
	- tipli\Model\Products\ProductDetailCrawler
	- tipli\Model\Products\Importers\HeurekaImporter
	- tipli\Model\Products\Importers\CJImporter
	- tipli\Model\Products\Importers\TradedoublerImporter
	- tipli\Model\Products\Importers\CJFeedImporter
	- tipli\Model\Products\Importers\EhubImporter
	- tipli\Model\Products\Importers\ApifyImporter
	- tipli\Model\Products\Importers\ApifyWsImporter
	- tipli\Model\Products\ProductsWebhookHandler
	- tipli\Model\Products\CategoryFacade
	- tipli\Model\Products\CategoryManager
	- tipli\ReportsModule\Components\Products\IImportCategoriesControlFactory
	- tipli\FrontModule\ProductsModule\Components\IProductFilterControlFactory
	- tipli\Model\Products\AvailableProductFiltersFactory
	- tipli\Model\Products\ProductsProvider
	- tipli\Model\Products\ProductDataProvider
	- tipli\Model\Products\Importers\DognetImporter
	- tipli\Model\Products\Importers\AwinImporter
	- tipli\Model\Products\Importers\GoogleShoppingImporter
	- tipli\AdminModule\ProductsModule\Forms\IProductControlFactory
	- tipli\Model\Products\ProductPriorityResolver
	- tipli\Model\Products\ProductCheckerFacade

	productCategoryRepository:
		class: tipli\Model\Products\Repositories\CategoryRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Products\Entities\Category')

	# Products2
	- tipli\Model\Products2\ProductFacade
	- tipli\Model\Products2\ProductManager
	- tipli\Model\Products2\ProductClient

	Products2ProductRepository:
		class: tipli\Model\Products2\Repositories\ProductRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Products2\Entities\Product')

	## Main dashboard
	- tipli\ReportsModule\Components\MainDashboard\ICountryFactory

	## CampaignsByCosts
	- tipli\ReportsModule\Components\CampaignsByCosts\ICampaignByCostsRowFactory

	- tipli\Model\Reports\StatisticDataProvider
	- tipli\Model\Reports\BusinessPlanDataProvider
	- tipli\Model\Reports\RoiDataProvider
	- tipli\Model\Reports\FakeStatisticDataProvider
	- tipli\Model\Reports\CampaignsDataProvider
	- tipli\Model\Reports\Queries\ICampaignsQueryFactory
	- tipli\Model\Reports\Queries\IUsersQueryFactory
	- tipli\Model\Reports\KPIManager
	- tipli\ReportsModule\Components\ICalendarFactory
	- tipli\ReportsModule\Components\IDataGridFactory
	- tipli\ReportsModule\Forms\IUserIdControlFactory
	- tipli\ReportsModule\Forms\IUtmReportControlFactory
	- tipli\ReportsModule\Forms\IUtmCostControlFactory
	- tipli\ReportsModule\Components\IRoiSourceRowFactory
	- tipli\ReportsModule\Components\IRoiSourceInTimeRowFactory
	- tipli\ReportsModule\Components\IImportKeywordsControlFactory
	- tipli\ReportsModule\Components\ImportVisits\ImportVisitsControlFactory
	- tipli\ReportsModule\Forms\IRefundSolutionStateControlFactory
	- tipli\ReportsModule\Forms\IRefundReportUploadControlFactory
	- tipli\ReportsModule\Forms\IEnableShopDealControlFactory

	## Tick

	# Algolia
	- AlgoliaSearch\Client(%algolia.appId%, %algolia.apiKey%)
	- tipli\Model\Algolia\AlgoliaFacade
	- tipli\Model\Algolia\AlgoliaManager
	- tipli\Model\Algolia\ObjectFactory
	- tipli\Model\Algolia\ObjectLoader
	- tipli\Model\Algolia\ObjectManager
	- tipli\Model\Algolia\Client

	# Images
	- tipli\Model\Images\ImageStorage
	- tipli\Model\Images\ImageFilter
	- tipli\Model\Images\ImageFilter2
	- tipli\Model\Images\SvgFilter

	# Tools
	- tipli\Model\CanonicalUrlFilter
	- tipli\Model\HardSpacesFilter

	# Pictures
	- tipli\AdminModule\PicturesModule\Forms\IPictureControlFactory
	- tipli\Model\Pictures\PictureFacade

	# Shortener
	- tipli\Model\Shortener\ShortcutFacade
	- tipli\Model\Shortener\ShortcutManager
	- tipli\AdminModule\ShortenerModule\Forms\IShortcutControlFactory

	# Datadog
	- tipli\Model\Datadog\DatadogClient(%datadog.apiKey%, %datadog.appKey%)
	- tipli\Model\Datadog\DatadogEventsQueue
	- tipli\Model\Datadog\DatadogWebhookHandler

	- tipli\Model\Datadog\Subscribers\DatadogEventSubscriber

	- tipli\AdminModule\ImageModule\Forms\IOriginalImageControlFactory

	# OpsGenie
	- tipli\Model\OpsGenie\OpsGenieClient(%opsGenie.apiKey%, %opsGenie.integrationApiKey%)

	# Regions
	- tipli\Model\Regions\RegionManager
	- tipli\Model\Regions\RegionFacade
	- tipli\Model\Regions\CoordinatesResolver
	- tipli\Model\Regions\GeoLocationLayer

	# Html builders
	htmlContentBuilder:
		class: tipli\Model\HtmlBuilders\ContentBuilder
		setup:
			- registerWidget('body', @bodyWidgetFactory)
			- registerWidget('xml', @xmlWidgetFactory)
			- registerWidget('picture', @pictureWidgetFactory)
			- registerWidget('img', @imgWidgetFactory)
			- registerWidget('a', @linkWidgetFactory)
			- registerWidget('cta', @ctaWidgetFactory)
			- registerWidget('iframe', @iframeWidgetFactory)
			- registerWidget('youtube', @youtubeWidgetFactory)
			- registerWidget('box', @boxWidgetFactory)
			- registerWidget('leaflets', @leafletsWidgetFactory)
			- registerWidget('hide', @hideWidgetFactory)
			- registerWidget('hideLink', @hideLinkWidgetFactory)
			- registerWidget('row', @rowWidgetFactory)
			- registerWidget('col', @colWidgetFactory)
			- registerWidget('ttable', @tableWidgetFactory)
			- registerWidget('trow', @tableRowWidgetFactory)
			- registerWidget('tcell', @tableCellWidgetFactory)
			- registerWidget('registrationBox', @registrationBoxWidgetFactory)

	- tipli\Model\HtmlBuilders\ContentFilter
	- tipli\Model\TextBreakingFilter
	- tipli\Model\HtmlBuilders\RedactorBuilder
	bodyWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IBodyWidgetFactory
	xmlWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IXmlWidgetFactory
	linkWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ILinkWidgetFactory
	pictureWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IPictureWidgetFactory
	imgWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IImgWidgetFactory
	ctaWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ICTAWidgetFactory
	iframeWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IIframeWidgetFactory
	youtubeWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IYoutubeWidgetFactory
	boxWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IBoxWidgetFactory
	leafletsWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ILeafletsWidgetFactory
	hideWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IHideWidgetFactory
	hideLinkWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IHideLinkWidgetFactory
	rowWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IRowWidgetFactory
	colWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IColWidgetFactory
	tableWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ITableWidgetFactory
	tableRowWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ITableRowWidgetFactory
	tableCellWidgetFactory: tipli\Model\HtmlBuilders\Widgets\ITableCellWidgetFactory
	registrationBoxWidgetFactory: tipli\Model\HtmlBuilders\Widgets\IRegistrationBoxWidgetFactory

	# Mjml builders
	mjmlContentBuilder:
		class: tipli\Model\Messages\Emails\MjmlBuilders\EmailBuilder
		setup:
			- registerWidget('custom', @customWidgetFactory)
			- registerWidget('shops', @shopsWidgetFactory)
			- registerWidget('cover', @coverWidgetFactory)
	shopsWidgetFactory: tipli\Model\Messages\Emails\MjmlBuilders\Widgets\IShopsWidgetFactory
	coverWidgetFactory: tipli\Model\Messages\Emails\MjmlBuilders\Widgets\ICoverWidgetFactory
	customWidgetFactory: tipli\Model\Messages\Emails\MjmlBuilders\Widgets\ICustomWidgetFactory

	# Localization
	- tipli\Model\Localization\LocalizationFacade
	- tipli\Model\Localization\FlagFilter
	- tipli\Model\Localization\TimeAgoFilter
	- tipli\Model\Localization\DateTimeFilter
	- tipli\Model\Localization\LocalDateFilter
	- tipli\Model\Localization\DateTimeZoneResolver

	# Redirections
	- tipli\AdminModule\RedirectionsModule\Forms\IRedirectionControlFactory
	- tipli\Model\Redirections\RedirectionFacade

	# Questions
	- tipli\AdminModule\QuestionsModule\Forms\IGroupControlFactory
	- tipli\AdminModule\QuestionsModule\Forms\IQuestionControlFactory
	- tipli\Model\Questions\GroupManager
	- tipli\Model\Questions\QuestionFacade
	- tipli\Model\Questions\QuestionManager

	# Users
	- tipli\AdminModule\AccountModule\Forms\IGroupRegistrationFactory
	- tipli\AdminModule\AccountModule\Forms\IRecommenderControlFactory
	- tipli\AdminModule\AccountModule\Forms\IRemoveUserControlFactory
	- tipli\AdminModule\AccountModule\Forms\IUserControlFactory
	- tipli\AdminModule\AccountModule\Forms\IUserEventControlFactory
	- tipli\AdminModule\AccountModule\Forms\IUserNoteControlFactory
	- tipli\AdminModule\AccountModule\Forms\IInvoicePayoutControlFactory

	- tipli\Model\Account\UserFacade
	- tipli\Model\Account\UserManager
	- tipli\Model\Account\SendingPolicyManager
	- tipli\Model\Account\AdminVisitManager
	- tipli\Model\Account\UserLoginManager
	- tipli\Model\Account\UserProfileChecker
	- tipli\Model\Account\CensorEmailFilter
	- tipli\Model\Account\VocalFirstNameFilter
	- tipli\Model\Account\EhubClient
	- tipli\Model\Account\AdminReLoginManager
	- tipli\Model\Account\ChangeManager
	- tipli\Model\Account\UserUtmManager
	- tipli\Model\Account\AccountNumberValidator

	- tipli\Model\Account\ShopInterestFacade
	- tipli\Model\Account\ShopInterestManager
	- tipli\Model\Account\ShopInterestsResolver
	- tipli\Model\Account\Subscribers\ShopInterestSubscriber


	- tipli\Model\Account\UserActivityManager
	- tipli\Model\Account\UserActivityFacade

	- tipli\Model\Account\EmailSubscriptionManager
	- tipli\Model\Account\EmailSubscriptionLogManager

	- tipli\Model\Account\FavoriteShopManager
	- tipli\Model\Account\FavoriteShopFacade

	shopInterestRepository:
		class: tipli\Model\Account\Repositories\ShopInterestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\ShopInterest')

	userActivityRepository:
		class: tipli\Model\Account\Repositories\UserActivityRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserActivity')

	userChangeRepository:
		class: tipli\Model\Account\Repositories\ChangeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\Change')

	userUtmRepository:
		class: tipli\Model\Account\Repositories\UserUtmRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserUtm')

	userFavoriteShop:
		class: tipli\Model\Account\Repositories\FavoriteShopRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\FavoriteShop')


	- tipli\Model\Account\UserDuplicityCheckerFacade
	- tipli\Model\Account\UserDuplicityManager

	userDuplicityRepository:
		class: tipli\Model\Account\Repositories\UserDuplicityRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserDuplicity')
	# User visits
	- tipli\Model\Account\UserVisitFacade
	- tipli\Model\Account\Managers\UserVisitManager

	userVisitRepository:
		class: tipli\Model\Account\Repositories\UserVisitRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserVisit')

	# Mailchimp
	- tipli\Model\Mailchimp\MailchimpUserProcessor
	- tipli\Model\Mailchimp\MailchimpUserResolver
	- tipli\Model\Mailchimp\MailchimpClient

	# Mailkit
	- tipli\Model\Mailkit\MailkitUserProcessor
	- tipli\Model\Mailkit\MailkitUserResolver
	- tipli\Model\Mailkit\MailkitClient
	- tipli\Model\Mailkit\EmailCampaignProcessor

	# zasilkovna
	- tipli\Model\Zasilkovna\ZasilkovnaFacade
	- tipli\Model\Zasilkovna\ZasilkovnaClient
	- tipli\Model\Zasilkovna\PacketManager
	- tipli\FrontModule\Forms\IZasilkovnaSignUpControlFactory
	- tipli\Model\Zasilkovna\ApprovalSynchronizationManager

	zasilkovnaPacketRepository:
		class: tipli\Model\Zasilkovna\Repositories\PacketRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Zasilkovna\Entities\Packet')

	zasilkovnaApprovalSynchronizationRepository:
		class: tipli\Model\Zasilkovna\Repositories\ApprovalSynchronizationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Zasilkovna\Entities\ApprovalSynchronization')

	# Edenred
	- tipli\FrontModule\Forms\IEdenredSignUpControlFactory

	- tipli\Model\Zasilkovna\Subscribers\ZasilkovnaApprovalSubscriber

	- tipli\FrontModule\AccountModule\Forms\IChangePasswordControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IChangePasswordControlFactory
	- tipli\FrontModule\AccountModule\Forms\IUserProfileControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\UserProfileControl\UserProfileControlFactory
	- tipli\FrontModule\AccountModule\Forms\IAccountNumberControlFactory
	- tipli\FrontModule\AccountModule\Forms\ITellFriendControlFactory
	- tipli\FrontModule\AccountModule\Forms\IGuaranteeControlFactory
	- tipli\FrontModule\AccountModule\Forms\IRequestPayoutControlFactory
	- tipli\FrontModule\AccountModule\Forms\IFeedbackControlFactory

	# User list without datagrid
	- tipli\AdminModule\AccountModule\Components\IUserListNewFactory
	- tipli\AdminModule\AccountModule\Components\UserFilterControl\UserFilterControlFactory

	# Segment data
	- tipli\Model\Account\UserSegmentDataFacade
	- tipli\Model\Account\UserSegmentDataManager

	# Permissions
	- tipli\Model\Account\PermissionFacade
	- tipli\Model\Account\UserPermissionManager

	UserLoginRepository:
		class: tipli\Model\Account\Repositories\UserLoginRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserLogin')

	UserPermissionRepository:
		class: tipli\Model\Account\Repositories\UserPermissionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\UserPermission')

	- tipli\Model\Account\Subscribers\UserSubscriber
	- tipli\Model\Account\Subscribers\UserCacheSubscriber
	- tipli\Model\Account\Subscribers\UserLoginSubscriber
	- tipli\Model\Account\Subscribers\UserActivitySubscriber

	# Banned User
	- tipli\Model\Account\BannedUserFacade
	- tipli\Model\Account\BannedUserManager

	bannedUserRepository:
		class: tipli\Model\Account\Repositories\BannedUserRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\BannedUser')

	# Account sessions
	- tipli\Model\Account\SessionFacade
	- tipli\Model\Account\SessionManager
	- tipli\FrontModule\AccountModule\Forms\ISessionVerificationControlFactory

	accountUserSessionRepository:
		class: tipli\Model\Account\Repositories\SessionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\Session')

	# Account mobile session
	- tipli\Model\Account\MobileSessionManager

	accountUserMobileSessionRepository:
		class: tipli\Model\Account\Repositories\MobileSessionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\MobileSession')

	# Account safari session
	- tipli\Model\Account\SafariSessionManager

	accountUserSafariSessionRepository:
		class: tipli\Model\Account\Repositories\SafariSessionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\SafariSession')

	# Account mobile device
	- tipli\Model\Account\MobileDeviceFacade
	- tipli\Model\Account\MobileDeviceManager

	accountUserMobileDeviceRepository:
		class: tipli\Model\Account\Repositories\MobileDeviceRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\MobileDevice')

	# Account device token
	- tipli\Model\Account\DeviceTokenFacade
	- tipli\Model\Account\DeviceTokenManager
	accountDeviceTokenRepository:
		class: tipli\Model\Account\Repositories\DeviceTokenRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\DeviceToken')

	# Account event
	- tipli\Model\Account\EventManager

	# Partner Organizations
	- tipli\AdminModule\PartnerOrganizationsModule\Forms\IPartnerOrganizationControlFactory
	- tipli\Model\PartnerOrganizations\PartnerOrganizationFacade

	# Tags
	- tipli\AdminModule\TagsModule\Forms\ITagControlFactory
	- tipli\AdminModule\TagsModule\Forms\IAddTagToShopControlFactory
	- tipli\Model\Tags\TagFacade
	- tipli\AdminModule\TagsModule\Forms\IMergeTagsControlFactory
	- tipli\Model\Tags\TagRuleResolver
	- tipli\Model\Tags\TagRuleFacade
	- tipli\AdminModule\TagsModule\Forms\ITagRulesControlFactory
	- tipli\Model\Tags\TagRuleManager

	tagRuleRepository:
		class: tipli\Model\Tags\Repositories\TagRuleRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Tags\Entities\TagRule')

	# Shops
	- tipli\AdminModule\ShopsModule\Forms\IShopControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IShopsImportControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IContactControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IAddRelatedShopControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IForeignShopControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IStoreControlFactory
	- tipli\AdminModule\ShopsModule\Forms\ISortDealTagSelectControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IShopCashbackCorrectionRulesControlFactory
	- tipli\Model\Shops\ShopFacade
	- tipli\Model\Shops\ShopConditionsProvider
	- tipli\Model\Shops\ShopsRecommender
	- tipli\Model\Shops\NewShopsRecommender
	- tipli\Model\Shops\RelatedShopManager
	- tipli\Model\Shops\ShopCashbackCorrectionRuleManager
	- tipli\Model\Shops\StoreManager
	- tipli\Model\Shops\StoreClient
	- tipli\Model\Shops\StoreFacade
	- tipli\Commands\ProcessStores
	- tipli\Commands\ImportDeals
	- tipli\Model\Shops\ContactFacade
	- tipli\Model\Shops\ShopManager
	- tipli\Model\Shops\ShopPauseLogManager
	- tipli\Model\Shops\ShopProductFeedFacade
	- tipli\Model\Shops\ShopProductFeedManager
	- tipli\Model\Shops\ShopChangeManager
	- tipli\Model\Shops\ShopReportManager
	- tipli\Model\Shops\ShopReportCalculator
	- tipli\Model\Shops\MobileShopCoverResolver

	- tipli\AdminModule\ShopsModule\Forms\IOfferControlFactory
	- tipli\Model\Shops\OfferFacade
	- tipli\Model\Shops\OfferResolver

	- tipli\Model\Shops\Redirection\RedirectionManager
	- tipli\Model\Shops\Redirection\RedirectionLinkFactory
	- tipli\Model\Shops\RedirectionFacade

	- tipli\Model\Shops\Listeners\ShopListener
	- tipli\Model\Shops\Listeners\OfferListener

	- tipli\AdminModule\ShopsModule\Forms\IDescriptionsBlockControlFactory
	- tipli\Model\Shops\DescriptionBlockManager

	- tipli\AdminModule\ShopsModule\Forms\IProductFeedControlFactory
	- tipli\AdminModule\ShopsModule\Forms\IRelatedShopsControlFactory

	- tipli\AdminModule\Components\Shop\ISettingsControlFactory
	- tipli\AdminModule\Components\Shop\INoteControlFactory
	- tipli\AdminModule\Components\Shop\IPartnerSystemsGridControlFactory
	- tipli\AdminModule\Components\Shop\ICashbackCorrectionRulesControlGridFactory
	- tipli\AdminModule\ShopsModule\Components\ShortDescription\ShortDescriptionControlFactory
	- tipli\AdminModule\Components\Shop\ChangesGrid\IChangesControlGridFactory

	# Shop Trigger
	- tipli\Model\Shops\ShopTriggerFacade
	- tipli\Model\Shops\ShopTriggerManager
	- tipli\Model\Shops\ShopTriggerProcessor
	- tipli\AdminModule\ShopsModule\Components\ShopTrigger\ShopTriggersControlFactory

	shopTriggerRepository:
		class: tipli\Model\Shops\Repositories\ShopTriggerRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopTrigger')

	# shopQuestion
	- tipli\Model\Shops\ShopQuestionManager

	# Transactions
	- tipli\Model\Transactions\TransactionFacade
	- tipli\Model\Transactions\TransactionFactory
	- tipli\Model\Transactions\TransactionManager
	- tipli\Model\Transactions\CommentManager
	- tipli\Model\Transactions\TransactionImporter
	- tipli\Model\Transactions\ShareCoefficientResolver(%minimalShareCoefficients%)
	- tipli\Model\Transactions\RedirectionResolver
	- tipli\Model\Transactions\AmountFilter
	- tipli\Model\Transactions\TransactionStatusHelperFilter
	- tipli\Model\Transactions\TransactionProgressResolver
	- tipli\Model\Transactions\ShareCoefficientCorrectionManager
	- tipli\Model\Transactions\SuspectedTransactionManager
	- tipli\Model\Transactions\SuspectedTransactionFacade
	- tipli\Model\Transactions\SuspectedTransactionResolver
	- tipli\Model\Transactions\TransactionProcessFacade
	- tipli\Model\Transactions\TransactionProcessManager
	- tipli\Model\Transactions\ImportedTransactionFailFacade
	- tipli\Model\Transactions\ImportedTransactionFailManager
	- tipli\Model\Transactions\TransactionTriggerProcessor
	- tipli\Model\Transactions\ImportedTransactionManager
	- tipli\Model\Transactions\ImportedTransactionQueue

	importedTransactionRepository:
		class: tipli\Model\Transactions\Repositories\ImportedTransactionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\ImportedTransaction')

	# Transaction checks
	- tipli\Model\Transactions\CheckManager
	- tipli\Model\Transactions\ImportedTransactionChecker

	checkRepository:
		class: tipli\Model\Transactions\Repositories\CheckRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\Check')

	- tipli\Model\Shops\ApifyShopCheckerWebhookHandler

	# Transactions Elastic

	- tipli\AdminModule\TransactionsModule\Forms\ITransactionControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\ITransactionConfirmControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\ITransactionShareCoefficientControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\ITransactionBonusEditControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\ITransactionUncancelControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\IRefundBonusControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\IDealBonusControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\IMarketingBonusControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\IImportTransactionsControlFactory
	- tipli\AdminModule\TransactionsModule\Forms\IAssignUserToTransactionControlFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundIncorrectAmountControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundIncorrectAmountControlFactory

	- tipli\Model\Transactions\TransactionsWebhookHandler

	- tipli\Model\Transactions\Subscribers\TransactionSubscriber

	# Payouts
	- tipli\Model\Payouts\CommentManager
	- tipli\Model\Payouts\FioClient
	- tipli\Model\Payouts\PayoutFacade
	- tipli\Model\Payouts\PayoutManager
	- tipli\Model\Payouts\PayoutRequestManager
	- tipli\AdminModule\PayoutsModule\Components\GroupConfirmationControl\IGroupConfirmationControlFactory
	- tipli\AdminModule\PayoutsModule\Forms\IAddCommentControlFactory
	- tipli\AdminModule\PayoutsModule\Forms\IPayoutBonusAmountControlFactory
	- tipli\AdminModule\PayoutsModule\Forms\IPayoutControlFactory
	- tipli\AdminModule\PayoutsModule\UploadPaymentsControl\IUploadPaymentsControlFactory

	- tipli\AdminModule\Payouts2Module\Forms\PayoutsForm\IPayoutsForm

	- tipli\AdminModule\Payouts2Module\Forms\PayoutsFilterForm\IPayoutsFilterFormFactory

	commentRepository:
		class: tipli\Model\Payouts\Repositories\CommentRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Payouts\Entities\Comment')

	- tipli\Model\Payouts\Subscribers\PayoutSubscriber

	# Bank
	- tipli\Model\Bank\Exporters\BcrPaymentsExporter
	- tipli\Model\Bank\Exporters\IngPaymentsExporter
	- tipli\Model\Bank\Exporters\FioNationalPaymentsExporter
	- tipli\Model\Bank\Exporters\FioEuroPaymentsExporter
	- tipli\Model\Bank\Exporters\WisePaymentsExporter
	- tipli\Model\Bank\Exporters\ErstePaymentsExporter
	- tipli\Model\Bank\PaymentExporterFactory

	# Rewards
	- tipli\Model\Rewards\RewardFacade
	- tipli\Model\Rewards\MoneyRewardFacade
	- tipli\Model\Rewards\ShareRewardFacade
	- tipli\AdminModule\RewardsModule\Forms\IShareRewardControlFactory
	- tipli\AdminModule\RewardsModule\Forms\IRewardCampaignControlFactory
	- tipli\Model\Shops\RewardFilter
	- tipli\Model\Rewards\AdvantageInPercentageFilter(%minimalShareCoefficients%)
	- tipli\Model\Rewards\CashbackCacheKeyResolver(%minimalShareCoefficients%)
	- tipli\Model\Rewards\EntranceCampaignResolver
	- tipli\Model\Rewards\AddonBonusResolver
	- tipli\AdminModule\RewardsModule\Forms\ConstantRewardControl\IConstantRewardControlFactory

	- tipli\Model\Rewards\Subscribers\RewardSubscriber
	- tipli\Model\Rewards\ConstantRewardManager

	# Currencies
	- tipli\Model\Currencies\CurrencyFacade
	- tipli\Model\Currencies\CurrencyFilter

	# PartnerSystems
	- tipli\Model\PartnerSystems\PartnerSystemFacade
	- tipli\Model\PartnerSystems\NetworkFactory

	- tipli\Model\PartnerSystems\Networks\IAnetFactory
	- tipli\Model\PartnerSystems\Networks\IAffilBoxFactory
	- tipli\Model\PartnerSystems\Networks\IZonkyAffilBoxFactory
	- tipli\Model\PartnerSystems\Networks\IAffiliateWindowFactory
	- tipli\Model\PartnerSystems\Networks\IAliexpressFactory
	- tipli\Model\PartnerSystems\Networks\IBonprixFactory
	- tipli\Model\PartnerSystems\Networks\IBookingFactory
	- tipli\Model\PartnerSystems\Networks\IRentalcarsFactory
	- tipli\Model\PartnerSystems\Networks\IRetailAdsFactory
	- tipli\Model\PartnerSystems\Networks\ICJFactory
	- tipli\Model\PartnerSystems\Networks\IDognetFactory
	- tipli\Model\PartnerSystems\Networks\IDognetNewFactory
	- tipli\Model\PartnerSystems\Networks\IKiwiFactory
	- tipli\Model\PartnerSystems\Networks\IPostAffiliateProFactory
	- tipli\Model\PartnerSystems\Networks\IEHubFactory
	- tipli\Model\PartnerSystems\Networks\IESpolupraceFactory
	- tipli\Model\PartnerSystems\Networks\IHasOffersFactory
	- tipli\Model\PartnerSystems\Networks\INetAffiliationFactory
	- tipli\Model\PartnerSystems\Networks\IShareASaleFactory
	- tipli\Model\PartnerSystems\Networks\IZanoxFactory
	- tipli\Model\PartnerSystems\Networks\IEbayFactory
	- tipli\Model\PartnerSystems\Networks\ISalesMediaFactory
	- tipli\Model\PartnerSystems\Networks\INetSalesMediaFactory
	- tipli\Model\PartnerSystems\Networks\IWedosFactory
	- tipli\Model\PartnerSystems\Networks\IGrouponFactory
	- tipli\Model\PartnerSystems\Networks\ITradedoublerFactory
	- tipli\Model\PartnerSystems\Networks\ITradetrackerFactory
	- tipli\Model\PartnerSystems\Networks\IWebgainsFactory
	- tipli\Model\PartnerSystems\Networks\IFantasyObchodFactory
	- tipli\Model\PartnerSystems\Networks\IAdmitadFactory
	- tipli\Model\PartnerSystems\Networks\IAdtractionFactory
	- tipli\Model\PartnerSystems\Networks\IAlzaFactory
	- tipli\Model\PartnerSystems\Networks\IDovolenaFactory
	- tipli\Model\PartnerSystems\Networks\IInviaFactory
	- tipli\Model\PartnerSystems\Networks\IAtotoFactory
	- tipli\Model\PartnerSystems\Networks\IGearbestFactory
	- tipli\Model\PartnerSystems\Networks\IRidewaysFactory
	- tipli\Model\PartnerSystems\Networks\IWebepartnersFactory
	- tipli\Model\PartnerSystems\Networks\ISystem3Factory
	- tipli\Model\PartnerSystems\Networks\IConvertiserFactory
	- tipli\Model\PartnerSystems\Networks\IMojraFactory
	- tipli\Model\PartnerSystems\Networks\IPartnerizeFactory
	- tipli\Model\PartnerSystems\Networks\IPerformantFactory
	- tipli\Model\PartnerSystems\Networks\IRakutenFactory
	- tipli\Model\PartnerSystems\Networks\IBewitFactory
	- tipli\Model\PartnerSystems\Networks\IMyLeadFactory
	- tipli\Model\PartnerSystems\Networks\IProfitShareFactory
	- tipli\Model\PartnerSystems\Networks\IOptimiseFactory
	- tipli\Model\PartnerSystems\Networks\ITrifftFactory
	- tipli\Model\PartnerSystems\Networks\IAdHydraFactory
	- tipli\Model\PartnerSystems\Networks\ISystemPartnerskiFactory
	- tipli\Model\PartnerSystems\Networks\IImpactRadiusFactory
	- tipli\Model\PartnerSystems\Networks\IAffilmaxFactory
	- tipli\Model\PartnerSystems\Networks\IKwankoFactory
	- tipli\Model\PartnerSystems\Networks\IAltexFactory
	- tipli\Model\PartnerSystems\Networks\ICityAdsFactory
	- tipli\Model\PartnerSystems\Networks\ITripFactory
	- tipli\Model\PartnerSystems\Networks\IWeDareFactory
	- tipli\Model\PartnerSystems\Networks\IShopeeFactory
	- tipli\Model\PartnerSystems\Networks\IBuyBoxFactory
	- tipli\Model\PartnerSystems\Networks\IScaleoFactory
	- tipli\Model\PartnerSystems\Networks\IAmazonFactory
	- tipli\Model\PartnerSystems\Networks\IInis360Factory
	- tipli\Model\PartnerSystems\Networks\IQwertyFactory

	- tipli\Model\PartnerSystems\IncomeManager
	- tipli\ReportsModule\Forms\IIncomeControlFactory

	- tipli\AdminModule\PartnerSystemsModule\Forms\IPartnerSystemControlFactory
	- tipli\AdminModule\PartnerSystemsModule\Forms\ICreateAffilboxControlFactory

	# Admitad
	- tipli\Model\Admitad\AdmitadClient

	# Articles
	- tipli\AdminModule\ArticlesModule\Forms\IArticleControlFactory
	- tipli\AdminModule\ArticlesModule\Forms\IArticleRedactorControlFactory
	- tipli\AdminModule\ArticlesModule\Forms\IAddRelatedArticleControlFactory
	- tipli\Model\Articles\ArticleFacade
	- tipli\Model\Articles\ArticlesRecommender
	- tipli\Model\Articles\RelatedArticleManager
	- tipli\AdminModule\ArticlesModule\Forms\IRemoveArticleControlFactory
	- tipli\Model\HtmlBuilders\ArticleTemplateBuilder

	# Jobs
	- tipli\AdminModule\JobsModule\Forms\IJobControlFactory
	- tipli\Model\Jobs\JobFacade
	- tipli\Model\Jobs\JobManager
	- implement: tipli\FrontModule\Jobs\IResponseFormFactory
	  arguments: [%freshdesk.jobs.email%]

	- implement: tipli\NewFrontModule\Jobs\IResponseFormFactory
	  arguments: [%freshdesk.jobs.email%]

	BannerRepository:
		class: tipli\Model\Marketing\Repositories\BannerRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\Banner')

	- tipli\Model\Marketing\SocialShareLinkFactory
	- tipli\Model\Marketing\BannerFacade
	- tipli\Model\Marketing\BannerManager
	- tipli\AdminModule\MarketingModule\Forms\IBannerControlFactory
	- tipli\Model\Marketing\BannersRecommender
	- tipli\Model\Marketing\GoogleAnalyticsClient(%googleAnalyticsViewIds%)
	- tipli\Model\Marketing\FacebookEventsClient(%facebook.pixelId%, %facebook.app.accessToken%)

	BannerClickRepository:
		class: tipli\Model\Marketing\Repositories\BannerClickRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\BannerClick')

	RegistrationPageRepository:
		class: tipli\Model\Marketing\Repositories\RegistrationPageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\RegistrationPage')

	- tipli\Model\Marketing\RegistrationPageManager
	- tipli\AdminModule\MarketingModule\Forms\IRegistrationPageControlFactory
	- tipli\Model\Marketing\RegistrationPageFacade

	# Builders / generatos
	- tipli\Model\HtmlBuilders\NewsletterGenerator
	- tipli\Model\HtmlBuilders\LatteBuilder

	# Notification template
	- tipli\Model\Templates\NotificationTemplateFacade
	- tipli\Model\Templates\NotificationTemplateParameterFacade
	- tipli\Model\Templates\Managers\NotificationTemplateManager
	- tipli\Model\Templates\Managers\NotificationTemplateParameterManager
	- tipli\Model\Templates\NotificationTemplateGenerator

	- tipli\AdminModule\TemplatesModule\Components\Notification\NotificationTemplateControl\NotificationTemplateControlFactory

	NotificationTemplateRepository:
		class: tipli\Model\Templates\Repositories\NotificationTemplateRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Templates\Entities\NotificationTemplate')

	NotificationTemplateParameterRepository:
		class: tipli\Model\Templates\Repositories\NotificationTemplateParameterRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Templates\Entities\NotificationTemplateParameter')

	# Newsletter
	NewsletterRepository:
		class: tipli\Model\Marketing\Repositories\NewsletterRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\Newsletter')

	NewsletterBlockRepository:
		class: tipli\Model\Marketing\Repositories\NewsletterBlockRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\NewsletterBlock')

	ContentSectionRepository:
		class: tipli\Model\Marketing\Repositories\ContentSectionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\ContentSection')

	ContentSectionBlockRepository:
		class: tipli\Model\Marketing\Repositories\ContentSectionBlockRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\ContentSectionBlock')

	- tipli\Model\Marketing\NewsletterFacade
	- tipli\Model\Marketing\NewsletterManager
	- tipli\Model\Marketing\ContentSectionFacade
	- tipli\Model\Marketing\ContentSectionManager
	- tipli\Model\Marketing\ContentSectionBlockFacade
	- tipli\Model\Marketing\ContentSectionBlockManager
	- tipli\Model\Marketing\NewsletterBlockDataResolver

	- tipli\AdminModule\MarketingModule\Forms\INewsletterControlFactory
	- tipli\AdminModule\MarketingModule\Forms\INewsletterBlockControlFactory
	- tipli\AdminModule\MarketingModule\Forms\INewsletterBlockDetailControlFactory
	- tipli\AdminModule\MarketingModule\Components\ContentSection\ContentSectionControlFactory
	- tipli\AdminModule\MarketingModule\Components\ContentSectionBlock\ContentSectionBlockControlFactory

	# Newsletter parameter
	- tipli\Model\Marketing\NewsletterParameterFacade
	- tipli\Model\Marketing\Managers\NewsletterParameterManager

	NewsletterParameterRepository:
		class: tipli\Model\Marketing\Repositories\NewsletterParameterRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Marketing\Entities\NewsletterParameter')

	# FRONT Components
	- tipli\FrontModule\Components\IMainNavbarFactory
	- tipli\FrontModule\Components\IAqPopupFactory
	- tipli\FrontModule\Components\IDropdownTagsSummaryFactory
	- tipli\FrontModule\Components\IFooterTagsSummaryFactory
	- tipli\FrontModule\Components\IFooterShopsSummaryFactory
	- tipli\FrontModule\Components\ISocialLoginButtonsFactory
	- tipli\FrontModule\Components\IBannerCarouselControlFactory
	- tipli\FrontModule\Components\ICampaignProgressBarControlFactory
	- tipli\NewFrontModule\Components\ICampaignProgressBarControlFactory

	# NEW FRONT Components
	- tipli\NewFrontModule\Components\AqPopup\AqPopupFactory
	- tipli\NewFrontModule\Components\ISocialLoginButtonsFactory
	- tipli\NewFrontModule\DealsModule\Components\IDealDetailControlFactory
	- tipli\NewFrontModule\Components\ContentSections\ContentSectionItem\ContentSectionItemFactory
	- tipli\NewFrontModule\Components\AddonPromo\AddonPromoFactory
	- tipli\NewFrontModule\Components\FirstPurchasePromo\FirstPurchasePromoFactory
	# NEW FRONT Components - banners
	- tipli\NewFrontModule\Components\Banners\BannerItem\BannerItemFactory
	- tipli\NewFrontModule\Components\Banners\BannerSlider\BannerSliderFactory
	- tipli\NewFrontModule\Components\Banners\MiniBanners\MiniBannersFactory
	# NEW FRONT Components - shops
	- tipli\NewFrontModule\ShopsModule\Components\ShopItem\ShopItemFactory

	# FRONT Forms
	- tipli\FrontModule\Forms\IContactControlFactory
	- tipli\NewFrontModule\Forms\IContactControlFactory
	- tipli\FrontModule\Forms\IAccountDeletionRequestControlFactory
	- tipli\FrontModule\Forms\ISignUpControlFactory
	- tipli\FrontModule\Forms\ISignInControlFactory
	- tipli\FrontModule\Forms\IForgottenPasswordControlFactory
	- tipli\FrontModule\Forms\INewPasswordControlFactory
	- tipli\FrontModule\Forms\ISearchControlFactory
	- tipli\FrontModule\Forms\IEmailSignUpControlFactory
	- tipli\NewFrontModule\Forms\IEmailSignUpControlFactory
	- tipli\FrontModule\Forms\IVasaVoucherControlFactory
	- tipli\FrontModule\ShopsModule\Forms\IAliexpressCashbackCheckControlFactory

	# Reviews
	- tipli\Model\Reviews\ReviewFacade
	- tipli\Model\Reviews\ReviewManager
	- tipli\Model\Reviews\ReviewRequestManager
	- tipli\Model\Reviews\Subscribers\ReviewRequestSubscriber
	- tipli\Model\Reviews\ReviewRequestProcessor

	- tipli\FrontModule\AccountModule\Forms\IReviewControlFactory
	- tipli\FrontModule\AccountModule\Forms\ReviewRequest\IReviewRequestControlFactory
	- tipli\Model\Reviews\FacebookReviewsProvider(%facebook.app.app_id%, %facebook.app.app_secret%, %facebook.app.accessToken%)
	- tipli\NewFrontModule\AccountModule\Forms\ReviewControlFactory

	# Reviews Admin
	- tipli\AdminModule\ReviewsModule\Forms\ReviewControl\ReviewControlFactory

	- tipli\AdminModule\ReviewModule\Components\ReviewNote\ReviewNoteControlFactory

	# Browser activities
	- tipli\Model\BrowserActivities\BrowserActivityFacade
	- tipli\Model\BrowserActivities\BrowserActivityManager
	- tipli\Model\BrowserActivities\Subscribers\BrowserActivitySubscriber

	# Repositories

	localizationLocalizationRepository:
		class: tipli\Model\Localization\Repositories\LocalizationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Localization\Entities\Localization')

	redirectionsRedirectionRepository:
		class: tipli\Model\Redirections\Repositories\RedirectionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Redirections\Entities\Redirection')

	questionsGroupRepository:
		class: tipli\Model\Questions\Repositories\GroupRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Questions\Entities\Group')

	questionsQuestionRepository:
		class: tipli\Model\Questions\Repositories\QuestionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Questions\Entities\Question')

	imagesImageRepository:
		class: tipli\Model\Images\Repositories\ImageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Images\Entities\Image')

	imagesPictureRepository:
		class: tipli\Model\Pictures\Repositories\PictureRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Pictures\Entities\Picture')

	accountUserRepository:
		class: tipli\Model\Account\Repositories\UserRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\User')

	AdminVisitRepository:
		class: tipli\Model\Account\Repositories\AdminVisitRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\AdminVisit')

	partnerOrganizationRepository:
		class: tipli\Model\PartnerOrganizations\Repositories\PartnerOrganizationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\PartnerOrganizations\Entities\PartnerOrganization')

	tagsTagRepository:
	  class: tipli\Model\Tags\Repositories\TagRepository
	  factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Tags\Entities\Tag')

	tagsTagGroupRepository:
	  class: tipli\Model\Tags\Repositories\TagGroupRepository
	  factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Tags\Entities\TagGroup')

	shopsShopRepository:
	   class: tipli\Model\Shops\Repositories\ShopRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\Shop')

	shopReportRepository:
	   class: tipli\Model\Shops\Repositories\ShopReportRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopReport')

	shopsShopPartnerSystemRepository:
	   class: tipli\Model\Shops\Repositories\ShopPartnerSystemRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\PartnerSystem')

	shopsRelatedShopRepository:
	   class: tipli\Model\Shops\Repositories\RelatedShopRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\RelatedShop')

	shopsForeignShopRepository:
	   class: tipli\Model\Shops\Repositories\ForeignShopRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ForeignShop')

	shopProductFeedRepository:
		class: tipli\Model\Shops\Repositories\ShopProductFeedRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopProductFeed')

	shopsOfferRepository:
	   class: tipli\Model\Shops\Repositories\OfferRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\Offer')

	shopNoteRepository:
		class: tipli\Model\Shops\Repositories\ShopNoteRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopNote')

	- tipli\Model\Shops\InternalNoteFacade
	- tipli\Model\Shops\InternalNoteManager
	- tipli\AdminModule\ShopsModule\Forms\InternalNoteForm\InternalNoteFormFactory
	- tipli\AdminModule\Components\Shop\InternalNotesControl\InternalNotesControlFactory

	internalNoteRepository:
		class: tipli\Model\Shops\Repositories\InternalNoteRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\InternalNote')

	shopQuestionRepository:
		class: tipli\Model\Shops\Repositories\ShopQuestionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopQuestion')

	shopChangeRepository:
	   class: tipli\Model\Shops\Repositories\ShopChangeRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopChange')

	descriptionBlockRepository:
	   class: tipli\Model\Shops\Repositories\DescriptionBlockRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\DescriptionBlock')

	shopsRedirectionRepository:
	   class: tipli\Model\Shops\Repositories\RedirectionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\Redirection')

	shopsStoreRepository:
	   class: tipli\Model\Shops\Repositories\StoreRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\Store')

	shopsContactRepository:
	   class: tipli\Model\Shops\Repositories\ContactRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\Contact')

	shopCheckerActivityepository:
	   class: tipli\Model\Shops\Repositories\ShopCheckerActivityRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopCheckerActivity')

	shopCheckerFailRepository:
	   class: tipli\Model\Shops\Repositories\ShopCheckerFailRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopCheckerFail')

	shopPauseLogRepository:
	   class: tipli\Model\Shops\Repositories\ShopPauseLogRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopPauseLog')

	shopCashbackCorrectionRuleRepository:
	   class: tipli\Model\Shops\Repositories\CashbackCorrectionRuleRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\CashbackCorrectionRule')

	partnerSystemsPartnerSystemsRepository:
	   class: tipli\Model\PartnerSystems\Repositories\PartnerSystemRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\PartnerSystems\Entities\PartnerSystem')

	transactionsTransactionRepository:
	   class: tipli\Model\Transactions\Repositories\TransactionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\Transaction')

	transactionsTransactionProcessRepository:
	   class: tipli\Model\Transactions\Repositories\TransactionProcessRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\TransactionProcess')

	transactionsSuspectedTransactionRepository:
	   class: tipli\Model\Transactions\Repositories\SuspectedTransactionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\SuspectedTransaction')

	transactionsCorrectionRepository:
	   class: tipli\Model\Transactions\Repositories\CorrectionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\Correction')

	transactionsArchivedTransactionRepository:
	   class: tipli\Model\Transactions\Repositories\ArchivedTransactionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\ArchivedTransaction')

	transactionsTransactionCommentRepository:
	  class: tipli\Model\Transactions\Repositories\CommentRepository
	  factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\Comment')

	importedTransactionFailRepository:
	  class: tipli\Model\Transactions\Repositories\ImportedTransactionFailRepository
	  factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Transactions\Entities\ImportedTransactionFail')

	payoutsPayoutRepository:
	   class: tipli\Model\Payouts\Repositories\PayoutRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Payouts\Entities\Payout')

	rewardsConstantRewardRepository:
	   class: tipli\Model\Rewards\Repositories\ConstantRewardRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\ConstantReward')

	rewardsMoneyRewardRepository:
	   class: tipli\Model\Rewards\Repositories\MoneyRewardRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\MoneyReward')

	rewardsMoneyRewardCampaignRepository:
	   class: tipli\Model\Rewards\Repositories\MoneyRewardCampaignRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\MoneyRewardCampaign')

	rewardsShareRewardCampaignRepository:
	   class: tipli\Model\Rewards\Repositories\ShareRewardCampaignRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\ShareRewardCampaign')

	rewardsRewardCampaignRepository:
	   class: tipli\Model\Rewards\Repositories\RewardCampaignRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\RewardCampaign')

	rewardsShareRewardRepository:
	   class: tipli\Model\Rewards\Repositories\ShareRewardRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards\Entities\ShareReward')

	utmUtmRepository:
	   class: tipli\Model\Utm\Repositories\UtmRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Utm\Entities\Utm')

	utmUtmCostRepository:
	   class: tipli\Model\Utm\Repositories\UtmCostRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Utm\Entities\UtmCost')

	messagesEmailRepository:
	   class: tipli\Model\Messages\Repositories\EmailRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\Email')

	messagesBlockedEmailRepository:
	   class: tipli\Model\Messages\Repositories\BlockedEmailRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\BlockedEmail')

	messagesSmsRepository:
	   class: tipli\Model\Messages\Repositories\SmsRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Messages\Entities\Sms')

	articlesArticleRepository:
	   class: tipli\Model\Articles\Repositories\ArticleRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Articles\Entities\Article')

	articlesRelatedArticleRepository:
	   class: tipli\Model\Articles\Repositories\RelatedArticleRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Articles\Entities\RelatedArticle')

	reviewRepository:
		class: tipli\Model\Reviews\Repositories\ReviewRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reviews\Entities\Review')

	reviewRequestRepository:
		class: tipli\Model\Reviews\Repositories\ReviewRequestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reviews\Entities\ReviewRequest')

	BrowserActivityRepository:
		class: tipli\Model\BrowserActivities\Repositories\BrowserActivityRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\BrowserActivities\Entities\BrowserActivity')
	BrowserTokenRepository:
		class: tipli\Model\BrowserActivities\Repositories\BrowserTokenRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\BrowserActivities\Entities\BrowserToken')

	IncomeRepository:
		class: tipli\Model\PartnerSystems\Repositories\IncomeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\PartnerSystems\Entities\Income')

	PartnerSystemProcessErrorRepository:
		class: tipli\Model\PartnerSystems\Repositories\ProcessErrorRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\PartnerSystems\Entities\ProcessError')

	ShortcutRepository:
		class: tipli\Model\Shortener\Repositories\ShortcutRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shortener\Entities\Shortcut')

	RegionRepository:
		class: tipli\Model\Regions\Repositories\RegionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Regions\Entities\Region')

	JobRepository:
		class: tipli\Model\Jobs\Repositories\JobRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Jobs\Entities\Job')

	ShopProductRepository:
		class: tipli\Model\Products\Repositories\ShopProductRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Products\Entities\ShopProduct')

	ProductRepository:
		class: tipli\Model\Products\Repositories\ProductRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Products\Entities\Product')

	BrandRepository:
		class: tipli\Model\Products\Repositories\BrandRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Products\Entities\Brand')

	CompetitorKeywordRepository:
		class: tipli\Model\Reports\Repositories\CompetitorKeywordRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\Keyword')

	CompetitorKeywordPositionRepository:
		class: tipli\Model\Reports\Repositories\CompetitorKeywordPositionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\KeywordPosition')

	# Layers
	- tipli\Model\Layers\UtmLayer
	- tipli\Model\Layers\BrowserTokenLayer
	- tipli\Model\Layers\ClientLayer
	- tipli\Model\Layers\DeviceDetector
	- tipli\Model\Layers\ParentReferencingLayer
	- tipli\Model\Layers\BotLayer
	- tipli\Model\Layers\AccessTokenLayer
	- tipli\Model\Layers\RegistrationSourceLayer
	- tipli\Model\Layers\MobileAccessTokenLayer
	- tipli\Model\Layers\BotDetector
	- tipli\Model\Layers\EventLayer
	- tipli\Model\Layers\GoogleAnalyticsHitClient
	- tipli\Model\Layers\SuspectedRequestDetector
	- tipli\Model\Layers\RondoDataLayer
	- tipli\Model\Layers\RedirectionLayer
	- tipli\Model\Layers\SafariSessionTokenLayer

	# Session
	- tipli\Model\Session\SectionFactory

	# Events
	- tipli\Model\NewRelic\NewRelicProfilingSubscriber

	- tipli\Model\Sentry\SentrySubscriber

	- tipli\Model\Subscribers\RefererSubscriber
	- tipli\Model\Subscribers\ParentUserSubscriber
	- tipli\Model\Subscribers\EntrancePageSubscriber
	- tipli\Model\Subscribers\PartnerOrganizationSubscriber
	- tipli\Model\Subscribers\AccessTokenSubscriber
	- tipli\Model\Subscribers\RegistrationSubscriber
	- tipli\Model\Subscribers\FbclidSubscriber
	- tipli\Model\Subscribers\UtmSubscriber
	- tipli\Model\Subscribers\RondoDataSubscriber
	- tipli\Model\Subscribers\SafariSessionSubscriber

	- tipli\Model\Marketing\Subscribers\FacebookEventsSubscriber

	# Security
	- tipli\Security\Hasher
	- tipli\Security\PasswordAuthenticator
	- tipli\Security\AccessTokenAuthenticator
	- tipli\Security\FacebookAuthenticator
	- tipli\Security\GoogleAuthenticator
	- tipli\Security\AppleAuthenticator
	- tipli\Security\OAuthAuthenticator

	# Facebook
	- Facebook\Facebook(%facebook.app%)

	# UTM
	- tipli\Model\Utm\UtmFacade
	- tipli\Model\Utm\UtmManager
	- tipli\Model\Utm\UtmCostsManager
	- tipli\Model\Utm\Importers\FbCostsImporter(%facebook.app.app_id%, %facebook.app.app_secret%, %facebook.app.accessToken%, %facebook.app.adAccountIds%)
	- tipli\Model\Utm\Importers\AdwordsCostsImporter

	# UTM
	- tipli\Model\SitemapGenerator
	- tipli\Model\Sitemap\SitemapGenerator
	- tipli\Model\Sitemap\NewSitemapGenerator
	- tipli\Model\AddonDataGenerator

	# Commands
	- tipli\Commands\SendSms
	- tipli\Commands\SendDailyScheduledEmails
	- tipli\Commands\SendScheduledEmails
	- tipli\Commands\SendTwoHoursScheduledEmails
	- tipli\Commands\PayoutConfirmation
	- tipli\Commands\ReadSqs
	- tipli\Commands\UpdateDbDataAtNight
	- tipli\Commands\UpdateDbDataEveryHour
	- tipli\Commands\SynchronizeDatadogDbData
	- tipli\Commands\CleanDbData
	- tipli\Commands\CleanOldResourceFiles(%webtempDir%)
	- tipli\Commands\UpdateAlgolia
	- tipli\Commands\UpdateTickEntries
	- tipli\Commands\UpdateCollabimData
	- tipli\Commands\ProcessPartnerSystems
	- tipli\Commands\ProcessPartnerSystemSales
	- tipli\Commands\ImportFacebookUtmCosts
	- tipli\Commands\ImportAdwordsUtmCosts
	- tipli\Commands\ImportMoreFinanci
	- tipli\Commands\ImportFacebookReviews
	- tipli\Commands\ProcessMailgunEvents
	- tipli\Commands\ProcessMailchimpCampaigns
	- tipli\Commands\ProcessMandrillEvents
	- tipli\Commands\ProcessMailchimpUsers
	- tipli\Commands\ProcessMailkitUsers
	- tipli\Commands\ProcessMailkitEvents
	- tipli\Commands\StartChallengeMailchimpUsers
	- tipli\Commands\StopChallengeMailchimpUsers
	- tipli\Commands\FetchFreshdeskTickets
	- tipli\Commands\SynchronizeFreshdeskTickets
	- tipli\Commands\SynchronizeStores
	- tipli\Commands\AnalyzeUsers
	- tipli\Commands\SaveContentSnapshot
	- tipli\Commands\ProcessFblEvents
	- tipli\Commands\ProcessLeaflets
	- tipli\Commands\SynchronizeSegmentData
	- tipli\Commands\ProcessNotificationCampaigns
	- tipli\Commands\UpdateShopData
	- tipli\Commands\UpdateBannersStats
	- tipli\Commands\UpdateDealsStats
	- tipli\Commands\ProcessPageExtensions
	- tipli\Commands\ProcessPageExtensionsHtml
	- tipli\Commands\ProcessEhubUsers
	- tipli\Commands\NotificationGarbageCollector
	- tipli\Commands\RemoveLeaflets
	- tipli\Commands\CompetitorsUpdateTipliOffers
	- tipli\Commands\ProcessMetrics
	- tipli\Commands\ProcessMetricsSaveQueue
	- tipli\Commands\RefreshCampaignMetrics
	- tipli\Commands\SynchronizeMetrics
	- tipli\Commands\ProcessShopChecker
	- tipli\Commands\SynchronizeZasilkovnaApprovals
	- tipli\Commands\SynchronizeFioPayments
	- tipli\Commands\SynchronizePayments
	- tipli\Commands\ImportVisits(%googleAnalyticsViewIds%)
	- tipli\Commands\UpdateSeoKeywords
	- tipli\Commands\ProcessEmailCampaignStats
	- tipli\Commands\CheckShopDescriptions
	- tipli\Commands\ProcessDataSnapshot
	- tipli\Commands\ProcessGooglebotLog
	- tipli\Commands\ProcessAccessErrorLog
	- tipli\Commands\ProcessAccessLog
	- tipli\Commands\SendUserRewardToNewUsersNotification
	- tipli\Commands\SendUserRewardExpirationNotification
	- tipli\Commands\ClearBrowserTokens
	- tipli\Commands\PushNotifications
	- tipli\Commands\UpdateDbStats
	- tipli\Commands\ProcessGroups
	- tipli\Commands\EnqueueTriggers
	- tipli\Commands\ProcessMonitors
	- tipli\Commands\ProcessPartnerSystemMonitors
	- tipli\Commands\ProcessAutomaticCashbackDeals
	- tipli\Commands\ScheduleAutoresponderTriggers
	- tipli\Commands\WarmUpCache(%appDir%)
	- tipli\Commands\ProcessOneSignalNotificationStats
	- tipli\Commands\ResetOpcache
	- tipli\Commands\ProcessApifyTaskReport
	- tipli\Commands\RemoveUnusedTranslations(%appDir%, %translations.localeDir%)
	- tipli\Commands\ProcessProductDeals
	- tipli\Commands\CreateTwistoPayouts
	- tipli\Commands\AnonymizeTwistoEmails
	- tipli\Commands\ProcessAdmitadHotProducts
	- tipli\Commands\ProcessShopProductFeed
	- tipli\Commands\RememberTransactionStates
	- tipli\Commands\SynchronizeShopVisits
	- tipli\Commands\EncodeArticleLinks
	- tipli\Commands\ElasticCreateIndex
	- tipli\Commands\ProcessRefunds
	- tipli\Commands\Sandbox
	- tipli\Commands\SynchronizeImportedTransactions
#	- tipli\Commands\SendReviewNotifications
	- tipli\Commands\ImportSqlData
	- tipli\Commands\ScheduleAutoresponders
	- tipli\Commands\ProcessProductCategories
	- tipli\Commands\FetchSmartsuppConversations
	- tipli\Commands\SynchronizeSmartsuppConversations
	- tipli\Commands\SynchronizeShopsToGithub
	- tipli\Commands\AssignTagsToProducts
	- tipli\Commands\ProcessProductTags
	- tipli\Commands\SynchronizeShopReports
	- tipli\Commands\ProcessImportedTransactions
	- tipli\Commands\FixRedirectChains
	- tipli\Commands\ProcessProductChecker
	- tipli\Commands\SendDailyStats
	- tipli\Commands\LoadSamples
	- tipli\Commands\SendOpsGeniePingEmails
	- tipli\Commands\ExpireTransactions
	- tipli\Commands\RunWebPageTests
	- tipli\Commands\CreateCompetitorsReport
	- tipli\Commands\RemoveOldData
	- tipli\Commands\ArchiveLeaflets
	- tipli\Commands\ArchiveRefunds
	- tipli\Commands\RemoveProducts
	- tipli\Commands\SynchronizeCampaignBonuses
	- tipli\Commands\SynchronizePaidTransactions
	- tipli\Commands\RemoveDealImages
	- tipli\Commands\Test
	- tipli\Commands\UnsubscribeNewsletters
	- tipli\Commands\RemoveDealPictures
	- tipli\Commands\ProcessProducts
	- tipli\Commands\RemoveProductImages
	- tipli\Commands\ProcessReviewRequests
	- tipli\Commands\GenerateMissingText
	- tipli\Commands\UpdateForeignShops
	- tipli\Commands\UpdateRelatedShops
	- tipli\Commands\ProcessFreshdeskRequest
	- tipli\Commands\ProcessShopTriggers
	- tipli\Commands\UnsubscribeMailchimpUsers
	- tipli\Commands\UnsubscribeMailkitUsers
	- tipli\Commands\ProcessShopExternalData
	- tipli\Commands\ConfirmPayouts
	- tipli\Commands\SortShops
	- tipli\Commands\ProcessSteveShops
	- tipli\Commands\ProcessLuckyShopCampaign
	- tipli\Commands\ProcessLuckyShopRewards
	- tipli\Commands\UpdateShopKeywords
	- tipli\Commands\ProcessQueuesSqlQueries
	- tipli\Commands\CalculateUserShopInterests
	- tipli\Commands\SynchronizeEmailCampaigns
	- tipli\Commands\ProcessEmailCampaigns
	- tipli\Commands\SynchronizeNotificationCampaignsStats

	# Files
	- tipli\Model\Files\FileStorage
	- tipli\Model\Files\FileFacade

	FileRepository:
		class: tipli\Model\Files\Repositories\FileRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Files\Entities\File')

	# Seo
	- tipli\Model\Seo\PageExtensionManager
	- tipli\Model\Seo\SeoFacade
	- tipli\AdminModule\SeoModule\Forms\IPageExtensionControlFactory
	- tipli\AdminModule\SeoModule\Forms\IExportKeywordsControlFactory
	- tipli\Model\Seo\PageExtensionResolver
	- tipli\Model\Seo\SitemapManager
	- tipli\Model\Seo\VisitManager
	- tipli\Model\Seo\KeywordManager

	PageExtensionRepository:
		class: tipli\Model\Seo\Repositories\PageExtensionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Seo\Entities\PageExtension')

	PageExtensionKeywordRepository:
		 class: tipli\Model\Seo\Repositories\PageExtensionKeywordRepository
		 factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Seo\Entities\PageExtensionKeyword')

	KeywordRepository:
		  class: tipli\Model\Seo\Repositories\KeywordRepository
		  factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Seo\Entities\Keyword')

	SitemapUrlRepository:
		class: tipli\Model\Seo\Repositories\SitemapUrlRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Seo\Entities\SitemapUrl')

	- tipli\Model\Seo\Subscribers\SitemapSubscriber

	VisitSeoRepository:
		 class: tipli\Model\Seo\Repositories\VisitRepository
		 factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Seo\Entities\Visit')

	# Leaflets
	- tipli\Model\Leaflets\LeafletFacade
	- tipli\Model\Leaflets\LeafletManager
	- tipli\Model\Leaflets\LeafletClient
	- tipli\AdminModule\LeafletsModule\Forms\ILeafletControlFactory

	Leaflet:
		class: tipli\Model\Leaflets\Repositories\LeafletRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Leaflets\Entities\Leaflet')
	LeafletPage:
		class: tipli\Model\Leaflets\Repositories\LeafletPageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Leaflets\Entities\LeafletPage')
	ShopIndex:
		class: tipli\Model\Leaflets\Repositories\LeafletShopIndexRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Leaflets\Entities\ShopIndex')

	# Steve
	- tipli\Model\Steve\SteveClient
	- tipli\Model\Steve\SteveFacade
	- tipli\Model\Steve\SteveManager

	# ChatGPT
	- tipli\Model\ChatGPT\ChatGPTClient

	# PhoneNumber verification code
	- tipli\FrontModule\AccountModule\Forms\IPhoneNumberVerificationControlFactory
	- tipli\Model\Account\PhoneNumberVerificationManager

	PhoneNumberVerificationCodeRepository:
		class: tipli\Model\Account\Repositories\PhoneNumberVerificationCodeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Account\Entities\PhoneNumberVerificationCode')

	# Logs
	- tipli\Model\Commands\LogManager
	- tipli\Model\Commands\LogFacade
	- tipli\Model\Log\BadRequestManager
	- tipli\Model\Log\Repositories\GooglebotManager
	- tipli\Model\Log\WebhookManager

	commandLogRepository:
		class: tipli\Model\Commands\Repositories\LogRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Commands\Entities\Log')

	# Addon
	- tipli\Model\Addon\AddonFacade
	- tipli\Model\Addon\ShopVisitManager
	- tipli\Model\Addon\FeedbackManager
	- tipli\Model\Addon\FeedbackFacade
	- tipli\Model\Addon\ShopsProvider
	- tipli\Model\Addon\FeedBuilders\PagesResponseBuilder
	- tipli\Model\Addon\FeedBuilders\PageResponseBuilder
	- tipli\Model\Addon\FeedBuilders\GoogleResponseBuilder
	- tipli\Model\Addon\FeedBuilders\SeznamResponseBuilder
	- tipli\Model\Addon\FeedBuilders\HeurekaResponseBuilder
	- tipli\Model\Addon\FeedBuilders\ZboziResponseBuilder
	- tipli\Model\Addon\FeedBuilders\AddonResponseBuilder
	- tipli\Model\Addon\UserSettingsManager
	- tipli\Model\Addon\SearchQueryManager
	- tipli\Model\Addon\AlternativeOfferManager

	shopVisitRepository:
		class: tipli\Model\Addon\Repositories\ShopVisitRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Addon\Entities\ShopVisit')

	alternativeOfferRepository:
		class: tipli\Model\Addon\Repositories\AlternativeOfferRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Addon\Entities\AlternativeOffer')

	# Addon Elastic
	addonFeedbackRepository:
		class: tipli\Model\Addon\Repositories\FeedbackRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Addon\Entities\Feedback')

	addonUserSettingsRepository:
		class: tipli\Model\Addon\Repositories\UserSettingsRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Addon\Entities\UserSettings')

	- tipli\Model\Addon\Subscribers\AddonSubscriber

	# Inbox notifications
	- tipli\Model\Inbox\NotificationFacade
	- tipli\Model\Inbox\NotificationFactory
	- tipli\Model\Inbox\NotificationManager
	- tipli\AdminModule\InboxModule\Forms\INotificationCampaignControlFactory

	notificationRepository:
		class: tipli\Model\Inbox\Repositories\NotificationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Inbox\Entities\Notification')
	notificationCampaignRepository:
		class: tipli\Model\Inbox\Repositories\NotificationCampaignRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Inbox\Entities\NotificationCampaign')

	# Tickspot
	- tipli\Model\Tickspot\TickFacade
	- tipli\Model\Tickspot\TickClient
	- tipli\Model\Tickspot\EntryManager
	- tipli\AdminModule\Components\ITimeSpentControlFactory

	TickEntry:
		class: tipli\Model\Tickspot\Repositories\EntryRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Tickspot\Entities\Entry')

	# Collabim
	- tipli\Model\Collabim\CollabimClient
	- tipli\Model\Collabim\CollabimFacade
	- tipli\Model\Collabim\KeywordManager
	- tipli\Model\Collabim\PositionManager

	CollabimKeyword:
		class: tipli\Model\Collabim\Repositories\KeywordRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Collabim\Entities\Keyword')

	CollabimPosition:
		class: tipli\Model\Collabim\Repositories\PositionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Collabim\Entities\Position')

	# Log changes
	- tipli\Model\Log\LogFacade
	- tipli\Model\Commands\ChangeManager

	LogChange:
		class: tipli\Model\Log\Repositories\ChangeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Commands\Entities\Change')

	# Log versions
	- tipli\Model\Log\Subscribers\VersionSubscriber

	# Log events
	- tipli\Model\Log\EventManager

	- tipli\Model\Log\Subscribers\BadRequestSubscriber
	- tipli\Model\Log\Subscribers\WebhookSubscriber

	# Mobile request log
	- tipli\Model\Log\MobileRequestManager
	- tipli\Model\Log\MobileRequestFacade

	LogMobileRequest:
		class: tipli\Model\Log\Repositories\MobileRequestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Log\Entities\MobileRequest')

	# Suspected request log
	- tipli\Model\Log\SuspectedRequestManager
	- tipli\Model\Log\SuspectedRequestFacade

	SuspectedRequest:
		class: tipli\Model\Log\Repositories\SuspectedRequestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Log\Entities\SuspectedRequest')

	LogGooglebot:
		class: tipli\Model\Log\Repositories\GooglebotRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Log\Entities\Googlebot')

	# Forgotten password request log
	- tipli\Model\Log\ForgottenPasswordRequestManager
	- tipli\Model\Log\ForgottenPasswordRequestFacade

	ForgottenPasswordRequest:
		class: tipli\Model\Log\Repositories\ForgottenPasswordRequestRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Log\Entities\ForgottenPasswordRequest')

	# Deals
	- tipli\Model\Deals\DealManager
	- tipli\Model\Deals\RawDealManager
	- tipli\Model\Deal\CompetitiveDealManager
	- tipli\Model\Deals\VoteManager
	- tipli\Model\Deals\DealFacade

	- tipli\AdminModule\DealsModule\Forms\IDealControlFactory
	- tipli\AdminModule\DealsModule\Forms\IAssignTagsToDealsControlFactory
	- tipli\AdminModule\DealsModule\Forms\ICompetitiveDealControlFactory
	- tipli\FrontModule\DealsModule\Components\IDealControlFactory
	- tipli\FrontModule\DealsModule\Components\IVoteControlFactory
	- tipli\FrontModule\DealsModule\Components\IDealDetailControlFactory
	- tipli\NewFrontModule\DealsModule\Components\DealItem\DealItemFactory
	- tipli\NewFrontModule\DealsModule\Components\TopDailyDeals\TopDailyDealsFactory

	- tipli\Model\Deals\DealsRecommender
	- tipli\Model\Deals\DealNameFilter
	- tipli\Model\Deals\ProductDealManager

	DealsDeal:
	   class: tipli\Model\Deals\Repositories\DealRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Deals\Entities\Deal')

	DealsRawDeal:
	   class: tipli\Model\Deals\Repositories\RawDealRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Deals\Entities\RawDeal')

	DealsVote:
	   class: tipli\Model\Deals\Repositories\VoteRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Deals\Entities\Vote')

	CompetitiveDeal:
		class: tipli\Model\Deals\Repositories\CompetitiveDealRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Deals\Entities\CompetitiveDeal')

	DealImporter:
		class: tipli\Model\Deals\DealImporter

	# Competitors
	- tipli\Model\Competitors\CompetitorFacade
	- tipli\Model\Competitors\OfferNoteFacade
	- tipli\Model\Competitors\OfferManager
	- tipli\Model\Competitors\OfferNoteManager
	- tipli\Model\Competitors\CompanyManager
	- tipli\Model\Competitors\CompetitorsWebhookHandler
	- tipli\Model\Competitors\ReportManager

	- tipli\ReportsModule\Components\Competitor\Filter\CompetitorFilterControlFactory
	- tipli\ReportsModule\Components\Competitor\OfferNote\OfferNoteControlFactory

	CompetitorOfferNote:
	   class: tipli\Model\Competitors\Repositories\OfferNoteRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competitors\Entities\OfferNote')

	CompetitorOffer:
	   class: tipli\Model\Competitors\Repositories\OfferRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competitors\Entities\Offer')

	CompetitorReport:
	   class: tipli\Model\Competitors\Repositories\ReportRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competitors\Entities\Report')

	CompetitorCompany:
	   class: tipli\Model\Competitors\Repositories\CompanyRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competitors\Entities\Company')

	# Twisto
	- tipli\Model\Messages\TwistoClient

	- tipli\Model\Twisto\Subcribers\TransactionSubscriber

	# Rondo
	- tipli\Model\Rondo\RondoFacade
	- tipli\Model\Rondo\RondoClient
	- tipli\Model\Rondo\TransactionManager

	- tipli\Model\Rondo\Subscribers\TransactionSubscriber

	RondoTransaction:
	   class: tipli\Model\Rondo\Repositories\TransactionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rondo\Entities\Transaction')

	# Homecredit
	- tipli\Model\HomeCredit\HomeCreditFacade
	- tipli\Model\HomeCredit\HomeCreditClient
	- tipli\Model\HomeCredit\TransactionManager

	- tipli\Model\HomeCredit\Subscribers\TransactionSubscriber

	HomeCreditTransaction:
	   class: tipli\Model\HomeCredit\Repositories\TransactionRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\HomeCredit\Entities\Transaction')

	# rabbitmq
	- tipli\Model\RabbitMq\RabbitMqClient

	# Reports -- aggregated metrics
	- tipli\Model\Reports\AggregatedMetricFacade
	- tipli\Model\Reports\AggregatedMetricManager

	ReportsAggregatedMetric:
	   class: tipli\Model\Reports\Repositories\AggregatedMetricRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\AggregatedMetric')

	# Reports -- metrics
	- tipli\Model\Reports\MetricFacade
	- tipli\Model\Reports\MetricManager
	- tipli\Model\Reports\Subscribers\MetricSubscriber
	- tipli\Model\Reports\MetricsQueue

	ReportsMetric:
	   class: tipli\Model\Reports\Repositories\MetricRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\Metric')

	ReportsMetricValue:
	   class: tipli\Model\Reports\Repositories\MetricValueRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\MetricValue')

	# Reports - email campaign stats
	- tipli\Model\Reports\EmailCampaignFacade
	- tipli\Model\Reports\EmailCampaignManager
	- tipli\Model\Reports\EmailCampaignMetricManager
	- tipli\Model\Reports\NotificationCampaignFacade
	- tipli\Model\Reports\NotificationCampaignManager
	- tipli\Model\Reports\NotificationCampaignMetricManager
	- tipli\Model\Reports\NotificationCampaignProcessor

	EmailCampaign:
	   class: tipli\Model\Reports\Repositories\EmailCampaignRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\EmailCampaign')

	EmailCampaignMetric:
	   class: tipli\Model\Reports\Repositories\EmailCampaignMetricRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\EmailCampaignMetric')

	NotificationCampaign:
	   class: tipli\Model\Reports\Repositories\NotificationCampaignRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\NotificationCampaign')

	NotificationCampaignMetric:
	   class: tipli\Model\Reports\Repositories\NotificationCampaignMetricRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\NotificationCampaignMetric')

	# Reports - business plan
	- tipli\Model\Reports\BusinessPlanManager
	- tipli\Model\Reports\BusinessPlanFacade
	- tipli\ReportsModule\Forms\IBusinessPlanControlFactory

	ReportsBusinessPlan:
	   class: tipli\Model\Reports\Repositories\BusinessPlanRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\BusinessPlan')

	# Reports -- campaign metrics
	- tipli\Model\Reports\CampaignMetricFacade
	- tipli\Model\Reports\CampaignMetricManager
	- tipli\Model\Reports\CampaignMetricCalculator
	- tipli\Model\Reports\CampaignMetricsDataProvider

	ReportsCampaignMetric:
		class: tipli\Model\Reports\Repositories\CampaignMetricRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\CampaignMetric')

	- tipli\Model\Reports\Subscribers\CampaignMetricSubscriber

	# Reports -- data snapshot
	DataSnapshot:
	   class: tipli\Model\Reports\Repositories\DataSnapshotRepository
	   factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\DataSnapshot')

	- tipli\Model\Reports\ReportFacade
	- tipli\Model\Reports\DataSnapshotManager

	# Conditions
	- tipli\Model\Conditions\ConditionsFacade
	- tipli\Model\Conditions\ApprovalManager

	ConditionsDocument:
		class: tipli\Model\Conditions\Repositories\DocumentRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Conditions\Entities\Document')

	ConditionsApproval:
		class: tipli\Model\Conditions\Repositories\ApprovalRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Conditions\Entities\Approval')

	- tipli\Model\Conditions\Subscribers\ApprovalSubscriber

	ReportCheckRepository:
		class: tipli\Model\Reports\Repositories\ReportCheckRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Reports\Entities\ReportCheck')

	- tipli\Model\Reports\ReportCheckManager

	# Accounting
	- tipli\Model\Accounting\AccountingFacade
	- tipli\Model\Accounting\PaymentManager
	- tipli\Model\Accounting\BcrPaymentsImporter
	- tipli\Model\Accounting\ErstePaymentsImporter
	- tipli\Model\Accounting\IngPaymentsImporter

	AccountingPayment:
		class: tipli\Model\Accounting\Repositories\PaymentRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Accounting\Entities\Payment')

	# Popups
	- tipli\Model\Popups\PopupFacade
	- tipli\Model\Popups\PopupCampaignManager
	- tipli\Model\Popups\PopupInteractionManager
	- tipli\Model\Popups\PopupResolver
	- tipli\AdminModule\PopupsModule\Forms\IPopupCampaignControlFactory

	PopupsPopupCampaign:
		class: tipli\Model\Popups\Repositories\PopupCampaignRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Popups\Entities\PopupCampaign')

	PopupsPopupInteraction:
		class: tipli\Model\Popups\Repositories\PopupInteractionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Popups\Entities\PopupInteraction')

	# Github
	- tipli\Model\Github\GithubClient

	# Translations
	- tipli\Model\Translations\DictionaryConverter
	- tipli\Model\Translations\TranslationFacade(%translations.localeDir%, %translations.localesShortcuts%)
	- tipli\Model\Translations\TranslationManager
	- tipli\AdminModule\TranslationsModule\Forms\IDictionaryControlFactory
	- tipli\AdminModule\TranslationsModule\Forms\IExportMobileDictionaryControlFactory
	- tipli\AdminModule\TranslationsModule\Forms\IImportMobileDictionaryControlFactory

	# Google Translation
	- tipli\Model\GoogleTranslation\GoogleTranslationClient

	# Google Tag Manager
	- tipli\Model\GoogleTagManager\GoogleTagManagerClient

	# Rewards 2 BETA
	- tipli\Model\Rewards2\RewardFacade
	- tipli\Model\Rewards2\RewardManager
	- tipli\Model\Rewards2\RewardObjectFactory
	- tipli\Model\Rewards2\RewardObjectsProvider
	- tipli\AdminModule\Rewards2Module\Forms\IRewardCampaignControlFactory
	- tipli\AdminModule\Rewards2Module\Forms\IAssignRewardCampaignControlFactory
	- tipli\FrontModule\RewardsModule\Forms\ICodeControlFactory

	- tipli\Model\Rewards2\Subscribers\RewardSubscriber

	Rewards2Campaign:
		class: tipli\Model\Rewards2\Repositories\RewardCampaignRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards2\Entities\RewardCampaign')

	Rewards2CampaignCondition:
		class: tipli\Model\Rewards2\Repositories\RewardCampaignConditionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards2\Entities\RewardCampaignCondition')

	Rewards2UserReward:
		class: tipli\Model\Rewards2\Repositories\UserRewardRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Rewards2\Entities\UserReward')

	# Mobile
	- tipli\Model\Mobile\ObjectFactory

	# Firebase
	- tipli\Model\Firebase\FirebaseClient

	# Groups
	- tipli\Model\Groups\GroupFacade
	- tipli\Model\Groups\GroupManager
	- tipli\Model\Groups\GroupAudienceProvider
	- tipli\AdminModule\GroupsModule\Forms\IGroupControlFactory

	GroupsGroup:
		class: tipli\Model\Groups\Repositories\GroupRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Groups\Entities\Group')

	# Slack
	- tipli\Model\Slack\SlackWebhookHandler
	- tipli\Model\Slack\SlackClient

	# Landing pages
	LandingPagesRepository:
		class: tipli\Model\LandingPages\Repositories\LandingPageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LandingPages\Entities\LandingPage')

	ModuleRepository:
		class: tipli\Model\LandingPages\Repositories\ModuleRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LandingPages\Entities\Module')

	- tipli\Model\LandingPages\LandingPageFacade
	- tipli\Model\LandingPages\LandingPageManager
	- tipli\AdminModule\LandingPagesModule\Forms\ILandingPageControlFactory
	- tipli\AdminModule\LandingPagesModule\Forms\IModuleControlFactory

	# Triggers
	- tipli\Model\Triggers\TriggerManager
	- tipli\Model\Triggers\TriggerFacade
	- tipli\Model\Triggers\TriggerProcessor

	TriggersTrigger:
		class: tipli\Model\Triggers\Repositories\TriggerRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Triggers\Entities\Trigger')

	- tipli\Model\Triggers\Subscribers\TriggerSubscriber

	# Tickets
	- tipli\AdminModule\TicketsModule\Forms\ITicketControlFactory

	- tipli\Model\Tickets\TicketFacade
	- tipli\Model\Tickets\TicketManager
	- tipli\Model\Tickets\UserAnalyzer

	TicketsTicket:
		class: tipli\Model\Tickets\Repositories\TicketRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Tickets\Entities\Ticket')


	# Shop checker
	- tipli\Model\Shops\ShopCheckerActivityFacade
	- tipli\Model\Shops\ShopCheckerActivityManager
	- tipli\Model\Shops\ShopCheckerFailManager
	- tipli\Model\Shops\ShopChecker

	# Transaction checker
	- tipli\Commands\ProcessTransactionChecker
	- tipli\Model\Transactions\TransactionCheckerFacade

	# Campaign 300
	- tipli\Model\Campaign\CampaignFacade
	- tipli\Model\Campaign\CampaignSubscriptionManager
	- tipli\Model\Campaign\CampaignTransactionManager
	- tipli\Model\Campaign\CampaignAvailableBonusResolver

	CampaignCampaignSubscription:
		class: tipli\Model\Campaign\Repositories\CampaignSubscriptionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Campaign\Entities\CampaignSubscription')

	CampaignCampaignTransaction:
		class: tipli\Model\Campaign\Repositories\CampaignTransactionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Campaign\Entities\CampaignTransaction')

	- tipli\Model\Campaign\Subscribers\CampaignSubscriber

	# Cache
	- tipli\Model\Cache\CacheManager
	- tipli\Model\Cache\CacheFacade

	- tipli\Model\Cache\Subscribers\CacheSubscriber

	# Webhook
	- tipli\Model\Webhooks\WebhookRouter


	# Elastic
	- Elastica\Client(%elasticSearch%)
	- tipli\Model\Elastic\MappingTool
	- tipli\Model\Elastic\ElasticFacade
	- tipli\Model\Elastic\ElasticManager
	- tipli\Model\Elastic\EntityFactory

	# Refunds
	- tipli\Model\Refunds\RefundFacade
	- tipli\Model\Refunds\RefundManager
	- tipli\FrontModule\AccountModule\Forms\IRefundMissingCommissionControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundMissingCommissionControlFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundMissingBonusControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundMissingBonusControlFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundUnconfirmedTransactionFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundUnconfirmedTransactionFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundCanceledCommissionFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundCanceledCommissionFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundPayoutControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundPayoutControlFactory
	- tipli\FrontModule\AccountModule\Forms\IRefundOtherControlFactory
	- tipli\NewFrontModule\AccountModule\Forms\IRefundOtherControlFactory
	- tipli\AdminModule\RefundsModule\Forms\IResolveRefundControlFactory
	- tipli\AdminModule\RefundsModule\Forms\IRefundSendMessageControlFactory
	- tipli\AdminModule\RefundsModule\Components\Refund\RefundResolveCheck\RefundResolveCheckControlFactory
	- tipli\AdminModule\RefundsModule\Components\Refund\ExternalFilter\RefundExternalFilterControlFactory

	- tipli\Model\Refunds\RefundProcessor
	- tipli\Model\Refunds\RefundResolver
	- tipli\Model\Refunds\RefundResponseProvider
	- tipli\Model\Refunds\RefundStatisticDataProvider
	- tipli\Model\Refunds\RefundSolutionManager
	- App\Model\Refunds\RefundsExportFileGenerator

	- tipli\Model\Refunds\Subscriber\RefundSubscriber
	- tipli\Model\Refunds\RefundGreetingsGenerator

	RefundRepository:
		class: tipli\Model\Refunds\Repositories\RefundRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Refunds\Entities\Refund')

	# Smartsupp
	- tipli\Model\Smartsupp\SmartsuppFacade
	- tipli\Model\Smartsupp\ConversationManager
	- tipli\Model\Smartsupp\Client(%smartsUpp.authToken%)
	- tipli\Model\Smartsupp\ConversationResponderResolver
	- tipli\Model\Smartsupp\MessageManager

	SmartsuppConversationRepository:
		class: tipli\Model\Smartsupp\Repositories\ConversationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Smartsupp\Entities\Conversation')

	SmartsuppMessageRepository:
		class: tipli\Model\Smartsupp\Repositories\MessageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Smartsupp\Entities\Message')

	# Survey
	- tipli\Model\Surveys\SurveyFacade
	- tipli\Model\Surveys\SurveyManager
	- tipli\Model\Surveys\SurveyResolver

	SurveyRepository:
		class: tipli\Model\Surveys\Repositories\SurveyRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Surveys\Entities\Survey')

	# ApiMetrics
	- App\Model\ApiMetrics\ApiMetricsWebhookHandler

	# Redis
	- tipli\Model\Redis\RedisStorageManager

	# WebPageTest
	- tipli\Model\WebPageTest\WebPageTestWebhookHandler

	# Monitors
	- tipli\Model\Monitors\MonitorFacade
	- tipli\Model\Monitors\MonitorProcessor
	- tipli\Model\Monitors\MonitorManager

	MonitorsRepository:
		class: tipli\Model\Monitors\Repositories\MonitorRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Monitors\Entities\Monitor')

	# Competition
	- tipli\Model\Competition\CompetitionFacade
	- tipli\Model\Competition\CompetitionDealManager
	- tipli\Model\Competition\CompetitionDealChangeManager
	- tipli\Model\Competition\LogManager

	CompetitionDealRepository:
		class: tipli\Model\Competition\Repositories\CompetitionDealRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competition\Entities\CompetitionDeal')

	CompetitionDealChangeRepository:
		class: tipli\Model\Competition\Repositories\ChangeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competition\Entities\Change')

	CompetitionDealLogRepository:
		class: tipli\Model\Competition\Repositories\LogRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Competition\Entities\Log')

	- tipli\AdminModule\CompetitionDealsModule\Forms\AddShopToCompetitionDealControl\IAddShopToCompetitionDealControlFactory
	- tipli\AdminModule\CompetitionDealsModule\Forms\ResolveCompetitionDealControl\IResolveCompetitionDealControlFactory
	- tipli\AdminModule\CompetitionDealsModule\Forms\AddCodeToCompetitionDealControl\IAddCodeToCompetitionDealControlFactory
	- tipli\AdminModule\CompetitionDealsModule\Forms\AddNoteToCompetitionDealControl\IAddNoteToCompetitionDealControlFactory

	# Accounting
	- tipli\AdminModule\AccountingModule\Components\Invoice\InvoiceControlFactory
	- tipli\AdminModule\AccountingModule\Components\InvoiceNote\InvoiceNoteControlFactory
	- tipli\Model\Accounting\InvoiceManager

	InvoiceRepository:
		class: tipli\Model\Accounting\Repositories\InvoiceRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Accounting\Entities\Invoice')

	InvoiceLogRepository:
		class: tipli\Model\Accounting\Repositories\InvoiceLogRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Accounting\Entities\InvoiceLog')

	- tipli\Model\Logger

	# Funnels
	- tipli\Model\Funnels\FunnelManager
	- tipli\Model\Funnels\FunnelFacade
	- tipli\Model\Funnels\Subscriber\FunnelSubscriber
	- tipli\AdminModule\FunnelsModule\Forms\IFunnelControlFactory

	FunnelRepository:
		class: tipli\Model\Funnels\Repositories\FunnelRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Funnels\Entities\Funnel')

	# Redirection feedback
	- tipli\Model\Shops\RedirectionFeedbackFacade
	- tipli\Model\Shops\RedirectionFeedbackManager

	RedirectionFeedbackRepository:
		class: tipli\Model\Shops\Repositories\RedirectionFeedbackRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\RedirectionFeedback')

	# WebCrawler
	- tipli\Model\WebCrawler\WebCrawlerClient

	# Experiments
	- tipli\Model\GoogleExperiment\Subscribers\GoogleExperimentSubscriber
	- tipli\Model\GoogleExperiment\GoogleExperimentVariantResolver
	- tipli\Model\GoogleExperiment\Layers\GoogleExperimentLayer

	# Shop external data
	- tipli\Model\Shops\ShopExternalDataFacade
	- tipli\Model\Shops\ShopExternalDataManager
	- tipli\Model\Shops\ShopExternalDataProcessor

	ShopExternalDataRepository:
		class: tipli\Model\Shops\Repositories\ShopExternalDataRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopExternalData')

	# Sazka
	- tipli\Model\Sazka\SazkaClient

	# Risk
	- tipli\Model\Risk\RiskFacade
	- tipli\Model\Risk\RiskEventManager
	- tipli\Model\Risk\RiskResolver
	- tipli\Model\Risk\Subscribers\RiskEventSubscriber

	RiskCaseRepository:
		class: tipli\Model\Risk\Repositories\RiskCaseRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Risk\Entities\RiskCase')

	RiskEventRepository:
		class: tipli\Model\Risk\Repositories\RiskEventRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Risk\Entities\RiskEvent')

	RiskPolicyRepository:
		class: tipli\Model\Risk\Repositories\RiskPolicyRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Risk\Entities\RiskPolicy')

	# Shop steve data
	- tipli\Model\Shops\ShopSteveDataFacade
	- tipli\Model\Shops\ShopSteveDataManager

	ShopSteveDataRepository:
		class: tipli\Model\Shops\Repositories\ShopSteveDataRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Shops\Entities\ShopSteveData')

	# Vouchers
	- tipli\Model\Vouchers\VoucherFacade
	- tipli\Model\Vouchers\VoucherCampaignManager
	- tipli\Model\Vouchers\UserVoucherManager
	- tipli\Model\Vouchers\UserVoucherEventManager
	- tipli\Model\Vouchers\UserVoucherTransactionManager
	- tipli\Model\Vouchers\VoucherProvider
	- tipli\Model\Vouchers\Subscribers\VoucherSubscriber
	- tipli\AdminModule\VouchersModule\Forms\IVoucherCampaignControlFactory
	- tipli\AdminModule\VouchersModule\Forms\IAssignUserToVoucherCampaignFactory
	- tipli\NewFrontModule\VouchersModule\Components\VoucherItem\VoucherItemFactory
	- tipli\NewFrontModule\VouchersModule\Components\VoucherSlider\VoucherSliderFactory

	VoucherCampaignRepository:
		class: tipli\Model\Vouchers\Repositories\VoucherCampaignRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Vouchers\Entities\VoucherCampaign')

	UserVoucherRepository:
		class: tipli\Model\Vouchers\Repositories\UserVoucherRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Vouchers\Entities\UserVoucher')

	UserVoucherEventRepository:
		class: tipli\Model\Vouchers\Repositories\UserVoucherEventRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Vouchers\Entities\UserVoucherEvent')

	UserVoucherTransactionRepository:
		class: tipli\Model\Vouchers\Repositories\UserVoucherTransactionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Vouchers\Entities\UserVoucherTransaction')

	# LuckyShop
	- tipli\NewFrontModule\LuckyShopsModule\Components\LuckyShopPicker\LuckyShopPickerControlFactory
	- tipli\Model\LuckyShop\LuckyShopFacade
	- tipli\Model\LuckyShop\LuckyShopManager
	- tipli\Model\LuckyShop\UserLuckyShopManager
	- tipli\Model\LuckyShop\UserLuckyShopCheckManager
	- tipli\Model\LuckyShop\LuckyShopCampaignManager
	- tipli\Model\LuckyShop\Subscribers\LuckyShopSubscriber
	- tipli\NewFrontModule\LuckyShopsModule\Components\LuckyShopSearch\LuckyShopSearchControlFactory
	- tipli\Model\LuckyShop\LuckyShopEmailProvider
	- tipli\NewFrontModule\LuckyShopsModule\Components\UserLuckyShopPopupControl\UserLuckyShopPopupControlFactory
	- tipli\Model\LuckyShop\LuckyShopNotificationProvider
	- tipli\NewFrontModule\LuckyShopsModule\Components\PhoneNumberVerification\PhoneNumberVerificationControlFactory
	- tipli\NewFrontModule\LuckyShopsModule\Components\NotificationSettingsControl\NotificationSettingsControlFactory
	- tipli\Model\LuckyShop\LuckyShopStateForUserResolver

	LuckyShopCampaignRepository:
		class: tipli\Model\LuckyShop\Repositories\LuckyShopCampaignRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LuckyShop\Entities\LuckyShopCampaign')

	LuckyShopRepository:
		class: tipli\Model\LuckyShop\Repositories\LuckyShopRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LuckyShop\Entities\LuckyShop')

	UserLuckyShopRepository:
		class: tipli\Model\LuckyShop\Repositories\UserLuckyShopRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LuckyShop\Entities\UserLuckyShop')

	UserLuckyShopCheckRepository:
		class: tipli\Model\LuckyShop\Repositories\UserLuckyShopCheckRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\LuckyShop\Entities\UserLuckyShopCheck')

	# Queues
	- tipli\Model\Queues\QueueFacade
	- tipli\Model\Queues\SqlQueryProcessor
	- tipli\Model\Queues\SqlQueryScheduler

	QueueSqlQueryRepository:
		class: tipli\Model\Queues\Repositories\SqlQueryRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('tipli\Model\Queues\Entities\SqlQuery')
