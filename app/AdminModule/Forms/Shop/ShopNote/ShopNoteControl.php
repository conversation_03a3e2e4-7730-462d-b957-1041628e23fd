<?php

namespace steve\AdminModule\Forms\Shop\ShopNote;

use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Utils\ArrayHash;
use steve\Model\Account\Entities\User;
use steve\Model\Shops\Entities\Shop;
use steve\Model\Shops\ShopFacade;
use Nette\Application\UI\Form;

/**
 * @property-read Template|\stdClass $template
 */
class ShopNoteControl extends Control
{
	public $onSuccess = [];

	private Shop $shop;

	private User $user;

	private ShopFacade $shopFacade;

	public function __construct(Shop $shop, User $user, ShopFacade $shopFacade)
	{
		$this->shop = $shop;
		$this->user = $user;
		$this->shopFacade = $shopFacade;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addText('note', 'Note:')
			->setHtmlAttribute('placeholder', 'Note')
			->setRequired('Enter note.');

		$form->addSubmit('submit', 'Add');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values)
	{
		$this->shopFacade->createShopNote($this->shop, $values->note, $this->user);

		$this->onSuccess();
	}

	public function render()
	{
		$this->template->notes = $this->shopFacade->getShopNotes($this->shop);
		$this->template->render(__DIR__ . '/control.latte');
	}
}

