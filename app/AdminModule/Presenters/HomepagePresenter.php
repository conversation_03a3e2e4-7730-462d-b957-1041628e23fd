<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette\Application\LinkGenerator;
use Nette\Utils\Html;

class HomepagePresenter extends BasePresenter
{
    /** @var WebsiteFacade @inject */
    public $websiteFacade;

    /** @var GeoFacade @inject */
    public $geoFacade;

    /** @var LinkGenerator @inject */
    public $linkGenerator;

    /** @var TagFacade @inject */
    public $tagFacade;

    public const WEBSITE_TYPES_TO_SHOW = [Website::MODULE_KAUFINO, Website::MODULE_LETADO, Website::MODULE_OFERTO];

    public function createComponentReportGrid($name)
    {
        $geoFacade = $this->geoFacade;
        $linkGenerator = $this->linkGenerator;
        $tagFacade = $this->tagFacade;

        $websites = $this->websiteFacade->getWebsites()
            ->andWhere('w.active = true')
            ->andWhere('w.module IN (:types)')
            ->setParameter('types', self::WEBSITE_TYPES_TO_SHOW)
        ;

        $grid = $this->dataGridFactory->create()
            ->getGrid($this, $name, $websites);

        $grid->addColumnText('localization', 'Localization')
            ->setSortable()
            ->setRenderer(static function (Website $website) {
                return $website->getLocalization()->getName();
            })->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs())
        ;

        $grid->addColumnText('name', 'Web')
            ->setFilterMultiSelect(['' => 'All', Website::MODULE_KAUFINO => 'Kaufino', Website::MODULE_LETADO => 'Letado', Website::MODULE_OFERTO => 'Oferto', Website::MODULE_OFERTO_COM => 'OfertoCom'], 'type');

        $grid->addColumnNumber('countOfShops', 'Shops')
            ->setRenderer(static function (Website $website) use ($linkGenerator) {
                $url = $linkGenerator->link('Admin:Shop:default', [
                    'shopsGrid-filter[localization]' => $website->getLocalization()->getId(),
                    'shopsGrid-filter[activeOferto]' => $website->isOferto() ? 1 : null,
                    'shopsGrid-filter[activeLeaflets]' => $website->isKaufino() ? 1 : null,
                ]);

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText($website->getCountOfShops());
            })
            ->setSortable()
        ;

        $grid->addColumnNumber('countOfOffers', 'Offers')
            ->setRenderer(static function (Website $website) use ($linkGenerator) {
                $url = $linkGenerator->link('Admin:Offer:default', [
                    'offersGrid-filter[localization]' => $website->getLocalization()->getId(),
                ]);

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText($website->getCountOfOffers());
            })
            ->setSortable()
        ;

        $grid->addColumnNumber('countOfOfferTags', 'Offer Tags')
            ->setRenderer(static function (Website $website) use ($linkGenerator, $tagFacade) {
                $url = $linkGenerator->link('Admin:Tag:default', [
                    'tagsGrid-filter[localization]' => $website->getLocalization()->getId(),
                    'tagsGrid-filter[type]' => Tag::TYPE_OFFERS,
                ]);

                $tags = $tagFacade->findTags($website->getLocalization(), Tag::TYPE_OFFERS, null);

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText(count($tags));
            })
            ->setSortable()
        ;

        $grid->addColumnNumber('countOfCities', 'Cities')
            ->setRenderer(static function (Website $website) use ($geoFacade, $linkGenerator) {
                $url = $linkGenerator->link('Admin:Cities:default', [
                    'citiesGrid-filter[localization]' => $website->getLocalization()->getId(),
                    'citiesGrid-filter[activeOferto]' => $website->isOferto() ? 1 : null,
                    'citiesGrid-filter[active]' => $website->isKaufino() ? 1 : null,
                ]);

                $totalCities = count($geoFacade->findCitiesByLocalization($website->getLocalization()));

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText($website->getCountOfActiveCities() . ' / ' . $totalCities);
            })
        ;

        $grid->addColumnNumber('countOfBrandsInCities', 'Shops in cities')
            ->setRenderer(static function (Website $website) use ($linkGenerator) {
                return Html::el('a')
                    ->setText($website->getCountOfActiveShopsInCities() . ' / ' . $website->getCountOfShopsInCities());
            })
            ->setSortable()
        ;

        $grid->addColumnNumber('countOfStores', 'Stores')
            ->setRenderer(static function (Website $website) use ($linkGenerator) {
                $url = $linkGenerator->link('Admin:Stores:default', [
                    'storesGrid-filter[localization]' => $website->getLocalization()->getId(),
                    'storesGrid-filter[activeOferto]' => $website->isOferto() ? 1 : null,
                    'storesGrid-filter[active]' => $website->isKaufino() ? 1 : null,
                ]);

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText($website->getCountOfActiveStores() . ' / ' . $website->getCountOfStores());
            })
            ->setSortable()
        ;

        $grid->addColumnNumber('countOfArticles', 'Articles')
            ->setRenderer(static function (Website $website) use ($linkGenerator) {
                $url = $linkGenerator->link('Admin:Article:default', [
                    'articlesGrid-filter[website]' => $website->getId(),
                    'articlesGrid-filter[active]' => true,
                ]);

                return Html::el('a')
                    ->href($url)
                    ->setAttribute('target', '_blank')
                    ->setText($website->getCountOfArticles());
            })
            ->setSortable()
        ;

        $grid->setDefaultSort(['localization' => 'ASC']);
    }
}
