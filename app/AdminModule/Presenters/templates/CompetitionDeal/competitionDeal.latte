{var $freeContent = true}
{varType steve\Model\Competition\Entities\CompetitionDeal $competitionDeal}
{varType steve\Model\Doctrine\QueryObject\ResultSet|array|null $deals}

{block headerContent}
    <a n:href="CompetitionDeals:default" class="btn btn-light btn-sm"> < zpět</a>
{/block}

{block content}

<div class="row">
    <div class="col-md-6 col-xl-6">
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Detail kuponu</strong>
                <div class="pull-right" n:if="$competitionDeal->getShop() && $competitionDeal->getShop()->getImageAbsoluteUrl()">
                    <img src="{$competitionDeal->getShop()->getImageAbsoluteUrl()|image:30,30}" width="30">
                </div>
            </div>
            <div class="card-body">
{*                <div style="margin-bottom: 10px">*}
{*                    <a href="{$competitionDeal->getListingUrl()}" class="btn btn-sm btn-primary text-white" target="_blank">přejít na výpis kuponů »</a>*}
{*                </div>*}

                <table class="table table-bordered">
                    <tr>
                        <td class="font-weight-bold">Název</td>
                        <td>
                            <span class="label label-danger" n:if="$competitionDeal->isExclusive()">Exklusivní</span>
                            {$competitionDeal->getName()}
                        </td>
                    </tr>
                    <tr>
                        <td class="font-weight-bold">Název EN</td>
                        <td>
                            {$competitionDeal->getEnglishName() ? $competitionDeal->getEnglishName() : ' -- čeká na vytvoření z chatGPT --'}
                        </td>
                    </tr>
                    <tr>
                        <td class="font-weight-bold">Popis</td>
                        <td>{$competitionDeal->getDescription()}</td>
                    </tr>
                    <tr>
                        <td class="font-weight-bold">Popis EN</td>
                        <td>
                            {$competitionDeal->getEnglishDescription() ? $competitionDeal->getEnglishDescription() : ' -- čeká na vytvoření z chatGPT --'}
                        </td>
                    </tr>
                        <tr>
                            <td class="font-weight-bold">Stav</td>
                            <td>
                                {if $competitionDeal->isNegotiated() === true}
                                    <span class="label label-success">vyjednáno</span>
                                {elseif $competitionDeal->isNegotiated() === false}
                                    <span class="label label-danger">nevyjednáno</span>
                                {elseif $competitionDeal->getState() === $competitionDeal::STATE_NEGOTIATE}
                                    <span class="label label-primary">domlouváme s obchodem</span>
                                {elseif $competitionDeal->getState() === $competitionDeal::STATE_WAITING_FOR_PROCESS}
                                    <span class="label label-warning">čeká na zpracování</span>
                                {elseif $competitionDeal->getState() === $competitionDeal::STATE_PAIRED}
                                    <span class="label label-success">spárováno</span>
                                {elseif $competitionDeal->getState() === $competitionDeal::STATE_REJECTED}
                                    <span class="label label-danger">zamítnut</span>
                                {elseif $competitionDeal->getState() === $competitionDeal::STATE_REMOVED}
                                    <span class="label label-danger">smazán</span>
                                {/if}
                            </td>
                        </tr>
                    <tr>
                        <td class="font-weight-bold">Staženo&nbsp;z</td>
                        <td>{$competitionDeal->getCompany()->getName()}</td>
                    </tr>
                        <tr>
                            <td class="font-weight-bold">Platnost do</td>
                            <td>{$competitionDeal->getStatedValidTill() ? $competitionDeal->getStatedValidTill()->format('d.m.Y H:i') : "-"}</td>
                        </tr>
                    <tr>
                        <td class="font-weight-bold">Obchod</td>
                        <td>
                            {if $competitionDeal->getShop()}
                                <a href="{plink Shop:shop $competitionDeal->getShop()->getId()}" target="_blank">{$competitionDeal->getShop()->getNameEnriched()}</a>
                            {else}
                                - je potřeba napárovat -
                            {/if}
                        </td>
                    </tr>

                    <tr n:if="$competitionDeal->getShop()">
                        <td class="font-weight-bold">Poznámky</td>
                        <td>{control shopNotesControl}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Hodnota</td>
                        <td>{$competitionDeal->getReward()}</td>
                    </tr>

                    <tr n:if="$competitionDeal->getShop()">
                        <td class="font-weight-bold">Kód</td>
                        <td>
                            <a href="{$competitionDeal->getDetailUrl()}" class="btn btn-sm btn-primary text-white" style="margin-bottom: 10px" target="_blank">přejít na detail kuponu »</a>
                            {control addCodeToCompetitionDealControl}
                        </td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Naposledy spatřen</td>
                        <td>{$competitionDeal->getLastSeenAt()->format('d.m.Y H:i')}</td>
                    </tr>
                </table>

                {if $dealByCode}
                    <div class="font-weight-bold" style="margin-bottom: 10px;">
                        Tento kupon už u nás pravděpodobně máme:
                    </div>

                    <div class="alert alert-info">
                        <table class="table table-bordered">
                            <thead>
                            <th>Název</th>
                            <th>Platnost</th>
                            <th>Akce</th>
                            </thead>

                            <tbody>
                            <tr>
                                <td style="width: 100%">
                                    <a href="{plink Deals:deal $dealByCode->getId()}" target="_blank"><i class="fa fa-external-link"></i> {$dealByCode->getName()}</a>
                                </td>
                                <td style="font-size: 13px">
                                    {$dealByCode->getValidSince()->format('d.m.Y')} <br />
                                    {$dealByCode->getValidTill()->format('d.m.Y')}
                                </td>
                                <td style="vertical-align: middle">
                                    <a href="{plink assignDealToCompetitionDeal! dealId => $dealByCode->getId()}" class="btn btn-sm btn-warning">Napárovat</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <hr />
                {/if}

                {if $competitionDeal->isPaired()}
                    {var $deal = $competitionDeal->getDeal()}

                    <div class="alert alert-success">
                        Napárováno na deal:

                        <strong>
                            <a n:href=":Admin:Deals:deal $deal->getId()" target="_blank">{$deal->getName()}</a>
                        </strong>
                        ({$competitionDeal->getUser() ? $competitionDeal->getUser()->getEmail() : '-'})
                        |
                        <a n:href="unAssignDealToCompetitionDeal!" onclick="return confirm('Opravdu chcete kupon odpárovat?')">Odpárovat</a>
                    </div>
                {/if}

                {if $competitionDeal->getShop() !== null && $competitionDeal->isPaired() === false}
                   {if $competitionDeal->isRejected() === true}
                        <div class="alert alert-warning" role="alert">
                            Kupon byl zamítnutý. <a n:href="unRejectCompetitionDeal!, $competitionDeal->getId()">vrátit zpět</a>
                        </div>
                    {else}
                        {form resolveCompetitionDealControl-form}
                            {if $competitionDeal->isNegotiate() === true}
                                <div class="alert alert-info" role="alert">
                                    Kupón aktuálně domlouváme s obchodem / affilem.
                                </div>
                            {/if}

                            <div>
                                <div class="form-group">
                                    {label note /}
                                {input note, class => "form-control"}
                                </div>
                            </div>

                            <div class="form-group">
                                {input rejected, class => "btn btn-danger btn-md", onclick => "return confirm('Opravdu chcete kupon zamítnout?')"}

                                {if $competitionDeal->isNegotiate() === false}
                                    {input negotiate, class => "btn btn-warning btn-md"}
                                {else}
                                    {input submit, class => "btn btn-success btn-md"}
                                {/if}

                                <a n:href=":Admin:Deals:deal, 'competitionDealId' => $competitionDeal->getId()" class="btn btn-md btn-success" n:if="$competitionDeal->getShop() !== null && $competitionDeal->isPaired() === false && $competitionDeal->isRejected() === false">
                                    Vytvořit kupon »
                                </a>
                            </div>
                        {/form}
                    {/if}
                {else}
                    {control addNoteToCompetitionDealControl}
                {/if}
            </div>
        </div>
    </div>

    <div class="col-md-6 col-xl-6">
        <div class="card card-accent-danger">
            <div class="card-header">
                <strong>Napárovat obchod</strong>
            </div>
            <div class="card-body">
                {control addShopToCompetitionDealControl}
            </div>
        </div>

        <div class="card card-accent-success mb1 mt1" n:if="$competitionDeal->getShop() !== null && $competitionDeal->isPaired() === false">
            <div class="card-header">
                <strong>Seznam aktivních kupónů v obchodě {$competitionDeal->getShop()->getName()}</strong>
            </div>
            <div class="card-body">
                {if count($deals) === 0}
                    Pro vybraný obchod nejsou aktivní žádné dealy.
                {else}
                    <table class="table table-bordered">
                        <thead>
                            <th>Název</th>
                            <th>Kód</th>
                            <th>Odměna</th>
                            <th>Platnost</th>
                            <th>Akce</th>
                        </thead>
                        <tbody>
                        <tr n:foreach="$deals as $deal">
                            {varType steve\Model\Deals\Entities\Deal $deal}

                            <td style="width: 100%">
                                <a n:href=":Admin:Deals:deal $deal->getId()" target="_blank">{$deal->getName()}</a>
                            </td>
                            <td>
                                {$deal->getCode()}
                            </td>
                            <td>
                                {$deal->getValue()}{if !($deal->getUnit() == 'percentage' && $deal->getLocale() === 'pl')} {/if}{$deal->getUnitSymbol()}
                            </td>
                            <td style="font-size: 13px">
                                {$deal->getValidSince()->format('d.m.Y')} <br />
                                {$deal->getValidTill()->format('d.m.Y')}
                            </td>
                            <td style="vertical-align: middle">
                                <a n:href="assignDealToCompetitionDeal! dealId => $deal->getId()" n:if="$competitionDeal->getState() !== $competitionDeal::STATE_NEGOTIATE || ($competitionDeal->getState() === $competitionDeal::STATE_NEGOTIATE && $competitionDeal->isNegotiated() !== null)" class="btn btn-sm btn-warning">Napárovat</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>

        <div class="card card-accent-success mb1" n:if="$competitionDeal->getShop() !== null && $competitionDeal->isPaired() === false">
            <div class="card-header">
                <strong>Seznam aktivních slev v obchodě {$competitionDeal->getShop()->getName()}</strong>
            </div>
            <div class="card-body">
                {if count($saleDeals) === 0}
                    Pro vybraný obchod nejsou aktivní žádné dealy.
                {else}
                    <table class="table table-bordered">
                        <thead>
                        <th>Název</th>
                        <th>Typ</th>
                        <th>Odměna</th>
                        <th>Platnost</th>
                        <th>Akce</th>
                        </thead>
                        <tbody>
                        <tr n:foreach="$saleDeals as $deal">
                            {varType steve\Model\Deals\Entities\Deal $deal}

                            <td style="width: 100%">
                                <a n:href=":Admin:Deals:deal $deal->getId()" target="_blank">{$deal->getName()}</a>
                            </td>
                            <td>
                                {$deal->getTypeLabel()}
                            </td>
                            <td>
                                {if $deal->getValue()}
                                    {$deal->getValue()}{if !($deal->getUnit() == 'percentage' && $deal->getLocale() === 'pl')} {/if}{$deal->getUnitSymbol()}
                                {else}
                                    -
                                {/if}
                            </td>
                            <td style="font-size: 13px">
                                {$deal->getValidSince()->format('d.m.Y')} <br />
                                {$deal->getValidTill()->format('d.m.Y')}
                            </td>
                            <td style="vertical-align: middle">
                                <a n:href="assignDealToCompetitionDeal! dealId => $deal->getId()" class="btn btn-sm btn-warning">Napárovat</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>

        <div class="card card-accent-success mb1">
            <div class="card-header">
                <strong>Poslední změny</strong>
            </div>
            <div class="card-body" style="">
                {if $competitionDeal->getChanges()->isEmpty()}
                    <div style="padding: 5px 0">
                        Zatím ke konkurenčnímu dealu neevidujeme žádně změny.
                    </div>
                {else}
                  <ul style="height: 400px; overflow-y: auto">
                      <li n:foreach="$competitionDeal->getChanges() as $change">
                          {varType steve\Model\Competition\Entities\Change $change}

                          {$change->getCreatedAt()->format('d.m.Y')} změna v: <strong>{$change->getChangeTypeLabel()}</strong> <br />

                          {if $change->getOldValue()}původní hodnota: <strong>{$change->getOldValue()}</strong>{/if} <br />
                          {if $change->getNewValue()}nová hodnota: <strong>{$change->getNewValue()}</strong>{/if}

                          <hr />
                      </li>
                  </ul>
                {/if}
            </div>
        </div>


        <div class="card card-accent-success mb1">
            <div class="card-header">
                <strong>Poslední změny u nás</strong>
            </div>
            <div class="card-body" style="">
                {if $competitionDeal->getLog()->isEmpty()}
                    <div style="padding: 5px 0">
                        Zatím ke konkurenčnímu dealu u nás neevidujeme žádně změny.
                    </div>
                {else}
                    <ul style="height: 400px; overflow-y: auto">
                        <li n:foreach="$competitionDeal->getLog() as $log">
                            {varType steve\Model\Competition\Entities\Log $log}
                                  {$log->getCreatedAt()->format('d.m.Y')} <strong>{$log->getChangeTypeLabel()}</strong> <br />
                            <hr />
                        </li>
                    </ul>
                {/if}
            </div>
        </div>

{*        {if $competitionDeal->getShop() && $competitionDeal->getShop()->getShopNotes()->isEmpty() === false}*}
{*            <div class="card card-accent-success">*}
{*                <div class="card-header">*}
{*                    <strong>Poznámky k obchody</strong>*}
{*                </div>*}
{*                <div class="card-body">*}
{*                    <ul style="max-height: 300px; overflow-y: auto">*}
{*                        <li n:foreach="$competitionDeal->getShop()->getShopNotes() as $shopNote">*}
{*                            {varType steve\Model\Shops\Entities\ShopNote $shopNote}*}
{*                              {$shopNote->getCreatedAt()->format('d.m.Y')} {$shopNote->getNote()} <br />*}
{*                            <hr />*}
{*                        </li>*}
{*                    </ul>*}
{*                </div>*}
{*            </div>*}
{*        {/if}*}
    </div>
</div>
