{block head}
    <!-- redactor.css -->
    <link rel="stylesheet" href="{$basePath}/libs/redactor/redactor.min.css" />

    <!-- annotorious -->
    <link rel="stylesheet" href="{$basePath}/libs/annotorious/annotorious.min.css">

    <style>
        .a9s-annotation.editable .a9s-inner {
            stroke: red;
        }        

        .a9s-handle .a9s-handle-inner {
            stroke: red;
        }
    </style>
{/block}

{block scripts}
    <!-- Redactor -->
    <script src="{$basePath}/libs/redactor/redactor.min.js"></script>
    <script src="{$basePath}/libs/redactor/langs/en.js"></script>
    <script src="{$basePath}/libs/redactor/plugins/widget/widget.min.js"></script>
    <script src="{$basePath}/js/redactor/redactor.js"></script>

    <!-- annotorious -->
    <script src="{$basePath}/libs/annotorious/annotorious.min.js"></script>    

    <script n:if="$leafletPage">
        (function() {

            // annotations
            var testinitAnnotations = {
                "@context": "http://www.w3.org/ns/anno.jsonld",
                "id": "#a88b22d0-6106-4872-9435-c78b5e89fede",
                "type": "Annotation",
                "body": [{
                    "type": "TextualBody",                    
                    "purpose": "tagging",
                    "value": "product"
                }],
                "target": {
                    "selector": {
                        "type": "FragmentSelector",
                        "conformsTo": "http://www.w3.org/TR/media-frags/",
                        "value": "xywh=pixel:62,31,400,400"
                    }
                }
            };

            // Init Annotorious
            var anno = Annotorious.init({
                image: 'leaflet', // image ID
            });

            // Add initAnnotations
            {foreach $annotations as $box}
                <!-- @todo jirka: docasne vypinam. Neni to presne a zdrzuje to -->
                {*anno.addAnnotation({$box|noescape});*}

            {/foreach}
            // 2 annotation
            //var newAnnotation = [{"type":"Annotation","body":[{"type":"TextualBody","value":"2","purpose":"tagging"}],"target":{"source":"https://www.tipli.cz/upload/thumbnails/700/700144/o/i-1740x-fit.jpg","selector":{"type":"FragmentSelector","conformsTo":"http://www.w3.org/TR/media-frags/","value":"xywh=pixel:682.9031982421875,37.41932678222656,376.0645751953125,372.32261657714844"}},"@context":"http://www.w3.org/ns/anno.jsonld","id":"#88b7c764-c7b6-4d37-a453-3fe614ef826c"},{"@context":"http://www.w3.org/ns/anno.jsonld","id":"#a88b22d0-6106-4872-9435-c78b5e89fede","type":"Annotation","body":[{"type":"TextualBody","value":"Test ctverec"}],"target":{"source":"https://www.tipli.cz/upload/thumbnails/700/700144/o/i-1740x-fit.jpg","selector":{"type":"FragmentSelector","conformsTo":"http://www.w3.org/TR/media-frags/","value":"xywh=pixel:92.12905883789062,45.8709716796875,350,400"}}}];        
            //anno.addAnnotation(newAnnotation[0]);
            //anno.addAnnotation(newAnnotation[1]);


            // Events
            anno.on('updateAnnotation', async function(selection) {
                refreshOutputData();
            });

            anno.on('changeSelectionTarget', async function(selection) {
                refreshOutputData();
            });

            anno.on('mouseEnterAnnotation', async function(selection) {
                // anno.saveSelected();
            });

            anno.on('mouseLeaveAnnotation', async function(selection) {
                // anno.saveSelected();
                // console.log("saved");
            });

            anno.on('createAnnotation', async function(selection) {
                refreshOutputData();
            });

            anno.on('deleteAnnotation', async function(selection) {
                refreshOutputData();
            });

            anno.on('createSelection', async function(selection) {
                selection.body = [{
                    type: 'TextualBody',
                    purpose: 'tagging',
                    value: 'product'
                }];

                // Make sure to wait before saving!
                await anno.updateSelected(selection);
                anno.saveSelected();    
            });

            $('body').keyup( function(e) {
                if (e.which === 13) {
                    anno.saveSelected();
                }

                if (e.key === "Escape") {
                    anno.cancelSelected();
                }

                if (e.keyCode === 46 || e.keyCode === 8) {
                    anno.removeAnnotation(
                        anno.getSelected()
                    );

                    anno.cancelSelected();

                    refreshOutputData();
                }
            });

            function refreshOutputData()
            {
                const annotations = anno.getAnnotations();
                $('.annotator').val(JSON.stringify(annotations));
            }
        })()
    </script>
{/block}


{block content}

<div class="card mx-2">
    <div class="card-body">
        <div class="btn-group" role="group" aria-label="Basic outlined example">
            <a n:href="this, id: null, localizationId: null"  class="btn {$localizationId === null ? "btn-primary" : "btn-outline-primary"}">All ({$countOfleafletAllPagesRemaining})</a>
            {foreach $localizationsWithLeafletPagesToAnnotate as $localizationItem}
                <a n:href="this, id: null, localizationId: $localizationItem['id']"  class="btn {$localizationId === $localizationItem['id'] ? "btn-primary" : "btn-outline-primary"}">{$localizationItem['name']} ({$localizationItem['count']})</a>
            {/foreach}
        </div>
    </div>
</div>

{if $leafletPage}
    <div class="row">
        <div class="col">
            <h1>Annotate {$leafletPage->getLeaflet()->getLocalization()->getRegion()|upper} leaflet by {$leafletPage->getLeaflet()->getShop()->getName()}
                    (remaining {$countOfleafletPagesRemaining})</h1>
            <a n:if="$backId" n:href="LeafletPage:annotations $backId">Back to previous</a>

            {control leafletPageControl}

            {if $leafletPage->getOffers()->isEmpty() === false}
                <hr>
                <h1>Current offers:</h1>

                <table class="table table-align-middle">
                    {foreach $leafletPage->getOffers() as $offer}
                        <tr>
                            <td style="width: 1%"><img src="{$offer->getImageUrl()|image:150,150}" style="max-width: 100px"></td>
                            <td>{$offer->getName()}</td>
                            <td class="text-right">
                                <a n:href=":Admin:Offer:offer $offer->getId()" class="btn btn-success" target="_blank">
                                    Edit
                                </a>
                            </td>
                        </tr>
                    {/foreach}
                </table>
            {/if}
        </div>
        <div class="col-8">
            <div class="container mt-4" style="text-align: center">
                <img id="leaflet" src="{$imageUrl}" alt="" class="img-fluid">
            </div>
        </div>
    </div>
{else}
    <h1>All done</h1>
{/if}

<style n:syntax="off">
    .r6o-editor-inner .r6o-widget.comment,
    .r6o-editor-inner .r6o-widget.r6o-tag {
        display:none !important;
    }

    /** Enable the dim mask, black with 60% transparency **/
    svg.a9s-annotationlayer .a9s-selection-mask {
        fill:rgba(0, 0, 0, 0.75);
    }

</style>


