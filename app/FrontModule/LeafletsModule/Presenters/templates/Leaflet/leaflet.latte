{var $variant = $googleExperimentVariantResolver->getExperimentVariant('googleoptimize', 2)->getVariant()}
{*var $variant = 0*}

{var $validSinceDay = $translator->translate('front.calendar.days.' . $leaflet->getValidSince()->format('w'))}
{var $validTillDay = $translator->translate('front.calendar.days.' . $leaflet->getValidTill()->format('w'))}

{block metaTitle}
    {if isset($pageExtension) && $pageExtension->getHeading()}
        {$pageExtension->getHeading()}
    {else}
		{_'front.leaflet.validSince', [brand => $leaflet->getShop()->getName(), date => $validSinceDay . ' ' . $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), dateTill => $validTillDay . ' ' . $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {/if}
{/block}

{block keywords}{_'front.leaflet.branch.keywords', [name => $leaflet->getShop()->getName(), title => $leaflet->getTitle(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n. Y'))]}{/block}

{block description}
    {var $random = 1}

    {if $random == 1}
        {_'front.leaflet.description1', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {elseif $random == 2}
        {_'front.leaflet.description2', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {else}
        {_'front.leaflet.description3', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {/if}
{/block}

{block #styles}
    {control cssBuilderControl ['css/leaflet/main.leaflet.css']}

    {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
        <script src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" defer></script>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({
                google_ad_client: "ca-pub-3375315304218610",
                 enable_page_level_ads: true
            });
        </script>
    {/if}

{/block}

{block #scripts}
    {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
        <script type="text/javascript" src="{$basePath}/js/leaflet.js" defer></script>
        <script type="text/javascript" src="{$basePath}/js/leaflet.detail.js" defer></script>

		<script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>
    {/if}
{/block}

{block content}
<!-- variant: {$variant} -->
<span itemscope itemtype="http://schema.org/SaleEvent">
    <meta itemprop="name" content="{$leaflet->getTitle()}">
    <meta itemprop="description" content="{include description}">
    <meta itemprop="url" content="{_'front.links.homepage'}{link :Front:Leaflets:Leaflet:leaflet, $leaflet}">
    <meta itemprop="image" content="{($leaflet->getFirstLeafletPage()) ? $leaflet->getFirstLeafletPage()->getDownloadUrl() |image:198,198,'exactTop',null}">
    <meta itemprop="startDate" content="{$leaflet->getValidSince()->format('Y-m-d')}">
    <meta itemprop="endDate" content="{$leaflet->getValidTill()->format('Y-m-d')}">
    <span itemprop="location" itemscope itemtype="http://schema.org/ShoppingCenter">
        <meta itemprop="name" content="{$leaflet->getShop()->getName()}">
        <meta itemprop="url" content="{_'front.links.homepage'}{link :Front:Shops:Shop:shop $leaflet->getShop()}">
        <meta itemprop="image" content="{$leaflet->getShop()->getLogo() |image:60}">
        <meta itemprop="address" content="{$leaflet->getShop()->getName()} {_'front.leaflets.country.' . $leaflet->getLocalization()->getLocale()}">
        <meta itemprop="telephone" content=" ">
        <meta itemprop="priceRange" content=" ">
    </span>
    <span itemprop="performer" itemtype="https://schema.org/Person" itemscope>
        <meta itemprop="name" content="{$leaflet->getShop()->getName()}">
    </span>
    <span itemprop="offers" itemtype="http://schema.org/Offer" itemscope>
        <meta itemprop="priceCurrency" content="{$leaflet->getLocalization()->getCurrency()}">
        <meta itemprop="availability" content="In Stock">
        <meta itemprop="price" content="">
        <meta itemprop="url" content="{_'front.links.homepage'}{link :Front:Shops:Shop:shop $leaflet->getShop()}">
        <meta itemprop="validFrom" content="{$leaflet->getValidSince()->format('Y-m-d')}">
    </span>
</span>

<div class="leaflet lf-n-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">

						{if isset($pageExtension) && $pageExtension->getHeading()}
							<h1 class="page-header__title">{$pageExtension->getHeading()}</h1>
						{else}
							<h1 class="page-header__title">
								{$leaflet->getTitle()}
								{var $sinceYear = $leaflet->getValidSince()->format('Y')}
								{var $tillYear = $leaflet->getValidTill()->format('Y')}
								<span class="leaflet__date">
									{$leaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} –
									{if $sinceYear != $tillYear}
										{$leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}
									{else}
										{$leaflet->getValidTill()->format($localization->getDateFormat('j. n.'))}
									{/if}
								</span>
							</h1>
						{/if}

						<p class="page-header__text">{_'front.leaflet.text', [brand => $leaflet->getShop()->getName(), validSinceDay => $validSinceDay , validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTillDay => $validTillDay, validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}</p>
					</div>

					<div class="leaflet__detail-header-side">
						<a href="{link ':Front:Leaflets:Leaflet:leaflets', 'shop' => $leaflet->getShop()}">
							<img src="{$basePath}/images/leaflet-bg-60.jpg" data-src="{$leaflet->getShop()->getLogo() |image:100}" data-src-retina="{$leaflet->getShop()->getLogo() |image:200}" class="unveil leaflet__detail-header-logo" alt="{$leaflet->getShop()->getName()}">
						</a>
					</div>
				</div>

				{if isset($pageExtension) && $pageExtension->getMiddleDescription()}
				<div class="page-extension">
					{$pageExtension->getMiddleDescription() |noescape}
				</div>
				{elseif $localization->isLiteVersion() && $leaflet->getShop()}
					{$leaflet->getShop()->getShortDescription() |noescape}
				{/if}

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div class="leaflet__ads-wrapper" style="justify-content: center;">
							<!-- Letaky - Detail letaku - Ctverec - 1 -->
							<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5330840193" data-ad-format="rectangle" data-full-width-responsive="true"></ins>

							<!-- Letaky - Detail letaku - Ctverec - 2 -->
							<ins class="adsbygoogle hide-xs" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5139268505" data-ad-format="rectangle" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet__paginator">
					{cache ($cdnImagesAllowed ? 'proxy' : 'native') . 'leaflet-pages-paginator-top-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '16 hours'}
						{control leafletPagesPaginator}
					{/cache}
				</div>

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div>
						<!-- Letaky - Detail letaku - Responsive - 1 -->
						<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="4372981741" data-ad-format="auto" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet-preview">
					{cache ($isAdmin ? 'a' : 'u') . ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-pages-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '15 minutes'}
						<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
						{foreach $getLeafletPages() as $leafletPage}
							{* Rozmery nahledu - width 870px - retina = 1740px *}
							{if $isAdmin}
								<img src="{$basePath}/images/leaflet-bg.png" data-src="{$leafletPage->getDownloadUrl() |image:870}" data-src-retina="{$leafletPage->getDownloadUrl() |image:1740}" class="unveil" alt="{$leaflet->getShop()->getName()}">
							{else}
								<img src="{$basePath}/images/leaflet-bg.png" data-src="{$leafletPage->getDownloadUrl() |image:870}" data-src-retina="{$leafletPage->getDownloadUrl() |image:1740}" class="unveil" alt="{$leaflet->getShop()->getName()}">
							{/if}
						{/foreach}
					{/cache}
				</div>

				{if $partnerLink = $leaflet->getPartnerLink()}
					<div class="ta-center">
						<a href="{$partnerLink |noescape}" target="_blank" class="btn btn-green mt-4">
							{_'front.leaflet.partnerLinkButtonText'}
						</a>
					</div>
				{/if}

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div>
						<!-- Letaky - Detail letaku - Responsive - 2 -->
						<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="6284829214" data-ad-format="auto" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet__paginator">
					{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-pages-paginator-bottom-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '16 hours'}
						{control leafletPagesPaginator}
					{/cache}
				</div>

				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-bottom-section-' . $leaflet->getId(), expire => '2 hours'}
					{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
						<div class="leaflet__ads-wrapper">
							<!-- Letaky - Detail letaku - Responsive - 3 -->
							<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5531803139" data-ad-format="auto" data-full-width-responsive="true"></ins>
						</div>
					{/if}

					{if isset($pageExtension) && $pageExtension->getTopDescription()}
						<p class="page-header__text"><strong>{$pageExtension->getTopDescription() |content |noescape}</strong></p>
					{else}
						<p class="page-header__text"><strong>{$leaflet->getShop()->getName()} {_'front.leaflet.branch.valid', [date => $leaflet->getValidSince()->format($localization->getDateFormat('j. n. Y'))]}</strong></p>
					{/if}

					{capture $leafletBrandLink}
						<a href="{link ':Front:Leaflets:Leaflet:leaflets', 'shop' => $leaflet->getShop()}">{$leaflet->getShop()->getName()}</a>
					{/capture}

					{capture $topLeafletShopLinks}
{*						{cache 'leafletTopShopsLinks-' . $leaflet->getShop()->getId(), expire => '1 hour'}*}
							{var $topLeafletShops = $getTopLeafletShops()}

							{foreach $topLeafletShops as $topLeafletShop}
								<a href="{link ':Front:Leaflets:Leaflet:leaflets', 'shop' => $topLeafletShop}">{$topLeafletShop->getName()}</a>{sep}, {/sep}
							{/foreach}
{*						{/cache}*}
					{/capture}

					{capture $moreShopsLink}
						{link :Front:Leaflets:Leaflet:shops}
					{/capture}

					<p class="page-header__text">{_'front.leaflet.leafletText', [brand => $leafletBrandLink, validSinceDay => $validSinceDay , validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTillDay => $validTillDay ,validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y')), countOfPages => $getCountOfPages(), topLeafletShops => $topLeafletShopLinks, moreShopsLink => $moreShopsLink] |noescape}</p>

					<div class="leaflet__line">
						<a n:href="default" class="leaflet__back"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_'front.leaflet.list.backLink'}</a>
						<a n:if="!$localization->isLiteVersion() && !$leaflet->getShop()->isOnlyLeafletShop()" n:href=":Front:Shops:Shop:shop, $leaflet->getShop()" class="leaflet__back">{_'front.leaflet.list.shopLink', ['shop' => $leaflet->getShop()->getName()]}</a>
						<a n:href="leaflets, shop => $leaflet->getShop()" class="leaflet__next">{_'front.leaflet.list.allLink', [title => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
					</div>

					{var $shopLeaflets = $getShopLeaflets()}
					{if count($shopLeaflets) > 0}
						<div class="leaflet__title-wrapper">
							<h2 class="leaflet__sub-title">
								<a n:href="leaflets, shop => $leaflet->getShop()">{_'front.leaflet.list.shop.title'} {$leaflet->getShop()->getName()}</a>
							</h2>
						</div>

						<div class="leaflet-list__wrapper">
							{foreach $shopLeaflets as $shopLeaflet}
								{include leafletSnippet, leaflet => $shopLeaflet}
							{/foreach}
						</div>
					{/if}

					{var $tagLeaflets = $getTagLeaflets()}
					{if $tagLeaflets !== null}
						<div class="leaflet__title-wrapper">
							<h2 class="leaflet__sub-title">
								{var $leafletFirstTag = $getLeafletFirstTag()}
								{_'front.leaflet.list.title.category', [category => $leafletFirstTag->getName()]}
							</h2>
						</div>

						<div class="leaflet-list__wrapper">
							{foreach $tagLeaflets as $tagLeaflet}
								{include leafletSnippet, leaflet => $tagLeaflet}
							{/foreach}
						</div>
					{/if}
				{/cache}
			</div>

			<div class="leaflet__aside">
				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'top-leaflets-global-' . $localization->getId(), expire => '6 hours'}
					<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
					<div class="lf__box lf__box-lg-border">
						<h3 class="lf__box-title">{_'front.leaflet.favoriteLeaflets'}</h3>
						<div class="d-flex flex-direction-row flex-direction-lg-column">
							{var $topLeaflets = $topLeaflets(5)}

							{foreach $topLeaflets as $topLeaflet}
								<div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
									<a n:href=":Front:Leaflets:Leaflet:leaflet, $topLeaflet" class="lf__box-image-wrapper mb-3 mb-lg-0 mr-lg-3"><img src="{$basePath}/images/leaflet-bg-198.jpg" data-src="{($topLeaflet->getFirstLeafletPage()) ? $topLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:268,268,'exactTop',null}" data-src-retina="{($topLeaflet->getFirstLeafletPage()) ? $topLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:536,536,'exactTop',null}" class="img-responsive unveil" alt="preview"></a>
									<p class="fz-xxs fz-sm-xs mb-0">
										<a n:href=":Front:Leaflets:Leaflet:leaflet, $topLeaflet" class="d-block color-black strong">{$topLeaflet->getTitle()}</a>
										<small>{$topLeaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} - {$topLeaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}</small>
									</p>
								</div>
							{/foreach}
						</div>
					</div>
				{/cache}

				<!-- Letaky - Detail letaku - Sidebar - 1 -->
				<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="2448365833" data-ad-format="auto" data-full-width-responsive="true"></ins>
			</div>
		</div>

		<div class="leaflet__sidebar">
			<div class="lf__box">
				<h3 class="lf__box-title mt-3 mt-md-0">{_'front.leaflet.otherLeaflets', [brand => $leaflet->getShop()->getName()]}</h3>
				<div class="d-flex flex-direction-row flex-direction-md-column">
					{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletTopShops-' . $leaflet->getShop()->getId(), expire => '8 hours'}
					<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
					{var $expiredLeaflets = $expiredLeaflets(3)}

					{foreach $expiredLeaflets as $expiredLeaflet}
						<div class="lf__box-item flex-direction-column flex-direction-lg-row mb-3">
							<a n:href=":Front:Leaflets:Leaflet:leaflet, $expiredLeaflet" class="lf__box-image-wrapper lf__box-image--medium mb-3 mb-lg-0 mr-lg-3"><img src="{$basePath}/images/leaflet-bg-198.jpg" data-src="{($expiredLeaflet->getFirstLeafletPage()) ? $expiredLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:268,268,'exactTop',null}" data-src-retina="{($expiredLeaflet->getFirstLeafletPage()) ? $expiredLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:536,536,'exactTop',null}" class="img-responsive unveil" alt="preview"></a>
							<p class="fz-xxs fz-sm-xs mb-0">
								<a n:href=":Front:Leaflets:Leaflet:leaflet, $expiredLeaflet" class="d-block color-black strong">{$expiredLeaflet->getTitle()}</a>
								<small>{$expiredLeaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} - {$expiredLeaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}</small>
							</p>
						</div>
					{/foreach}
					{/cache}
				</div>
			</div>

			<div class="float-wrapper">
				<!-- Letaky - Detail letaku - Sidebar - 2 -->
				<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="7931313721" data-ad-format="auto" data-full-width-responsive="true"></ins>
			</div>

			{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletStores-' . $leaflet->getId(), expire => '8 hours'}
			{foreach $leaflet->getStores() as $store}
				{if $store}
					<div class="leaflet-box">
						<h3 class="leaflet-box__title">{$store->getName()}</h3>
						<p class="leaflet-box__text">{$store->getDescription()}</p>

						{if $store->getCity() || $store->getAddress()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.address'}:</strong><br>
								{$store->getCity()} <br>
								{$store->getAddress()}

								{if $store->getZipCode()}
									, {$store->getZipCode()}
								{/if}
								<br>
							</p>
						{/if}

						{if $store->getPhoneNumber()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.phone'}:</strong><br>
								{$store->getPhoneNumber()}
							</p>
						{/if}

						{if $store->getEmail()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.email'}:</strong><br>
								<a href="mailto:">{$store->getEmail() }</a>
							</p>
						{/if}

						<p class="leaflet-box__text">
							<strong>{_'front.leaflet.branch.openHour'}:</strong><br>
							7:00-21:00
						</p>
					</div>

					{if $store->getCity()}
						<div class="leaflet-box__map">
							<iframe width="" height="200" frameborder="0" style="border:0" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyA3FEliQngvGpLZ_g2wlGzmZczNcPEEyCA&q={$store->getFullAddress()}" allowfullscreen></iframe>
						</div>
					{/if}
				{/if}
			{/foreach}
			{/cache}
		</div>
	</div>

	<div class="float-form__stop"></div>

	<div class="container">
		<div>
			<h2 class="leaflet__sub-title mt-3">{_'front.leaflet.favoriteShops'}</h2>
			<div class="leaflet__wrapper mt-3">
				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletShops-' . $leaflet->getShop()->getId(), expire => '8 hour'}
					{var $leafletShops = $getLeafletShops()}
					{if !$leafletShops->isEmpty()}
						<div class="leaflet-list__wrapper">
							<div class="leaflet-list-compact__wrapper">
								{foreach $leafletShops as $leafletShop}
									{include shopSnippet, shop => $leafletShop}
								{/foreach}
							</div>
						</div>
					{/if}
				{/cache}
			</div>
		</div>
	</div>
</div>

{if isset($pageExtension) && $pageExtension->getBottomDescription()}
    <div class="page-extension page-extension--left page-extension--mt page-extension--mb">
        <div class="container">
			{$pageExtension->getBottomDescription() |noescape}
        </div>
    </div>
{elseif $localization->isLiteVersion() && $leaflet->getShop() && !empty($leaflet->getShop()->getDescription())}
    <div class="page-extension page-extension--left page-extension--mt page-extension--mb">
        <div class="container">
			{$leaflet->getShop()->getDescription() |noescape}
        </div>
    </div>
{/if}
