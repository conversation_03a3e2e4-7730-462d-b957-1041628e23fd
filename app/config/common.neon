parameters:
	database:
		dsn: "mysql:host=************:3306;dbname=kaufino"
		driver: 'pdo_mysql'
		host: ***********:3306
		dbname: kaufino
		user: kaufino
		password: ooheibieh8AhpuToh3gu
	steve:
		api:
			url: https://letaky.tipli.cz/api
			token: mZc2QbG9
	tipli:
		api:
			url: https://www.tipli.cz/api/v1/deals
			token: RlPE62gvwWdwx7nO
	chatGPT:
		apiToken: '***************************************************'
	upload:
		images:
			path: %wwwDir%/upload/
			url: /upload/
	translations:
		localeDir: %appDir%/locale/
	responseCacheDir: %tempDir%/response
	datadog:
		apiKey: ********************************
		appKey: 31492f93f7d25fd1e2f514089e9921533c197810
database:
	default:
		dsn: %database.dsn%
		user: %database.user%
		password: %database.password%

application:
#	catchExceptions: true
	errorPresenter: Root:Error
	mapping:
		*: Kaufino\*Module\Presenters\*Presenter

session:
	expiration: 14 days
	autoStart: no
#	savePath: "%tempDir%/sessions"
	savePath: "%wwwDir%/../sessions" #3

extensions:
	autowired: Kdyby\Autowired\DI\AutowiredExtension
	translation: Contributte\Translation\DI\TranslationExtension
	console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)
	events: Contributte\EventDispatcher\DI\EventDispatcherExtension
	events.extra: Contributte\Events\Extra\DI\EventBridgesExtension
	nettrine.migrations: Nettrine\Migrations\DI\MigrationsExtension

	# Common
	nettrine.annotations: Nettrine\Annotations\DI\AnnotationsExtension
	nettrine.cache: Nettrine\Cache\DI\CacheExtension
	# nettrine.fixtures: Nettrine\Fixtures\DI\FixturesExtension

	# DBAL
	nettrine.dbal: Nettrine\DBAL\DI\DbalExtension
	nettrine.dbal.console: Nettrine\DBAL\DI\DbalConsoleExtension

	# ORM
	nettrine.orm: Nettrine\ORM\DI\OrmExtension
	nettrine.orm.cache: Nettrine\ORM\DI\OrmCacheExtension
	nettrine.orm.console: Nettrine\ORM\DI\OrmConsoleExtension(%consoleMode%)
	nettrine.orm.annotations: Nettrine\ORM\DI\OrmAnnotationsExtension

events.extra:
	security: false
	latte: false

nettrine.orm.cache:
	defaultDriver: Doctrine\Common\Cache\FilesystemCache("%tempDir%/cache/nette.orm.cache")

nettrine.migrations:
	table: doctrine_migrations
	column: version
	directory: %appDir%/../migrations
	namespace: Migrations
	versionsOrganization: null

console:
	name: Kaufino
	version: '1.0'
	catchExceptions: false
	autoExit: true
	url: https://contributte.org
	lazy: false

nettrine.dbal:
	debug:
		panel: %debugMode%
	connection:
		driver: %database.driver%
		host: %database.host%
		user: %database.user%
		password: %database.password%
		dbname: %database.dbname%
		charset: UTF8MB4

nettrine.orm.annotations:
	mapping:
		Kaufino\Model: %appDir%/Model

nettrine.orm:
	entityManagerDecoratorClass: Kaufino\Model\EntityManager
	configuration:
		autoGenerateProxyClasses: %debugMode%
		customStringFunctions:
			JSON_CONTAINS: DoctrineExtensions\Query\Mysql\JsonContains
			DATE_FORMAT: DoctrineExtensions\Query\Mysql\DateFormat
			YEAR: DoctrineExtensions\Query\Mysql\Year
			LPAD: DoctrineExtensions\Query\Mysql\Lpad
			MONTH: DoctrineExtensions\Query\Mysql\Month

decorator:
	Kaufino\Commands\Job:
		inject: yes

translation:
	locales:
		whitelist: [bg_BG, cs_CZ, da_DK, de_AT, de_CH, de_DE, el_CY, el_GR, en_CA, en_GB, en_MT, en_US, en_ZA, es_AR, es_ES, et_EE, fi_FI, fr_FR, hr_HR, hu_HU, it_IT, lt_LT, lv_LV, nl_BE, nl_NL, no_NO, pl_PL, pt_BR, pt_PT, ro_MD, ro_RO, sk_SK, sl_SI, sr_RS, sv_SE, tr_TR, ja_JP, en_AU, es_MX, es_PE, es_CO, uk_UA, hr_BA, en_NZ, en_TR, en_CL, fr_TN, fr_MA, en_AE, en_IE]
		default: en_US
		fallback: [en_US]
	dirs:
		- %appDir%/locale
	localeResolvers:
		- Kaufino\Model\Localization\TranslatorResolver
	logger: true

services:
	configuration:
		class: Kaufino\Model\Configuration
		setup:
			- setImagesPath(%upload.images.path%)
			- setImagesUrl(%upload.images.url%)
			- setMode(%mode%)
			- setSteveApiUrl(%steve.api.url%)
			- setSteveApiToken(%steve.api.token%)
			- setAzureApiToken(%azure.api.token%)
			- setTipliApiUrl(%tipli.api.url%)
			- setTipliApiToken(%tipli.api.token%)
			- setChatGPTApiToken(%chatGPT.apiToken%)
			- setResponseCacheDir(%responseCacheDir%)

	- Kaufino\Model\SteveClient
	- Kaufino\Model\TipliClient
	- Kaufino\Model\SyncManager
	- Kaufino\Model\Logger
	# Commands

	- Kaufino\Model\Commands\LogFacade
	- Kaufino\Model\Commands\LogManager

	LogRepository:
		class: Kaufino\Model\Commands\Repositories\LogRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Commands\Entities\Log')

	# Components

	- Kaufino\AdminModule\Components\IDataGridFactory

	# Forms

	- Kaufino\Forms\FormFactory
	- Kaufino\Forms\SignInFormFactory

	# Github

	- Kaufino\Model\Github\GithubClient

	# Leaflets
	- Kaufino\Model\Leaflets\LeafletFacade
	- Kaufino\AdminModule\Forms\ILeafletPageControlFactory
	- Kaufino\Model\Leaflets\LeafletUserMilestoneResolver

	LeafletRepository:
		class: Kaufino\Model\Leaflets\Repositories\LeafletRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Leaflets\Entities\Leaflet')

	LeafletPageRepository:
		class: Kaufino\Model\Leaflets\Repositories\LeafletPageRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Leaflets\Entities\LeafletPage')

	commands.processLeaflets:
		class: Kaufino\Commands\ProcessLeaflets
		tags: [console.command: kaufino:process-leaflets]

	commands.processLeafletPages:
		class: Kaufino\Commands\ProcessLeafletPages
		tags: [console.command: kaufino:process-leaflet-pages]

	commands.processTopLeaflets:
		class: Kaufino\Commands\ProcessTopLeaflets
		tags: [console.command: kaufino:process-top-leaflets]

	commands.archiveLeaflets:
		class: Kaufino\Commands\ArchiveLeaflets
		tags: [console.command: kaufino:archive-leaflets]

	commands.prepareContentBlocks:
		class: Kaufino\Commands\PrepareContentBlocks
		tags: [console.command: kaufino:prepare-content-blocks]

	commands.generateContentBlocks:
		class: Kaufino\Commands\GenerateContentBlocks
		tags: [console.command: kaufino:generate-content-blocks]

	commands.processCitiesFromFile:
		class: Kaufino\Commands\ProcessCitiesFromFile
		tags: [console.command: kaufino:process-cities-from-file]

	commands.processStoresActivation:
		class: Kaufino\Commands\ProcessStoresActivation
		tags: [console.command: kaufino:process-stores]

	commands.sandbox:
		class: Kaufino\Commands\Sandbox
		tags: [console.command: kaufino:sandbox:run]

	commands.importSqlData:
		class: Kaufino\Commands\ImportSqlData
		tags: [console.command: kaufino:import-sql-data:run]

	# Localization
	- Kaufino\Model\Localization\FlagFilter
	- Kaufino\Model\Localization\LocalizationFacade
	- Kaufino\Model\Localization\LocalDateFilter
	- Kaufino\Model\Localization\GeoResolver
	- Kaufino\Model\Localization\PriceFilter
	- Kaufino\Model\Localization\DayGenitiveFilter
	- Kaufino\Model\Localization\DayNominativeFilter
	- Kaufino\Model\Localization\MonthNameFilter
	- Kaufino\Model\Localization\LocalizationManager

	LocalizationRepository:
		class: Kaufino\Model\Localization\Repositories\LocalizationRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Localization\Entities\Localization')

	# Websites
	- Kaufino\Model\Websites\WebsiteFacade
	- Kaufino\Model\Websites\WebsiteResolver

	WebsiteRepository:
		class: Kaufino\Model\Websites\Repositories\WebsiteRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Websites\Entities\Website')


	# Sitemap
	- Kaufino\Model\Sitemap\SitemapGenerator

	# Articles
	- Kaufino\AdminModule\Forms\IArticleControlFactory

	- Kaufino\Model\Articles\ArticleFacade
	- Kaufino\Model\Articles\ArticleManager

	ArticleRepository:
		class: Kaufino\Model\Articles\Repositories\ArticleRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Articles\Entities\Article')


	# Shops
	- Kaufino\AdminModule\Forms\IShopControlFactory

	- Kaufino\Model\Shops\ShopFacade
	- Kaufino\Model\Shops\ShopManager
	- Kaufino\Forms\ShopReviewControl\ShopReviewControlFactory

	- Kaufino\Model\Shops\StoreFacade
	- Kaufino\Model\Shops\StoreManager

	ShopRepository:
		class: Kaufino\Model\Shops\Repositories\ShopRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Shops\Entities\Shop')

	StoreRepository:
		class: Kaufino\Model\Shops\Repositories\StoreRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Shops\Entities\Store')

	commands.processShops:
		class: Kaufino\Commands\ProcessShops
		tags: [console.command: kaufino:process-shops]

	commands.processStores:
		class: Kaufino\Commands\ProcessStores
		tags: [console.command: kaufino:process-stores]

	# ContentBlocks
	- Kaufino\Model\Shops\ContentBlockFacade
	- Kaufino\Model\Shops\ContentBlockManager
	- Kaufino\AdminModule\Forms\ContentBlocksControl\IContentBlockControlFactory
	- Kaufino\Model\Shops\ContentBlockTypeManager

	ContentBlockRepository:
		class: Kaufino\Model\Shops\Repositories\ContentBlockRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Shops\Entities\ContentBlock')

	ContentBlockTypeRepository:
		class: Kaufino\Model\Shops\Repositories\ContentBlockTypeRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Shops\Entities\ContentBlockType')

	# Reviews
	- Kaufino\Model\Shops\ReviewFacade
	- Kaufino\Model\Shops\ReviewManager
	- Kaufino\AdminModule\Forms\ReviewControl\IReviewControlFactory

	ReviewRepository:
		class: Kaufino\Model\Shops\Repositories\ReviewRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Shops\Entities\Review')

	# Offers
	- Kaufino\AdminModule\Forms\IOfferControlFactory
	- Kaufino\AdminModule\Forms\IOfferLeafletPageControlFactory

	- Kaufino\Model\Offers\OfferFacade
	- Kaufino\Model\Offers\OfferManager
	- Kaufino\Model\Offers\OfferAutocomplete

	OfferRepository:
		class: Kaufino\Model\Offers\Repositories\OfferRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Offers\Entities\Offer')

	commands.removeOfferImages:
		class: Kaufino\Commands\RemoveOfferImages
		tags: [console.command: kaufino:remove-offer-images:run]

	# Geo
	- Kaufino\Model\Geo\GeoFacade
	- Kaufino\Model\Geo\GeoManager

	cityRepository:
		class: Kaufino\Model\Geo\Repositories\CityRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Geo\Entities\City')

	- Kaufino\Forms\CityPickerControl\CityPickerControlFactory
	- Kaufino\Forms\ContestControl\ContestControlFactory

	commands.processCities:
		class: Kaufino\Commands\ProcessCities
		tags: [console.command: kaufino:process-cities]

	# Products
	- Kaufino\AdminModule\Forms\IProductControlFactory

	- Kaufino\Model\Products\ProductFacade
	- Kaufino\Model\Products\ProductManager
	- Kaufino\Model\Products\ProductAutocomplete

	ProductRepository:
		class: Kaufino\Model\Products\Repositories\ProductRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Products\Entities\Product')


	# Competitors
	- Kaufino\AdminModule\Forms\ICouponControlFactory

	- Kaufino\Model\Competitors\CompetitorFacade
	- Kaufino\Model\Competitors\CompetitorManager

	CouponRepository:
		class: Kaufino\Model\Competitors\Repositories\CouponRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Competitors\Entities\Coupon')

	commands.processCoupons:
		class: Kaufino\Commands\ProcessCoupons
		tags: [console.command: kaufino:process-coupons]

	commands.processProduts:
		class: Kaufino\Commands\ProcessProducts
		tags: [console.command: kaufino:process-products]

	commands.processReviews:
		class: Kaufino\Commands\ProcessReviews
		tags: [console.command: kaufino:process-reviews]

	commands.processReport:
		class: Kaufino\Commands\ProcessReport
		tags: [console.command: kaufino:process-report:run]

	# Tags
	- Kaufino\AdminModule\Forms\TagControl\TagControlFactory

	- Kaufino\Model\Tags\TagFacade
	- Kaufino\Model\Tags\TagManager

	TagRepository:
		class: Kaufino\Model\Tags\Repositories\TagRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Tags\Entities\Tag')

	commands.processTags:
		class: Kaufino\Commands\ProcessTags
		tags: [console.command: kaufino:process-tags]

	# Users
	UserRepository:
		class: Kaufino\Model\Users\Repositories\UserRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Users\Entities\User')

	- Kaufino\Model\Users\UserFacade
	- Kaufino\AdminModule\Forms\UserControl\UserControlFactory

	- Kaufino\Model\Users\UserManager
	- Kaufino\Model\Users\UserAuthenticator
	- Kaufino\Model\Users\UserIdentity

	# Seo
	- Kaufino\Model\Seo\SeoGenerator
	- Kaufino\Model\Seo\SeoFacade
	- Kaufino\Model\Seo\SeoManager

	PageExtensionRepository:
		class: Kaufino\Model\Seo\Repositories\PageExtensionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Seo\Entities\PageExtension')

	RedirectionRepository:
		class: Kaufino\Model\Seo\Repositories\RedirectionRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Seo\Entities\Redirection')

	- Kaufino\AdminModule\Forms\IPageExtensionControlFactory

	# ChatGPT
	- Kaufino\Model\ChatGPT\ChatGPTClient

	# Content
	- Kaufino\Model\Content\ContentGenerator
	- Kaufino\Model\Content\ContentFilter


	# Translation
	- Kaufino\AdminModule\TranslationsModule\Forms\IDictionaryControlFactory
	- Kaufino\Model\Translations\TranslationFacade(%translations.localeDir%)


	# Images
	- Kaufino\Model\Images\ImageFilter
	- Kaufino\Model\Images\ImageStorage

	# Azure
	- Kaufino\Model\Azure\AzureClient

	# GoogleOptimize
	- Kaufino\Model\GoogleOptimize\GoogleOptimize

	# Router
	router: Kaufino\Router\RouterFactory::createRouter

	# Sentry & newrelic
	- Kaufino\Model\Sentry\SentrySubscriber
	- Kaufino\Model\NewRelic\NewRelicSubscriber

	# Log
	- Kaufino\Model\Log\Subscribers\VersionSubscriber
	- Kaufino\Model\Log\Subscribers\BadRequestSubscriber

	# Marketing
	- Kaufino\Model\Marketing\EmailManager
	- Kaufino\Model\Marketing\MarketingFacade

	EmailRepository:
		class: Kaufino\Model\Marketing\Repositories\EmailRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Marketing\Entities\Email')

	AdUnitRepository:
		class: Kaufino\Model\Marketing\Repositories\AdUnitRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Marketing\Entities\AdUnit')

	# Cached response manager
	- Kaufino\Model\ResponseCacheManager

	- Kaufino\Model\Offerista\ClientFactory

	# DatadogLog
	- Kaufino\Model\Datadog\DatadogClient(%datadog.apiKey%, %datadog.appKey%)

	# Document
	- Kaufino\Model\Conditions\DocumentFacade
	DocumentRepository:
		class: Kaufino\Model\Conditions\Repositories\DocumentRepository
		factory: @Doctrine\ORM\EntityManagerInterface::getRepository('Kaufino\Model\Conditions\Entities\Document')
tracy:
	strictMode: false
	bar:
		- Kaufino\Model\Tracy\DomainSwitcherPanel