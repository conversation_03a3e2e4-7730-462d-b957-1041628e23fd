{block title}{if $pageExtension && $pageExtension->getTitle()}{$pageExtension->getTitle()}{else}{$article->getName()}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$pageExtension->getDescription()}{else}{$article->getShortDescription()}{/if}{/block}

{block image}{if $article->getImageUrl()}{$article->getImageUrl() |image:700,350}{/if}{/block}

{block scripts}
    {include parent}

    {var $link = '"' . $presenter->link('//this') . '"'}

    <!-- Article schema -->
	<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Article",
        "headline": {$article->getName()},
        "url": {$link|noescape},
        "author": {
        	"@type": "Person",
        	"name": "Mr<PERSON><PERSON><PERSON>"
        },
        "datePublished": {$article->getPublishedAt()|date:'%Y-%m-%d'},
        "dateModified": {$article->getPublishedAt()|date:'%Y-%m-%d'},
        "publisher": {
        	"@type": "Organization",
        	"name": "MrOferto"        	
        },
        "image": {
        	"@type": "ImageObject",
        	"width": "700",
        	"height": "350",
        	"url": {if $article->getImageUrl()}{$article->getImageUrl() |image:700,350}{/if}
        }
    }
  </script>
{/block}

{block content}
	<div class="container mw-700">        
		<h1 class="k__title mt-5 mb-3 fw-700">{$article->getName()}</h1>	
		
        <div class="d-flex align-items-center mt-3 mb-3">
            <strong class="">MrOferto</strong>
            {*<span class="mx-3">|</span>*}
            {*<span>{$article->getPublishedAt()|localDate:'long'}</span>*}                                                                          
        </div>

        <div class="color-grey fz-m lh-15 mb-3">
            {$article->getShortDescription()}
        </div>
                                
        <img n:if="$article->getImageUrl()" src="{$article->getImageUrl() |image:700,350}" width="700" height="350" alt="{$article->getName()}" class="img-responsive mt-4 mb-4" loading="lazy">                            

        <div class="k-content">
            {cache md5($article->getContent()), expire => '20 minutes'}
                {$article->getContent()|content|noescape}
            {/cache}        
        </div>

        <div class="d-flex flex-wrap mt-5 mb-3">
            {*<a n:href="Articles:articles" class="k-button">{_"$websiteType.article.button"}</a>*}

            {if $previousArticle}
                <a n:href="Article:article $previousArticle" class="td-hover-underline flex-shrink-0 mb-3">« {$previousArticle->getName()}</a>
            {/if}

            {if $nextArticle}
                <a n:href="Article:article $nextArticle" class="ml-auto td-hover-underline flex-shrink-0 mb-3">{$nextArticle->getName()} »</a>
            {/if}            
        </div>
    </div>    
{/block}
