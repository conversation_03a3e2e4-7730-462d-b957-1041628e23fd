{var $logo = $shop->getSvgLogo() ? $shop->getSvgLogo() : $shop->getLogo()}

{block title}
{$shop->getName()}
{/block}

{block metaDescription}
	{var $hasOffers = $topOfferRelative !== null || $topOfferAbsolute !== null}

	{var $shopName = ucfirst($shop->getName())}
	{if $pageExtension && $pageExtension->isOverride()}
		{$pageExtension->getMetaDescription()}
	{elseif $hasOffers}
		{var $topOfferRelativeValue = $topOfferRelative !== null ? $topOfferRelative->getValue() : 0}
		{var $topOfferAbsoluteValue = $topOfferAbsolute !== null ? $topOfferAbsolute->getValue() : 0}

		{var $topOfferRelativeValueFormatted = $topOfferRelative !== null ? $topOfferRelativeValue . " " .
		$topOfferRelative->getUnitSymbol() : ""}
		{var $topOfferAbsoluteValueFormatted = $topOfferAbsolute !== null ? $topOfferAbsoluteValue . " " .
		$topOfferAbsolute->getUnitSymbol() : ""}

		{if
		$topOfferRelative !== null && $topOfferRelative->isCouponType() && $topOfferRelative->getCode() !== null &&
		$topOfferAbsolute !== null && $topOfferAbsolute->isCouponType() && $topOfferAbsolute->getCode() !== null
		}
			{if $topOfferRelative->isExclusive()}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
			$topOfferRelativeValueFormatted])}
			{elseif $topOfferAbsolute->isExclusive()}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
				$topOfferAbsoluteValueFormatted])}
			{elseif $topOfferRelativeValue * $coefficient >= $topOfferAbsoluteValue}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
				$topOfferRelativeValueFormatted])}
			{else}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
				$topOfferAbsoluteValueFormatted])}
			{/if}
			{elseif $topOfferRelative !== null && $topOfferRelative->isCouponType() && $topOfferRelative->getCode() !== null}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
				$topOfferRelativeValueFormatted])}
			{elseif $topOfferAbsolute !== null && $topOfferAbsolute->isCouponType() && $topOfferAbsolute->getCode() !== null}
				{$translator->translate('front.shops.shop.shop.metaDescription.salesCode', ['brand' => $shopName, 'value' =>
				$topOfferAbsoluteValueFormatted])}
			{elseif $topOfferRelativeValue * $coefficient >= $topOfferAbsoluteValue}
				{$translator->translate('front.shops.shop.shop.metaDescription.sales', ['brand' => $shopName, 'value' =>
				$topOfferRelativeValueFormatted])}
			{else}
				{$translator->translate('front.shops.shop.shop.metaDescription.sales', ['brand' => $shopName, 'value' =>
				$topOfferAbsoluteValueFormatted])}
			{/if}
		{elseif $shop->isCashbackAllowed()}
			{$translator->translate('front.shops.shop.shop.metaDescription.salesAndCode', ['brand' => $shopName])}
		{elseif $pageExtension && $pageExtension->getMetaDescription()}
			{$pageExtension->getMetaDescription()}{elseif
			$descriptionBlock('short_description')}{$descriptionBlock('short_description')[0]->getDescription()
			|stripHtml|truncate:160}
		{else}
			{_'front.head.description'}
	{/if}
{/block}

{block metaKeywords}
	{if $pageExtension &&
		$pageExtension->getMetaKeywords()}{$pageExtension->getMetaKeywords()}{else}{_'front.shops.shop.shop.metaKeywords.' .
		($shop->isCashbackActive() ? 'cashback' : 'withoutCashback')}
	{/if}
{/block}

{block metaTitle}
    {var $title = $pageExtension && $pageExtension->getMetaTitle() ? $pageExtension->getMetaTitle() : $shop->getName()}
    {var $hasOffers = $topOfferRelative !== null || $topOfferAbsolute !== null}
    {var $shopName = ucfirst($shop->getName())}

    {if $pageExtension && $pageExtension->isOverride()}
        {$pageExtension->getMetaTitle()}
    {elseif $hasOffers}
        {var $topOfferRelativeValue = $topOfferRelative !== null ? $topOfferRelative->getValue() : 0}
        {var $topOfferAbsoluteValue = $topOfferAbsolute !== null ? $topOfferAbsolute->getValue() : 0}

        {var $topOfferRelativeValueFormatted = $topOfferRelative !== null ? $topOfferRelativeValue . " " . $topOfferRelative->getUnitSymbol() : ""}
        {var $topOfferAbsoluteValueFormatted = $topOfferAbsolute !== null ? $topOfferAbsoluteValue . " " . $topOfferAbsolute->getUnitSymbol() : ""}

        {if $topOfferRelative !== null && $topOfferRelative->isCouponType() && $topOfferRelative->getCode() !== null && $topOfferAbsolute !== null && $topOfferAbsolute->isCouponType() && $topOfferAbsolute->getCode() !== null}
            {if $topOfferRelative->isExclusive()}
                {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferRelativeValueFormatted])}
            {elseif $topOfferAbsolute->isExclusive()}
                {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferAbsoluteValueFormatted])}
            {elseif $topOfferRelativeValue * $coefficient >= $topOfferAbsoluteValue}
                {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferRelativeValueFormatted])}
            {else}
                {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferAbsoluteValueFormatted])}
            {/if}
        {elseif $topOfferRelative !== null && $topOfferRelative->isCouponType() && $topOfferRelative->getCode() !== null}
            {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferRelativeValueFormatted])}
        {elseif $topOfferAbsolute !== null && $topOfferAbsolute->isCouponType() && $topOfferAbsolute->getCode() !== null}
            {$translator->translate('front.shops.shop.shop.metaTitle.salesCode', ['brand' => $shopName, 'value' => $topOfferAbsoluteValueFormatted])}
        {elseif $topOfferRelativeValue * $coefficient >= $topOfferAbsoluteValue}
            {$translator->translate('front.shops.shop.shop.metaTitle.sales', ['brand' => $shopName, 'value' => $topOfferRelativeValueFormatted])}
        {else}
            {$translator->translate('front.shops.shop.shop.metaTitle.sales', ['brand' => $shopName, 'value' => $topOfferAbsoluteValueFormatted])}
        {/if}
        {$translator->translate('newFront.calendar.months.' . date('n'))} {date('Y')}

{*    {elseif $shop->isCashbackAllowed()}*}
{*        {$shop->getName()|firstUpper}*}
{*        {if $shop->isCashbackActive()}*}
{*            {$shop|reward:false,'complete' |noescape}*}
{*        {else}*}
{*			{$translator->translate('front.shops.shop.shop.metaTitle.salesAndCode')}*}
{*        {/if}*}
{*        {$translator->translate('newFront.calendar.months.' . date('n'))} {date('Y')}*}

{*    {elseif $pageExtension && $pageExtension->isDisableGeneratedMetaTitle()}*}
{*        {$title}*}
	{else}
		{$shop->getName()|firstUpper}
		{if $shop->isCashbackActive()}
			{$shop|reward:false,'complete' |noescape}
		{else}
			{$translator->translate('front.shops.shop.shop.metaTitle.salesAndCode')}
		{/if}
		{$translator->translate('newFront.calendar.months.' . date('n'))} {date('Y')}
	{/if}
{*    {else}*}
{*        {var $currentMonth = $translator->translate('newFront.calendar.months.' . date('n'))}*}
{*        {var $titleOfferType = null}*}

{*        {cache 'shop-title-reward' . $cashbackCacheKeyPostfix, 'expire' => ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}*}
{*            {capture $titleReward |trim}*}
{*                {var $titleContainsDealPrefix = false}*}

{*                {foreach explode('|', $translator->translate('model.shops.dealPrefixes')) as $dealPrefix} *}{*kod|kupon|sleva|slevy*}
{*                    {if \Nette\Utils\Strings::contains(\Nette\Utils\Strings::webalize($title), $dealPrefix)}*}
{*                        {var $titleContainsDealPrefix = true}*}
{*                        {breakIf $titleContainsDealPrefix}*}
{*                    {/if}*}
{*                {/foreach}*}

{*                {var $topDeal = $topDeal()}*}
{*                {if $topDeal && ($topDeal->getValue() || $topDeal->isProductType()) && !($topDeal->isProductType() && $shop->isCashbackActive())}*}
{*                    {if !$titleContainsDealPrefix}*}
{*                        *}{* Kupon/Sleva *}
{*                        {$topDeal->isCouponType() ? $translator->translate('front.deals.deal.coupon') : $translator->translate('front.deals.deal.sale') |lower}*}
{*                    {/if}*}

{*                    {if $topDeal->isProductType()}*}
{*                        *}{* 100 kc *}
{*                        {$topDeal->getOriginalPrice() - $topDeal->getPrice()} {$topDeal->getPriceCurrency() |currency}*}
{*                    {else}*}
{*                        *}{* 20 % *}
{*                        -{$topDeal->getValue() |amount: 2}*}
{*                        {if !($topDeal->getUnit() == 'percentage' && $topDeal->getLocalization()->isPolish())}*}
{*                        {/if}{$topDeal->getUnitSymbol()}*}
{*                    {/if}*}

{*                    {var $titleOfferType = 'topDeal'}*}
{*                {elseif $shop->isCashbackActive()}*}
{*                    {$shop|reward:false,'complete' |noescape}*}

{*                    {var $titleOfferType = 'cashback'}*}
{*                {elseif !$titleContainsDealPrefix}*}
{*                    {$translator->translate('model.shops.rewardFilter.withoutOffer')}*}

{*                    {var $titleOfferType = 'other'}*}
{*                {/if}*}
{*            {/capture}*}

{*            {if $localization->isPolish()}*}
{*                {capture $titleReward |replace: [' %', ' %'], '%'}*}
{*                    {$titleReward}*}
{*                {/capture}*}
{*            {/if}*}

{*			{if $pageExtension && $pageExtension->getMetaTitle()}*}
{*				{$title}*}
{*			{else}*}
{*				{$titleReward}*}
{*			{/if}*}

{*			{$currentMonth . ' ' . date('Y')}*}
{*        {/cache}*}
{*    {/if}*}
{/block}


{define logo}
{var $shopCookie = "shop-cookie-" . $shop->getId()}
{if $shop->isCashbackAllowed()}
{if $shop->isCashbackActive()}
{if $isUserLoggedIn}
{if $popup}
<a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
	class="shop-profile__logo-img-wrapper" data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
	title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
	data-action="click" data-label="cashbackPopupBtnLogOut"
	data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{else}
<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
	target="_blank"
	data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
	data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
	title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
	data-action="click" data-label="cashbackBtnLogIn">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{/if}
{else}
<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
	class="shop-profile__logo-img-wrapper"
	onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
	class="" data-shop-id="{$shop->getId()}">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{/if}
{else}
<div class="shop-profile__logo-img-wrapper">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</div>
{/if}
{else}
{if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
	class="ajax shop-profile__logo-img-wrapper" data-ajax-call="js-open-popup" data-hit="event"
	data-category="shopDetail" data-action="click" data-label="noCashbackPopupBtnLogOut">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{else}
{if $isUserLoggedIn}
<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
	target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
	data-label="noCashbackRedirectBtnLogIn">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{else}
<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
	target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
	data-label="noCashbackRedirectBtnLogOut">
	<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
</a>
{/if}
{/if}
{/if}
{/define}

{define ctaLink}
{var $shopCookie = "shop-cookie-" . $shop->getId()}
	{if $shop->isCashbackAllowed()}
		{if $shop->isCashbackActive()}
			{if $isUserLoggedIn}
				{if $popup}
					<a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
						class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
						data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
						title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
						data-action="click" data-label="cashbackPopupBtnLogOut"
						data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
						{if $shop->getShopData()->getRedirectLabel()}
							{$shop->getShopData()->getRedirectLabel()}
						{else}
							{_'front.shops.shop.shop.shopDetail.btntitle'}
						{/if}
					</a>
				{else}
					<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
						class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
						data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
						data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
						title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
						data-action="click" data-label="cashbackBtnLogIn">
						{if $shop->getShopData()->getRedirectLabel()}
							{$shop->getShopData()->getRedirectLabel()}
						{else}
							{_'front.shops.shop.shop.shopDetail.btntitle'}
						{/if}
					</a>
				{/if}
			{else}
				<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
					class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
					onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
					data-shop-id="{$shop->getId()}">
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'front.shops.shop.shop.shopDetail.btntitle'}
					{/if}
				</a>
			{/if}
		{else}
			{if $shop->isPaused() === true}
				<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
					class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
					data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
					data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'front.nocashbackShop.goToShop'}
					{/if}
				</a>
			{else}
				<span class="shop-detail__button shop-detail__button--disabled">
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'front.shops.shop.shop.btnNoCashback'}
					{/if}
				</span>
			{/if}
		{/if}
	{else}
		{if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
			<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
				class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
				data-ajax-call="js-open-popup" data-hit="event" data-category="shopDetail" data-action="click"
				data-label="noCashbackPopupBtnLogOut">
				{if $shop->isGamble()}
					{_'newFront.nocashbackShop.goToShopGamble'}
				{else}
					{_'front.nocashbackShop.goToShop'}
				{/if}
			</a>
		{else}
			<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
				class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
				data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
				data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
				{if $shop->isGamble()}
					{_'newFront.nocashbackShop.goToShopGamble'}
				{else}
					{_'front.nocashbackShop.goToShop'}
				{/if}
			</a>
		{/if}
	{/if}
{/define}

{define offersWithConditions}
<div class="hidden mb-5 xl:block" n:if="$shop->isActive() && $shop->isCashbackActive()">
    <div id="conditions" class="bg-white pt-[21px] px-5 pb-[25px] rounded-2xl">
        <div class="px-5 flex justify-between items-center">
            <div class="text-lg text-dark-1 font-medium leading-[31.5px]">
                {_newFront.shops.shop.shop.conditions.title}
            </div>
            <button id="open-conditions-popup" n:if="$shop->isGamble() === false"
                class="text-sm text-dark-2 leading-[24.5px] hover:cursor-pointer hover:underline">
                {_newFront.shops.shop.shop.conditions.conditionsTitle} »
            </button>
        </div>

        <div class="w-full h-px bg-light-5 mt-[19px] mb-[17px]"></div>
            {if count($offers) >= 1}
                <div id="offer-list">
                    {foreach $offers as $offer}
                            <div class="flex justify-between items-center bg-[#FAFAFB] rounded-lg px-5 py-2 mb-1 {if $iterator->counter > 6}hidden extra-offers{/if} {if $iterator->counter == 6}opacity-10{/if}">
                                    <div class="text-sm text-dark-1 leading-[24.5px]">{$offer->getName()}</div>
                                    <div class="shop-offers text-xl text-secondary-green font-bold leading-[35px]">
                                            {$offer |reward:true |noescape}
                                    </div>
                            </div>
                    {/foreach}
                </div>

                {if count($offers) >= 6}
                        <div id="show-more-offers" class="relative top-[-53px] flex items-center justify-center gap-1.5 cursor-pointer mt-3 text-sm leading-[24.5px] underline text-dark-2 xl:hover:no-underline">
							{_newFront.shops.shop.shop.offers.more}
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="7" viewBox="0 0 10 7" fill="none">
                                    <path d="M1 1L4.38967 5.23708C4.70256 5.6282 5.29744 5.6282 5.61033 5.23708L9 1" stroke="#646C7C"/>
                            </svg>
                        </div>
                {/if}
            {/if}

		<div class="mb-5"></div>

		{*Pre-redirect message for web*}
        <div n:if="$warningMessage = $shop->getWarningMessage()" class="bg-pastel-orange-light text-primary-orange text-sm px-5 py-[15px] rounded-lg leading-[24.5px] font-medium mb-2.5">
            {$warningMessage |noescape}
        </div>

        <div n:if="$infoMessage = $shop->getInfoMessage()" class="bg-[#F8F3EE] text-[#6B5E62] text-sm px-5 py-[15px] font-medium rounded-lg leading-[24.5px] mb-2.5">
            {$infoMessage}
        </div>

		<div n:if="$cashbackConditions = $shop->getConditions()" class="bg-[#F8F8F9] text-slate-700 text-sm px-5 py-[15px] font-medium rounded-lg leading-[24.5px] mb-2.5">
			{$cashbackConditions}
		</div>


        <div class="flex items-center gap-[7px] pl-5 text-dark-2 text-sm leading-[24.5px] mt-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19"
                fill="none">
                <path
                    d="M9.5 18C14.1944 18 18 14.1944 18 9.5C18 4.80558 14.1944 1 9.5 1C4.80558 1 1 4.80558 1 9.5C1 14.1944 4.80558 18 9.5 18Z"
                    stroke="#80899C" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round" />
                <path d="M9.40527 8.49121V13.704" stroke="#80899C" stroke-width="1.5"
                    stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="9.40559" cy="5.40803" r="1.02766" fill="#80899C" />
            </svg>
			{if $shop->isGamble()}
				{_newFront.shops.shop.shop.conditions.text|replace: 'z nákupu', ''}
			{else}
				{_newFront.shops.shop.shop.conditions.text}
			{/if}

		</div>
    </div>
</div>
{/define}

{block styles}

<link rel="preload" fetchpriority="high" as="image" href="/new-design/wp-mobile-bg-orange-small.svg" as="image" type="image/svg+xml">

<style>
	.bg-img {
		left: 0;
		z-index: 10;
		position: absolute;
		width: 100%;
		height: 367px;
		background-image: url(/new-design/wp-mobile-bg-orange-small.svg);
		background-repeat: no-repeat;
		background-size: cover;
		background-position: bottom;
	}

	.no-scroll {
		overflow: hidden;
	}

	.description-transparency::after {
		content: '';
		position: absolute;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 1.5em;
		background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1) 80%);
	}

	@media only screen and (max-width: 768px) {
		.description-transparency::after {
			background: linear-gradient(to right, rgba(255,255,255,0), rgba(244, 244, 246,1) 75%);
		}
	}
</style>
{/block}

{block scripts}
	<script src="{$basePath}/js/shop.front-new.js?v=0.04" defer></script>

	{if !$shop->isCashbackActive()}
		<script src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" defer></script>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({
                google_ad_client: "ca-pub-3515231881192503",
                 enable_page_level_ads: true
            });
        </script>

		<script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>
	{/if}

	<script>
		document.addEventListener('DOMContentLoaded', function () {
			if (window.location.hash === '#conditions') {
				const showMoreOffers = document.getElementById('open-conditions-modal');
				if (showMoreOffers) {
					showMoreOffers.click();
				}
			}
		});
	</script>
{/block}

{block footerBackgroundColor}bg-light-6{/block}

{block content}
{var $offers = $shopOffers()}
{var $campaignBonusAmount = $getCampaignBonusAmount()}

<div class="bg-light-6 relative">
	<div class="hidden md:block absolute w-full h-[220px] bg-repeat z-10" style="background-image:url('{$basePath}/new-design/profile-shop/bg-profile-shop.svg'); background-position: 0 -166px;">
	</div>

	<div class="bg-img lg:hidden"></div>

	<div class="container relative z-20 xl:p-0">
		<div id="conditions-modal"
			class="hidden fixed z-30 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center px-5">
			<div class="relative bg-white m-auto p-5 w-full max-w-[500px] rounded-2xl">
				<div id="close-conditions-modal" class="hover:cursor-pointer absolute top-[-19px] right-[-28px]">
					<img src="{$basePath}/new-design/close-btn.svg" alt="close" loading="lazy">
				</div>
				<div class="text-lg text-dark-1 font-bold leading-[31.5px]">{_newFront.shops.shop.shop.conditions.heading}
				</div>
				<div class="w-full h-px bg-light-5 mt-[19px] mb-[20px]"></div>

				{if count($offers) >= 1}
					{foreach $offers as $offer}
						<div class="flex justify-between items-center bg-[#FAFAFB] rounded-lg px-5 py-2 mb-1 ">
							<div class="text-sm text-dark-1 leading-[24.5px]">{$offer->getName()}</div>
							<div class="text-secondary-green font-bold leading-[35px]">{$offer|reward:true|noescape}</div>
						</div>
					{/foreach}
				{/if}

				<div class="mb-5"></div>

				<div n:if="$warningMessage = $shop->getMobileWarningMessage()"
						class="bg-pastel-orange-light text-primary-orange text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5">
					{$warningMessage |noescape}
				</div>

				<div class="bg-[#F8F3EE] text-[#6B5E62] text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5"
						n:if="$infoMessage = $shop->getInfoMessage()">
					{$infoMessage}
				</div>

				<div class="bg-[#F8F8F9] text-slate-700 text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5"
						n:if="$cashbackConditions = $shop->getConditions()">
					{$cashbackConditions}
				</div>

				<div class="flex items-center gap-[7px] text-dark-2 text-xs leading-[21px]">
					<div class="self-start">
						<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
							<path
								d="M9.5 18C14.1944 18 18 14.1944 18 9.5C18 4.80558 14.1944 1 9.5 1C4.80558 1 1 4.80558 1 9.5C1 14.1944 4.80558 18 9.5 18Z"
								stroke="#80899C" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
								stroke-linejoin="round"></path>
							<path d="M9.40527 8.49121V13.704" stroke="#80899C" stroke-width="1.5" stroke-miterlimit="10"
								stroke-linecap="round" stroke-linejoin="round"></path>
							<circle cx="9.40559" cy="5.40803" r="1.02766" fill="#80899C"></circle>
						</svg>
					</div>
					{_newFront.shops.shop.shop.conditions.text}
				</div>
			</div>
		</div>

		<div
			class="hidden xl:flex px-5 justify-between items-center text-dark-2 text-xs leading-[21px] md:text-sm md:leading-[24.5px] mb-6 md:mb-[25px] md:mt-5">
			<div class="flex gap-[10px] items-center">
				<svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12"
					fill="none">
					<path
						d="M2.46667 6.95165V11H5.4V8.05575C5.4 7.86049 5.47726 7.67329 5.61477 7.53525C5.7523 7.39722 5.93886 7.31968 6.13334 7.31968H6.86667C7.06115 7.31968 7.24766 7.39722 7.38523 7.53525C7.52276 7.67329 7.6 7.86049 7.6 8.05575V11H10.5333V6.95165M1 6.21559L5.98129 1.21575C6.04939 1.14735 6.13026 1.09309 6.21923 1.05607C6.30826 1.01905 6.40364 1 6.5 1C6.59636 1 6.69174 1.01905 6.78072 1.05607C6.86975 1.09309 6.95061 1.14735 7.01871 1.21575L12 6.21559"
						stroke="#646C7C" stroke-width="0.858714" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>

				<a class="hover:underline hover:cursor-pointer" n:href=":NewFront:Shops:Shops:">
					{_'front.shops.shop.shop.shopDetail.nav.shop'}
				</a>

				{if $navigationTag}
				<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
					<path
						d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z"
						fill="#646C7C" />
				</svg>
				<a class="hover:underline hover:cursor-pointer"
					n:href=":NewFront:Shops:Shops:, $navigationTag">
					{$navigationTag->getName()}
				</a>
				{/if}

				<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
					<path
						d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z"
						fill="#646C7C" />
				</svg>
				<div>{$shop->getName()}</div>
			</div>

			{if $isUserLoggedIn}
			{if $isShopFavorite}
			<a n:href="removeFromFavourites!"
				class="hidden xl:flex items-center gap-[10px] hover:cursor-pointer xl:hover:underline">
				{_newFront.shops.shop.shop.favorites.remove}
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16"
					fill="none">
					<path
						d="M15.9867 2.13876C15.6713 1.7789 15.2917 1.49243 14.8715 1.29667C14.4512 1.10093 13.9986 1 13.5413 1C13.0841 1 12.6315 1.10093 12.2112 1.29667C11.7909 1.49243 11.4114 1.7789 11.096 2.13876L9 4.49434L6.904 2.13876C6.58862 1.7789 6.20907 1.49243 5.7888 1.29667C5.36848 1.10093 4.91595 1 4.45867 1C4.00139 1 3.54885 1.10093 3.12853 1.29667C2.7082 1.49243 2.3288 1.7789 2.01333 2.13876C1.36292 2.87932 1 3.86378 1 4.8876C1 5.91161 1.36292 6.89601 2.01333 7.63653L8.25511 14.6533C8.35129 14.7629 8.46685 14.8502 8.59485 14.9098C8.72285 14.9693 8.8608 15 9 15C9.1392 15 9.27715 14.9693 9.40516 14.9098C9.53315 14.8502 9.64871 14.7629 9.74489 14.6533L15.9867 7.63653C16.6372 6.89601 17 5.91161 17 4.8876C17 3.86378 16.6372 2.87932 15.9867 2.13876Z"
						fill="url(#paint0_linear_827_2476)" stroke="url(#paint1_linear_827_2476)"
						stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
					<defs>
						<linearGradient id="paint0_linear_827_2476" x1="1" y1="15" x2="13.9429" y2="2.99748"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#EF7F1A" />
							<stop offset="1" stop-color="#FFA439" />
						</linearGradient>
						<linearGradient id="paint1_linear_827_2476" x1="1" y1="15" x2="13.9429" y2="2.99748"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#EF7F1A" />
							<stop offset="1" stop-color="#FFA439" />
						</linearGradient>
					</defs>
				</svg>
			</a>
			{else}
			<a n:href="addToFavourites!"
				class="hidden xl:flex items-center gap-[10px] hover:cursor-pointer xl:hover:underline">
				{_newFront.shops.shop.shop.favorites.add}
				<img src="{$basePath}/new-design/profile-shop/heart.svg" alt="heart" loading="lazy">
			</a>
			{/if}
			{/if}
		</div>

		<div class="flex flex-col xl:flex-row xl:gap-5">
			<div class="mt-10 xl:mt-0 xl:w-[310px] flex-shrink-0">
				{if $shop->isCashbackAllowed() && $shop->isCashbackActive()}
					<div class="mx-5 relative border-2 rounded-2xl border-primary-orange mb-5 xl:mx-0"
					style="background: linear-gradient(180deg, #FFF 33.01%, #FEF3E9 77.61%)">
					<div class="absolute right-[20px] top-[21px] xl:hidden">
						{if $isUserLoggedIn}
							{if $isShopFavorite}
								<a n:href="removeFromFavourites!">
									<svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16" fill="none">
										<path
												d="M15.9867 2.13876C15.6713 1.7789 15.2917 1.49243 14.8715 1.29667C14.4512 1.10093 13.9986 1 13.5413 1C13.0841 1 12.6315 1.10093 12.2112 1.29667C11.7909 1.49243 11.4114 1.7789 11.096 2.13876L9 4.49434L6.904 2.13876C6.58862 1.7789 6.20907 1.49243 5.7888 1.29667C5.36848 1.10093 4.91595 1 4.45867 1C4.00139 1 3.54885 1.10093 3.12853 1.29667C2.7082 1.49243 2.3288 1.7789 2.01333 2.13876C1.36292 2.87932 1 3.86378 1 4.8876C1 5.91161 1.36292 6.89601 2.01333 7.63653L8.25511 14.6533C8.35129 14.7629 8.46685 14.8502 8.59485 14.9098C8.72285 14.9693 8.8608 15 9 15C9.1392 15 9.27715 14.9693 9.40516 14.9098C9.53315 14.8502 9.64871 14.7629 9.74489 14.6533L15.9867 7.63653C16.6372 6.89601 17 5.91161 17 4.8876C17 3.86378 16.6372 2.87932 15.9867 2.13876Z"
												fill="url(#paint0_linear_827_2476_1)" stroke="url(#paint1_linear_827_2476_1)"
												stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
										<defs>
											<linearGradient id="paint0_linear_827_2476_1" x1="1" y1="15" x2="13.9429" y2="2.99748" gradientUnits="userSpaceOnUse">
												<stop stop-color="#EF7F1A" />
												<stop offset="1" stop-color="#FFA439" />
											</linearGradient>
											<linearGradient id="paint1_linear_827_2476_1" x1="1" y1="15" x2="13.9429" y2="2.99748" gradientUnits="userSpaceOnUse">
												<stop stop-color="#EF7F1A" />
												<stop offset="1" stop-color="#FFA439" />
											</linearGradient>
										</defs>
									</svg>

								</a>
							{else}
								<a n:href="addToFavourites!">
									<img src="{$basePath}/new-design/profile-shop/heart.svg" alt="heart" loading="lazy">
								</a>
							{/if}
						{/if}
					</div>
					<div class="py-5 px-5 h-[140px] flex items-center justify-center">
						{include logo}
					</div>
					<div>
						<img class="m-auto" src="{$basePath}/new-design/divider-profile-shop.svg" alt="divider" loading="lazy">
					</div>

					<div class="text-center leading-[28px] text-lg text-dark-2 {if $isUserLoggedIn}my-[30px] md:my-[35px]{else}my-[23px] md:my-[35px] reward-box--small{/if}">
						{if $isUserLoggedIn && $userIdentity->isActiveUser()}
						{cache 'shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
						($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
						{$shop |reward:true,'interval' |noescape}
						{/cache}
						{else}
						{cache 'shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
						($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
						{$shop |reward:true,'extended' |noescape}
						{/cache}
						{/if}
					</div>

					<div class="px-5 mb-4">
						{include ctaLink}
					</div>

					<div id="open-conditions-modal" n:if="$shop->isGamble() === false"
						class="flex items-center justify-center text-xs text-dark-2 mb-4 xl:hidden hover:cursor-pointer">
						<svg class="mr-1.5" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15"
							fill="none">
							<path
								d="M7.5 14C11.0898 14 14 11.0898 14 7.5C14 3.91015 11.0898 1 7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0898 3.91015 14 7.5 14Z"
								stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
							<path d="M7.42871 6.72852V10.7148" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round"
								stroke-linejoin="round" />
							<circle cx="7.49974" cy="4.90013" r="0.483333" stroke="currentColor" stroke-width="0.1" />
						</svg>
						{_newFront.shops.shop.shop.conditions.title}
					</div>

					{if $user->isLoggedIn() === false && $isLessDetailed === false}
					<div class="hidden md:block">
						<div
							class="max-w-[194px] text-center text-dark-1 text-sm font-medium leading-[24.5px] md:text-base leading-[26px] mx-auto mb-1">
							{_newFront.shops.shop.shop.ctaBox.title}
						</div>
						<div class="text-center text-dark-1 text-xs leading-[21px] md:text-sm leading-[24.5px] mb-1.5">
							{_newFront.shops.shop.shop.ctaBox.text}
						</div>

						<div class="text-center pb-[22px] md:pb-[25px]">
							<a class="open-register-popup text-sm leading-[24.5px] font-medium hover:underline text-primary-orange" href="#">
								{_newFront.shops.shop.shop.ctaBox.more}
							</a>
						</div>
						<img class="h-[188px] w-[91px] top-[135px] left-[-43px] xl:h-[155px] xl:w-[75px] xl:top-[286px] xl:right-[266px] xl:left-auto absolute"
							src="{$basePath}/new-design/donkey-profile-shop-side.png" alt="donkey" loading="lazy">
					</div>
					{/if}
				</div>
				{else}
					<div class="mx-5 relative border-2 rounded-2xl border-light-2 mb-10 xl:mb-5 xl:mx-0"
						style="background: linear-gradient(180deg, #FFF 33.01%, #E1E4E8 77.61%);">
						<div class="py-5 px-5 h-[140px] flex items-center justify-center">
							{include logo}
						</div>
						<div>
							<img class="m-auto" src="{$basePath}/new-design/divider-profile-shop.svg" alt="divider" loading="lazy">
						</div>
						<div class="md:text-lg px-[100px] leading-[31.5px] text-dark-1 text-center mt-[34px] mb-10">
							{_'front.nocashbackShop.info'}
						</div>
						<div class="px-5 mb-[19px]">
							{include ctaLink}
						</div>
					</div>
				{/if}

				<div n:if="
					$descriptionBlock('short_description') ||
					$descriptionBlock('coupon_instructions') ||
					count($reviews) > 0 ||
					($hasShopAllowedDeals && !$user->isLoggedIn() && !$getExpiredCoupons()->isEmpty())
				"
					class="hidden text-dark-1 text-lg font-medium leading-[31.5px] pt-5 px-5 border border-light-4 rounded-2xl mb-5 xl:block">
					{_'newFront.shops.shop.shop.navigation'}
					<div class="w-full h-px bg-light-4 mt-[18px] mb-5"></div>

					<a href="#description"
						class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]"
						n:if="$descriptionBlock('short_description')">
						{_'front.shops.shop.shop.shopDetail.nav.aboutShop', [shop => $shop->getName()]}
					</a>

					<a href="#shopReview" data-offset="70"
						class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]"
						n:if="count($reviews)">
						{_'front.shops.shop.shop.sidebarMenu.review', [shop => $shop->getName()]}
					</a>

					<a href="#expired"
						n:if="$hasShopAllowedDeals && !$user->isLoggedIn() && !$getExpiredCoupons()->isEmpty()"
						data-offset="70"
						class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]">
						{if $shop->getId() === 50}
						{_'front.shops.shop.shop.expiredCouponsTitleCedok', [shop => $shop->getName()]}
						{else}
						{_'front.shops.shop.shop.sidebarMenu.expired', [shop => $shop->getName()]}
						{/if}
					</a>

					<a href="#couponsDescription" n:if="$descriptionBlock('coupon_instructions')" data-offset="70"
						class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]">
						{if $descriptionBlock('coupon_instructions')[0]->getTitle()}
						{$descriptionBlock('coupon_instructions')[0]->getTitle()}
						{else}
						{_'front.shops.shop.shop.sidebarMenu.howApply', [name => $shop->getName()]}
						{/if}
					</a>
				</div>

				{if $presenter->getHttpRequest()->getCookie('d2s0KZA1rp9pwsRI9n0l')}
				<div class="text-block shop-profile--text-block mb-3">
					<div class="_content">
						<a n:href="aqPopup-open!, aqPopup-type => exit, aqPopup-shopId => $shop->getId()" class="ajax"
							data-ajax-call="js-open-popup">EXIT POPUP (s obchodem)</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
							class="ajax" data-ajax-call="js-open-popup">SHOP REDIRECTION POPUP</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => default" class="ajax"
							data-ajax-call="js-open-popup">DEFAULT POPUP</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => password" class="ajax"
							data-ajax-call="js-open-popup">PASSWORD POPUP</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => education, aqPopup-shopId => $shop->getId()"
							class="ajax" data-ajax-call="js-open-popup">EDUCATION POPUP</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()"
							class="ajax" data-ajax-call="js-open-popup">REDIRECT POPUP</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()" class="ajax"
							data-ajax-call="js-open-popup">ADDON</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => shopWarning, aqPopup-shopId => $shop->getId()"
							class="ajax" data-ajax-call="js-open-popup">SHOP WARNING</a>
						<br>
						<a n:href="aqPopup-open!, aqPopup-type => addonAfterRegistration" class="ajax"
							data-ajax-call="js-open-popup">ADDON After Registration</a>
							<br>
						<a n:href="aqPopup-open!, aqPopup-type => virtualCoupon,  aqPopup-shopId => $shop->getId()" class="ajax"
							data-ajax-call="js-open-popup">Virtual coupon
							</a><br>
						<button class="open-register-popup"
							data-ajax-call="js-open-popup">Registration popup
							</button>
					</div>
				</div>
				{/if}

				<div id="desktop-sidebar">
				    {cache 'shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $similarShops = $recommendedShops()}
                        <div class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$similarShops">
                            <div class="text-lg text-dark-1 font-medium leading-[31.5px]">
                                {_newFront.shops.shop.shop.favoriteShops.title}</div>
                            <div class="text-sm text-dark-1 leading-[24.5px]" n:if="$shop->isLessDetailed() === false">{_newFront.shops.shop.shop.favoriteShops.text,
                                ['shop' => $shop->getName()]}</div>
                            <div class="w-full h-px bg-light-5 my-5"></div>

                            {foreach $similarShops as $shopItem}
                            <a href="{plink Shop:default, $shopItem}" class="group flex gap-4 items-center mb-5 last:mb-0 hover:underline">
                                <div class="border border-light-5 rounded-xl flex justify-center items-center w-[97px] h-[55px] duration-200 group-hover:shadow-lg group-hover:border-transparent group-hover:cursor-pointer">
                                    <img class="max-w-[58px] max-h-[38px]" alt="{$shopItem->getName()}" src="{$shopItem->getLogo() |image:116,0,'fit',false,$shopItem->getName()}"  loading="lazy" />
                                </div>

                                <div class="text-sm text-dark-1 leading-[18.75px]">
                                    <div class="leading-[24.5px] mb-1.5 line-clamp-1">{$shopItem->getName()}</div>
                                    <div class="similar-shop__value">
                                        {$shopItem |reward:true,'extended'|noescape}
                                    </div>
                                </div>
                            </a>
                            {/foreach}
                        </div>
					{/cache}

					<div class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$shop->hasDisabledDeals() === false">
						<div class="text-lg text-dark-1 font-medium leading-[31.5px]">
							{_newFront.shops.shop.shop.recommendedShops.title}</div>
						<div class="text-sm text-dark-1 leading-[24.5px]" n:if="$shop->isLessDetailed() === false">{_newFront.shops.shop.shop.recommendedShops.text,
							['year' => date('Y'), 'month' => ('newFront.calendar.months.' . date('n') |translate)]}</div>

						<div class="w-full h-px bg-light-5 my-5"></div>

						<div class="grid grid-cols-3 gap-[10px] ">
                            {cache 'sidebarRecommendedShopsWithCoupons-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                                {foreach $sidebarRecommendedShopsWithCoupons() as $shopItem}
                                    <a n:href=":NewFront:Shops:Shop:default $shopItem" title="{_newFront.shops.shop.shop.recommendedShops.alt, ['brand' => $shopItem->getName()]}"
                                        class="border border-light-5 rounded-xl shadow-hover flex items-center justify-center h-[47px]">
                                        <img class="max-w-[50px] max-h-[22px]" alt="{_newFront.shops.shop.shop.recommendedShops.alt, ['brand' => $shopItem->getName()]}"
                                            src="{$shopItem->getLogo() |image:100,0}" loading="lazy" />
                                    </a>
                                {/foreach}
                            {/cache}
						</div>
					</div>

                    {cache 'shopTags-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}

					{/cache}

                    {cache 'shopForeignShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $foreignShops = $getForeignShops()}

                        <div class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$foreignShops">
                            <div class="text-lg text-dark-1 font-medium leading-[31.5px]">
                                {_newFront.shops.shop.shop.shopDetail.foreignShops, ['brand' => $shop->getName()]}
                            </div>

                            <div class="w-full h-px bg-light-5 mt-[18px] mb-5"></div>

                            <div class="flex gap-2">
                                {foreach $foreignShops as $foreignShop}
                                <a href="{$foreignShop->getForeignShopUrl()}">
                                    <img class="rounded-full w-[30px] h-[30px]"
                                        src="{$basePath}/new-design/flags/{$foreignShop->getShop()->getLocalization()->getLocale()}.svg"
                                        loading="lazy"
                                        alt="{$foreignShop->getShop()->getName()}">
                                </a>
                                {/foreach}
                            </div>
                        </div>
					{/cache}

                    {cache 'reviews-' . $shop->getId() . '-' . $localization->getLocale(), expire => '168 hours', tags => ['shop/' . $shop->getId()]}
                     <div id="shopReview" class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$reviews->isEmpty() === false">
                        <div class="text-lg text-dark-1 font-medium leading-[31.5px]">
                            {_newFront.shops.shop.shop.sidebarMenu.review, [shop => $shop->getName()]}
                        </div>

                        <div class="w-full h-px bg-light-5 mt-[18px] mb-5"></div>

                        {foreach $reviews as $review}
                            <div class="flex items-center gap-2 mb-[3px]">
                                <div class="text-sm text-dark-1 font-medium leading-[24.5px]">{$review->getShortUsername()}
                                </div>
								<div>
									{include stars.latte, 'rate' => $review->getRate()}
								</div>
                            </div>
                            <div class="text-sm text-dark-2 leading-[24.5px] mb-5 last:mb-0">
                                {$review->getText() |truncate: 100}
                            </div>
                        {/foreach}

                        <div class="w-full h-px bg-light-5 mt-[18px] mb-[25px]"></div>

                        <div class="flex items-center gap-2 text-sm text-dark-1 leading-[24.5px]">
							{var $averageShopReview = $getAverageShopReview()}
							<div>
								{include stars.latte, 'rate' => (int)$averageShopReview}
							</div>
                            <div class="font-bold">{number_format($averageShopReview, 1)}
                                <span class="font-normal">({$reviews->getTotalCount()})</span>
                            </div>
                        </div>
                    </div>
                    {/cache}
				</div>
			</div>

			<div>
                <div class="md:bg-white md:pt-0 p-5 mb-5 rounded-2xl">
                    <h1 class="text-[26px] text-dark-1 font-bold leading-[39px] xl:text-[40px] xl:leading-[58px] mb-2">
						{if !$user->isLoggedIn() && $shop->getShopData()->getHeading() !== null}
							{$shop->getShopData()->getHeading()}
						{elseif !$user->isLoggedIn() && isset($pageExtension) && $pageExtension->getHeading()}
							{$pageExtension->getHeading()}
						{else}
							{$shop->getName()}
						{/if}
                    </h1>

                    {cache 'shopShortDescriptionBlock-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $shortDescriptionBlock = $descriptionBlock('short_description')}
                        <div class="text-sm leading-[24.5px] text-dark-1 relative">
                            {if isset($shortDescriptionBlock[0])}
                                {var $length = strlen(strip_tags($shortDescriptionBlock[0]->getDescription()))}

                                <div id="description" class="description-transparency shop-description text-block shop-profile--text-block line-clamp-2">
                                    <div class="absolute right-[-17px] -top-[20px]">
                                        <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-short_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                            <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                                        </a>
                                    </div>
                                    {$shortDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                                </div>
                            {/if}

                            <div id="show-more" n:if="$shortDescriptionBlock" class="hidden md:block absolute right-0 top-[24px] bg-light-6 md:bg-white text-end text-sm leading-[24.5px] cursor-pointer underline xl:hover:no-underline">
                                {_newFront.shops.shop.shop.showAllText}
                            </div>
                            <div id="show-more-mobile" n:if="$shortDescriptionBlock" class="mr-10 absolute md:hidden text-primary-orange right-0 top-[24px] bg-light-6 text-end text-sm leading-[24.5px] cursor-pointer underline">
                                {_newFront.shops.shop.shop.showAllText}
                            </div>
                        </div>
                    {/cache}
                </div>

				{if $user->isLoggedIn()}
				    {include offersWithConditions}
				{/if}

				{if $shop->isActive() && $shop->isCashbackActive() && $userEntity?->isActiveUser() === false}
				    {include _guide.latte}
				{/if}

                {cache 'deals-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
				    <div>
					{var $deals = $getDeals()}

					<h2 n:if="$deals !== null && $deals->count() > 0" class="hidden pl-10 text-lg text-dark-1 font-medium leading-[31.5px] mb-5 xl:block">
						{_newFront.shops.shop.shop.deals.title}
					</h2>

					<div id="js-show-more-deals">
						<div class="grid grid-cols-1 gap-5 mb-10 xl:hidden" n:if="$deals">
							{foreach $deals as $deal}
								{if $user->isLoggedIn() === false && $cashbackDeal && $shop->isGamble() === false && $shop->isCashbackAllowed()}
									{if $iterator->counter === 1 && $shop->getCountOfCouponDeals() === 0}
										{include ../../../../DealsModule/Presenters/templates/Deals/dealItem-mobile.latte, deal: $cashbackDeal}
									{elseif $iterator->counter === 2 && $shop->getCountOfCouponDeals() > 0}
										{include ../../../../DealsModule/Presenters/templates/Deals/dealItem-mobile.latte, deal: $cashbackDeal}
									{/if}
								{/if}

								<div class="deal-item-wrapper">
									{include ../../../../DealsModule/Presenters/templates/Deals/dealItem-mobile.latte, deal: $deal}
								</div>
							{/foreach}
						</div>

						{var $countOfCashbackShops = $countOfCashbackShops}

						<div class="hidden xl:block" n:if="$deals">
							{foreach $deals as $deal}
								{if $user->isLoggedIn() === false && $cashbackDeal && $cashbackDeal && $shop->isGamble() === false && $shop->isCashbackAllowed()}
									{if $iterator->counter === 1 && $shop->getCountOfCouponDeals() === 0}
										{include dealItem.latte, deal: $cashbackDeal}
									{elseif $iterator->counter === 2 && $shop->getCountOfCouponDeals() > 0}
										{include dealItem.latte, deal: $cashbackDeal}
									{/if}
								{/if}

								<div class="deal-item-wrapper">
									{include dealItem.latte, deal: $deal}
								</div>

								{if $iterator->counter === 3 && $showAddonPromo && $shop->isAddonAllowed()}
									<div
										class="hidden xl:flex items-center justify-between bg-primary-blue-dark py-10 pl-[44px] pr-2.5 rounded-2xl relative mb-5">
										<div class="flex justify-evenly">
											<div class="text-[20px] text-white font-bold leading-[35px] max-w-[52%]">
												{_newFront.shops.shop.shop.addon.title} <span
													class="font-normal">{_newFront.shops.shop.shop.addon.titleSuffix, ['shopName' =>
													$shop->getName(), 'count' => ($countOfShops |amount)]}</span></div>
											<img class="block" width="200px" src="{$basePath}/new-design/ps-browsers.svg" alt="browsers" loading="lazy">
										</div>
										<a n:href=":NewFront:Static:addon"
											class="flex flex-shrink-0 items-center gap-[10px] text-primary-orange font-bold leading-[28px] border border-primary-orange rounded-xl pt-[17px] pb-[16px] px-[19px] xl:hover:bg-orange-gradient xl:hover:text-white">
											{_newFront.shops.shop.shop.addon.cta}
											<svg xmlns="http://www.w3.org/2000/svg" width="22" height="16" viewBox="0 0 22 16"
												fill="none">
												<path
													d="M11 0.999512V11.1813M11 11.1813L7 7.36315M11 11.1813L15 7.36315M21 11.1813V12.4541C21 13.1291 20.719 13.7766 20.2189 14.2539C19.7188 14.7313 19.0405 14.9995 18.3333 14.9995H3.66667C2.95942 14.9995 2.28115 14.7313 1.78105 14.2539C1.28095 13.7766 1 13.1291 1 12.4541V11.1813"
													stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
													stroke-linejoin="round" />
												<defs>
													<linearGradient id="paint0_linear_470_5710" x1="1" y1="14.9995" x2="13.8394"
														y2="0.116331" gradientUnits="userSpaceOnUse">
														<stop stop-color="#EF7F1A" />
														<stop offset="1" stop-color="#FFA439" />
													</linearGradient>
												</defs>
											</svg>
										</a>
									</div>
								{/if}
							{/foreach}

							<div n:if="$user->isLoggedIn() === false" class="open-register-popup bg-primary-blue-dark py-[28px] px-[44px] md:rounded-2xl -mx-5 md:mx-0 mb-5">

							<div class="flex flex-col md:flex-row justify-between items-center relative">
								<div>
									<div class="hidden md:block text-[20px] text-white leading-[35px] font-bold mb-[11px]">
										{_newFront.shops.shop.shop.shopDetail.promo.title}
									</div>
									<div class="md:hidden text-white leading-7 text-center px-4 mb-4"><span
											class="font-bold"
											{_newFront.shops.shop.shop.shopDetail.promo.promoTextMobile1, ['shop' =>$shop->getName(), 'count' => ($countOfCashbackShops |amount)] |noescape}
									</div>
									<div class="hidden md:block">
										<div class="flex items-center gap-2.5 text-white font-bold leading-7 mb-1.5">
											<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19"
												viewBox="0 0 19 19" fill="none">
												<circle cx="9.5" cy="9.5" r="8.75" stroke="#66B940" stroke-width="1.5" />
												<path d="M6.33594 9.98L8.35657 11.8743L12.6693 7.91602" stroke="#66B940"
													stroke-width="1.5" stroke-linecap="round" />
											</svg>
											{_newFront.shops.shop.shop.shopDetail.promo.promoText1, ['shop' =>$shop->getName(), 'cashback' => ($shop|reward:false,'pure')]}
										</div>
										<div class="flex items-center gap-2.5 text-white leading-7 mb-1.5">
											<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19"
												viewBox="0 0 19 19" fill="none">
												<circle cx="9.5" cy="9.5" r="8.75" stroke="#66B940" stroke-width="1.5" />
												<path d="M6.33594 9.98L8.35657 11.8743L12.6693 7.91602" stroke="#66B940"
													stroke-width="1.5" stroke-linecap="round" />
											</svg>
											{_newFront.shops.shop.shop.shopDetail.promo.promoText2, ['shop' =>$shop->getName(), 'count' => ($countOfCashbackShops |amount)]}
										</div>
										<div class="flex items-center gap-2.5 text-white leading-7">
											<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19"
												viewBox="0 0 19 19" fill="none">
												<circle cx="9.5" cy="9.5" r="8.75" stroke="#66B940" stroke-width="1.5" />
												<path d="M6.33594 9.98L8.35657 11.8743L12.6693 7.91602" stroke="#66B940"
													stroke-width="1.5" stroke-linecap="round" />
											</svg>
											{_newFront.shops.shop.shop.shopDetail.promo.promoText3, ['shop' =>
											$shop->getName(), 'count' => $countOfCashbackShops]}

										</div>
									</div>
								</div>
								<div class="text-center w-full md:w-auto">
									<div
										class="bg-[#66b94080] inline-block uppercase text-xs font-bold text-white mb-6 py-2 pr-3 pl-[2px] xl:pl-[3px] rounded-full xl:text-sm mb-6 md:mb-[14px]">

										<span class="bg-secondary-green py-[7px] px-[9px] rounded-full mr-[6px]">
											{_newFront.shops.shop.shop.shopDetail.promo.bonusAmount, ['amount' => $campaignBonusAmount]}
										</span>
										{_newFront.shops.shop.shop.shopDetail.promo.bonus}
									</div>
									<button
										class="open-register-popup flex w-full md:w-auto justify-center items-center gap-[10px] text-primary-orange font-bold leading-[28px] border border-primary-orange rounded-xl pt-[17px] pb-[16px] md:px-[44px] xl:hover:bg-orange-gradient xl:hover:text-white">
										{_newFront.shops.shop.shop.shopDetail.promo.cta}
									</button>
								</div>

								<svg class="hidden xl:block absolute top-[10px] left-[385px]" width="168" height="101"
									viewBox="0 0 168 101" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M158.047 91.823C158.804 91.9624 159.428 91.2298 159.17 90.5045L155.056 78.935C154.798 78.2096 153.852 78.0355 153.353 78.6215L145.39 87.9689C144.891 88.5549 145.213 89.4615 145.97 89.6009L158.047 91.823Z"
										fill="url(#paint0_linear_758_4585)" stroke="url(#paint1_linear_758_4585)"
										stroke-width="2" />
									<path
										d="M147.894 82.7056C135.202 73.6575 101.563 37.1226 119.999 11.838C137.718 -12.4615 161.467 9.94355 142.994 22.3846C128.215 32.3375 11 22.3846 1.5 22.3846"
										stroke="url(#paint2_linear_758_4585)" stroke-width="2" stroke-linecap="round" />
									<defs>
										<linearGradient id="paint0_linear_758_4585" x1="142.382" y1="89.9574" x2="163.338"
											y2="87.5929" gradientUnits="userSpaceOnUse">
											<stop stop-color="#EF7F1A" />
											<stop offset="1" stop-color="#FFA439" />
										</linearGradient>
										<linearGradient id="paint1_linear_758_4585" x1="142.382" y1="89.9574" x2="163.338"
											y2="87.5929" gradientUnits="userSpaceOnUse">
											<stop stop-color="#EF7F1A" />
											<stop offset="1" stop-color="#FFA439" />
										</linearGradient>
										<linearGradient id="paint2_linear_758_4585" x1="149.689" y1="82.7056" x2="79.2619"
											y2="-20.9416" gradientUnits="userSpaceOnUse">
											<stop stop-color="#EF7F1A" />
											<stop offset="1" stop-color="#FFA439" />
										</linearGradient>
									</defs>
								</svg>
							</div>
						</div>
					</div>
					</div>
				</div>

					<div class="flex justify-center mb-10" n:if="false && $deals && count($deals) > 4">
						<button id="show-more-deals" class="hover:underline text-primary-orange">{_newFront.shops.shop.shop.showAllDeals, ['count' => count($deals) - 4]} ↓</button>
					</div>
				{/cache}

				{if $user->isLoggedIn() === false}
					{include offersWithConditions}
				{/if}

				{if !$shop->isCashbackActive() && !$shop->isAffiliateActive() && (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
				<!-- Tipli - responsive - 1 -->
				<ins class="adsbygoogle"
					style="display:block"
					data-ad-client="ca-pub-3515231881192503"
					data-ad-slot="8926602594"
					data-ad-format="auto"
					data-full-width-responsive="true"></ins>
				{/if}

                {cache 'bestTipsDescriptionBlock-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
				    <div class="">
					{var $bestTipsForSaleBoxBlock = $getDescriptionBlock('best_tips_for_sale_box')}

					{if $bestTipsForSaleBoxBlock && $bestTipsForSaleBoxBlock->getCountOfCharacters() > 0}
						{include 'blocks/bestTipsForSaleBox.latte', block => $bestTipsForSaleBoxBlock, logo => $logo}
					{else}
						{var $bestTipsDescriptionBlock = $descriptionBlock('best_tips_for_sale')}

						<div class="relative bg-white rounded-2xl mt-10 px-5 md:px-10 pt-7 pb-[18px] mb-5"
							n:if="$bestTipsDescriptionBlock">

							<div class="absolute right-[-17px] -top-[20px]">
								<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-best_tips_for_sale" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
									<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
								</a>
							</div>

							<div class="flex items-center gap-[23px]">
								<img class="max-w-20 max-h-[30px]" src="{$logo |image:170,0}" alt="logo" loading="lazy">

								<div class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
									{if $bestTipsDescriptionBlock[0]->getTitle()}
										{$bestTipsDescriptionBlock[0]->getTitle()}
									{else}
										{_newFront.shops.shop.shop.tips.title}
									{/if}
								</div>
							</div>

							<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

							<div class="content-block">
								{$bestTipsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
							</div>
						</div>
					{/if}


					{if $shop->getShopQuestions()->count() > 0}
						{include '../snippets/question-answer.latte'}
					{/if}

					{var $couponsDescriptionBoxBlock = $getDescriptionBlock('coupon_instructions_box')}

					{if $couponsDescriptionBoxBlock && $couponsDescriptionBoxBlock->getCountOfCharacters() > 0}
						{include 'blocks/couponInstructionsBox.latte', block => $couponsDescriptionBoxBlock, logo => $logo}
					{else}
						{var $couponsDescriptionBlock = $descriptionBlock('coupon_instructions')}

						<div id="couponsDescription" class="relative bg-white rounded-2xl mt-5 px-5 md:px-10 pt-7 pb-[18px]"
									n:if="$couponsDescriptionBlock">

								<div class="absolute right-[-17px] -top-[20px]">
									<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-coupon_instructions" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
										<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
									</a>
								</div>

								<div class="flex items-center gap-[23px]">
									<img class="max-w-20 max-h-[30px]" src="{$logo |image:170,0}" alt="logo" loading="lazy">

									<div class="text-dark-1 text:base md:text-lg font-medium md:leading-7">
										<h2 class="shop-detail__section-title">
											{_'front.shops.shop.shop.couponsDescriptionTitle',
											[name => $shop->getName()]}</h2>
									</div>
								</div>

								<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

								<div class="content-block">
									{$couponsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
								</div>
							</div>
					{/if}

					{var $longDescriptionBlock = $descriptionBlock('long_description')}

					<div class="relative bg-white rounded-2xl mt-5 px-5 md:px-10 pt-7 pb-[18px] mb-[50px] md:mb-5"
						n:if="$longDescriptionBlock">

						<div class="absolute right-[-17px] -top-[20px]">
							<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-long_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
								<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
							</a>
						</div>

						<div class="flex items-center gap-[23px]">
							<img class="max-w-20 max-h-[30px]" src="{$logo |image:170,0}" alt="logo" loading="lazy">

							<div class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
								{if $longDescriptionBlock[0]->getTitle()}
									{$longDescriptionBlock[0]->getTitle()}
								{/if}
							</div>
						</div>

						<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

						<div class="content-block text-dark-1 text-sm leading[24.5px] mb-[30px]">
							{$longDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
						</div>
					</div>
				</div>
				{/cache}

				<div class="max-w-full">
				    {cache 'otherDeals-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $otherDeals = $getOtherDeals()}

                        {if $otherDeals !== null && $otherDeals->isEmpty() === false}
                            <div class="absolute left-0">
                                <img class="hidden w-screen h-[740px] mt-[-52px] object-cover object-top"
                                    src="{$basePath}/new-design/profile-shop/bg-grey-img.svg" alt="bg" loading="lazy">
                            </div>

                            <div class="pl-10 text-lg text-dark-1 font-medium leading-[31.5px] mb-5 mt-5 md:mt-10 relative">
                                {_newFront.shops.shop.shop.shopDetail.otherDeals, ['tag' => $navigationTag->getName()]}
                            </div>

                            {foreach $otherDeals as $deal}
                                {include dealItem.latte, deal: $deal}
                            {/foreach}

                            <div class="grid grid-cols-1 gap-5 p-2.5 mb-10 xl:hidden" n:if="$otherDeals">
                                {foreach $otherDeals as $deal}
                                    {include ../../../../DealsModule/Presenters/templates/Deals/dealItem.latte, deal: $deal}
                                {/foreach}
                            </div>
                        {/if}
					{/cache}

                    {cache 'expiredCoupons-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $expiredCoupons = $getExpiredCoupons()}
                        {if $expiredCoupons->isEmpty() === false}
                            <div id="expired" class="relative pl-10 text-lg text-dark-1 font-medium leading-[31.5px] mb-5 mt-5 md:mt-10">
                                {_newFront.shops.shop.shop.expiredCouponsTitle, ['shop' => $shop->getName()]}
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-1 px-5 gap-[15px] md:gap-5 mb-10 xl:px-0">
                                {foreach $expiredCoupons as $expiredCoupon}

                                {capture $dealType}
                                    {if $expiredCoupon->getType() == $expiredCoupon::TYPE_FREE_SHIPPING}
                                        {_'newFront.deals.deal.freeShipping'}
                                    {else}
                                        {_'newFront.deals.deal.' . $expiredCoupon->getType()}
                                    {/if}
                                {/capture}
                                <div
                                    class="flex flex-col items-center md:flex-row bg-white border border-light-2 p-2.5 rounded-2xl relative">
                                    <div class="md:hidden absolute left-0 top-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M56.0003 0L0 56.0003V24C0 12.6863 0 7.02944 3.51472 3.51472C7.02944 0 12.6863 0 24 0H56.0003Z"
                                                fill="#CCD0D7" />
                                        </svg>
                                    </div>
                                    <div
                                        class="md:hidden absolute text-white text-[10px] font-bold leading-[17.5px] -rotate-45 top-[11px] left-[5px] uppercase">
                                        {$dealType}
                                    </div>
                                    <div class="flex flex-col">
                                        <div class="flex justify-center py-9 md:px-6">
                                            <img src="{$expiredCoupon->getShop()->getLogo() |image:200,0,'fit',false,$expiredCoupon->getShop()->getName()}"
                                                loading="lazy" class="grayscale max-h-[30px] md:max-h-[50px] max-w-[100px]">
                                        </div>
                                        <div
                                            class="hidden md:flex w-[169px] items-center justify-center gap-2 bg-light-2 text-white text-sm leading-[24.5px] font-medium py-[5px] rounded-md">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19"
                                                fill="none">
                                                <path
                                                    d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
                                                    stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                                <path
                                                    d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
                                                    stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            {$dealType}
                                        </div>
                                    </div>

                                    <img class="hidden absolute top-[-18px] left-[190px]" src="/new-design/ellipse-white.svg"
                                        alt="ellipse" loading="lazy">
                                    <img class="hidden absolute bottom-[-18px] left-[190px]" src="/new-design/ellipse-white.svg"
                                        alt="ellipse" loading="lazy">

                                    <div class="hidden md:block ml-5 mr-5">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="2" height="125" viewBox="0 0 2 125" fill="none">
                                            <path d="M1 0L1.00001 125" stroke="#ADB3BF" stroke-linejoin="round" stroke-dasharray="4 4">
                                            </path>
                                        </svg>
                                    </div>

                                    <div class="md:hidden mb-3">
                                        <img class="m-auto" src="/new-design/divider-coupon.svg" alt="divider" loading="lazy">
                                    </div>

                                    <div class="flex flex-col justify-between h-full">
                                        <div
                                            class="text-xs md:text-lg text-dark-1 text-center mb-3 leading-[21px] md:leading-[31.5px] md:mb-0 mt-2.5">
                                            {$expiredCoupon->getName()}
                                        </div>

                                        <div
                                            class="flex rounded-md items-center justify-center text-xs md:text-sm leading-[24.5px] gap-[10px] pb-1.5 pt-[7px] bg-[#FFEBED] text-secondary-red font-medium md:justify-start md:bg-transparent">
                                            <svg class="hidden md:block" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 18 18" fill="none">
                                                <path
                                                    d="M1.90644 12.88C1.13813 11.3837 0.847729 9.68163 1.07555 8.01011C1.30337 6.33858 2.03811 4.78057 3.17774 3.55246C4.31742 2.32435 5.80532 1.4871 7.43499 1.15698C9.06457 0.826858 10.755 1.02025 12.2715 1.7103C13.7879 2.40036 15.0551 3.55283 15.897 5.00764C16.739 6.46246 17.114 8.14742 16.9698 9.82853C16.8257 11.5096 16.1696 13.1034 15.0928 14.3886C14.0158 15.6738 12.5716 16.5865 10.9605 17M8.27343 3.95258V9.86085H11.9098"
                                                    stroke="#F72F49" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            {_'newFront.deals.deal.expiredAt', ['date' =>
                                            $expiredCoupon->getValidTill()->format('d.m.Y')]}
                                        </div>
                                    </div>
                                </div>
                                {/foreach}
                            </div>
                        {/if}
					{/cache}

					<div
					    n:if="$user->isLoggedIn() === false"
						class="flex flex-col xl:hidden items-center justify-between bg-primary-blue-dark pt-[37px] pb-[34px] px-10 xl:py-10 xl:pl-[44px] md:rounded-2xl relative xl:mx-0 xl:pr-2.5 mb-[50px] xl-[41px]">
						<div
							class="px-5 text-center text-base text-white font-bold leading-[28px] mb-4 xl:mb-0 xl:text-start xl:px-0 xl:leading-[35px] xl:text-[20px] xl:max-w-[62%]">
							{_newFront.shops.shop.shop.shopDetail.promo.promoTextMobile1, ['shop' =>$shop->getName(), 'count' => $countOfCashbackShops] |noescape}
						</div>
						<div
							class="bg-[#66b94080] uppercase text-xs font-bold text-white mb-6 py-2 pr-3 pl-[2px] xl:pl-[3px] rounded-full xl:text-sm xl:mb-0 xl:absolute xl:left-[393px] xl:bottom-[34px]">
							<span class="bg-secondary-green py-[7px] px-[9px] rounded-full mr-[6px]">{_newFront.shops.shop.shop.shopDetail.promo.bonusAmount, ['amount' => $campaignBonusAmount]}</span>
							{_newFront.shops.shop.shop.shopDetail.promo.bonus}
						</div>
						<button
							class="open-register-popup flex w-full xl:w-auto justify-center items-center gap-[10px] text-primary-orange font-bold leading-[28px] border border-primary-orange rounded-xl pt-[17px] pb-[16px] px-[19px]">
							{_newFront.shops.shop.shop.shopDetail.promo.cta}
						</button>
					</div>

					{var $countOfAllCoupons = $countOfAllCoupons()}
					<div class="hidden xl:flex bg-primary-blue-dark border border-light-2 p-2.5 rounded-2xl mb-5 relative" n:if="$countOfAllCoupons > 0 && $isLessDetailed === false">
						<div class="flex pl-[49px] justify-center flex-col">
							<div class="">
								{if $localization->isHungarian()}
									<img src="{$basePath}/images/tiplino_logo_new_white.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[93px] h-[47px]">
								{else}
								<svg xmlns="http://www.w3.org/2000/svg" width="90" height="45" viewBox="0 0 90 45" fill="none">
									<path fill-rule="evenodd" clip-rule="evenodd"
										d="M25.2023 8.00819H31.658V31.1559H25.2023V8.00819ZM71.939 30.8886V8.26867H77.8643V30.8891L71.939 30.8886ZM61.4071 30.8886V0H67.3387V30.8891L61.4071 30.8886ZM51.7238 19.5239C51.7238 18.9756 51.6221 18.4074 51.4251 17.8178C51.2212 17.2287 50.9224 16.6941 50.501 16.2218C50.0775 15.7391 49.5607 15.3444 48.9853 15.0646C48.3804 14.77 47.7011 14.6259 46.9401 14.6259H42.0002V24.3736H46.9401C47.7011 24.3736 48.3804 24.2159 48.9853 23.9009C50.0901 23.3301 50.9492 22.3699 51.3973 21.2026C51.6148 20.6339 51.7233 20.0789 51.7233 19.5244L51.7238 19.5239ZM35.6603 8.17956H46.9128C48.8836 8.17956 50.5823 8.52916 52.0226 9.22782C53.4629 9.92649 54.6453 10.8238 55.5762 11.9271C57.3347 14.0194 58.3132 16.6715 58.3357 19.4154C58.3111 22.1943 57.3263 24.882 55.5557 27.0111C54.5851 28.1772 53.3754 29.1264 52.0158 29.7921C50.5938 30.4708 49.0366 30.8251 47.4626 30.8251C47.2939 30.8251 47.1204 30.8215 46.9527 30.8136L42.0002 30.8131V38.8559H35.6603V8.17956ZM11.2526 24.1954C10.709 24.1954 10.1451 24.0927 9.56065 23.8941C8.36196 23.4748 7.37765 22.5895 6.82941 21.4354C6.53747 20.8326 6.39439 20.1408 6.39439 19.3802V14.749H16.4032V8.23407H6.39386V0H0V19.353C0 21.3326 0.346442 23.0518 1.03987 24.4973C1.73277 25.9497 2.62327 27.1416 3.71713 28.0803C4.81099 29.019 6.01387 29.7108 7.31843 30.1695C8.6298 30.6286 9.90765 30.8613 11.1441 30.8613C12.3805 30.8613 13.6651 30.6286 14.9969 30.1695C22.0973 27.717 22.4574 21.1408 22.4574 17.8524H16.1181C16.1044 19.2639 15.9959 20.6612 15.595 21.4354C15.2894 22.0313 14.9089 22.5381 14.4534 22.9417C13.5666 23.7263 12.4319 24.1713 11.2526 24.1954ZM25.2023 0H31.658V6.51596H25.2023V0Z"
										fill="white" />
									<path fill-rule="evenodd" clip-rule="evenodd"
										d="M71.9386 0H77.8639V5.98082H71.9386V0ZM86.1472 30.2449L90.0001 33.9102C84.3846 39.8386 78.7323 43.9101 70.5721 44.1312C62.3024 43.7701 56.6628 39.4487 51.3418 33.9238L55.1674 30.2245C59.4281 34.6628 64.1243 38.4759 70.6125 38.7668C77.1385 38.5729 81.6292 35.0056 86.1472 30.2449Z"
										fill="#EF7F1A" />
								</svg>
								{/if}
							</div>
						</div>

						<img class="hidden absolute top-[-18px] left-[190px]" src="/new-design/ellipse-white.svg" alt="ellipse" loading="lazy">
						<img class="hidden absolute bottom-[-18px] left-[190px]" src="/new-design/ellipse-white.svg" alt="ellipse" loading="lazy">

						<div class="ml-[56px] mr-5">
							<svg xmlns="http://www.w3.org/2000/svg" width="2" height="125" viewBox="0 0 2 125" fill="none">
								<path d="M1 0L1.00001 125" stroke="#ADB3BF" stroke-linejoin="round" stroke-dasharray="4 4">
								</path>
							</svg>
						</div>

                        {cache 'allShopsCoupons-' . $localization->getId()}
                            <div class="flex items-center justify-between gap-14">
                                <div class="text-lg text-white font-bold leading-[31.5px]">
                                    {_newFront.shops.shop.shop.allCoupons, $countOfAllCoupons, ['countOfCoupons' => $countOfAllCoupons]}
									{_newFront.shops.shop.shop.allShops, ['countOfShops' => $countOfAllCashbackShops()]}
                                </div>
                                <div>
                                    <a n:href=":NewFront:Shops:Shops:default"
                                        class="h-full pt-[18px] pb-[15px] px-4 relative z-20 rounded-xl bg-orange-gradient text-white font-bold leading-7 whitespace-nowrap cursor-pointer xl:hover:bg-orange-gradient-hover">
                                        {_newFront.shops.shop.shop.allShopsCouponCta}
                                    </a>
                                </div>
                            </div>
						{/cache}
					</div>

					<div id="mobile-sidebar"></div>
				</div>
			</div>
		</div>
	</div>
</div>

{cache 'shopConditionsPopup-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
{var $newShopConditions = $getNewShopConditions()}
<div id="conditions-popup"
	class="hidden fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
	<div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
		<div class="py-10 rounded-t-2xl relative">
			<div class="conditions-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
				<img src="{$basePath}/new-design/close-btn.svg" alt="close" loading="lazy">
			</div>

			<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 35 35"
				fill="none">
				<path
					d="M17.5 34C26.6127 34 34 26.6127 34 17.5C34 8.3873 26.6127 1 17.5 1C8.3873 1 1 8.3873 1 17.5C1 26.6127 8.3873 34 17.5 34Z"
					stroke="#EF7F1A" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"
					stroke-linejoin="round" />
				<path d="M17.9023 15.4883L17.9023 25.9517" stroke="#EF7F1A" stroke-width="2" stroke-miterlimit="10"
					stroke-linecap="round" stroke-linejoin="round" />
				<circle cx="17.9027" cy="9.8539" r="1.60976" fill="#EF7F1A" />
			</svg>

			<div class="text-lg text-center text-dark-1 leading-[31.5px] my-5 font-bold">
				{_newFront.shopConditionsProvider.generalConditions}
			</div>

			{$newShopConditions |noescape}

			<div class="px-5 md:px-[78px]">
				<button
					class="conditions-popup-close w-full py-4 rounded-xl bg-orange-gradient text-white font-medium mt-2 leading-[28px] xl:hover:bg-orange-gradient-hover">{_newFront.shopConditionsProvider.button}</button>
			</div>
		</div>
	</div>
</div>
{/cache}

{if $user->isLoggedIn() === true && $user->getIdentity()->hasInstalledAddon() === false}
	{control addonPromo}
{/if}

{if $isLoginFromAddonPopupAllowed}
	{snippetArea signInFromAddon}
		{include signInModal.latte, shop: $shop}
	{/snippetArea}
{else}
	{snippetArea signUp}
		{include signUpModal.latte, shop: $shop}
	{/snippetArea}
{/if}
