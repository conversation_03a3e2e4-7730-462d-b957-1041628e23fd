{var $logo = $shop->getSvgLogo() ? $shop->getSvgLogo() : $shop->getLogo()}

{define logo}
    {var $shopCookie = "shop-cookie-" . $shop->getId()}
    {if $shop->isCashbackAllowed()}
        {if $shop->isCashbackActive()}
            {if $isUserLoggedIn}
                {if $popup}
                    <a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
                            class="shop-profile__logo-img-wrapper" data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                            data-action="click" data-label="cashbackPopupBtnLogOut"
                            data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
                        <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
                    </a>
                {else}
                    <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
                                                                                            target="_blank"
                                                                                            data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
                                                                                            data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
                                                                                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                                                                                            data-action="click" data-label="cashbackBtnLogIn">
                        <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
                    </a>
                {/if}
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
                        class="shop-profile__logo-img-wrapper"
                        onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
                        class="" data-shop-id="{$shop->getId()}">
                    <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
                </a>
            {/if}
        {else}
            <div class="shop-profile__logo-img-wrapper">
                <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
            </div>
        {/if}
    {else}
        {if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
            <a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
                    class="ajax shop-profile__logo-img-wrapper" data-ajax-call="js-open-popup" data-hit="event"
                    data-category="shopDetail" data-action="click" data-label="noCashbackPopupBtnLogOut">
                <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
            </a>
        {else}
            {if $isUserLoggedIn}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
                                                                                        target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
                                                                                        data-label="noCashbackRedirectBtnLogIn">
                    <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
                </a>
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper"
                                                                                        target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
                                                                                        data-label="noCashbackRedirectBtnLogOut">
                    <img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$logo |image:340,0}" alt="logo">
                </a>
            {/if}
        {/if}
    {/if}
{/define}

{define ctaLink}
    {var $shopCookie = "shop-cookie-" . $shop->getId()}
    {if $shop->isCashbackAllowed()}
        {if $shop->isCashbackActive()}
            {if $isUserLoggedIn}
                {if $popup}
                    <a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
                            class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                            data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                            data-action="click" data-label="cashbackPopupBtnLogOut"
                            data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
                        {if $shop->getShopData()->getRedirectLabel()}
                            {$shop->getShopData()->getRedirectLabel()}
                        {else}
                            {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                        {/if}
                    </a>
                {else}
                    <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                                                                                            class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                                                                                            data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
                                                                                            data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
                                                                                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                                                                                            data-action="click" data-label="cashbackBtnLogIn">
                        {if $shop->getShopData()->getRedirectLabel()}
                            {$shop->getShopData()->getRedirectLabel()}
                        {else}
                            {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                        {/if}
                    </a>
                {/if}
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
                        class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                        onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
                        data-shop-id="{$shop->getId()}">
                    {if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                    {/if}
                </a>
            {/if}
        {else}
            {if $shop->isPaused() === true}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                    class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                    data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
                    data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
                    {if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.nocashbackShop.goToShop'} 🛒
                    {/if}
                </a>
            {else}
                <span class="shop-detail__button shop-detail__button--disabled">
					{if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.shops.shop.shop.btnNoCashback'} 🛒
                    {/if}
				</span>
            {/if}
        {/if}
    {else}
        {if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
            <a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
                    class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                    data-ajax-call="js-open-popup" data-hit="event" data-category="shopDetail" data-action="click"
                    data-label="noCashbackPopupBtnLogOut">
                {if $shop->isGamble()}
                    {_'newFront.nocashbackShop.goToShopGamble'}
                {else}
                    {_'front.nocashbackShop.goToShop'} 🛒
                {/if}
            </a>
        {else}
            <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
                data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
                {if $shop->isGamble()}
                    {_'newFront.nocashbackShop.goToShopGamble'}
                {else}
                    {_'front.nocashbackShop.goToShop'} 🛒
                {/if}
            </a>
        {/if}
    {/if}
{/define}

{block content}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>


<div class="bg-light-6">
    <div class="hidden lg:block absolute w-full lg:h-[278px] bg-repeat"
         style="background-image:url('{$basePath}/new-design/bg-profile-shop.svg'); background-position: 0"></div>
    <div class="container relative px-0 md:px-5 z-30 lg:pt-10">
        <div class="flex flex-col lg:flex-row lg:gap-[60px]">
            <div class="w-full lg:max-w-[335px] shrink-0">
                <div class="lg:mb-[19px]">
                    <div class="lg:bg-white lg:pb-5 rounded-2xl relative z-10">
                        <div class="relative mb-16">
                            {if $shop->getMobileCover()}
                                <img class="lg:rounded-2xl object-cover max-h-[130px] lg:max-h-[200px] w-full" src="{$shop->getMobileCover() |image}" alt="">
                            {/if}

                            <div class="hidden lg:flex absolute left-1/2 shadow-sm bottom-0 transform -translate-x-1/2 translate-y-1/2 bg-white rounded-2xl justify-center items-center w-[177px] h-[81px]">
                                {include logo}
                            </div>

                            <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white w-[163px] h-[53px] rounded-lg absolute shadow-sm bottom-0 transform translate-y-1/2">
                                <div class="flex items-center justify-center h-full w-full">
                                    <div class="flex items-center ml-auto mr-auto">
                                        {include logo}
                                    </div>
                                    <div class="flex ml-auto">
                                        <div class="h-[41px] w-px bg-light-4"></div>
                                        <svg width="45" height="41" viewBox="0 0 45 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="45" height="41" rx="6" transform="matrix(-1 0 0 1 45 0)" fill="white"/>
                                            <path d="M20.0002 15.8H18.5602C17.6641 15.8 17.2157 15.8 16.8734 15.9744C16.5724 16.1278 16.3278 16.3724 16.1744 16.6734C16 17.0157 16 17.4641 16 18.3602V24.4402C16 25.3362 16 25.7841 16.1744 26.1263C16.3278 26.4274 16.5724 26.6724 16.8734 26.8258C17.2154 27 17.6632 27 18.5575 27H24.6425C25.5368 27 25.984 27 26.3259 26.8258C26.627 26.6724 26.8724 26.4271 27.0258 26.1261C27.2 25.7842 27.2 25.3368 27.2 24.4425V23M28 19V15M28 15H24M28 15L22.4 20.6" stroke="#080B10" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <div class="hidden lg:block w-full max-w-[168px] m-auto text-center text-dark-1 font-medium leading-[28px] mb-[22px]">
                                {_newFront.shops.shop.shop.ctaBox.getReward}

                                {if $isUserLoggedIn && $userIdentity->isActiveUser()}
                                    {cache 'new-shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
                                    ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                        {$shop |reward:true,'common' |noescape}
                                    {/cache}
                                {else}
                                    {cache 'new-shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
                                    ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                        {$shop |reward:true,'common' |noescape}
                                    {/cache}
                                {/if}

                                {_newFront.shops.shop.shop.ctaBox.fromPurchase}
                            </div>

                            <div class="mx-5">
                                {include ctaLink}
                            </div>
                        </div>
                    </div>

                    <div class="hidden lg:block relative z-5 text-center text-sm leading-[24.5px] border border-light-4 border-t-0 rounded-b-2xl relative top-[-14px] pt-8 pb-[25px]">
                        <div class="text-dark-1 font-medium mb-[5px]">{_newFront.shops.shop.shop.ctaBox.title}</div>
                        <div class="text-dark-2 mb-2.5 m-auto w-full max-w-[225px]">{_newFront.shops.shop.shop.ctaBox.newText}</div>
                        <div class="text-primary-orange hover:underline hover:cursor-pointer font-medium">Zistiť viac</div>
                    </div>
                </div>

                {php
                    $links = [
                    'O Dr. Max',
                    'Naše služby',
                    'Kontakt',
                    'Kariéra'
                    ];
                }

                <div class="hidden text-dark-1 font-medium leading-7 p-5 border border-light-4 rounded-2xl mb-[30px] xl:block">
                    Navigácia
                    <div class="w-full h-px bg-light-4 mt-[17px] mb-5"></div>

                    {foreach $links as $index => $text}
                        <a href="#" class="flex justify-between items-center text-sm leading-[24.5px] hover:underline hover:cursor-pointer">
                            {$text}
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                                <path d="M1 6.49913H14M8.49077 1L13.9987 6.5L8.49077 12" stroke="#66B940" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        {if $index !== count($links) - 1}
                            <div class="w-full h-px bg-light-4 my-2.5"></div>
                        {/if}
                    {/foreach}
                </div>

                {cache 'new-shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $similarShops = $recommendedShops()}
                    <div class="hidden lg:block p-5 rounded-2xl border border-light-4 rounded-2xl" n:if="$similarShops">
                        <div class="text-dark-1 font-medium leading-7 mb-[3px]">{_newFront.shops.shop.shop.favoriteShops.title}</div>
                        <div class="text-sm text-dark-2 leading-[24.5px] mb-5" n:if="$shop->isLessDetailed() === false">
                            {_newFront.shops.shop.shop.favoriteShops.text,
                            ['shop' => $shop->getName()]}
                        </div>

                        <div class="grid grid-cols-3 gap-[10px] ">
                            {foreach $similarShops as $shopItem}
                                <a href="{plink Shop:default, $shopItem}" title="{$shopItem->getName()}" class="bg-white rounded-xl shadow-hover flex items-center justify-center h-[47px]">
                                    <img class="max-w-[50px] max-h-[22px]" alt="{$shopItem->getName()}" src="{$shopItem->getLogo() |image:116,0,'fit',false,$shopItem->getName()}" loading="lazy">
                                </a>
                            {/foreach}
                        </div>
                    </div>
                {/cache}
            </div>

            <div class="w-full min-w-0">
                <div>
                    <div class="hidden lg:flex gap-[10px] text-sm leading-[24.5px] text-dark-2 items-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                            <path d="M2.46667 6.95165V11H5.4V8.05575C5.4 7.86049 5.47726 7.67329 5.61477 7.53525C5.7523 7.39722 5.93886 7.31968 6.13334 7.31968H6.86667C7.06115 7.31968 7.24766 7.39722 7.38523 7.53525C7.52276 7.67329 7.6 7.86049 7.6 8.05575V11H10.5333V6.95165M1 6.21559L5.98129 1.21575C6.04939 1.14735 6.13026 1.09309 6.21923 1.05607C6.30826 1.01905 6.40364 1 6.5 1C6.59636 1 6.69174 1.01905 6.78072 1.05607C6.86975 1.09309 6.95061 1.14735 7.01871 1.21575L12 6.21559" stroke="#646C7C" stroke-width="0.858714" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <a class="hover:underline hover:cursor-pointer" n:href=":NewFront:Shops:Shops:">
                            {_'front.shops.shop.shop.shopDetail.nav.shop'}
                        </a>

                        {if $navigationTag}
                            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                            </svg>
                            <a class="hover:underline hover:cursor-pointer" n:href=":NewFront:Shops:Shops:, $navigationTag">
                                {$navigationTag->getName()}
                            </a>
                        {/if}

                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                        </svg>
                        <div>{$shop->getName()}</div>
                    </div>

                    <div class="px-5 md:px-0">
                        <div class="text-xl lg:text-[26px] leading-[35px] lg:leading-[39px] font-bold text-dark-1 mb-[5px] -mt-5 lg:mt-0">
                            {_newFront.shop.title, ['shop' => $shop->getName()]}
                        </div>

                        {cache 'new-shopShortDescriptionBlock-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                            {var $shortDescriptionBlock = $descriptionBlock('short_description')}
                            {if isset($shortDescriptionBlock[0])}
                                <div id="description" class="text-dark-2 leading-[21px] lg:leading-[24.5px] line-clamp-2 text-xs lg:text-sm transition-all duration-300">
                                    <div class="absolute right-[-17px] -top-[20px]">
                                        <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-short_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                            <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                                        </a>
                                    </div>
                                    {$shortDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                                </div>
                            {/if}

                            <button n:if="$shortDescriptionBlock" id="toggle-description" class="text-dark-2 underline text-xs lg:text-sm">
                                {_newFront.shops.shop.shop.showAllText}
                            </button>
                        {/cache}

                        <div class="flex items-center gap-3 my-[25px] lg:mt-10 font-medium leading-7 lg:mb-[43px] overflow-hidden overflow-x-auto whitespace-nowrap mr-[-20px] lg:mr-0">
                            <a href="#cupons" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] bg-white border border-primary-orange rounded-lg text-primary-orange">{_newFront.shop.tabs.coupons}</a>
                            <a href="#cashback" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] bg-white border border-light-4 rounded-lg text-dark-2">{_newFront.shop.tabs.cashback, ['reward' => ($shop |reward:false, 'common')]}Cashback (až 7%)</a>
                            <a href="#other-shops" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] bg-white border border-light-4 rounded-lg text-dark-2">{_newFront.shop.tabs.otherShops}</a>
                        </div>

                        {var $coupons = $getCoupons()}
                        {if $coupons->isEmpty() === false}
                        <div id="coupons">
                            <div class="inline-flex items-center gap-1 text-[10px] lg:text-xs text-secondary-green border border-secondary-green leading-[13px] lg:leading-[21px] font-medium px-2 py-1 bg-pastel-green-light rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <rect width="13" height="13" rx="6.5" fill="#66B940"/>
                                    <path d="M4 6.56429L5.59524 8L9 5" stroke="white" stroke-width="1.2087" stroke-linecap="round"/>
                                </svg>
                                {_newFront.shop.coupons.updated}
                            </div>
                            <span class="text-lg lg:text-xl font-bold text-dark-1 leading-[31.5px] lg:leading-[35px] ml-1">{_newFront.shop.coupons.title, ['shop' => $shop->getName()]} <span class="font-normal">(7)</span></span>
                        </div>

                        <div class="text-xs lg:text-sm leading-[21px] lg:leading-[24.5px] text-dark-2 mb-5 lg:mb-[23px] mt-[5px] lg:mt-0">
                            {_newFront.shop.coupons.text}
                        </div>

                        {var $topCoupon = $coupons->getFirst()}
                        <div class="bg-[#182B4A] rounded-2xl px-[5px] pb-[5px] mb-5 lg:mb-10" style="box-shadow: 0px 34px 31.9px -10px rgba(42, 73, 123, 0.22);">
                            <div class="flex items-center justify-between px-[15px]">
                                <div class="text-xs lg:text-sm leading-[21px] text-white font-bold lg:leading-[24.5px]">🔥 TOP</div>
                                <div class="flex gap-5 py-2">
                                    <div class="hidden flex items-center gap-1.5 text-white text-xs leading-[21px] font-bold">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                            <path opacity="0.7" d="M5.00063 9L5.00063 1M9 4.83251L5 1.00089L1 4.83251" stroke="white" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <div>214</div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                            <path opacity="0.7" d="M5.1061 1L5.1061 9M9.10547 5.16749L5.10547 8.99911L1.10547 5.16749" stroke="white" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div
                                            class="hidden hover:cursor-pointer flex items-center gap-1.5 text-primary-orange font-medium text-xs leading-[21px]">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                            <path d="M9 4.42857H3.13333C2.56757 4.42857 2.02492 4.64263 1.62485 5.02366C1.22478 5.40468 1 5.92146 1 6.46032V9M9 4.42857L5.57143 7.28571M9 4.42857L5.57143 1" stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Zdieľať
                                    </div>
                                </div>
                            </div>

                            <div class="rounded-t-2xl rounded-b-[14px] p-5" style="background:linear-gradient(132deg, #FEF3E9 2.4%, #FFF 97.51%);">
                                <div class="flex flex-col lg:flex-row gap-3 lg:gap-5 lg:items-center lg:mb-[15px]">
                                    <div class="flex items-center gap-3 lg:gap-5">
                                        <div class="rounded-[14px] p-4 bg-white text-[35px] text-primary-orange font-bold leading-[54px]">{$topCoupon->getValue()}<span class="text-[22px] font-medium leading-[38px]">{$topCoupon->getUnitSymbol()}</span></div>
                                        <div class="text-sm lg:text-base leading-[22px] w-full max-w-[312px] leading-7">
                                            <span class="font-bold">{$topCoupon |dealName:true|noescape}</span>
                                            {$topCoupon->getDescription()}
                                        </div>
                                    </div>

                                    <div class="flex flex-col lg:hidden items-start gap-1 lg:gap-2.5">
                                        <div n:if="$shop->isCashbackWithCouponAllowed()" class="flex items-center gap-1 text-xs text-secondary-green border border-secondary-green/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
                                            {_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')]}
                                        </div>
                                        <div class="flex items-center gap-1 text-xs text-secondary-red border border-secondary-red/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
                                            {if $topCoupon->getValidTillDays() === 0}
                                                {_'newFront.deals.deal.validTillToday'}
                                            {elseif $topCoupon->getValidTillDays() <= 3}
                                                {_'newFront.deals.deal.validTillDays', ['count' => $topCoupon->getValidTillDays()]}
                                            {else}
                                                {_'newFront.deals.deal.validTill', ['date' => ($topCoupon->getValidTillForUser()|localDate:'d.m.Y')]}
                                            {/if}
                                        </div>
                                    </div>

                                    {var $topCouponId = $topCoupon->getId()}
                                    <a href="{plink "this!#deal-$topCouponId", openDeal => $topCoupon->getFullSlug()}" class="lg:ml-auto relative min-w-[248px] max-w-[248px] cursor-pointer h-[58px] border border-dashed border-dark-4 hover:border-primary-orange hover:border-solid rounded-[10px] overflow-visible">
                                        <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-black z-0">
                                            {if $topCoupon->getCode() !== null && Nette\Utils\Strings::length($topCoupon->getCode()) > 3}{mb_substr($topCoupon->getCode(), -3, 3)}
                                            {else}
                                                {$topCoupon->getCode()}
                                            {/if}
                                        </span>
                                        <div class="absolute top-[-1px] left-[-16px] z-10">
                                            <svg width="248" height="58" viewBox="0 0 211 56"  fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M199 0L10 0C4.47716 0 0 4.47715 0 10V46C0 51.5229 4.47716 56 10 56H211L199 0Z"
                                                      fill="url(#paint0_linear_7837_2928)"/>
                                                <defs>
                                                    <linearGradient id="paint0_linear_7837_2928" x1="211" y1="56" x2="180.319" y2="-37.8025"
                                                                    gradientUnits="userSpaceOnUse">
                                                        <stop stop-color="#EF7F1A"/>
                                                        <stop offset="1" stop-color="#FFA439"/>
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                        </div>
                                        <svg class="absolute top-0 right-[29px] z-20" xmlns="http://www.w3.org/2000/svg" width="30" height="56" viewBox="0 0 30 56" fill="none">
                                            <path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z" fill="#CE6D14"/>
                                        </svg>
                                        <span class="absolute inset-0 flex items-center justify-center z-30 text-white font-bold">
                                            {_'newFront.deals.deal.getCode'}
                                        </span>
                                    </a>
                                </div>

                                <div class="hidden lg:flex items-center items-start gap-2.5">
                                    <div n:if="$shop->isCashbackWithCouponAllowed()" class="flex items-center gap-1 text-xs lg:text-sm text-secondary-green border border-secondary-green/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
                                        {_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')]}
                                    </div>
                                    <div class="flex items-center gap-1 text-xs lg:text-sm text-secondary-red border border-secondary-red/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
                                        {if $topCoupon->getValidTillDays() === 0}
                                            {_'newFront.deals.deal.validTillToday'}
                                        {elseif $topCoupon->getValidTillDays() <= 3}
                                            {_'newFront.deals.deal.validTillDays', ['count' => $topCoupon->getValidTillDays()]}
                                        {else}
                                            {_'newFront.deals.deal.validTill', ['date' => ($topCoupon->getValidTillForUser()|localDate:'d.m.Y')]}
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col" n:if="$coupons->count() > 1">
                        <div class="order-1">
                            <div class="!pl-5 md:!pl-0 swiper swiper-profile-shop swiper-coupons relative mb-[50px] lg:mb-10">
                                <div class="swiper-wrapper">
                                    {foreach $coupons as $coupon}
                                        {continueIf $coupon->getId() === $topCoupon->getId()}

                                        <div class="swiper-slide pb-10">
                                            <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                                <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                                    <div class="flex items-center gap-[15px] mb-[15px]">
                                                        <div class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px]">
                                                            <div class="text-primary-orange font-black text-[28px] leading-[54px]">{$coupon->getValue()}<span class="font-medium text-lg leading-[38px]">{$coupon->getUnitSymbol()}</span></div>
                                                        </div>
                                                        <div class="text-sm leading-[22x] text-dark-1"><span class="font-bold underline">{$coupon |dealName:true|noescape}</span> {$coupon->getDescription()}</div>
                                                    </div>


                                                    {var $couponId = $coupon->getId()}
                                                    <a href="{plink "this!#deal-$couponId", openDeal => $coupon->getFullSlug()}" class="block relative w-full max-w-[300px] h-[56px] cursor-pointer rounded-[10px] group">
                                                        <div class="absolute inset-0 z-20">
                                                            <div class="absolute">
                                                                <svg viewBox="0 0 216 56" xmlns="http://www.w3.org/2000/svg" class="w-full h-full max-w-[216px] relative z-30" fill="none" preserveAspectRatio="xMidYMid slice">
                                                                    <path d="M204 0H10C4.47716 0 0 4.47715 0 10V46C0 51.5228 4.47715 56 10 56H216L204 0Z" fill="#FEF3E9"/>
                                                                    <path d="M10 0.5C4.7533 0.500003 0.5 4.7533 0.5 10V46C0.5 51.2467 4.7533 55.5 10 55.5H215.381L203.596 0.5H10Z"
                                                                          stroke="url(#paint0_linear_0_1)" stroke-opacity="0.2"/>
                                                                    <path d="M187.836 26.6337L204 0L216 56L190 37.2761C186.613 34.8365 185.67 30.2024 187.836 26.6337Z" fill="#E9D6C5"/>
                                                                    <defs>
                                                                        <linearGradient id="paint0_linear_0_1" x1="216" y1="56" x2="185.896" y2="-38.2192" gradientUnits="userSpaceOnUse">
                                                                            <stop stop-color="#EF7F1A"/>
                                                                            <stop offset="1" stop-color="#FFA439"/>
                                                                        </linearGradient>
                                                                    </defs>
                                                                </svg>
                                                                <div class="absolute top-1/2 right-[-15px] -translate-y-1/2 text-dark-3 font-consolas text-[14px] z-20">
                                                                    {if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}{mb_substr($coupon->getCode(), -3, 3)}
                                                                    {else}
                                                                        {$coupon->getCode()}
                                                                    {/if}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="absolute z-10 inset-0 rounded-[10px] border border-dashed border-dark-4 group-hover:border-primary-orange group-hover:border-solid group-hover:z-20"></div>
                                                        <span class="text-sm lg:text-base absolute inset-0 flex items-center justify-center z-30 font-bold text-primary-orange mr-8 lg:mr-0">
                                                        {_'newFront.deals.deal.getCode'}
                                                    </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div class="swiper-button-prev !hidden lg:!flex">
                                    <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                                </div>
                                <div class="swiper-button-next z-40 !hidden lg:!flex">
                                    <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                                </div>

                                <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="lg:block hidden pointer-events-none absolute top-0 right-0 h-full w-[153px] z-30"></div>
                                <div class="swiper-pagination absolute -bottom-6 left-1/2 -translate-x-1/2 z-20"></div>
                            </div>
                        </div>

                        {var $deals = $getDeals(true)}

                        <div class="order-3 lg:order-2 lg:ml-[-30px]" n:if="$deals->isEmpty() === false">
                            <div
                                    class="w-full h-full bg-no-repeat bg-cover bg-center lg:bg-white lg:rounded-2xl relative z-20"
                                    style="background-image:url('{$basePath}/new-design/white-top.png')"
                            >
                                <div class="pt-[50px] pb-10 lg:py-10 lg:mb-[50px]">
                                    <div class="px-5 lg:px-[30px] z-20">
                                        <div class="text-lg lg:text-xl text-dark-1 font-bold leading-[31.5px] leading-[35px] mb-1 lg:mb-2.5">{_newFront.shop.otherDeals.title} <span class="font-normal">({$deals->count()})</span></div>
                                        <div class="text-dark-2 text-xs lg:text-sm leading-[21px] lg:leading-[24.5px] mb-5 lg:mb-[25px]">
                                            {_newFront.shop.otherDeals.text}
                                        </div>

                                        <div class="grid grid-cols-1 xl:grid-cols-2 gap-[15px] lg:gap-5">
                                            {foreach $deals as $deal}
                                                {var $id = $deal->getId()}
                                                <a href="{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}" class="flex justify-between border border-pastel-green-light hover:border-secondary-green hover:cursor-pointer hover:-translate-y-0.5 transition-all duration-200 bg-pastel-green-light/50 flex items-center rounded-2xl">
                                                    <div class="flex flex-col items-center px-5 text-secondary-green font-medium leading-[23px]">
                                                        <span>🤩</span>
                                                        {_'newFront.deals.deal.' . $deal->getType()}
                                                    </div>
                                                    <div class="text-sm bg-white rounded-l-[3px] rounded-r-[15px] w-full xl:max-w-[235px] pt-3 pb-2.5 pl-[18px] pr-[39px]">
                                                        <div class="text-dark-1 leading-[22px] mb-3.5">
                                                            <span class="font-bold"> {$deal |dealName:true|noescape}</span> {$deal->getDescription() |striptags|truncate:65|noescape}</div>
                                                        <div class="text-[10px] lg:text-sm text-secondary-red leading-[17.5px] lg:leading-[24.5px]">
                                                            {if $deal->getValidTillDays() === 0}
                                                                {_'newFront.deals.deal.validTillToday'}
                                                            {elseif $deal->getValidTillDays() <= 3}
                                                                {_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}
                                                            {else}
                                                                {_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}
                                                            {/if}
                                                        </div>
                                                    </div>
                                                    <div class="px-5">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                                            <path d="M1 14L14 1M13.9979 12.0163L13.9978 1.00041L2.98193 1.0004" stroke="#66B940" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                </a>
                                            {/foreach}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                    id="cashback"
                                    class="lg:hidden w-full h-[293px] bg-no-repeat bg-cover bg-center mb-[30px] -mt-2 z-10 relative"
                                    style="background-image:url('{$basePath}/new-design/bg-orange-profile.png');background-position: center bottom; background-repeat: no-repeat;"
                            >
                                <div class="text-lg text-white leading-[31.5px] pt-11 pl-5 w-ful max-w-[173px] font-bold mb-5">
                                    Tipli cashback na Zalando <span class="font-normal">(až 7%)</span>
                                </div>
                                <div
                                        style="backdrop-filter: blur(4.951241970062256px); background: rgba(255, 255, 255, 0.80);"
                                        class="text-center w-full max-w-[213px] m-auto rounded-[20px] mt-5 relative"
                                >
                                    <div class="px-10 pt-[30px] text-xl leading-7 text-[#182B4A]">
                                        <span class="font-black">{_newFront.shop.promo1}</span> {_newFront.shop.promo2}

                                    </div>
                                    <div class="text-xs leading-[21px] text-primary-orange py-4">Ako to funguje?</div>

                                    <img class="absolute left-[-73px] bottom-0" src="{$basePath}/new-design/oslik-bg-orange.png" alt="oslik">

                                    <div class="absolute flex flex-col right-[-26px] bottom-2">
                                        <img src="{$basePath}/new-design/bg-white-money.svg" alt="money">
                                        <img class="-mt-2" src="{$basePath}/new-design/bg-white-thumbs-up.svg" alt="thumbs up">
                                    </div>

                                    <div class="absolute -top-10 -right-11">
                                        <div class="flex flex-col items-center justify-center text-center">
                                            <img src="{$basePath}/new-design/bg-green-flower-sm.svg" alt="flower">
                                            <div class="absolute text-xs font-bold w-[60px] leading-tight text-center text-white leading-[18px]">
                                                {_newFront.shop.bluescreen.guarantee}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {var $expiredCoupons = $getExpiredCoupons()}
                        <div class="order-2 lg:order-3 pl-5 md:pl-0" n:if="$expiredCoupons->isEmpty() === false">
                            <div class="text-lg lg:text-xl font-bold leading-[31.5px] lg:leading-[35px] mb-[5px] w-full max-w-[196px] lg:max-w-full">
                                {_newFront.shop.expiredCoupons.title}
                                <span class="font-normal">({$expiredCoupons->count()})</span>
                            </div>
                            <div class="text-xs leading-[21px] lg:text-sm lg:leading-[24.5px] text-dark-2 mb-5 lg:mb-[25px] pr-5 lg:pr-0">
                                {_newFront.shop.expiredCoupons.text}
                            </div>

                            <div class="swiper swiper-profile-shop swiper-older-coupons relative mb-[50px] lg:mb-[100px]">
                                <div class="swiper-wrapper">
                                    {foreach $expiredCoupons as $coupon}
                                        <div class="swiper-slide pb-10">
                                            <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                                <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                                    <div class="flex items-center gap-[15px] mb-[15px]">
                                                        <div class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px]">
                                                            <div class="text-dark-2 font-black text-[28px] leading-[54px]">{$coupon->getValue()}<span class="font-medium text-lg leading-[38px]">{$coupon->getUnitSymbol()}</span></div>
                                                        </div>
                                                        <div class="text-sm leading-[22x] text-dark-1"><span class="font-bold">{$coupon |dealName:true|noescape}</span> {$coupon->getDescription() |striptags|truncate:65|noescape}</div>
                                                    </div>

                                                    <a href="{plink "this!#deal-$couponId", openDeal => $coupon->getFullSlug()}" class="block relative w-full max-w-[300px] h-[56px] cursor-pointer rounded-[10px] overflow-hidden group">
                                                        <div class="absolute inset-0 z-20">
                                                            <div class="absolute">
                                                                <svg viewBox="0 0 216 56" xmlns="http://www.w3.org/2000/svg" class="w-full h-full max-w-[216px] relative z-30" fill="none" preserveAspectRatio="xMidYMid slice">
                                                                    <path d="M204 0H10C4.47716 0 0 4.47715 0 10V46C0 51.5228 4.47715 56 10 56H216L204 0Z" fill="#FEF3E9"/>
                                                                    <path d="M10 0.5C4.7533 0.500003 0.5 4.7533 0.5 10V46C0.5 51.2467 4.7533 55.5 10 55.5H215.381L203.596 0.5H10Z"
                                                                          stroke="url(#paint0_linear_0_1)" stroke-opacity="0.2"/>
                                                                    <path d="M187.836 26.6337L204 0L216 56L190 37.2761C186.613 34.8365 185.67 30.2024 187.836 26.6337Z" fill="#E9D6C5"/>
                                                                    <defs>
                                                                        <linearGradient id="paint0_linear_0_1" x1="216" y1="56" x2="185.896" y2="-38.2192" gradientUnits="userSpaceOnUse">
                                                                            <stop stop-color="#EF7F1A"/>
                                                                            <stop offset="1" stop-color="#FFA439"/>
                                                                        </linearGradient>
                                                                    </defs>
                                                                </svg>
                                                                <div class="absolute top-1/2 right-[-15px] -translate-y-1/2 text-dark-3 font-consolas text-[14px] z-20">
                                                                    {if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}{mb_substr($coupon->getCode(), -3, 3)}
                                                                    {else}
                                                                        {$coupon->getCode()}
                                                                    {/if}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="absolute z-10 inset-0 rounded-[10px] border border-dashed border-dark-4 group-hover:border-primary-orange group-hover:border-solid group-hover:z-20"></div>
                                                        <span class="text-sm lg:text-base absolute inset-0 flex items-center justify-center z-30 mr-8 lg:mr-0 font-bold text-primary-orange">
                                                            {_'newFront.deals.deal.getCode'}
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div class="swiper-button-prev !hidden lg:!flex">
                                    <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                                </div>
                                <div class="swiper-button-next z-40 !hidden lg:!flex">
                                    <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                                </div>

                                <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="hidden lg:block pointer-events-none absolute top-0 right-0 h-full w-[153px] z-30"></div>
                                <div class="swiper-pagination absolute -bottom-6 left-1/2 -translate-x-1/2 z-20"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {/if}

                <div class="px-5 md:px-0 lg:hidden">
                    <div class="text-xs text-dark-2 leading-[21px] my-[30px]">Nakupuj výhodne a získaj cashback až 7 % na Zalando s Tipli! Šetrenie pri online nákupoch nebolo nikdy jednoduchšie.
                        <span class="font-medium">Aktivuj odmenu a ušetri hneď teraz!</span>
                    </div>

                    <div class="mb-[50px]">
                        <div class="bg-white rounded-2xl p-[2px] pb-5 relative z-20" style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);">
                            <div class="relative w-full h-[180px] rounded-2xl z-20">
                                <img
                                        class="w-full h-full object-cover rounded-2xl"
                                        src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=1746&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                        alt=""
                                />
                                <div class="absolute inset-0 z-10" style="background: linear-gradient(82deg, rgba(0, 0, 0, 0.70) 38.89%, rgba(24, 43, 74, 0.70) 86.86%); backdrop-filter: blur(2.65px);border-radius: 1rem;"></div>
                                <div class="m-auto w-full max-w-[164px] absolute inset-0 flex items-center justify-center z-20 text-white text-center">
                                    <div>
                                        Získajte späť
                                        <span class="text-secondary-green text-[22px]">až <span class="text-[35px] font-medium">7</span>%</span>
                                        z vašich nákupov
                                    </div>
                                </div>
                                <div class="flex absolute left-1/2 z-30 shadow-sm bottom-0 transform -translate-x-1/2 translate-y-1/2 bg-white rounded-lg justify-center items-center w-[115px] h-[53px]">
                                    <img class="max-w-[68px] max-h-[30px]" alt="logo" src="https://www.tipli.cz/upload/images/shops-shop-logo/788948.svg">
                                </div>
                            </div>
                            <div class="m-auto mt-10 text-sm text-dark-1 leading-[22px] text-center w-full max-w-[196px] mb-5">
                                <span class="font-bold underline">Práve teraz navyše</span> 10 EUR bonus pre novo registrovaných
                            </div>
                            <div class="bg-orange-gradient hover:bg-orange-gradient-hover hover:cursor-pointer text-sm py-[14px] rounded-[10px] text-center text-white font-bold leading-7 mx-5">
                                <span class="mr-1">Nakupovať</span>  🛒
                            </div>
                        </div>
                        <div class="bg-[#FAFAFB] -mt-[67px] pt-20 px-5 pb-2 rounded-b-2xl relative z-10 mb-[50px]">
                            <div class="divide-y divide-light-6">
                                {for $i = 1; $i <= 4; $i++}
                                    <div class="flex justify-between items-center text-dark-2 text-xs leading-[21px] py-2">
                                        <div>Peniaze späť z nákupu</div>
                                        <div class="text-secondary-green font-bold">až 7%</div>
                                    </div>
                                {/for}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:hidden">
                    <div>
                        <div class="pl-5 text-lg text-dark-1 leading-[31.5px] font-bold w-full max-w-[251px]">
                            Peniaze späť dostanete aj v ďalších 1500 obchodoch
                        </div>

                        <div
                                id="marquee"
                                class="relative overflow-hidden w-full bg-transparent group py-5 flex flex-col gap-4"
                        >
                            <div
                                    class="flex gap-4 w-full animate-marquee"
                                    style="--marquee-duration: 30s;"
                            >

                                {for $i = 1; $i <= 4; $i++}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="logo" src="https://www.tipli.cz/upload/images/shops-shop-logo/789033.svg">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]"><span class="font-bold">7% </span> späť</div>
                                            </div>
                                        </div>
                                    </div>
                                {/for}
                            </div>
                            <div
                                    class="flex gap-4 w-full animate-marquee"
                                    style="--marquee-duration: 30s; animation-direction: reverse;"
                            >
                                {for $i = 1; $i <= 4; $i++}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="logo" src="https://www.tipli.cz/upload/images/shops-shop-logo/792258.svg">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]"><span class="font-bold">7% </span> späť</div>
                                            </div>
                                        </div>
                                    </div>
                                {/for}
                            </div>
                            <div
                                    class="flex gap-4 w-full animate-marquee"
                                    style="--marquee-duration: 30s;"
                            >
                                {for $i = 1; $i <= 4; $i++}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="logo" src="https://www.tipli.cz/upload/images/shops-shop-logo/789107.svg">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]"><span class="font-bold">7% </span> späť</div>
                                            </div>
                                        </div>
                                    </div>
                                {/for}
                            </div>
                        </div>

                        <div class="text-center mt-2.5 mb-[50px]">
                            <a href="#" class="text-sm text-dark-1 underline leading-[24.5px] font-medium">Prehľadávať všetky obchody</a>
                        </div>
                    </div>

                    <div class="w-full mb-10">
                        <div class="bg-primary-blue-dark"
                        >
                            <div class="px-5 pt-[50px] pb-10">
                                <div class="text-white text-lg leading-[31.5px] font-bold w-full max-w-[304px] mb-5">
                                    Registrácia je úplne zadarmo.
                                    <span class="font-normal">Navyše získate ďalšie skvelé ponuky</span>
                                </div>
                                <div class="flex flex-col gap-[15px] mb-[30px]">
                                    <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-[2px] hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                        <img src="/new-design/money-bag.png" alt="money bag">
                                        <div class="text-sm">
                                            <div class="text-secondary-green font-bold">Bonus 10 EUR</div>
                                            <div class="text-dark-1">za registráciu úplne zadarmo</div>
                                        </div>
                                    </div>
                                    <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-[2px] hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                        <img src="/new-design/champion.png" alt="bonus">
                                        <div class="text-sm">
                                            <div class="text-secondary-green font-bold">Bonus 10 EUR</div>
                                            <div class="text-dark-1">za registráciu úplne zadarmo</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-[30px]">
                                    <a href="#" class="text-sm text-white underline leading-[24.5px] font-medium">Načítať ďalšie bonusy <span class="font-normal">(6)</span></a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="px-5 md:px-0 lg:px-5 w-full">
                        <div class="flex justify-center gap-[57px] md:gap-[81px] mb-[60px] md:mb-[130px] flex-wrap">
                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">4,8/5</span> (1 tis. hodnocení)
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="108" height="26" viewBox="0 0 108 26" fill="none">
                                        <path d="M17.0515 14.2646C17.025 11.1005 19.6462 9.5582 19.7682 9.48929C18.2825 7.32689 15.9796 7.03009 15.1678 7.00889C13.231 6.80752 11.3579 8.16431 10.371 8.16431C9.36808 8.16431 7.85047 7.03009 6.21089 7.06191C4.1043 7.09369 2.1357 8.31271 1.05855 10.1995C-1.17007 14.0526 0.490798 19.7129 2.6292 22.8293C3.69571 24.3557 4.94271 26.0571 6.57703 25.9987C8.17417 25.9351 8.77378 24.9811 10.6999 24.9811C12.6102 24.9811 13.1726 25.9987 14.8388 25.9616C16.5527 25.9351 17.6352 24.4299 18.6645 22.8929C19.9009 21.144 20.3944 19.4215 20.4156 19.3367C20.3732 19.3101 17.0833 18.0541 17.0515 14.2646ZM13.9049 4.9578C14.7645 3.88191 15.3535 2.42443 15.189 0.94043C13.9473 0.993401 12.3873 1.799 11.4958 2.84843C10.7053 3.77591 9.99419 5.29169 10.18 6.7174C11.5702 6.81809 13.0081 6.01249 13.9049 4.9578Z" fill="#BDC2CC"></path>
                                        <path d="M40.197 21.4867H37.9631L36.7374 17.6495H32.4871L31.3197 21.4867H29.1442L33.3573 8.41696H35.9573L40.197 21.4867ZM36.3712 16.0383L35.2622 12.6252C35.1455 12.2754 34.9226 11.4539 34.6042 10.1607H34.5671C34.4397 10.7172 34.2275 11.5387 33.9463 12.6252L32.8585 16.0383H36.3712ZM51.0113 16.6584C51.0113 18.2591 50.576 19.5258 49.7058 20.4586C48.9254 21.2854 47.9546 21.6987 46.7978 21.6987C45.5455 21.6987 44.649 21.2535 44.1026 20.3631V25.308H42.0065V15.1586C42.0065 14.1516 41.9801 13.118 41.9269 12.0634H43.7731L43.8904 13.558H43.927C44.6278 12.4344 45.6891 11.8672 47.1107 11.8672C48.2252 11.8672 49.1537 12.3072 49.8969 13.187C50.6395 14.0668 51.0113 15.2275 51.0113 16.6584ZM48.878 16.7379C48.878 15.8211 48.6709 15.0632 48.2572 14.4696C47.8059 13.8495 47.196 13.542 46.4317 13.542C45.9173 13.542 45.4448 13.717 45.0254 14.0562C44.6067 14.4007 44.3303 14.8512 44.2033 15.4023C44.145 15.6091 44.1129 15.8263 44.1078 16.0383V17.6124C44.1078 18.2962 44.32 18.8738 44.7394 19.3508C45.1582 19.8226 45.7051 20.061 46.3785 20.061C47.1691 20.061 47.7847 19.7536 48.2252 19.1495C48.6548 18.5347 48.878 17.7343 48.878 16.7379ZM61.8675 16.6584C61.8675 18.2591 61.4327 19.5258 60.562 20.4586C59.7822 21.2854 58.8113 21.6987 57.6546 21.6987C56.4022 21.6987 55.5052 21.2535 54.9588 20.3631V25.308H52.8626V15.1586C52.8626 14.1516 52.8363 13.118 52.7831 12.0634H54.6299L54.7466 13.558H54.7838C55.484 12.4344 56.5453 11.8672 57.9675 11.8672C59.082 11.8672 60.0105 12.3072 60.7531 13.187C61.4911 14.0668 61.8675 15.2275 61.8675 16.6584ZM59.729 16.7379C59.729 15.8211 59.5225 15.0632 59.1083 14.4696C58.6574 13.8495 58.047 13.542 57.2833 13.542C56.7684 13.542 56.2958 13.717 55.8713 14.0562C55.4525 14.4007 55.1762 14.8512 55.0492 15.4023C54.9851 15.662 54.9485 15.874 54.9485 16.0383V17.6124C54.9485 18.2962 55.1608 18.8738 55.5796 19.3508C55.9989 19.8226 56.5453 20.061 57.2249 20.061C58.015 20.061 58.6306 19.7536 59.0711 19.1495C59.5116 18.5347 59.729 17.7343 59.729 16.7379ZM74.0029 17.8244C74.0029 18.9375 73.6156 19.8438 72.841 20.538C71.992 21.3012 70.798 21.6828 69.2751 21.6828C67.8689 21.6828 66.739 21.4126 65.8842 20.8666L66.3671 19.123C67.2853 19.6688 68.2985 19.9391 69.3969 19.9391C70.1876 19.9391 70.8031 19.759 71.2437 19.4038C71.6842 19.0487 71.9067 18.5718 71.9067 17.9782C71.9067 17.4482 71.7214 17.003 71.3604 16.6372C70.9994 16.2768 70.3998 15.9376 69.5565 15.6303C67.2641 14.777 66.1177 13.5262 66.1177 11.8885C66.1177 10.8179 66.521 9.93811 67.3328 9.25439C68.1395 8.57068 69.2116 8.22622 70.5486 8.22622C71.7374 8.22622 72.7294 8.43291 73.5201 8.84628L72.9949 10.5529C72.2517 10.1501 71.4187 9.95399 70.4793 9.95399C69.7419 9.95399 69.1584 10.1342 68.7442 10.4946C68.394 10.8179 68.219 11.2101 68.219 11.6765C68.219 12.1906 68.4209 12.6199 68.8242 12.9538C69.1744 13.2612 69.8054 13.6004 70.7236 13.9608C71.8484 14.4166 72.6762 14.9412 73.2071 15.5508C73.7375 16.1603 74.0029 16.9182 74.0029 17.8244ZM80.9539 13.6375H78.6404V18.2114C78.6404 19.3774 79.0488 19.955 79.8664 19.955C80.2428 19.955 80.5506 19.9233 80.8 19.8596L80.8584 21.4496C80.4448 21.6034 79.9036 21.6828 79.2296 21.6828C78.4018 21.6828 77.7542 21.4284 77.2873 20.925C76.8205 20.4214 76.5871 19.5734 76.5871 18.3863V13.6375H75.2129V12.0687H76.5871V10.3409L78.6461 9.72079V12.0687H80.9591L80.9539 13.6375ZM91.3593 16.7008C91.3593 18.1478 90.9457 19.3403 90.1178 20.2678C89.2528 21.227 88.1018 21.704 86.6635 21.704C85.2784 21.704 84.1749 21.243 83.3522 20.3314C82.5301 19.4144 82.1216 18.2538 82.1216 16.8599C82.1216 15.3971 82.5461 14.2046 83.3951 13.2718C84.2441 12.339 85.3848 11.8779 86.8225 11.8779C88.2076 11.8779 89.3221 12.339 90.1602 13.2559C90.956 14.141 91.3593 15.2911 91.3593 16.7008ZM89.1842 16.7486C89.1842 15.8847 88.9982 15.1427 88.6212 14.5279C88.1813 13.7806 87.5548 13.4043 86.7379 13.4043C85.8992 13.4043 85.2521 13.7806 84.8168 14.5279C84.4403 15.1479 84.2544 15.9006 84.2544 16.791C84.2544 17.6548 84.4403 18.3968 84.8168 19.017C85.2681 19.7642 85.9049 20.1406 86.7219 20.1406C87.5234 20.1406 88.1492 19.759 88.6058 19.0011C88.9931 18.3598 89.1842 17.6124 89.1842 16.7486ZM98.1782 13.9078C97.9602 13.8707 97.7376 13.8495 97.5145 13.8495C96.7771 13.8495 96.2096 14.1251 95.8062 14.6816C95.4561 15.1744 95.2811 15.7946 95.2811 16.5419V21.4867H93.1849V15.0314C93.1849 14.0403 93.1689 13.0544 93.1265 12.0634H94.9521L95.0259 13.8654H95.0842C95.3074 13.2452 95.6523 12.7471 96.1352 12.3708C96.57 12.0422 97.106 11.8672 97.6524 11.8672C97.8492 11.8672 98.0186 11.8832 98.1782 11.9044V13.9078ZM107.554 16.3299C107.559 16.6479 107.533 16.9659 107.48 17.2786H101.187C101.208 18.2114 101.516 18.9215 102.099 19.4144C102.63 19.8543 103.314 20.0716 104.158 20.0716C105.092 20.0716 105.941 19.9233 106.705 19.6264L107.034 21.0786C106.142 21.4655 105.087 21.6616 103.872 21.6616C102.407 21.6616 101.261 21.2323 100.423 20.3738C99.5895 19.5151 99.1702 18.3598 99.1702 16.9128C99.1702 15.4924 99.5575 14.3106 100.338 13.3672C101.15 12.3602 102.253 11.8567 103.638 11.8567C104.996 11.8567 106.026 12.3602 106.726 13.3672C107.273 14.1675 107.554 15.1532 107.554 16.3299ZM105.554 15.7892C105.57 15.1692 105.432 14.6339 105.145 14.178C104.784 13.5951 104.222 13.3036 103.474 13.3036C102.789 13.3036 102.232 13.5898 101.802 14.1568C101.452 14.6074 101.245 15.1532 101.181 15.784L105.554 15.7892Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">4,8/5</span> (1,1 tis. hodnocení)
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="105" height="26" viewBox="0 0 105 18" fill="none">
                                        <path d="M92.356 17.7011H95.8498V12.2781L100.725 17.7011H105L99.3244 11.4512L104.173 6.03195H100.283L95.8498 11.0267V0L92.356 0.439333V17.7011ZM84.2986 5.73304C80.4199 5.73304 77.7265 8.23589 77.7265 11.8647C77.7265 15.4935 80.4199 17.9964 84.2986 17.9964C88.1772 17.9964 90.8707 15.4935 90.8707 11.8647C90.8707 8.23589 88.1772 5.73304 84.2986 5.73304ZM84.2986 15.1391C82.5016 15.1391 81.2704 13.8064 81.2704 11.8647C81.2704 9.92295 82.5016 8.59029 84.2986 8.59029C86.0955 8.59029 87.3268 9.92295 87.3268 11.8647C87.3268 13.8064 86.0955 15.1391 84.2986 15.1391ZM70.1 5.73304C66.2214 5.73304 63.5279 8.23589 63.5279 11.8647C63.5279 15.4935 66.2214 17.9964 70.1 17.9964C73.9787 17.9964 76.6721 15.4935 76.6721 11.8647C76.6721 8.23589 73.9787 5.73304 70.1 5.73304ZM70.1 15.1391C68.3031 15.1391 67.0718 13.8064 67.0718 11.8647C67.0718 9.92295 68.3031 8.59029 70.1 8.59029C71.897 8.59029 73.1282 9.92295 73.1282 11.8647C73.1282 13.8064 71.897 15.1391 70.1 15.1391ZM56.7287 5.73304C55.1127 5.73304 53.7736 6.34212 52.9348 7.45694V0L49.4371 0.43558V17.6973H52.5385L52.6 15.8959C53.4273 17.2396 54.9087 17.9964 56.7287 17.9964C60.0456 17.9964 62.4736 15.4122 62.4736 11.8647C62.4736 8.31707 60.0571 5.73304 56.7287 5.73304ZM55.9015 15.1391C54.1045 15.1391 52.8733 13.8064 52.8733 11.8647C52.8733 9.92295 54.1045 8.59029 55.9015 8.59029C57.6984 8.59029 58.9297 9.92295 58.9297 11.8647C58.9297 13.8064 57.6984 15.1391 55.9015 15.1391ZM42.434 15.2904C40.5794 15.2904 39.248 14.4635 38.794 13.0386H47.4015C47.4863 12.6031 47.544 12.0862 47.544 11.7392C47.544 7.98484 45.3545 5.73304 41.676 5.73304C37.9629 5.73304 35.3924 8.23589 35.3924 11.8647C35.3924 15.5415 38.086 17.9964 42.0839 17.9964C44.1539 17.9964 46.1664 17.3319 47.4246 16.2281L46.1664 14.1018C44.9467 14.9102 43.7499 15.2904 42.434 15.2904ZM41.6529 8.33924C43.269 8.33924 44.3002 9.28055 44.3002 10.7387V10.7499H38.7209C39.0557 9.20312 40.0983 8.33924 41.6529 8.33924ZM29.9208 18C31.7293 18 33.4993 17.3355 34.7459 16.1838L33.38 13.98C32.4103 14.7035 31.3099 15.0948 30.2671 15.0948C28.3278 15.0948 27.0349 13.7953 27.0349 11.8683C27.0349 9.94137 28.3278 8.64191 30.2671 8.64191C31.2252 8.64191 32.268 8.97413 33.1145 9.56116L34.5036 7.30924C33.38 6.33473 31.6408 5.73667 29.9169 5.73667C26.123 5.73667 23.441 8.27648 23.441 11.8683C23.4448 15.4492 26.1268 18 29.9208 18ZM18.8121 6.03195L18.7506 7.84829C17.9232 6.49345 16.4418 5.73667 14.6218 5.73667C11.2934 5.73667 8.87694 8.32082 8.87694 11.8683C8.87694 15.416 11.3088 18 14.6218 18C16.4418 18 17.927 17.2432 18.7506 15.8995L18.8121 17.701H21.9134V6.03195H18.8121ZM15.449 15.1391C13.6522 15.1391 12.4208 13.8064 12.4208 11.8647C12.4208 9.92295 13.6522 8.59029 15.449 8.59029C17.246 8.59029 18.4773 9.92295 18.4773 11.8647C18.4773 13.8064 17.2421 15.1391 15.449 15.1391ZM8.61526 6.05389H5.4716V5.0571C5.4716 3.56571 6.07957 2.99358 7.65721 2.99358C8.14583 2.99358 8.54214 3.00461 8.76921 3.02678V0.468553C8.33818 0.354058 7.2878 0.239677 6.67983 0.239677C3.46684 0.239677 1.98544 1.69411 1.98544 4.83561V6.05014H0V8.87055H1.98544V17.6971H5.47539V8.87419H8.07662L8.61526 6.05389Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">4,8/5</span> (3,4 tis. hodnocení)
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="113" height="25" viewBox="0 0 113 25" fill="none">
                                        <path d="M57.0823 10.6854C54.7526 10.6854 52.9082 12.4432 52.9082 14.8846C52.9082 17.2283 54.7526 19.0837 57.0823 19.0837C59.412 19.0837 61.2569 17.3259 61.2569 14.8846C61.2569 12.4432 59.412 10.6854 57.0823 10.6854ZM57.0823 17.3259C55.8202 17.3259 54.7526 16.2517 54.7526 14.7869C54.7526 13.3221 55.8202 12.2479 57.0823 12.2479C58.3444 12.2479 59.412 13.2245 59.412 14.7869C59.412 16.3494 58.3444 17.3259 57.0823 17.3259ZM48.0537 10.6854C45.724 10.6854 43.8796 12.4432 43.8796 14.8846C43.8796 17.2283 45.724 19.0837 48.0537 19.0837C50.384 19.0837 52.2284 17.3259 52.2284 14.8846C52.2284 12.4432 50.384 10.6854 48.0537 10.6854ZM48.0537 17.3259C46.7922 17.3259 45.724 16.2517 45.724 14.7869C45.724 13.3221 46.7922 12.2479 48.0537 12.2479C49.3158 12.2479 50.384 13.2245 50.384 14.7869C50.384 16.3494 49.3158 17.3259 48.0537 17.3259ZM37.2782 11.9549V13.7127H41.4525C41.3555 14.6893 40.9672 15.4705 40.4818 15.9588C39.8993 16.5447 38.9285 17.2283 37.2782 17.2283C34.657 17.2283 32.7154 15.1775 32.7154 12.5409C32.7154 9.90425 34.7541 7.8535 37.2782 7.8535C38.6373 7.8535 39.7051 8.4394 40.4818 9.123L41.7438 7.8535C40.6759 6.87697 39.3168 6.09573 37.3752 6.09573C33.8804 6.09573 30.8709 9.02536 30.8709 12.5409C30.8709 16.0564 33.8804 18.986 37.3752 18.986C39.3168 18.986 40.6759 18.4001 41.8409 17.1306C43.0058 15.9588 43.3941 14.2986 43.3941 13.0292C43.3941 12.6385 43.3941 12.2479 43.297 11.9549H37.2782ZM81.3524 13.3221C80.964 12.3456 79.9928 10.6854 77.8575 10.6854C75.7217 10.6854 73.9743 12.3456 73.9743 14.8846C73.9743 17.2283 75.7217 19.0837 78.0514 19.0837C79.8958 19.0837 81.061 17.9119 81.4493 17.2283L80.0903 16.2517C79.6049 16.9353 79.0221 17.4235 78.0514 17.4235C77.0808 17.4235 76.4979 17.033 76.0126 16.1541L81.5463 13.8104L81.3524 13.3221ZM75.7217 14.6893C75.7217 13.1268 76.9838 12.2479 77.8575 12.2479C78.5368 12.2479 79.2166 12.6385 79.4105 13.1268L75.7217 14.6893ZM71.1587 18.6931H73.003V6.48635H71.1587V18.6931ZM68.2467 11.5643C67.7608 11.0761 66.9846 10.5878 66.0133 10.5878C63.975 10.5878 62.0331 12.4432 62.0331 14.7869C62.0331 17.1306 63.8781 18.8884 66.0133 18.8884C66.9846 18.8884 67.7608 18.4001 68.1491 17.9119H68.2467V18.4978C68.2467 20.0602 67.3729 20.9391 66.0133 20.9391C64.9457 20.9391 64.1689 20.1579 63.975 19.4743L62.4215 20.1579C62.9069 21.2321 64.072 22.5992 66.1109 22.5992C68.2467 22.5992 69.9941 21.3297 69.9941 18.3025V10.8808H68.2467V11.5643ZM66.1109 17.3259C64.8488 17.3259 63.7806 16.2517 63.7806 14.7869C63.7806 13.3221 64.8488 12.2479 66.1109 12.2479C67.3729 12.2479 68.3436 13.3221 68.3436 14.7869C68.3436 16.2517 67.3729 17.3259 66.1109 17.3259ZM89.7981 6.48635H85.4295V18.6931H87.2739V14.1033H89.7981C91.8364 14.1033 93.7783 12.6385 93.7783 10.2949C93.7783 7.95114 91.8364 6.48635 89.7981 6.48635ZM89.8951 12.4432H87.2739V8.24412H89.8951C91.2541 8.24412 92.0309 9.41599 92.0309 10.2949C91.9339 11.2714 91.1571 12.4432 89.8951 12.4432ZM101.059 10.6854C99.6998 10.6854 98.3408 11.2714 97.8554 12.5409L99.5059 13.2245C99.8943 12.5409 100.477 12.3456 101.156 12.3456C102.127 12.3456 103.001 12.9315 103.098 13.9081V14.0057C102.807 13.8104 102.03 13.5174 101.253 13.5174C99.5059 13.5174 97.7585 14.494 97.7585 16.2517C97.7585 17.9119 99.2145 18.986 100.768 18.986C102.03 18.986 102.612 18.4001 103.098 17.8142H103.195V18.7908H104.942V14.1033C104.748 11.8573 103.098 10.6854 101.059 10.6854ZM100.865 17.3259C100.283 17.3259 99.4089 17.033 99.4089 16.2517C99.4089 15.2752 100.477 14.9822 101.35 14.9822C102.127 14.9822 102.515 15.1776 103.001 15.3728C102.807 16.5447 101.836 17.3259 100.865 17.3259ZM111.058 10.8808L109.02 16.1541H108.923L106.787 10.8808H104.845L108.049 18.3025L106.204 22.4039H108.049L113 10.8808H111.058ZM94.7489 18.6931H96.5933V6.48635H94.7489V18.6931Z" fill="#BDC2CC"></path>
                                        <path d="M0.582458 25C0.194134 24.8047 0 24.3165 0 23.8282C0 23.7305 0 23.5352 0 23.4376C0 16.2112 0 8.98477 0 1.85604C0 1.46542 0.0970671 1.17249 0.194134 0.781871C0.291257 0.586531 0.485391 0.391248 0.679581 0.195965C4.85396 4.39506 8.93128 8.59415 13.0086 12.7933C8.83421 16.6994 4.7569 20.8009 0.582458 25Z" fill="#BDC2CC"></path>
                                        <path d="M17.0856 8.49588C15.9206 9.66775 14.6586 10.8396 13.4937 12.1091C9.61049 8.10526 5.63024 4.10145 1.74706 0.0976415C1.74706 0.0976415 1.74706 0 1.84412 0C6.89227 2.83194 11.9404 5.66394 17.0856 8.49588Z" fill="#BDC2CC"></path>
                                        <path d="M1.74706 24.9995C5.63024 21.0933 9.61049 17.1872 13.4937 13.281C14.5615 14.3552 15.7265 15.4294 16.9885 16.6013C11.9404 19.4333 6.89227 22.2652 1.84412 24.9995H1.74706Z" fill="#BDC2CC"></path>
                                        <path d="M17.6685 16.2107C16.4064 15.0389 15.2415 13.867 13.9795 12.6952C15.2415 11.4257 16.5036 10.1562 17.7655 8.88669C18.1539 9.08198 18.5422 9.27732 18.9305 9.57024C19.9013 10.1562 20.9692 10.6445 21.94 11.2304C22.2312 11.328 22.4254 11.5234 22.6195 11.8163C23.0078 12.3045 23.0078 12.7929 22.6195 13.2811C22.4254 13.4764 22.2312 13.6717 21.94 13.7694C20.5809 14.6483 19.1247 15.4295 17.6685 16.2107Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="w-full h-px bg-light-4 mt-[50px] mb-10"></div>

                        <div class="text-lg font-bold leading-[31.5px] text-dark-1 mb-5">Nechajte si vrátiť peniaze ešte dnes a získajte k tomu bonus za nákupy až 10€</div>

                        <button class="text-sm lg:text-base w-full bg-orange-gradient py-4 rounded-[10px] text-center text-white font-bold leading-[24.5px]">
                            Zapojiť sa zadarmo a čerpať výhody 💰
                        </button>

                        <div class="w-full h-px bg-light-4 my-[50px]"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="hidden lg:block w-full min-h-[1277px] h-auto bg-repeat"
         style="background-image:url('{$basePath}/new-design/bg-profile-shop-blue.svg'); background-position: 0">
        <div class="container pt-[110px]">
            <div class="flex gap-[30px]">
                <div class="w-full max-w-[335px] shrink-0">
                    {cache 'new-shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $similarShops = $recommendedShops()}
                        <div class="text-white border border-white/15 rounded-2xl p-5 mb-[30px]" n:if="$similarShops">
                            <div class="leading-7 font-bold mb-5">
                                {_newFront.shop.bluescreen.similarShops.title, ['countOfCashbackShops' => $countOfCashbackShops]}
                            </div>

                            <div class="space-y-[14px] mb-5">
                                {foreach $similarShops as $shopItem}
                                    <div class="flex items-center gap-5 bg-white/5 p-[2px] rounded-lg hover:cursor-pointer hover:bg-white/10 hover:shadow-md hover:scale-[1.01] transition-all duration-200">
                                        <div>
                                            <a title="{$shopItem->getName()}" class="bg-white min-w-[113px] h-[49px] rounded-lg flex items-center justify-center h-[47px]" href="{plink Shop:default, $shopItem}">
                                                <img class="max-w-[70px] max-h-[27px]" alt="{$shopItem->getName()}" src="{$shopItem->getLogo() |image:116,0,'fit',false,$shopItem->getName()}" loading="lazy">
                                            </a></div>
                                        <div class="text-sm font-bold leading-[24.5px]">{$shopItem |reward:true,'extended'|noescape}</div>
                                    </div>
                                {/foreach}
                            </div>

                            <div class="text-center">
                                <a n:href="Shops:default" class="text-sm leading-[24.5px] underline font-medium">
                                    {_newFront.shop.bluescreen.similarShops.link}
                                </a>
                            </div>
                        </div>
                    {/cache}

                    <div class="text-white border border-white/15 rounded-2xl p-5 mb-[30px]">
                        <div class="leading-7 font-bold mb-5">{_newFront.shop.bluescreen.rating.title}</div>

                        <div class="space-y-5">
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-app-store.svg" alt="app store">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.apple.ratingValue}</span> {_newFront.homepage.review.apple.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.apple.count}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-facebook.svg" alt="facebook">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.facebook.rating}</span> {_newFront.homepage.review.facebook.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.facebook.count}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-google-play.svg" alt="google play">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.google.ratingValue}</span> {_newFront.homepage.review.google.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.google.count}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="w-full min-w-0">
                    <div class="rounded-[20px] mb-[60px]" style="background: linear-gradient(180deg, rgba(255, 255, 255,0.08) 0%,rgba(24,43, 74, 0.00) 100%)">
                        <div class="flex items-center pl-[50px] gap-[51px] pt-[39px] mb-[55px]">
                            <img src="{$basePath}/new-design/donkey-profile-blue.png" alt="app store">
                            <div class="pt-[19px]">
                                <div class="flex items-center gap-6">
                                    <div class="text-[26px] text-white w-full max-w-[297px] leading-[39px] mb-2">
                                        <span class="font-bold">{_newFront.shop.bluescreen.promo1} </span> {_newFront.shop.bluescreen.promo2}
                                    </div>
                                    <div>
                                        <img src="{$basePath}/new-design/flying-money-profile.svg" alt="icons">
                                    </div>
                                </div>

                                <div class="text-[#B9BFC8] text-sm leading-[24.5px] mb-2">
                                    {_newFront.shop.bluescreen.text, ['shop' => $shop->getName(), 'reward' => ($shop |reward:false,'common')]}
                                </div>

                                <a href="#" class="text-primary-orange underline font-medium text-sm leading-[24.5px]">
                                    {_newFront.shop.bluescreen.howItWorks}
                                </a>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="bg-white ml-[30px] pb-5 rounded-2xl relative z-10 w-full max-w-[335px]">
                                <div class="relative mb-16">
                                    <img class="rounded-2xl object-cover max-h-[200px] w-full" src="https://plus.unsplash.com/premium_photo-1664300792059-863ccfe55932?q=80&amp;w=1740&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.1.0&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="">
                                    <div class="absolute left-1/2 shadow-sm bottom-0 transform -translate-x-1/2 translate-y-1/2 bg-white rounded-2xl flex justify-center items-center w-[177px] h-[81px]">
                                        {include logo}
                                    </div>
                                </div>
                                <div class="w-full max-w-[168px] m-auto text-center text-dark-1 font-medium leading-[28px] mb-[22px]">
                                    {_newFront.shops.shop.shop.ctaBox.getReward}

                                {if $isUserLoggedIn && $userIdentity->isActiveUser()}
                                        {cache 'new-shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                            {$shop |reward:true,'common' |noescape}
                                        {/cache}
                                    {else}
                                        {cache 'new-shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                            {$shop |reward:true,'common' |noescape}
                                        {/cache}
                                    {/if}

                                {_newFront.shops.shop.shop.ctaBox.fromPurchase}
                                </div>

                                <div class="mx-5">
                                    {include ctaLink}
                                </div>
                            </div>

                            <div class="text-white bg-white/5 w-full max-w-[440px] mt-[26px] rounded-r-2xl h-max relative">
                                <div class="px-10 py-[30px]">
                                    <div class="text-xl leading-[35px] font-bold mb-3">{_newFront.shop.bluescreen.shopBox.title, ['shop' => $shop->getName()]} <span class="font-normal">({$shop |reward:false,'common'|noescape})</span></div>

                                    {var $offers = $shopOffers()}

                                    {foreach $offers as $offer}
                                        <div class="flex items-center justify-between border-b border-white/10 last:border-none py-2">
                                            <div class="text-sm leading-[24.5px]">{$offer->getName()}</div>
                                            <div class="text-secondary-green font-bold leading-7">{$offer |reward:true |noescape}</div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div class="absolute top-[-56px] right-[-20px]">
                                    <div class="flex flex-col items-center justify-center text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="107" height="107" viewBox="0 0 107 107" fill="none">
                                            <path d="M48.708 2.8501C51.3303 0.429895 55.3279 0.353667 58.0361 2.62256L58.2939 2.8501L63.6562 7.79932C65.3098 9.32532 67.5424 10.0461 69.7627 9.7876L70.207 9.72217L77.3936 8.4585C80.908 7.84028 84.3116 9.93713 85.3633 13.3101L85.457 13.6401L87.293 20.7026C87.859 22.8806 89.3476 24.6943 91.3555 25.6772L91.7637 25.8628L98.4932 28.6841C101.784 30.064 103.513 33.6687 102.574 37.0747L102.475 37.4038L100.201 44.3374C99.4999 46.4755 99.7712 48.8059 100.929 50.7183L101.173 51.0952L105.308 57.1069C107.33 60.0471 106.837 64.0148 104.205 66.3726L103.943 66.5952L98.2822 71.1987C96.5362 72.6183 95.5053 74.7257 95.4453 76.9604L95.4463 77.4087L95.6748 84.7026C95.7864 88.2693 93.2256 91.3391 89.7373 91.8999L89.3975 91.9468L82.1455 92.7583C79.9091 93.0086 77.9022 94.2242 76.6436 96.0718L76.4023 96.4487L72.6514 102.708C70.8172 105.769 67.0034 106.968 63.7656 105.554L63.4541 105.409L56.9141 102.171C54.8976 101.173 52.5526 101.11 50.4951 101.984L50.0879 102.171L43.5479 105.409C40.3498 106.992 36.4934 105.939 34.5342 102.999L34.3506 102.708L30.5996 96.4487C29.443 94.5188 27.5039 93.1983 25.3008 92.8208L24.8564 92.7583L17.6045 91.9468C14.0581 91.5497 11.3838 88.5781 11.3252 85.0454L11.3271 84.7026L11.5557 77.4087C11.6262 75.1596 10.709 73.0005 9.05957 71.4917L8.71973 71.1987L3.05859 66.5952C0.289922 64.3441 -0.354287 60.3986 1.50586 57.395L1.69434 57.1069L5.8291 51.0952C7.10445 49.2412 7.50077 46.9282 6.92871 44.7671L6.80078 44.3374L4.52734 37.4038C3.4152 34.0131 5.00641 30.3457 8.19531 28.8247L8.50879 28.6841L15.2383 25.8628C17.3136 24.9926 18.8968 23.2606 19.584 21.1333L19.709 20.7026L21.5449 13.6401C22.4426 10.1867 25.7638 7.96237 29.2686 8.40674L29.6084 8.4585L36.7949 9.72217C39.0112 10.1121 41.2796 9.5114 43.0078 8.09326L43.3457 7.79932L48.708 2.8501Z" fill="#66B940" stroke="url(#paint0_linear_7837_3309)" stroke-width="0.625969"/>
                                            <defs>
                                                <linearGradient id="paint0_linear_7837_3309" x1="2.73046" y1="38.9648" x2="110.001" y2="89.8596" gradientUnits="userSpaceOnUse">
                                                    <stop stop-color="#66B940"/>
                                                    <stop offset="0.495" stop-color="white"/>
                                                    <stop offset="1" stop-color="#66B940"/>
                                                </linearGradient>
                                            </defs>
                                        </svg>
                                        <div class="absolute text-sm font-bold w-[60px] leading-tight text-center leading-[18px]">
                                            {_newFront.shop.bluescreen.guarantee}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pl-[30px] text-white">
                        <div class="text-xl font-bold leading-[35px] mb-[36px]">Registrácia je úplne <span class="underline">zadarmo.</span> <span class="font-normal">Navyše získate ďalšie skvelé ponuky</span></div>

                        <div class="flex gap-5 mb-[30px]">
                            <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-1.5 hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                <img src="{$basePath}/new-design/money-bag.png" alt="money bag">
                                <div>
                                    <div class="text-secondary-green font-bold">Bonus 10 EUR</div>
                                    <div class="text-dark-1">za registráciu úplne zadarmo</div>
                                </div>
                            </div>
                            <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-1.5 hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                <img src="{$basePath}/new-design/champion.png" alt="bonus">
                                <div>
                                    <div class="text-secondary-green font-bold">Bonus 10 EUR</div>
                                    <div class="text-dark-1">za registráciu úplne zadarmo</div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-orange-gradient hover:bg-orange-gradient-hover hover:cursor-pointer py-[18px] rounded-[10px] text-center text-white font-bold text-lg leading-[31.5px] mb-[30px]">
                            <span class="mr-1">Zapojiť sa a získať všetky výhody</span> 🛒
                        </div>

                        <div class="text-center">
                            <a href="#"><span class="underline font-medium">Nakúpiť na Zalande </span>bez Cashbacku</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container relative z-30 lg:pt-[100px]">
        <div class="flex flex-col lg:flex-row gap-[30px] text-dark-1">
            <div class="hidden lg:block w-full max-w-[335px] shrink-0">
                {cache 'new-shopTags-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $countOfMainTags = 0}
                    {foreach $shop->getTags() as $tagItem}
                        {if $tagItem->hasMainNavigationGroupType()}
                            {var $countOfMainTags = $countOfMainTags + 1}
                            {breakIf $countOfMainTags > 1}
                        {/if}
                    {/foreach}

                    <div class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$countOfMainTags > 1">
                        <div class="leading-7 font-bold mb-5 leading-7">
                            {_newFront.shops.shop.shop.categoryTitle}
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <a n:href=":NewFront:Shops:Shops:default $tagItem" n:foreach="$shop->getTags() as $tagItem"
                                        n:if="$tagItem->hasMainNavigationGroupType()"
                                    class="text-xs text-dark-1 font-medium leading-[21px] py-[7px] px-2.5 rounded-md bg-white hover:shadow-md transition duration-200">
                                {$tagItem->getName()}
                            </a>
                        </div>
                    </div>
                {/cache}

                {cache 'new-shopForeignShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $foreignShops = $getForeignShops()}
                    <div class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$foreignShops">
                        <div class="leading-7 font-bold mb-5 leading-7">
                            {_newFront.shops.shop.shop.shopDetail.foreignShops, ['brand' => $shop->getName()]}
                        </div>

                        <div class="flex gap-2">
                            {foreach $foreignShops as $foreignShop}
                                <a href="{$foreignShop->getForeignShopUrl()}">
                                    <img class="rounded-full w-[30px] h-[30px]"
                                         src="{$basePath}/new-design/flags/{$foreignShop->getShop()->getLocalization()->getLocale()}.svg"
                                         loading="lazy"
                                         alt="{$foreignShop->getShop()->getName()}">
                                </a>
                            {/foreach}
                        </div>
                    </div>
                {/cache}

                {cache 'new-reviews-' . $shop->getId() . '-' . $localization->getLocale(), expire => '168 hours', tags => ['shop/' . $shop->getId()]}
                    <div class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$reviews->isEmpty() === false">
                    <div class="leading-7 font-bold mb-5 leading-7">
                        {_newFront.shops.shop.shop.sidebarMenu.review, [shop => $shop->getName()]}
                    </div>
                    <div class="w-full h-px bg-light-4 mt-[17px] mb-5"></div>

                    <div class="space-y-5">
                        {foreach $reviews as $review}
                            <div>
                                <div class="flex items-center gap-2 mb-[3px]">
                                    <div class="text-sm text-dark-1 font-medium leading-[24.5px]">{$review->getShortUsername()}</div>
                                    <div>
                                        {include '../stars.latte', 'rate' => $review->getRate()}
                                    </div>

                                </div>
                                <div class="text-sm text-dark-2 leading-[24.5px]">{$review->getText() |truncate: 100}</div>
                            </div>
                        {/foreach}
                    </div>

                    <div class="w-full h-px bg-light-4 mt-[18px] mb-[25px]"></div>

                    <div class="flex items-center gap-2 text-sm mb-[11px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="91" height="16" viewBox="0 0 91 16" fill="none">
                            <path d="M7.52447 1.46352C7.67415 1.00287 8.32585 1.00287 8.47553 1.46353L9.68386 5.18237C9.75079 5.38838 9.94277 5.52786 10.1594 5.52786H14.0696C14.554 5.52786 14.7554 6.14767 14.3635 6.43237L11.2001 8.73075C11.0248 8.85807 10.9515 9.08375 11.0184 9.28976L12.2268 13.0086C12.3764 13.4693 11.8492 13.8523 11.4573 13.5676L8.29389 11.2693C8.11865 11.1419 7.88135 11.1419 7.70611 11.2693L4.54267 13.5676C4.15081 13.8523 3.62357 13.4693 3.77325 13.0086L4.98157 9.28976C5.04851 9.08375 4.97518 8.85807 4.79994 8.73075L1.6365 6.43237C1.24464 6.14767 1.44603 5.52786 1.93039 5.52786H5.84062C6.05723 5.52786 6.24921 5.38838 6.31614 5.18237L7.52447 1.46352Z" fill="#FDBB47"/>
                            <path d="M26.1924 1.46352C26.3421 1.00287 26.9938 1.00287 27.1435 1.46353L28.3518 5.18237C28.4188 5.38838 28.6107 5.52786 28.8274 5.52786H32.7376C33.2219 5.52786 33.4233 6.14767 33.0315 6.43237L29.868 8.73075C29.6928 8.85807 29.6195 9.08375 29.6864 9.28976L30.8947 13.0086C31.0444 13.4693 30.5172 13.8523 30.1253 13.5676L26.9619 11.2693C26.7866 11.1419 26.5493 11.1419 26.3741 11.2693L23.2106 13.5676C22.8188 13.8523 22.2915 13.4693 22.4412 13.0086L23.6495 9.28976C23.7165 9.08375 23.6431 8.85807 23.4679 8.73075L20.3045 6.43237C19.9126 6.14767 20.114 5.52786 20.5984 5.52786H24.5086C24.7252 5.52786 24.9172 5.38838 24.9841 5.18237L26.1924 1.46352Z" fill="#FDBB47"/>
                            <path d="M44.8565 1.46352C45.0062 1.00287 45.6579 1.00287 45.8076 1.46353L47.0159 5.18237C47.0828 5.38838 47.2748 5.52786 47.4914 5.52786H51.4016C51.886 5.52786 52.0874 6.14767 51.6955 6.43237L48.5321 8.73075C48.3568 8.85807 48.2835 9.08375 48.3505 9.28976L49.5588 13.0086C49.7085 13.4693 49.1812 13.8523 48.7894 13.5676L45.6259 11.2693C45.4507 11.1419 45.2134 11.1419 45.0381 11.2693L41.8747 13.5676C41.4828 13.8523 40.9556 13.4693 41.1053 13.0086L42.3136 9.28976C42.3805 9.08375 42.3072 8.85807 42.132 8.73075L38.9685 6.43237C38.5767 6.14767 38.7781 5.52786 39.2624 5.52786H43.1726C43.3893 5.52786 43.5812 5.38838 43.6482 5.18237L44.8565 1.46352Z" fill="#FDBB47"/>
                            <path d="M63.5245 1.46352C63.6741 1.00287 64.3259 1.00287 64.4755 1.46353L65.6839 5.18237C65.7508 5.38838 65.9428 5.52786 66.1594 5.52786H70.0696C70.554 5.52786 70.7554 6.14767 70.3635 6.43237L67.2001 8.73075C67.0248 8.85807 66.9515 9.08375 67.0184 9.28976L68.2268 13.0086C68.3764 13.4693 67.8492 13.8523 67.4573 13.5676L64.2939 11.2693C64.1186 11.1419 63.8814 11.1419 63.7061 11.2693L60.5427 13.5676C60.1508 13.8523 59.6236 13.4693 59.7732 13.0086L60.9816 9.28976C61.0485 9.08375 60.9752 8.85807 60.7999 8.73075L57.6365 6.43237C57.2446 6.14767 57.446 5.52786 57.9304 5.52786H61.8406C62.0572 5.52786 62.2492 5.38838 62.3161 5.18237L63.5245 1.46352Z" fill="#FDBB47"/>
                            <path d="M82.1924 1.46352C82.3421 1.00287 82.9938 1.00287 83.1435 1.46353L84.3518 5.18237C84.4188 5.38838 84.6107 5.52786 84.8274 5.52786H88.7376C89.2219 5.52786 89.4233 6.14767 89.0315 6.43237L85.868 8.73075C85.6928 8.85807 85.6195 9.08375 85.6864 9.28976L86.8947 13.0086C87.0444 13.4693 86.5172 13.8523 86.1253 13.5676L82.9619 11.2693C82.7866 11.1419 82.5493 11.1419 82.3741 11.2693L79.2106 13.5676C78.8188 13.8523 78.2915 13.4693 78.4412 13.0086L79.6495 9.28976C79.7165 9.08375 79.6431 8.85807 79.4679 8.73075L76.3045 6.43237C75.9126 6.14767 76.114 5.52786 76.5984 5.52786H80.5086C80.7252 5.52786 80.9172 5.38838 80.9841 5.18237L82.1924 1.46352Z" fill="#FDBB47"/>
                        </svg>

                        <span class="font-bold">4.9</span> (165x)
                    </div>

                    <a href="#" class="text-sm font-medium leading-[24.5px] underline">Pridať novú recenziu</a>
                </div>
                {/cache}
            </div>

            {cache 'new-otherDeals-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
            {var $otherDeals = $getOtherDeals()}
            <div class="w-full min-w-0" n:if="$otherDeals !== null && $otherDeals->isEmpty() === false">
                <div class="lg:pl-7">
                    <div class="text-lg lg:text-xl text-[35px] font-bold mb-[5px]">Prezrite si kupóny z iných obchodov v kategórii Parfémy a kozmetika</div>
                    <div class="lg:text-sm text-dark-2 text-xs leading-[21px] lg:leading-[24.5px] mb-2">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.</div>

                    <div class="swiper swiper-profile-shop swiper-another-coupons relative mb-10 !mr-[-20px] lg:!mr-0">
                        <div class="swiper-wrapper">
                            {foreach $otherDeals as $deal}
                                <div class="swiper-slide pt-[30px] pb-[45px]">
                                    <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                        <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                            <div class="flex flex-col lg:flex-row justify-content-between w-full">
                                                <div class="flex-col w-full justify-between">
                                                    <div class="flex justify-center items-center bg-white rounded-lg w-[100px] h-[43px] mt-[-24px] mb-2.5">
                                                        <img class="max-w-[60px] max-h-[30px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789033.svg" alt="shop">
                                                    </div>
                                                    <div class="flex items-center gap-[15px] relative mb-[15px] lg:mb-0 lg:top-[1px]">
                                                        <div class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px]">
                                                            <div class="text-primary-orange font-black text-[28px] leading-[54px]">14<span class="font-medium text-lg leading-[38px]">%</span></div>
                                                        </div>
                                                        <div class="text-sm leading-[22x] w-full max-w-[243px] text-dark-1"><span class="font-medium underline">{$deal |dealName:true|noescape}</span>{$deal->getDescription() |striptags|truncate:65|noescape}</div>
                                                    </div>

                                                    <div class="flex flex-col lg:flex-row lg:items-center gap-[5px] lg:gap-2.5 mb-[15px] lg:mb-0 lg:mt-[13px]">
                                                        <div class="w-max text-xs text-secondary-red ring-1 ring-inset ring-secondary-red/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {if $deal->getValidTillDays() === 0}
                                                                {_'newFront.deals.deal.validTillToday'}
                                                            {elseif $deal->getValidTillDays() <= 3}
                                                                {_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}
                                                            {else}
                                                                {_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}
                                                            {/if}
                                                        </div>
                                                        <div n:if="$shop->isCashbackWithCouponAllowed()" class="w-max text-xs text-secondary-green ring-1 ring-inset ring-secondary-green/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')]}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-[13px]">
                                                    <div class="relative coupon-code-copy cursor-pointer">
                                                        <div class="hidden lg:block w-[207px] h-[72px]">
                                                            <img src="{$basePath}/new-design/coupon-profile.svg" alt="kupon">
                                                        </div>
                                                        <div class="h-full w-full lg:hidden" >
                                                            <img class="w-full h-full" src="{$basePath}/new-design/coupon-border-dashed-mb.svg" alt="kupon">
                                                        </div>
                                                        <span class="font-consolas text-dark-1 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">V39U5EBB</span>
                                                        <svg class="svg-copy-btn absolute top-1/2 right-2 -translate-x-1/2 -translate-y-1/2" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                            <path d="M12 3.57812V1.6875C12 1.30781 11.6922 1 11.3125 1H1.6875C1.30781 1 1 1.3078 1 1.6875V11.3125C1 11.6922 1.30781 12 1.6875 12H3.57812" stroke="#80899C" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round" stroke-dasharray="3 3"/>
                                                            <path d="M6 6.6875C6 6.3078 6.30781 6 6.6875 6H16.3125C16.6922 6 17 6.30781 17 6.6875V16.3125C17 16.6922 16.6922 17 16.3125 17H6.6875C6.3078 17 6 16.6922 6 16.3125V6.6875Z" stroke="#80899C" stroke-width="1.5" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        {var $dealId = $deal->getId()}
                                                        <a href="{plink "this!#deal-$dealId", openDeal => $deal->getFullSlug()}" class="text-sm lg:text-base border border-primary-orange/20 hover:border-primary-orange rounded-[10px] flex justify-center items-center bg-pastel-orange-light h-[56px] leading-7 font-bold text-primary-orange">
                                                            {_'newFront.deals.deal.getCode'}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>

                        <div class="swiper-button-prev !hidden lg:!flex">
                            <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                        </div>
                        <div class="swiper-button-next !hidden lg:!flex z-40">
                            <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                        </div>

                        <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="hidden lg:block pointer-events-none absolute top-0 right-0 h-full w-[153px] z-30"></div>
                        <div class="swiper-pagination swiper-another-coupons absolute !top-[218px] -bottom-6 left-1/2 -translate-x-1/2 z-20 hidden lg:block"></div>
                    </div>
                </div>
            </div>
            {/cache}
        </div>
    </div>
</div>

<style>
    .swiper-button-prev.swiper-button-disabled {
        display: none !important;
    }
    .swiper-button-next::after,
    .swiper-button-prev::after {
        display: none !important;
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 80px;
        z-index: 40;
    }

    .swiper-button-next.swiper-button-disabled,
    .swiper-button-prev.swiper-button-disabled {
        display: none;
    }

    .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        top: 193px;
    }

    .swiper-pagination-bullet-active {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        width: 29px;
    }

    @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }
    .animate-marquee {
        animation: marquee var(--marquee-duration, 30s) linear infinite;
        will-change: transform;
    }

    @media only screen and (max-width: 768px) {
        .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
            top: 183px;
        }
    }

</style>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const desc = document.getElementById('shop-description');
        const toggle = document.getElementById('toggle-description');

        toggle.addEventListener('click', () => {
            desc.classList.remove('line-clamp-2');
            toggle.style.display = 'none';
        });

        function applyBlurToLastVisibleSlide() {
            const { slides, activeIndex, params, currentBreakpoint } = this;

            let perView = params.slidesPerView;
            if (params.breakpoints && params.breakpoints[currentBreakpoint]) {
                perView = params.breakpoints[currentBreakpoint].slidesPerView;
            }

            slides.forEach(slide =>
                slide.classList.remove(
                    'blur-[2px]',
                    'sm:blur-[2px]',
                    'md:blur-[2px]',
                    'lg:blur-[2px]'
                )
            );
            let targetIndex;
            let blurClass;

            if (perView >= 2.5) {
                targetIndex = activeIndex + 2;
                blurClass = 'lg:blur-[2px]';
            }

            if (targetIndex < slides.length) {
                slides[targetIndex].classList.add(blurClass);
            }
        }

        const swiper = new Swiper('.swiper.swiper-profile-shop.swiper-coupons', {
            direction: 'horizontal',
            loop: false,
            spaceBetween: 20,
            slidesPerView: 1.2,
            breakpoints: {
                768: {
                    slidesPerView: 1.8,
                },
                1024: {
                    slidesPerView: 1.8,
                },
                1280: {
                    slidesPerView: 2.6,
                }
            },
            pagination: {
                el: '.swiper-pagination',
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            on: {
                slideChange: applyBlurToLastVisibleSlide,
                resize: applyBlurToLastVisibleSlide,
                init: applyBlurToLastVisibleSlide
            }
        });

        const swiperOlderCoupons = new Swiper('.swiper.swiper-profile-shop.swiper-older-coupons', {
            direction: 'horizontal',
            loop: false,
            spaceBetween: 20,
            slidesPerView: 1.2,
            breakpoints: {
                768: {
                    slidesPerView: 1.8,
                },
                1024: {
                    slidesPerView: 1.8,
                },
                1280: {
                    slidesPerView: 2.6,
                }
            },
            pagination: {
                el: '.swiper-pagination',
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            on: {
                slideChange: applyBlurToLastVisibleSlide,
                resize: applyBlurToLastVisibleSlide,
                init: applyBlurToLastVisibleSlide
            }
        });

        const swiperAnotherCoupons = new Swiper('.swiper.swiper-profile-shop.swiper-another-coupons', {
            direction: 'horizontal',
            loop: false,
            spaceBetween: 20,
            slidesPerView: 1.2,
            breakpoints: {
                768: {
                    slidesPerView: 2.2,
                },
                1024: {
                    slidesPerView: 1.1,
                },
                1280: {
                    slidesPerView: 1.3,
                }
            },
            pagination: {
                el: '.swiper-pagination.swiper-another-coupons',
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });

        // Copy to clipboard
        const couponContainers = document.querySelectorAll('.coupon-code-copy');
        couponContainers.forEach(container => {
            const span = container.querySelector('span');
            const img = container.querySelector('img');
            if (!span || !img) return;

            const originalText = span.textContent;
            const originalSrc = img.src;
            const greenSrc = originalSrc.replace('coupon-profile.svg', 'coupon-profile-green.svg');

            container.addEventListener('click', function () {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(originalText);
                } else {
                    const textarea = document.createElement('textarea');
                    textarea.value = originalText;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                }

                span.textContent = 'Skopírované!';
                span.classList.remove('text-dark-1');
                span.classList.add('text-secondary-green');
                img.src = greenSrc;

                const svg = container.querySelector('.svg-copy-btn');
                const svgPaths = svg ? svg.querySelectorAll('path') : [];
                const originalStroke = '#80899C';
                const copiedStroke = '#66B940';
                svgPaths.forEach(path => path.setAttribute('stroke', copiedStroke));

                setTimeout(function () {
                    span.textContent = originalText;
                    span.classList.remove('text-secondary-green');
                    span.classList.add('text-dark-1');
                    img.src = originalSrc;
                    svgPaths.forEach(path => path.setAttribute('stroke', originalStroke));
                }, 1500);
            });
        });

        // Marquee effect
        document.querySelectorAll('.animate-marquee').forEach(function(track) {
            track.innerHTML += track.innerHTML;
            const items = Array.from(track.children).slice(0, track.children.length / 2);
            const totalWidth = items.reduce((acc, el) => acc + el.offsetWidth + 16, 0);
            track.style.minWidth = totalWidth * 2 + 'px';
        });
    });
</script>
