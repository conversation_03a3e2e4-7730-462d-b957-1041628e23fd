<?php

namespace tipli\NewFrontModule\ShopsModule\Presenters;

use Nette\Caching\Cache;
use tipli\FrontModule\Forms\ISignInControlFactory;
use tipli\FrontModule\Forms\SignInControl;
use tipli\Model\Doctrine\QueryObject\ResultSet;
use Nette\Application\UI\Multiplier;
use Nette\Http\Url;
use tipli\NewFrontModule\Components\AddonPromo\AddonPromo;
use tipli\NewFrontModule\Components\AddonPromo\AddonPromoFactory;
use tipli\NewFrontModule\Components\AqPopup;
use tipli\FrontModule\Components\IBannerCarouselControlFactory;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\FrontModule\Components\ISorterFactory;
use tipli\FrontModule\DealsModule\Components\IDealControlFactory;
use tipli\NewFrontModule\DealsModule\Components\DealDetailControl;
use tipli\NewFrontModule\DealsModule\Components\IDealDetailControlFactory;
use tipli\FrontModule\ShopsModule\Forms\AliexpressCashbackCheckControl;
use tipli\FrontModule\ShopsModule\Forms\IAliexpressCashbackCheckControlFactory;
use tipli\InvalidStateException;
use tipli\Model\Account\Entities\FavoriteShop;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\FavoriteShopFacade;
use tipli\Model\Algolia\AlgoliaFacade;
use tipli\Model\Articles\ArticlesRecommender;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\ParentReferencingLayer;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Marketing\BannerFacade;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Reviews\ReviewFacade;
use tipli\Model\Rewards\ShareRewardFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\NewShopsRecommender;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\ShopConditionsProvider;
use tipli\Model\Shops\ShopsRecommender;
use tipli\Model\Tags\TagFacade;
use tipli\NewFrontModule\Presenters\BasePresenter;

class ShopPresenter extends BasePresenter
{
	public const SORT_NEWEST = 'newest';
	public const SORT_ALPHABETICALLY = 'alphabetically';

	/** @var DealFacade @autowire */
	public $dealFacade;

	/** @var ReviewFacade @autowire */
	public $reviewFacade;

	/** @var ShopsRecommender @autowire */
	public $shopsRecommender;

	/** @var NewShopsRecommender @autowire */
	public $newShopsRecommender;

	/** @var ArticlesRecommender @autowire */
	public $articlesRecommender;

	/** @var  AlgoliaFacade @autowire */
	public $algoliaFacade;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var ISorterFactory @inject */
	public $sorterFactory;

	/** @var ParentReferencingLayer @inject */
	public $parentReferencingLayer;

	/** @var BannerFacade @autowire */
	public $bannerFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var LeafletFacade @autowire */
	public $leafletFacade;

	/** @var IDealControlFactory @autowire */
	public $dealControlFactory;

	/** @var IDealDetailControlFactory @autowire */
	public $dealDetailControlFactory;

	/** @var ShareRewardFacade @autowire */
	public $shareRewardFacade;

	/** @var FavoriteShopFacade @autowire */
	public $favoriteShopFacade;

	/** @var IBannerCarouselControlFactory @autowire */
	public $bannerControlFactory;

	/** @var IAliexpressCashbackCheckControlFactory @autowire */
	public $aliexpressCashbackCheckControlFactory;

	/** @var AddonPromoFactory @inject */
	public AddonPromoFactory $addonPromoFactory;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopConditionsProvider @inject */
	public $shopConditionsProvider;

	/** @var Shop */
	private $shop;

	private Cache $cache;

	/** @var ISignInControlFactory @inject */
	public $signInControlFactory;

	public function startup()
	{
		parent::startup();

		$this->cache = new Cache($this->storage, self::class);
	}

	public function actionSortedShops()
	{
		$query = $this->shopFacade->createShopsQuery($this->getLocalization())
			->onlyPublished()
			->optimized()
			->sortByFirstDescriptionAt()
			->sortByCreatedAt();

		// NON CASHBACK
		$nonCashbackShopsQuery = clone ($query);

		$nonCashbackShopsQuery->notIn([1998]);
		$nonCashbackShopsQuery->onlyWithCashbackDisabled();

		$this->template->nonCashbackShops = $this->shopFacade->fetch($nonCashbackShopsQuery)->applyPaging(0, 56);

		// CASHBACK
		$cashbackShopsQuery = clone ($query);

		$cashbackShopsQuery
			->onlyWithCashbackAllowed()
			->exceptPaused();

		$this->template->shops = $this->shopFacade->fetch($cashbackShopsQuery)->applyPaging(0, 56);
	}

	public function renderDefault(Shop $shop, $deepUrl = null, $shortcut = null, $fromAddon = null): void
	{
		if ($shop->isOnlyLeafletShop() === true && $this->leafletFacade->isShopIndexable($shop) === true) {
			$this->redirectPermanent(':NewFront:Leaflets:Leaflet:leaflets', ['shop' => $shop]);
		}

		/** @var null|User $user */
		$user = $this->getUserIdentity();

		if ((!$user || !$user->isAdmin()) && (!$shop->isPublished() || !$shop->isActive())) {
			$this->redirectPermanent(':NewFront:Shops:Shops:default');
		}

		if ($user && $shortcut) {
			$this->redirectPermanent(':NewFront:Shops:Redirection:shop', [
				'shop' => $shop,
				'deepUrl' => $deepUrl,
				'utm_source' => $this->getParameter('utm_source'),
				'utm_medium' => $this->getParameter('utm_medium'),
				'utm_campaign' => $this->getParameter('utm_campaign'),
			]);
		}

		$isAdmin = $user?->isAdmin();

		$this->shop = $shop;

		$this->template->userEntity = $user;
		$this->template->shop = $shop;
		$this->template->deepUrl = $deepUrl;
		$this->template->isAdmin = $isAdmin;

		$this->eventLayer->trackFacebookShopDetail($this->getUser()->isLoggedIn());

		$this->template->countOfShops = $this->shopFacade->findCountOfShops($this->getLocalization());

		$this->template->getLeaflets = (function () use ($shop) {
			$leafletsQuery = $this->leafletFacade->createLeafletsQuery()
				->withShop($shop)
				->onlyValidOrScheduled()
				->onlyVisible()
				->onlyConfirmed()
				->newestFirst();

			return $this->leafletFacade->fetch($leafletsQuery)->applyPaging(0, 4);
		});

		$this->template->unLoggedRedirectionUserId = $this->configuration->getUnLoggedRedirectionUser($this->getLocalization());

		$getNavigationTag = (function () use ($shop) {
			$parentTags = array_map(static function ($map) {
				return $map['entity'];
			}, $this->tagFacade->getNavigationTagsTree($shop->getLocalization()));

			foreach ($parentTags as $parentTag) {
				if ($shop->hasTag($parentTag)) {
					return $parentTag;
				}
			}

			return null;
		});

		$this->template->getNewShopConditions = function () use ($shop) {
			return $this->shopConditionsProvider->getNewConditionsForFrontend($shop);
		};

		$navigationTag = $getNavigationTag();
		$this->template->navigationTag = $navigationTag;

		$this->template->recommendedShops = (function () use ($shop, $navigationTag) {
			$relatedShops = $this->shopFacade->findRelatedShops($shop, true, 5);

			if ($relatedShops) {
				$shops = [];

				foreach ($relatedShops as $relatedShop) {
					$shops[] = $relatedShop->getRelatedShop();
				}

				return $shops;
			}

			if ($navigationTag === null) {
				return null;
			}

			bdump($relatedShops);
			return $this->shopFacade->findPublishedShopsByTag($navigationTag, true, 6, $shop);
		});

		$this->template->getOtherDeals = function () use ($navigationTag, $shop) {

			if ($navigationTag === null) {
				return null;
			}

			$salesTag = $this->tagFacade->findSalesTagByShopTag($navigationTag);

			if (!$salesTag) {
				return null;
			}

			$alternativeDealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops([$shop->getId(), Shop::WITHOUT_DEALS_SHOP_IDS])
				->withTag($salesTag)
				->onlyValid()
				->visibleOnWeb()
				->withType(Deal::TYPE_COUPON)
				->sortByCouponFirst()
				->sortByShopPriority();

			$dealIds = [];
			/** @var Deal $deal */
			foreach ($this->dealFacade->fetch($alternativeDealsQuery)->applyPaging(0, 30) as $deal) {
				$shopId = $deal->getShop()->getId();

				if (count($dealIds) >= 3) {
					break;
				}

				if (isset($dealIds[$shopId])) {
					continue;
				}

				$dealIds[$shopId] = $deal->getId();
			}

			$dealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
				->in($dealIds)
				->sortByExclusive()
				->sortByCouponFirst()
				->sortByShopPriority();

			$deals = $this->dealFacade->fetch($dealsQuery);

			return $deals->applyPaging(0, 3);
		};


		$this->template->alternativeNames = (function () {
			return $this->shop->getAlternativeNamesArray();
		});

		$this->template->countOfAllCoupons = (function () use ($shop) {
			return $this->dealFacade->findCountOfAllCoupons($shop->getLocalization());
		});

		$this->template->countOfAllCashbackShops = (function () use ($shop) {
			return $this->shopFacade->findCountOfShops($shop->getLocalization(), true);
		});

		$this->template->sidebarRecommendedShopsWithCoupons = (function () use ($shop, $navigationTag) {
			$shopsQuery = $this->shopFacade->createShopsQuery($shop->getLocalization())
				->onlyWithSomeCoupons()
				->exceptShop($shop)
				->sortBoosted()
				->sortTop()
				->onlyPublished();

			if ($navigationTag) {
				$shopsQuery->withTag($navigationTag);
			}

			return $this->shopFacade->fetch($shopsQuery)
				->applyPaging(0, 12);
		});

		$this->template->getForeignShops = (function () {
			return $this->shopFacade->findForeignShops($this->shop);
		});

		$this->template->cashbackDeal = $this->dealFacade->findShopCashbackDeal($this->shop);

		$this->template->recommendedArticles = (function () use ($shop) {
			return $this->articlesRecommender->getRecommendedArticles($this->getLocalization(), $shop, 3);
		});

		$this->template->getDescriptionBlock = (static function ($type) use ($shop) {
			return $shop->getDescriptionBlock($type);
		});

		$this->template->descriptionBlock = (static function ($type) use ($shop) {
			$data = $shop->getDescriptionBlocks($type);

			if ($type === 'long_description' && isset($data[0])) {
				$data[0]->setDescription(ContentFilter::replaceIframe($data[0]->getDescription()));
			}

			return $data;
		});

		$this->template->banners = $this->bannerFacade->findValidBanners($this->localizationFacade->getCurrentLocalization(), 6, Banner::FORMAT_HOMEPAGE2, null, Banner::SIZE_FULL, null, $this->getUserIdentity(), $shop);

		$this->template->getDeals = function (bool $exceptCoupons = false) use ($shop, $user) {

			if ($shop->isShowDeals() === false || $shop->hasDisabledDeals()) {
				return null;
			}

			if ($shop->getCountOfDeals() > 0) {
				$dealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
					->withShop($shop)
					->onlyValid()
					->visibleOnWeb()
					->sortByExclusive()
					->sortByDealTypePriority($user);

				// vyjimka pro HU a neprihlasene uzivatele
				if ($this->getLocalization()->getLocale() === 'hu' && $this->getUser()->isLoggedIn() === false) {
					$dealsQuery
						->withoutTypes([Deal::TYPE_CASHBACK, Deal::TYPE_PRODUCT]);
				} else {
					$dealsQuery
						->withoutTypes([Deal::TYPE_CASHBACK, Deal::TYPE_PRODUCT]);
				}

				$dealsQuery
					->sortHighest()
					->sortTop();
			} elseif (!$shop->isCashbackAllowed()) {
				/** @var ResultSet $recommendedShops */
				$recommendedShops = $this->shopsRecommender->getRecommendedShops($this->getLocalization(), $shop, 6);

				$alternativeDealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
					->withShops($recommendedShops->toArray())
					->onlyValid()
					->visibleOnWeb()
					->withType(Deal::TYPE_COUPON)
					->sortByCouponFirst()
					->sortByShopPriority();

				$dealIds = [];
				/** @var Deal $deal */
				foreach ($this->dealFacade->fetch($alternativeDealsQuery)->applyPaging(0, 15) as $deal) {
					$shopId = $deal->getShop()->getId();

					if (count($dealIds) >= 3) {
						break;
					}

					if (isset($dealIds[$shopId])) {
						continue;
					}

					$dealIds[$shopId] = $deal->getId();
				}

				$dealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
					->withoutType(Deal::TYPE_CASHBACK)
					->in($dealIds)
					->sortByExclusive()
					->sortByCouponFirst()
					->sortByShopPriority();
			}

			if (!isset($dealsQuery)) {
				return null;
			}

			if ($exceptCoupons) {
				$dealsQuery->withoutType(Deal::TYPE_COUPON);
			}

			$deals = $this->dealFacade->fetch($dealsQuery);

			$this['paginatorDeals']->setItemsCount($deals->getTotalCount());
			$deals->applyPaginator($this['paginatorDeals']->getPaginator());

			return $deals;
		};

		$this->template->getCoupons = (function () use ($shop) {
			$dealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
				->withShop($shop)
				->onlyValid()
				->visibleOnWeb()
				->withType(Deal::TYPE_COUPON)
				->sortTop();

			return $this->dealFacade->fetch($dealsQuery);
		});

		$this->template->topDeal = (function () use ($shop) {
			// fashion RO
			if ($shop->hasDisabledDeals()) {
				return null;
			}

			return $this->dealFacade->findTopPriorityDealByShop($shop);
		});

		$coefficients = [
			Deal::UNIT_CZK => 20,
			Deal::UNIT_EUR => 0.8,
			Deal::UNIT_USD => 0.8,
			Deal::UNIT_PLN => 3,
			Deal::UNIT_HUF => 320,
			Deal::UNIT_RON => 4,
			Deal::UNIT_BGN => 1.5,
		];

		$topOfferRelative = $this->dealFacade->findTopValueDealByShop($shop, [Deal::TYPE_COUPON, Deal::TYPE_SALE], [Deal::UNIT_PERCENTAGE]);
		$topOfferAbsolute = $this->dealFacade->findTopValueDealByShop($shop, [Deal::TYPE_COUPON, Deal::TYPE_SALE], [Deal::UNIT_CZK, Deal::UNIT_EUR, Deal::UNIT_USD, Deal::UNIT_PLN, Deal::UNIT_HUF, Deal::UNIT_RON, Deal::UNIT_BGN]);

		if ($shop->hasDisabledDeals()) {
			$topOfferRelative = null;
			$topOfferAbsolute = null;
		}

		$coefficient = 1;

		if ($topOfferAbsolute !== null && isset($coefficients[$topOfferAbsolute->getUnit()])) {
			$coefficient = $coefficients[$topOfferAbsolute->getUnit()];
		}

		$this->template->topOfferRelative = $topOfferRelative;
		$this->template->topOfferAbsolute = $topOfferAbsolute;
		$this->template->coefficient = $coefficient;

		$this->template->reviews = $this->reviewFacade->findReviewsWithShop($shop, 4)
			->applyPaging(0, 3);

		$this->template->getAverageShopReview = function () use ($shop) {
			return $this->reviewFacade->findAverageShopReview($shop);
		};

		$this->template->getCountOfTotalReviews = (function () use ($shop) {
			return $this->reviewFacade->findApprovedShopReviewsCount($shop);
		});

		$this->template->shopOffers = (function () use ($shop) {
			return $this->offerFacade->resolveOffersForShop($shop);
		});

		$this->template->hasShopAllowedDeals = $shop->getId() !== 3085;
		$this->template->isLessDetailed = $shop->isLessDetailed();

		$this->template->getExpiredCoupons = (function ($limit = 5) use ($shop) {
			$dealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops(Shop::WITHOUT_DEALS_SHOP_IDS)
				->withShop($shop)
				->withTypes([Deal::TYPE_COUPON, Deal::TYPE_SALE, Deal::TYPE_TIP, Deal::TYPE_FREE_SHIPPING])
				->onlyExpired()
				->sortNewest();

			return $this->dealFacade->fetch($dealsQuery)->applyPaging(0, $limit);
		});

		if ($this->getUserIdentity()) {
			$this->template->isShopFavorite = $this->favoriteShopFacade->isShopFavorite($this->getUserIdentity(), $shop);

			if ($shop->isCashbackActive()) {
				$referralLink = new Url($this->link('//:NewFront:Shops:Redirection:shop', ['shop' => $shop]));
				$referralLink->setQueryParameter('userId', $this->getUserIdentity()->getId());
				$referralLink->setQueryParameter('ref', 'friend');
				$this->template->userReferralLink = $referralLink->getAbsoluteUrl();
			}
		}

		// constants
		$cashbackCacheKeyPostfix = 's' . $shop->getId() . '_';
		$cashbackCacheKey = $this->getCashbackCacheKey();

		$this->template->cashbackCacheDisabled = $cashbackCacheKey === null;
		$this->template->cashbackCacheKeyPostfix = $cashbackCacheKeyPostfix . $cashbackCacheKey;

		$this->template->currentDealsPageNumber = $this['paginatorDeals']->getPaginator()->getPage();
		$this->template->canUserAddReview = $user ? $this->reviewFacade->canUserAddReview($this->getUserIdentity(), $shop) : false;

		$this->template->isSafariMobile = $this->clientLayer->isSafari() && $this->clientLayer->isMobile();

		if ($user) {
			$this->template->userStatus = 'logged-' . ($user->isActiveUser() ? 'active' : 'inactive');
		} else {
			$this->template->userStatus = 'unlogged-' . ($this->utmLayer->isUtmSourcePaid() || $this->parentReferencingLayer->getParentUser() ? 'paid' : 'default');
		}

		if ($deepUrl && ($aqPopupControl = $this->getComponent('aqPopup'))) {
			$aqPopupControl->setDeepUrl($deepUrl);
		}

		$this->template->variant = '';

		$this->template->getShopConditions = function () use ($shop) {
			return $this->shopConditionsProvider->getConditionsForFrontend($shop);
		};

		$this->template->refundLink = $this->link(':NewFront:Account:Refund:default');

		$this->listenOpenDeal('dealDetailControl');

		$this->template->popup = null;
		$this->template->isLoginFromAddonPopupAllowed = false;

		if ($shop->isCashbackAllowed() && $this->getParameter('fromAddon') && $this->getUser()->isLoggedIn() === false) {
			$this->template->isLoginFromAddonPopupAllowed = true;
		} else {
			$this->template->popup = $this->resolvePopup($shop, $user);
		}

		if ($this->localizationFacade->getCurrentLocalization()->isSlovak()) {
			$this->template->promoRegistrationVariant = $this->googleExperimentVariantResolver->getExperimentVariant('sk-002', 2)->getVariant();
		}

//		if ($this->localizationFacade->getCurrentLocalization()->isCzech()) {
//			$this->template->popupPromoRegistrationVariant = $this->googleExperimentVariantResolver->getExperimentVariant('cz-004', 2)->getVariant();
//		}

		$this->template->backLink = $this->link(':NewFront:Shops:Redirection:shop', $shop);

		if ($shop->isGamble()) {
			$this->setView('gambleShop');
		}

		if ($this->getHttpRequest()->getCookie('newShopDetail') && $this->getHttpRequest()->getCookie('newShopDetail') === 'q7wV2ZiZX2omBPf') {
			$this->setView('new/shop');
		}
	}

	public function renderSearch($id)
	{
		$this->redirect('shops', ['searchQuery' => $id]);
	}

	public function handleAddToFavourites(Shop $shop)
	{
		if ($user = $this->getUserIdentity()) {
			if (!$this->favoriteShopFacade->isShopFavorite($user, $shop)) {
				$this->favoriteShopFacade->markShopAsFavorite($user, $shop, FavoriteShop::SOURCE_USER, FavoriteShop::REASON_FAVORITE);
			}

			$this->redrawControl('favouriteShop');
		}
	}

	public function handleRemoveFromFavourites(Shop $shop)
	{
		if ($user = $this->getUserIdentity()) {
			if ($this->favoriteShopFacade->isShopFavorite($user, $shop)) {
				$this->favoriteShopFacade->unMarkShopAsFavorite($user, $shop, FavoriteShop::SOURCE_USER, FavoriteShop::REASON_FAVORITE);
			}

			$this->redrawControl('favouriteShop');
		}
	}

	public function createComponentPaginatorDeals()
	{
		$control = $this->paginatorFactory->create()
			->enableNext();

		$control->setItemsPerPage(50);

		$control->onChange[] = function () {
			$this->redrawControl('deals');
		};

		return $control;
	}

	public function createComponentPaginatorReviews()
	{
		$control = $this->paginatorFactory->create();

		$control->setItemsPerPage(3);
		$control->disablePages();
		$control->onChange[] = function () {
			$this->redrawControl('reviews');
		};

		return $control;
	}

	public function createComponentAliexpressCashbackCheckControl(): AliexpressCashbackCheckControl
	{
		/** @var Shop $shop */
		$shop = $this->getParameter('shop');

		if (!$shop->isAliexpress()) {
			throw new InvalidStateException('Trying to create aliexpress cashback-check form on non-aliexpress shop!');
		}

		$control = $this->aliexpressCashbackCheckControlFactory->create($shop, $this->getUserIdentity());

		$control->onSuccess[] = function () {
			$this->template->aliexpressCashbackCheckResult = true;
		};

		$control->onFailure[] = function () {
			$this->template->aliexpressCashbackCheckResult = false;
		};

		return $control;
	}

	public function createComponentSorter() // @todo trait
	{
		$control = $this->sorterFactory->create();

		$control->setDefaultSort(null);
		$control->setSorts([
			null => $this->translator->translate('front.shops.shop.shopsList.sort.favourites'),
			self::SORT_NEWEST => $this->translator->translate('front.shops.shop.shopsList.sort.newest'),
		]);

		$control->setNewTemplate();

		$control->onSort[] = function () {
			$this->redrawControl('shopSorter');
		};

		return $control;
	}

	public function createComponentNonCashbackShopSorter()
	{
		$control = $this->sorterFactory->create();

		$control->setDefaultSort(null);
		$control->setSorts([
			null => $this->translator->translate('front.shops.shop.shopsList.sort.favourites'),
			self::SORT_NEWEST => $this->translator->translate('front.shops.shop.shopsList.sort.newest'),
		]);

		$control->setNewTemplate();

		$control->onSort[] = function () {
			$this->redrawControl('nonCashbackShopSorter');
		};

		return $control;
	}

	protected function createComponentSideSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();
//		$control->setSignInUrl($this->link(':NewFront:Sign:in'));

		$control->onSuccess[] = function ($user) {
//			$this->flashMessage($this->translator->translate('front.emailSignUp.successMessage'));

//            $this->redirect('this');
			$this->afterSignUp();
		};

		return $control;
	}

	protected function createComponentDealControl()
	{
		return new Multiplier(function ($dealId) {
			$deal = $this->dealFacade->find($dealId);

			return $this->dealControlFactory->create($deal);
		});
	}

	protected function createComponentDealDetailControl(): DealDetailControl
	{
		return $this->dealDetailControlFactory->create();
	}

	public function createComponentBannerControl()
	{
		return $this->bannerControlFactory->create(
			6,
			Banner::FORMAT_HOMEPAGE2,
			null,
			Banner::SIZE_FULL,
			null,
			$this->getUserIdentity(),
			$this->shop,
			null,
			null
		);
	}

	private function resolvePopup(Shop $shop, ?User $user = null): ?string
	{
		if ($this->isWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if ($this->isMobileWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if ($this->isWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if (!$user && !$this->getHttpRequest()->getCookie(AqPopup::COOKIE_SHOP_UNLOGGED_USER_POPUP_PREFIX . '-' . $shop->getId())) {
			return AqPopup::TYPE_SHOP_REDIRECTION;
		}

		if (!$this->isPromoPopupAllowed()) {
			return null;
		}

		if ($this->isAddonPopupAllowed($user)) {
			return AqPopup::TYPE_ADDON;
		}

		if ($user && !$user->isActiveUser()) {
			return AqPopup::TYPE_EDUCATION;
		}

		return null;
	}

	private function isWarningPopupRequired(Shop $shop): bool
	{
		if ($this->clientLayer->isMobile()) {
			return false;
		}

		$httpRequest = $this->getHttpRequest();
		$warningPopupCookieName = AqPopup::COOKIE_SHOP_WARNING_POPUP_PREFIX . '-' . $shop->getId();

		return $shop->getWarningMessage() && (!$httpRequest->getCookie($warningPopupCookieName) || $httpRequest->getCookie($warningPopupCookieName) !== $shop->getWarningMessageHash());
	}

	private function isMobileWarningPopupRequired(Shop $shop): bool
	{
		if (!$this->clientLayer->isMobile()) {
			return false;
		}

		$httpRequest = $this->getHttpRequest();
		$warningPopupCookieName = AqPopup::COOKIE_SHOP_WARNING_POPUP_PREFIX . '-' . $shop->getId();

		return $shop->getMobileWarningMessage() && (!$httpRequest->getCookie($warningPopupCookieName) || $httpRequest->getCookie($warningPopupCookieName) !== $shop->getMobileWarningMessageHash());
	}

	private function isPromoPopupAllowed(): bool
	{
		$httpRequest = $this->getHttpRequest();

		return !$httpRequest->getCookie(AqPopup::COOKIE_SHOP_PROMO_POPUP_OPENED);
	}

	private function isAddonPopupAllowed(?User $user = null): bool
	{
		if ($this->configuration->isThereAddon($this->getLocalization()) === false) {
			return false;
		}

		return $user && !$user->hasInstalledAddon()
			&& $user->isOlderThanThreeDays()
			&& !$this->clientLayer->isMobile()
			&& ($this->clientLayer->isChrome() || $this->clientLayer->isFirefox() || $this->clientLayer->isSafari())
		;
	}

	protected function createComponentEmailSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function () {
			$this->afterSignUp();
		};

		$control->onFailure[] = function ($form) {
			$this->redrawControl('registrationForm');
			$this->redrawControl('errors');
			$this->redrawControl('signUp');
		};

		return $control;
	}

	public function createComponentAddonPromo(): AddonPromo
	{
		return $this->addonPromoFactory->create($this->getLocalization());
	}

	protected function createComponentSignInControl(): SignInControl
	{
		$shop = $this->getParameter('shop');

		$control = $this->signInControlFactory->create($this->getParameter('email') ?? '');
		$control->onSuccess[] = function () use ($shop) {
			$this->redirect(':NewFront:Shops:Redirection:shop', ['shop' => $shop]);
		};

		$control->onError[] = function ($errorMessage) {
			$this->flashMessage($errorMessage);

			$this->redirect('this');
		};

		return $control;
	}
}
