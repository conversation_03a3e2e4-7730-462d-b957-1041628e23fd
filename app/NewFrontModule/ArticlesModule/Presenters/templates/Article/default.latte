{layout '../@defaultLayout.latte'}

{block title}{$article->getName()}{/block}

{block description}{$article->getDescription() |stripHtml}{/block}

{block image}{$article->getPreviewImage() |image:1200,600}{/block}

{block #styles}
    {control cssBuilderControl ['plugins/font-awesome/css/font-awesome.min.css']}
    {control cssBuilderControl ['js/swipebox/src/css/swipebox.min.css']}

    {control cssBuilderControl ['css/article/main.article.css']}
    {control cssBuilderControl ['css/articles/main.articles.css']}
{/block}

{block #scripts}

	<script src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" defer></script>
	<script>
		(adsbygoogle = window.adsbygoogle || []).push({
			google_ad_client: "ca-pub-3515231881192503",
				enable_page_level_ads: true
		});
	</script>

	<script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>




    {capture $publisherLogo}{$baseUrl}/images/logo-1.png{/capture}
    {capture $articleImage}{$baseUrl}{$article->getPreviewImage() |image:850,0}{/capture}

    <!-- Article schema -->
    <script type="application/ld+json">
        {
            "@context": "http://schema.org",
            "@type": "Article",
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": {$baseUrl}
        },
        "headline": {$article->getName()},
        "url": {link //this},
        "author": {
            "@type": "Person",
            "name": "Tipli"
        },
        "datePublished": {$article->getPublishedAt() |date:'c'},
        "dateModified": {$article->getPublishedAt() |date:'c'},
        "publisher": {
            "@type": "Organization",
            "name": "Tipli",
            "logo": {
                "@type": "ImageObject",
                "width": "160",
                "height": "78",
                "url": {$publisherLogo}
        }
    },
    "image": {
        "@type": "ImageObject",
        "width": "850",
        "height": "470",
        "url": {$articleImage}
        }
    }
    </script>
{/block}

{block content}

<div class="container pb-10 md:pb-36">
		<div class="max-w-[770px] w-full mx-auto">
			<div class="inline-flex items-center gap-[15px] mt-5 md:pt-10 mb-5">
				<div class="inline-flex text-dark-2 text-sm leading-[24.5px] py-[3px] px-3 rounded-lg bg-light-6" n:if="$article->getFirstTag()">
					<a n:href=":NewFront:Articles:Articles:default, 'tag' => $article->getFirstTag()">
						{$article->getFirstTag()->getName()}
					</a>
				</div>
				<div class="text-dark-3 text-sm leading-[24.5px]">{$article->getPublishedAt() |localDate}</div>
			</div>
		</div>
        <h1 class="mx-auto text-dark-1 text-2xl md:text-[55px] md:leading-[72px] max-w-[770px] w-full mb-5 md:mb-10">
            {$article->getName()}
        </h1>

		<div class="content">

			<div class="text-dark-1 text-[20px] leading-[35px] max-w-[770px] w-full mb-[58px]">
				{$article->getDescription() |noescape}
			</div>
{*			<div class="mb-[60px]">*}
{*				<img class="rounded-2xl max-h-[802px] w-full cover" src=" {$article->getPreviewImage() |image:1200,600}" loading="lazy" alt="{$article->getName()}">*}
{*			</div>*}

			<div class="w-full max-w-[770px] m-auto">
				{var $adScript = null}
				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					{var $adScript = '<span class="article-ads__content flex justify-between"><ins class="adsbygoogle" style="display:inline-block;width:300px;height:250px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="3083807738"></ins><ins class="adsbygoogle" style="display:inline-block;width:300px;height:250px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="4931123948"></ins></span>'}
				{/if}

				{if $anchorLinks = $article->getAnchorLinks()}
					{$anchorLinks |noescape}
				{/if}


				{$article->getContent() |content:'html',$adScript,true |noescape}

				{*
				<div>
					<div class="text-sm text-dark-2 leading-[24.5px] mb-2.5">Zdieľaj tento článok</div>
					<div class="inline-flex items-center gap-[40px] md:gap-[60px] py-[18px] md:py-[36px] px-[44px] bg-light-6 rounded-2xl">
						<svg width="14" height="26" viewBox="0 0 14 26" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M12.9597 14.3011L13.6791 9.69989H9.18224V6.71398C9.18224 5.45542 9.8102 4.22784 11.8244 4.22784H13.8685V0.310729C13.8685 0.310729 12.0137 6.10352e-05 10.2399 6.10352e-05C6.5369 6.10352e-05 4.11651 2.20413 4.11651 6.19355V9.70068H0V14.3019H4.11651V25.4256H9.18224V14.3019L12.9597 14.3011Z" fill="#080B10"/>
						</svg>
						<svg width="28" height="23" viewBox="0 0 28 23" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M24.6198 5.87392C24.6363 6.11634 24.6363 6.35874 24.6363 6.60339C24.6363 14.0579 19.0709 22.6551 8.89455 22.6551V22.6507C5.88842 22.6551 2.94473 21.7771 0.414062 20.1216C0.851178 20.1752 1.29048 20.202 1.73089 20.2031C4.22212 20.2053 6.64213 19.3529 8.60204 17.7835C6.23461 17.7376 4.15858 16.1637 3.43333 13.8657C4.26265 14.0288 5.11717 13.9953 5.93115 13.7686C3.35008 13.2368 1.49316 10.9244 1.49316 8.23885C1.49316 8.21427 1.49316 8.19081 1.49316 8.16736C2.26222 8.60415 3.1233 8.84656 4.00411 8.87336C1.57313 7.21669 0.823789 3.91898 2.2918 1.34068C5.10073 4.86517 9.24511 7.00779 13.6941 7.23457C13.2482 5.27515 13.8573 3.2219 15.2946 1.84449C17.523 -0.291423 21.0275 -0.181947 23.1221 2.08914C24.3612 1.84003 25.5488 1.37643 26.6355 0.719563C26.2226 2.02548 25.3581 3.13477 24.2034 3.83966C25.3 3.70783 26.3715 3.40846 27.3805 2.95155C26.6377 4.08654 25.7022 5.07518 24.6198 5.87392Z" fill="#080B10"/>
						</svg>
						<svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M22.3417 22.3436H17.7154V15.0861C17.7154 13.3555 17.6845 11.1276 15.3093 11.1276C12.8999 11.1276 12.5312 13.0132 12.5312 14.9601V22.3432H7.90499V7.41874H12.3461V9.45832H12.4083C12.8528 8.69706 13.495 8.0708 14.2667 7.64628C15.0383 7.22173 15.9104 7.0148 16.7902 7.04752C21.479 7.04752 22.3436 10.1371 22.3436 14.1563L22.3417 22.3436ZM2.68512 5.37868C2.15415 5.37879 1.63505 5.22116 1.19352 4.92571C0.751967 4.63028 0.407826 4.21034 0.204538 3.71895C0.00127172 3.22757 -0.0520045 2.68685 0.0514869 2.16515C0.154978 1.64346 0.410581 1.16421 0.785983 0.788039C1.16136 0.411847 1.63967 0.155643 2.16043 0.0517732C2.68118 -0.0520967 3.22096 0.0010754 3.71155 0.204523C4.20214 0.407992 4.62148 0.752605 4.91655 1.19482C5.21163 1.63702 5.36918 2.15696 5.36927 2.68886C5.36934 3.04203 5.29995 3.39175 5.16509 3.71805C5.03023 4.04438 4.83255 4.34088 4.58331 4.59064C4.33407 4.84043 4.03814 5.03858 3.71243 5.17379C3.38674 5.309 3.03768 5.37861 2.68512 5.37868ZM4.9982 22.3436H0.367208V7.41874H4.9982V22.3436Z" fill="#080B10"/>
						</svg>
					</div>
				</div>
				*}
			</div>
		</div>
    </div>
{/block}
