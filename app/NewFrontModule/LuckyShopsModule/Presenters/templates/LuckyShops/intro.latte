{block title}{_newFront.luckyShops.meta.title}{/block}
{block metaDescription}{_newFront.luckyShops.meta.description}{/block}
{block image}{_newFront.luckyShops.meta.image}{/block}

{block head}
    {if $localization->isSlovak()}
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
                crossorigin="anonymous"></script>
    {/if}
{/block}

{block content}
<div class="relative bg-primary-blue-dark min-h-screen lg:overflow-x-hidden">
    <div class="container relative p-0">
        <div class="hidden lg:block absolute top-0 left-[-160px]" >
            <img src="{$basePath}/new-design/gives-out-bg-star.svg" alt="star">
        </div>

        <div class="hidden lg:block absolute top-[-50px] right-[-500px]" >
            <img class="blur-3xl" src="{$basePath}/new-design/gives-out-bg-circle.png" alt="circle">
        </div>

        <div class="absolute right-0 sm:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="93" height="430" viewBox="0 0 93 430" fill="none">
                <g filter="url(#filter0_f_1182_3874)">
                    <path d="M244.582 57.2222C331.135 73.9053 387.765 158.082 370.989 245.118C354.213 332.153 270.352 389.25 183.799 372.567C97.2459 355.884 40.6154 271.707 57.3914 184.671C74.1675 97.6363 158.029 40.5391 244.582 57.2222Z" stroke="white" stroke-width="5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <defs>
                    <filter id="filter0_f_1182_3874" x="0.547264" y="0.42617" width="427.286" height="428.937" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                        <feGaussianBlur stdDeviation="25.7" result="effect1_foregroundBlur_1182_3874"/>
                    </filter>
                </defs>
            </svg>
        </div>

        <div class="relative z-20 flex flex-col lg:flex-row items-center justify-center lg:gap-[106px]">
            <div class="hidden lg:block">
                <div class="flex items-center text-white lg:text-primary-orange text-[26px] lg:text-[45px] font-bold leading-[60px] text-nowrap mt-4 lg:mt-0">
                    {_newFront.luckyShops.title}
                    <div class="relative mx-2 lg:ml-4 lg:mr-0">
                        <img class="hidden lg:block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-md.svg" alt="donkey">
                        <img class="lg:hidden absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
						<div class="relative text-center font-bold text-white">
							<span class="text-[26px] lg:text-[45px] text-white text-nowrap leading-[67px] rounded-[13px] bg-orange-gradient px-2 py-1 lg:px-[17px]">
								{_newFront.luckyShops.amount}
							</span>
						</div>
                    </div>
                </div>
                <div class="text-white text-[26px] lg:text-[45px] font-bold text-nowrap mb-2">
                    {_newFront.luckyShops.everyDay}
                </div>

                <div class="w-full max-w-[390px] text-white text-sm lg:text-base leading-[24.5px] lg:leading-[28px]">
                    {_newFront.luckyShops.text}
                </div>

                <div class="mt-[15px] mb-[22px] lg:mt-5 lg:mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="91" height="7" viewBox="0 0 91 7" fill="none">
                        <g opacity="0.2">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M38.2358 0.333673C37.9414 0.307544 37.6423 0.322802 37.3628 0.378193C37.0833 0.433585 36.8312 0.527566 36.6271 0.652498L30.8651 4.18154L24.7559 0.706279C24.3909 0.49931 23.9061 0.384348 23.4069 0.386399C22.9077 0.388452 22.4345 0.507369 22.0904 0.717253L16.3273 4.24537L10.2202 0.770597C9.85577 0.563033 9.37069 0.447655 8.87116 0.449708C8.37162 0.451762 7.89832 0.571087 7.55481 0.781561L0.501375 5.10205C0.163937 5.31398 -0.0158436 5.59819 0.00109731 5.89297C0.0180383 6.18775 0.230331 6.46931 0.59186 6.67657C0.953389 6.88382 1.43499 7.00005 1.93216 7C2.42933 6.99995 2.90193 6.88362 3.2474 6.67629L9.00846 3.14717L15.1178 6.62215C15.4828 6.82906 15.9677 6.94397 16.4668 6.94192C16.966 6.93987 17.4391 6.82101 17.7832 6.61119L23.5455 3.083L29.6548 6.55824C30.0199 6.76497 30.5046 6.87974 31.0037 6.87763C31.5027 6.87552 31.9757 6.75669 32.3198 6.54697L38.0803 3.01835L44.1896 6.49331C44.3697 6.59841 44.5823 6.68174 44.8152 6.73848C45.0481 6.79522 45.2966 6.82425 45.5464 6.82387C45.7962 6.82349 46.0422 6.7937 46.2703 6.73625C46.4983 6.6788 46.7038 6.59483 46.8748 6.4892C47.0458 6.38356 47.179 6.25837 47.2666 6.12088C47.3542 5.9834 47.3945 5.83635 47.3851 5.68826C47.3757 5.54016 47.3169 5.39399 47.212 5.25818C47.1071 5.12238 46.9582 4.99966 46.774 4.89714L39.2926 0.641538C39.0017 0.475853 38.6317 0.368038 38.2358 0.333673Z" fill="white"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M81.8494 0.0124119C81.555 -0.0137174 81.2559 0.00154108 80.9764 0.0569323C80.6969 0.112324 80.4448 0.206304 80.2407 0.331237L74.4788 3.86027L68.3695 0.385018C68.0045 0.178049 67.5197 0.0630866 67.0205 0.0651383C66.5213 0.0671905 66.0481 0.186108 65.704 0.395992L59.9409 3.92411L53.8338 0.449336C53.4694 0.241772 52.9843 0.126394 52.4848 0.128447C51.9852 0.1305 51.5119 0.249826 51.1684 0.4603L44.115 4.78079C43.7775 4.99272 43.5978 5.27693 43.6147 5.57171C43.6316 5.86648 43.8439 6.14805 44.2055 6.3553C44.567 6.56256 45.0486 6.67879 45.5458 6.67874C46.0429 6.67869 46.5155 6.56236 46.861 6.35503L52.6221 2.82591L58.7314 6.30089C59.0964 6.5078 59.5813 6.62271 60.0804 6.62066C60.5796 6.61861 61.0527 6.49975 61.3969 6.28993L67.1591 2.76174L73.2684 6.23698C73.6335 6.44371 74.1182 6.55848 74.6173 6.55637C75.1163 6.55426 75.5893 6.43543 75.9334 6.2257L81.6939 2.69709L87.8032 6.17205C87.9833 6.27715 88.1959 6.36048 88.4288 6.41722C88.6617 6.47396 88.9103 6.50299 89.16 6.50261C89.4098 6.50223 89.6559 6.47243 89.8839 6.41499C90.1119 6.35754 90.3174 6.27357 90.4884 6.16794C90.6594 6.0623 90.7926 5.93711 90.8802 5.79962C90.9678 5.66214 91.0081 5.51509 90.9987 5.36699C90.9893 5.2189 90.9305 5.07273 90.8256 4.93692C90.7207 4.80112 90.5718 4.6784 90.3876 4.57587L82.9062 0.320277C82.6153 0.154592 82.2453 0.0467765 81.8494 0.0124119Z" fill="white"/>
                        </g>
                    </svg>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-0 w-full max-w-[450px]">
                    <div class="flex items-center gap-[13px] text-white text-base font-bold leading-7">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4134)"/>
                            <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="paint0_linear_1182_4134" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#EF7F1A"/>
                                    <stop offset="1" stop-color="#FFA439"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        {_newFront.luckyShops.info.first}
                    </div>

                    <div class="flex items-center gap-[13px] text-white text-base leading-7">
                        <svg class="flex-shrink-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
														 viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4134)"/>
                            <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="paint0_linear_1182_4134" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#EF7F1A"/>
                                    <stop offset="1" stop-color="#FFA439"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        {_newFront.luckyShops.info.second}
                    </div>

                    <div
											class="flex items-center gap-[13px] text-white text-base leading-7 lg:col-span-2 lg:mt-[14px]">
                        <svg class="flex-shrink-0 self-start" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4134)"/>
                            <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                            <defs>
                                <linearGradient id="paint0_linear_1182_4134" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#EF7F1A"/>
                                    <stop offset="1" stop-color="#FFA439"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        {_newFront.luckyShops.info.third}
                    </div>
                </div>
            </div>

            <div class="lg:hidden pt-10 px-10 pb-[30px] text-white text-lg leading-[31.px]">
                {_newFront.luckyShops.chooseShop |noescape}
                {_newFront.luckyShops.get} <span class="text-white text-nowrap rounded-lg font-bold bg-orange-gradient px-2 py-1 lg:px-[17px]">{_newFront.luckyShops.amount}</span>
                {_newFront.luckyShops.everyDay}
            </div>

            <div class="relative z-20">
                <div class="lg:bg-white/10 lg:backdrop-blur-[11px] text-white w-full min-w-[415px] rounded-2xl shadow-custom relative lg:mt-[79px] px-10 lg:py-[50px] lg:px-[50px]">
                    <img class="hidden lg:block absolute right-[-122px] bottom-[-7px]" src="{$basePath}/new-design/hp-register-donkey.png" alt="donkey">

  					<div class="hidden md:block text-center">
						<h2 class="text-lg font-normal leading-loose mb-6">{_newFront.luckyShops.intro.title}
                            <strong class="text-lg font-bold leading-loose">{_newFront.luckyShops.intro.title2}</strong>
						</h2>
					</div>

                    {control luckyShopPickerControl}
                </div>
            </div>
        </div>
    </div>

	{control luckyShopPickerModalControl}

    <div class="hidden lg:block relative z-10 text-[#2F67C2] ml-[-500px] mt-[-90px] opacity-10 text-[160px] leading-[178px] font-bold text-nowrap">
        {_newFront.luckyShops.bottomLargeText}
    </div>

    <div id="mobile-intro-screen" n:if="$isRegisteredFromLuckyShops === null" class="absolute w-full md:hidden top-0 left-0 bg-primary-blue-dark min-h-screen z-20">
        <div class="px-10 mt-6">
            <div class="flex items-center text-white text-2xl font-bold leading-[60px] text-nowrap">
                {_newFront.luckyShops.title}
                <div class="relative mx-2 lg:mr-0">
                    <img class="lg:hidden absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
                    <div class="relative text-center font-bold text-white">
						<span class="text-[26px] text-white text-nowrap leading-[67px] rounded-[13px] bg-orange-gradient px-2 py-1">
							{_newFront.luckyShops.amount}
						</span>
                    </div>
                </div>
            </div>
            <div class="relative top-[-8px] text-white text-2xl font-bold text-nowrap mb-2.5">{_newFront.luckyShops.everyDay}</div>
            <div class="w-full max-w-[371px] text-white text-sm  leading-normal mt-2.5 mb-4">
                {_newFront.luckyShops.text}
            </div>
            <div class="mb-[22px]">
                <svg xmlns="http://www.w3.org/2000/svg" width="70" height="7" viewBox="0 0 91 7" fill="none">
                    <g opacity="0.2">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M38.2358 0.333673C37.9414 0.307544 37.6423 0.322802 37.3628 0.378193C37.0833 0.433585 36.8312 0.527566 36.6271 0.652498L30.8651 4.18154L24.7559 0.706279C24.3909 0.49931 23.9061 0.384348 23.4069 0.386399C22.9077 0.388452 22.4345 0.507369 22.0904 0.717253L16.3273 4.24537L10.2202 0.770597C9.85577 0.563033 9.37069 0.447655 8.87116 0.449708C8.37162 0.451762 7.89832 0.571087 7.55481 0.781561L0.501375 5.10205C0.163937 5.31398 -0.0158436 5.59819 0.00109731 5.89297C0.0180383 6.18775 0.230331 6.46931 0.59186 6.67657C0.953389 6.88382 1.43499 7.00005 1.93216 7C2.42933 6.99995 2.90193 6.88362 3.2474 6.67629L9.00846 3.14717L15.1178 6.62215C15.4828 6.82906 15.9677 6.94397 16.4668 6.94192C16.966 6.93987 17.4391 6.82101 17.7832 6.61119L23.5455 3.083L29.6548 6.55824C30.0199 6.76497 30.5046 6.87974 31.0037 6.87763C31.5027 6.87552 31.9757 6.75669 32.3198 6.54697L38.0803 3.01835L44.1896 6.49331C44.3697 6.59841 44.5823 6.68174 44.8152 6.73848C45.0481 6.79522 45.2966 6.82425 45.5464 6.82387C45.7962 6.82349 46.0422 6.7937 46.2703 6.73625C46.4983 6.6788 46.7038 6.59483 46.8748 6.4892C47.0458 6.38356 47.179 6.25837 47.2666 6.12088C47.3542 5.9834 47.3945 5.83635 47.3851 5.68826C47.3757 5.54016 47.3169 5.39399 47.212 5.25818C47.1071 5.12238 46.9582 4.99966 46.774 4.89714L39.2926 0.641538C39.0017 0.475853 38.6317 0.368038 38.2358 0.333673Z" fill="white"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M81.8494 0.0124119C81.555 -0.0137174 81.2559 0.00154108 80.9764 0.0569323C80.6969 0.112324 80.4448 0.206304 80.2407 0.331237L74.4788 3.86027L68.3695 0.385018C68.0045 0.178049 67.5197 0.0630866 67.0205 0.0651383C66.5213 0.0671905 66.0481 0.186108 65.704 0.395992L59.9409 3.92411L53.8338 0.449336C53.4694 0.241772 52.9843 0.126394 52.4848 0.128447C51.9852 0.1305 51.5119 0.249826 51.1684 0.4603L44.115 4.78079C43.7775 4.99272 43.5978 5.27693 43.6147 5.57171C43.6316 5.86648 43.8439 6.14805 44.2055 6.3553C44.567 6.56256 45.0486 6.67879 45.5458 6.67874C46.0429 6.67869 46.5155 6.56236 46.861 6.35503L52.6221 2.82591L58.7314 6.30089C59.0964 6.5078 59.5813 6.62271 60.0804 6.62066C60.5796 6.61861 61.0527 6.49975 61.3969 6.28993L67.1591 2.76174L73.2684 6.23698C73.6335 6.44371 74.1182 6.55848 74.6173 6.55637C75.1163 6.55426 75.5893 6.43543 75.9334 6.2257L81.6939 2.69709L87.8032 6.17205C87.9833 6.27715 88.1959 6.36048 88.4288 6.41722C88.6617 6.47396 88.9103 6.50299 89.16 6.50261C89.4098 6.50223 89.6559 6.47243 89.8839 6.41499C90.1119 6.35754 90.3174 6.27357 90.4884 6.16794C90.6594 6.0623 90.7926 5.93711 90.8802 5.79962C90.9678 5.66214 91.0081 5.51509 90.9987 5.36699C90.9893 5.2189 90.9305 5.07273 90.8256 4.93692C90.7207 4.80112 90.5718 4.6784 90.3876 4.57587L82.9062 0.320277C82.6153 0.154592 82.2453 0.0467765 81.8494 0.0124119Z" fill="white"/>
                    </g>
                </svg>
            </div>
            <div class="flex flex-col gap-3 w-full mb-10">
                <div class="flex items-center gap-[13px] text-white text-sm font-bold leading-normal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4135)"/>
                        <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        <defs>
                            <linearGradient id="paint0_linear_1182_4135" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#EF7F1A"/>
                                <stop offset="1" stop-color="#FFA439"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    {_newFront.luckyShops.info.first}
                </div>
                <div class="flex items-center gap-[13px] text-white text-sm leading-normal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4135)"/>
                        <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        <defs>
                            <linearGradient id="paint0_linear_1182_4135" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#EF7F1A"/>
                                <stop offset="1" stop-color="#FFA439"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    {_newFront.luckyShops.info.second}
                </div>
                <div class="flex items-center gap-[13px] text-white text-sm leading-normal">
                    <svg class="flex-shrink-0 self-start mt-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_1182_4135)"/>
                        <path d="M8 12.6071L10.5524 15L16 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        <defs>
                            <linearGradient id="paint0_linear_1182_4135" x1="7.74242e-07" y1="24" x2="21.7738" y2="6.33212" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#EF7F1A"/>
                                <stop offset="1" stop-color="#FFA439"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    {_newFront.luckyShops.info.third}
                </div>
            </div>
            <button class="js-mobile-continue flex justify-center text-white font-bold leading-7 bg-orange-gradient py-[14px] w-full max-w-[372px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative mx-auto z-10" style="box-shadow: 6px 6px 13px 0 rgba(239, 127, 26, 0.51);">
                {_newFront.luckyShops.intro.ctaUnloggedUser}
            </button>
        </div>
    </div>
</div>


<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<script>
    tippy('#popular-shop-tooltip', {
        content: `
				<div class="text-xs text-white px-[19px] z-50 py-[15px] bg-[#060300] text-center rounded-md">
					<div class="font-bold mb-[5px]">{_newFront.luckyShops.shopPicker.tooltip.title |noescape}</div>
					<p>{_newFront.luckyShops.shopPicker.tooltip.text |noescape}</p>
				</div>
			`,
        allowHTML: true,
        placement: 'bottom',
        theme: 'tooltip-shop',
        arrow: true,
    })

	document.querySelectorAll('.tooltip-icon').forEach(el => {
		el.addEventListener('click', (e) => {
			e.stopPropagation();
			e.preventDefault();
		});
	});

	document.addEventListener("DOMContentLoaded", () => {
		const overlay = document.getElementById("overlay");
		const modal = document.getElementById("all-shops-list-modal");
		const openModalButton = document.getElementById("open-all-shops-list-modal");
		const closeButton = document.querySelector(".close-button");

		const openModal = () => {
			overlay.classList.remove("hidden");
			modal.classList.remove("hidden");
			document.body.style.overflow = "hidden";
		};

		const closeModal = () => {
			overlay.classList.add("hidden");
			modal.classList.add("hidden");
			document.body.style.overflow = "";
		};

		if (openModalButton) {
			openModalButton.addEventListener("click", openModal);
		}

		if (closeButton) {
			closeButton.addEventListener("click", closeModal);
		}

		if (overlay) {
			overlay.addEventListener("click", closeModal);
		}
	});

	document.addEventListener("DOMContentLoaded", function () {
		const button = document.querySelector(".js-mobile-continue")
		const introScreen = document.getElementById("mobile-intro-screen")

		if (button && introScreen) {
			button.addEventListener("click", function () {
				introScreen.classList.add("hidden")
			})
		}
	})

</script>

<style>
    .tippy-box[data-theme~='tooltip-shop'] {
        background-color: transparent !important;
        box-shadow: none !important;
        border: none !important;
    }

    .tippy-box[data-theme~='tooltip-shop'] .tippy-content {
        padding: 0 !important;
        background-color: transparent !important;
    }

	.tippy-box[data-theme~='tooltip-shop'][data-placement^='bottom'] > .tippy-arrow::before {
		border-bottom-color: #060300 !important;
	}
</style>
