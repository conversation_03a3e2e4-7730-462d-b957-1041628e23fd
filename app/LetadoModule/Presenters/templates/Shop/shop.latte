{block title}{$metaTitle}{/block}
{block description}{$metaDescription}{/block}

{block head}
    {include parent}      
{/block}

{block scripts}
    {include parent}

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
            ]
        }
    </script>
{/block}


{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">

                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a n:href="Shop:shop $shop" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="lazyload k-profile-header__logo">
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {$heading1}
                        </h1>
                        
                        <p class="k-profile-header__text mw-700 ml-0">                                                     
                            {_"$websiteType.shop.text", [brand => $shop->getName()]|noescape}
                        </p>                        
                    </div>                                       
                </div>

                {* Letáky *}
                {if count($leafletsInTop) > 0}
                    <div class="k-leaflets__wrapper mt-3">                        
                        
                        <div class="k-leaflets__item k-leaflets__item--first mb-5">              
                            <!-- letado.com / mobile_rectangle1 -->
                            {include "../components/mobile_rectangle1.latte"}
                        </div> 
                        
                        {foreach $leafletsInTop as $leaflet}
                            {if $leaflet->isExpired() === false && $iterator->counter == 1}
                                <div class="k-leaflets__large-wrapper mb-3 mb-sm-0" n:attr="data-brochure-id: $leaflet->getOfferistaBrochureId()">
                                    <div class="k-leaflets__large">
                                        {if $user->isLoggedIn()}
                                            <div class="k-leaflets__bubble">
                                                {if $leaflet->hasOfferistaId()}<span class="k-leaflets__bubble-top">Offerista: {$leaflet->getOfferistaBrochureId()}</span>{/if}
                                                {if $leaflet->isTop()}<span class="k-leaflets__bubble-top">TOP</span>{/if}
                                                {if $leaflet->isPrimary()}<span class="k-leaflets__bubble-primary">Primary</span>{/if}
                                                <small>{$leaflet->getPriority()}</small>
                                            </div>
                                        {/if}
                                        <a n:href="Leaflet:leaflet $shop, $leaflet" class="k-leaflets__large-thumbnail">
                                            <div class="img">
                                                <picture>
                                                    <source
                                                        srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
                                                        type="image/webp"
                                                        importance="high"
                                                    >
                                                    <img
                                                        src="{$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'}"
                                                        srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
                                                        width="242"
                                                        height="336"
                                                        alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
                                                        importance="high"
                                                    >
                                                </picture>

                                            </div>
                                            <div class="img" n:if="$secondPage = $leaflet->getPageByNumber(2)">
                                                <picture>
                                                    <source
                                                        srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
                                                        type="image/webp"
                                                        importance="high"
                                                    >
                                                    <img
                                                        src="{$secondPage->getImageUrl() |image:242,336,'exactTop'}"
                                                        srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
                                                        width="242"
                                                        height="336"
                                                        alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
                                                        importance="high"
                                                    >
                                                </picture>
                                            </div>
                                            <img class="chevron-right" src="{$basePath}/images/icons/chevron_right.svg" alt="right arrow">
                                        </a>
                                        <span n:if="$leaflet->isValid()" class="k-leaflets__corner"><span>{_'kaufino.leaflet.valid'}</span></span>
                                        <div class="k-leaflets__large-detail">
                                            <div class="logo">
                                                <img src="{$shop->getLogoUrl() |image:80,80}" alt="logo" loading="lazy">
                                                <div class="k-leaflets__large-detail-title">
                                                    <a n:href="Leaflet:leaflet $shop, $leaflet">
                                                        {$leaflet->getName()}
                                                    </a>
                                                    <span class="note">
                                                        <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="mt-3 mt-md-0 ml-md-auto">
                                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button ">{_oferto.shop.showLeaflet}</a>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                {continueIf $iterator->counter == 1}
                            {/if}

                            <div class="k-leaflets__item mb-5">
                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                    {dump $leaflet->getId()}
                                    <picture n:if="$leaflet->getFirstPage()">
                                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                    </picture>                                                            
                                </a>
                                <div class="k-leaflets__title mt-0 mb-0">
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                        {$leaflet->getName()}
                                    </a>
                                </div>                
                                <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button k-leaflets__button--secondary mt-3">{_oferto.shop.showLeaflet}</a>
                            </div>            
                        {/foreach}                        
                    </div>
                {elseif (!$shop->isEshop())}
                    <div class="alert alert-info mx-3">{_"$websiteType.shop.noLeaflets"}</div>
                {/if}

                {if $contentBlocksAllowed}
                    {foreach $contentBlocks as $contentBlock}
                        {continueIf $contentBlock->getType() === 'legacy'}

                        <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                            <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                            {$contentBlock->getContent() |content|noescape}
                        </div>
                    {/foreach}
                {/if}

                {if $leafletsInBottom}
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherLeaflets", [brand => $shop->getName()]}</h2>

                    <div class="k-leaflets__wrapper mt-3">
                        {foreach $leafletsInBottom as $leaflet}
                            <div class="k-leaflets__item mb-5">
                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                    {dump $leaflet->getId()}
                                    <picture n:if="$leaflet->getFirstPage()">
                                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                    </picture>
                                </a>
                                <div class="k-leaflets__title mt-0 mb-0">
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                        {$leaflet->getName()}
                                    </a>
                                </div>
                                <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                            </div>
                        {/foreach}
                    </div>

                    <p class="d-flex">
                        <a n:href="Archive:archive, $shop" class="link ml-auto">{_'kaufino.leaflets.expiredMetaTitle', [brand => $shop->getName()]} »</a>
                    </p>
                {/if}
                
                <!-- letado.com / pr_native1 -->                    
                {include "../components/pr_native1.latte"}                

                <!-- letado.com / pr_native3 -->                    
                {include "../components/pr_native3.latte"}                

                <div n:if="count($similarShops)-1 > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherShops"}</h2>
                    <div class="k-shop">    
                        {foreach $similarShops as $similarShop}
                            {continueIf $similarShop->getId() == $shop->getId()}
                            <a n:href="Shop:shop $similarShop" class="k-shop__item">
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$similarShop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$similarShop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$similarShop->getName()}" class="lazyload">
                                    </picture>                                    
                                </span>
                                <small class="k-shop__title">{$similarShop->getName()}</small>
                            </a>
                        {/foreach}
                    </div>
                </div>                
                
            </div>
        </div>                      
                
    </div>	    

	<div class="float-wrapper__stop"></div>	
</div>
