<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Conditions\DocumentFacade;
use <PERSON><PERSON><PERSON>\Model\Content\ContentGenerator;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoGenerator;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\TagFacade;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
    /** @var string @persistent */
    public ?string $channel = null;

	/** @var EntityManager @inject */
	public $entityManager;

    /** @var DocumentFacade @inject */
    public $documentFacade;

	/** @var SeoGenerator @inject */
	public $seoGenerator;

	/** @var ContentGenerator @inject */
	public $contentGenerator;

	/** @var ITranslator @inject */
	public $translator;

	/** @var string @persistent */
	public $region;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	protected function startup()
	{
		parent::startup();

		if (!$this->localization) {
			# Debugger::log('Region does not exist.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->website->isActive() && !$this->getUser()->isLoggedIn()) {
			# Debugger::log('Website is not active.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if ($utmSource = $this->getParameter('utm_source')) {
			//Debugger::barDump($utmSource);
			if ($utmSource === 'google' || $utmSource === 'seznam') {
				$this->getHttpResponse()->setCookie('isP', 'yes', '1 day');
			}
		}

		if ($this->getParameter('cookie') && $this->getParameter('cookie') === 'wVPkTDuR8QSQXKsU') {
			$this->getHttpResponse()->setCookie('d2s0KZA1rp9pwsRI9n0l', 'Rj1Z53FM17fL6nskc5NG', new \DateTime('+ 1 month'));

			$this->redirect('this');
		}

		if ($this->getUser()->isLoggedIn()) {
			$this->template->userLoggedIn = true; // only for starting session purpose
		}

        if ($this->websiteFacade->isMrOfferto()) {
            $allowedLocales = ['de', 'pl', 'cs', 'sk', 'hu', 'it', 'us', 'ca'];
            $locale = $this->localization->getLocale();

            if (!in_array($locale, $allowedLocales) && !in_array($this->localization->getRegion(), $allowedLocales)) {
                $this->error('Page not found', 404);
            }
        }

		$pageExtensionSlug = Strings::substring($this->getHttpRequest()->getUrl()->getPath(), 4);
		$this->template->pageExtension = $this->seoFacade->findPageExtensionBySlug($this->website, $pageExtensionSlug);
		$this->template->pageExtensionSlug = $pageExtensionSlug;

        $this->template->channel = $this->channel;

		$this->template->isTrafficPaid = $utmSource === 'google' || $utmSource === 'seznam' || $this->getHttpRequest()->getCookie('isP') === 'yes';
		$this->template->allPageCacheAllowed = false;
		$this->template->canonicalUrl = $this->seoGenerator->generateCanonicalUrl(
            $this->link('//this', ['channel' => null])
        );
		$this->template->translator = $this->translator;
		$this->template->headerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, true, 10, $this->website->getModule());
		};
		$this->template->footerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, true, 10, $this->website->getModule());
		};
		$this->template->footerShopsTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_SHOPS, 11);
		};
		$this->template->footerOffersTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 11);
		};
		$this->template->footerWebsites = function () {
			return $this->websiteFacade->findActiveWebsites($this->website->getModule());
		};

        $isOferito = $this->websiteFacade->isOferito();
        $isMrOfferto = $this->websiteFacade->isMrOfferto();

        $this->template->isOferito = $isOferito;
        $this->template->isMrOfferto = $isMrOfferto;

        if ($isOferito) {
            $this->template->websiteType = 'oferito';
            $this->template->websiteName = 'Oferito';
        } elseif ($isMrOfferto) {
            $this->template->websiteType = 'oferito';
            $this->template->websiteName = 'Mr. Offerto';
        } else {
            $this->template->websiteType = 'letado';
            $this->template->websiteName = 'Letado';
        }

        $this->template->conditions = $this->documentFacade->findByLocalization($this->localization);
	}
}
