<?php

declare(strict_types=1);

namespace Kaufino\Model\Websites\Entities;

use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Doctrine\ORM\Mapping as ORM;
use Nette\Http\Url;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Websites\Repositories\WebsiteRepository")
 * @ORM\Table(name="kaufino_websites_website")
 */
class Website
{
	public const MODULE_KAUFINO = 'kaufino';
    public const MODULE_KAUFINO_SUBDOMAIN = 'kaufino_subdomain';
	public const MODULE_LETADO = 'letado';
	public const MODULE_OFERTO = 'oferto';
	public const MODULE_OFERTO_COM = 'oferto_com';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
     * @ORM\JoinColumn(name="parent_website_id", referencedColumnName="id", nullable=true)
     */
    protected $parentWebsite;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected string $module;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected ?string $type = null;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $domain;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfShops = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfOffers = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfActiveCities = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfShopsInCities = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfStores = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfActiveStores = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected ?int $countOfArticles = 0;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return mixed
	 */
	public function getLocalization()
	{
		return $this->localization;
	}

	/**
	 * @return mixed
	 */
	public function getModule()
	{
		return $this->module;
	}

    public function getLocaleFileName()
    {
        if ($this->getModule() === self::MODULE_KAUFINO_SUBDOMAIN) {
            return self::MODULE_KAUFINO;
        }

        return $this->getModule();
    }

	/**
	 * @return mixed
	 */
	public function getDomain()
	{
		return $this->domain;
	}

	/**
	 * @return mixed
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	public function isKaufino(): bool
	{
		return $this->module === self::MODULE_KAUFINO;
	}

    public function isKaufinoSubdomain(): bool
    {
        return $this->module === self::MODULE_KAUFINO_SUBDOMAIN;
    }

	public function isLetado(): bool
	{
		return $this->module === self::MODULE_LETADO;
	}

	public function isOferto(): bool
	{
		return $this->module === self::MODULE_OFERTO;
	}

	public function isOfertoCom(): bool
	{
		return $this->module === self::MODULE_OFERTO_COM;
	}

	public function hasKaufinoAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLatvian() || $localization->isSerbian() || $localization->isBulgarian() || $localization->isFrancian() || $localization->isSpaian() || $localization->isUnitedStatesAmerican();
	}

	public function hasOfertoAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLatvian() === false && $localization->isSerbian() === false && $localization->isBulgarian() === false && $localization->isFrancian() === false && $localization->isSpaian() === false && $localization->isLithuanian() === false && $localization->isEstonian() === false && $localization->isMoldavian() === false && $localization->isUnitedStatesAmerican() === false;
	}

	public function hasAdsalvaAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLithuanian() || $localization->isEstonian();
	}

    public function hasStarioAdsense(): bool
    {
        /** @var Localization $localization */
        $localization = $this->getLocalization();

        return $localization->isMoldavian();
    }

	public function hasAdSense(): bool
	{
		if ($this->module === self::MODULE_KAUFINO)
			return true;

		if ($this->module === self::MODULE_LETADO)
			return true;

		if ($this->module === self::MODULE_OFERTO) {
			$localization = $this->getLocalization();

			if (
				$localization->isCzech()
				|| $localization->isSlovak()
				|| $localization->isPolish()
				|| $localization->isRomanian()
				|| $localization->isHungarian()
				|| $localization->isCroatian()
				|| $localization->isItaly()
				|| $localization->isGermany()
				|| $localization->isCanadian()
				|| $localization->isBelgian()
				|| $localization->isNetherlandian()
				|| $localization->isDenmarkian()
				|| $localization->isGreecian()
				|| $localization->isJar()
				|| $localization->isSwedian()
				|| $localization->isFinlandian()
				|| $localization->isAustrian()
				|| $localization->isSwitzerlandian()				
				|| $localization->isNorwaian()
				|| $localization->isGreatbritian()
				|| $localization->isSlovenian()
				|| $localization->isLatvian()
				|| $localization->isSerbian()
				|| $localization->isBulgarian()
				|| $localization->isFrancian()
				|| $localization->isLithuanian()
				|| $localization->isSpaian()
				|| $localization->isEstonian()
                || $localization->isMoldavian()	
				|| $localization->isUnitedStatesAmerican()			   				
				) {
				return true;
			}
		}

		return false;
	}

	public static function getModules(): array
	{
		return [self::MODULE_KAUFINO, self::MODULE_LETADO, self::MODULE_OFERTO, self::MODULE_OFERTO_COM];
	}

    public function getCountOfShops(): int
    {
        return $this->countOfShops;
    }

    public function setCountOfShops(int $countOfShops)
    {
        $this->countOfShops = $countOfShops;
    }

    public function getCountOfOffers(): ?int
    {
        return $this->countOfOffers;
    }

    public function setCountOfOffers(int $countOfOffers)
    {
        $this->countOfOffers = $countOfOffers;
    }

    public function getCountOfActiveCities(): ?int
    {
        return $this->countOfActiveCities;
    }

    public function setCountOfActiveCities(int $countOfActiveCities)
    {
        $this->countOfActiveCities = $countOfActiveCities;
    }

    public function getCountOfShopsInCities(): ?int
    {
        return $this->countOfShopsInCities;
    }

    public function setCountOfShopsInCities(int $countOfShopsInCities)
    {
        $this->countOfShopsInCities = $countOfShopsInCities;
    }

    public function getCountOfStores(): ?int
    {
        return $this->countOfStores;
    }

    public function setCountOfStores(int $countOfStores)
    {
        $this->countOfStores = $countOfStores;
    }

    public function getCountOfActiveStores(): ?int
    {
        return $this->countOfActiveStores;
    }

    public function setCountOfActiveStores(int $countOfActiveStores)
    {
        $this->countOfActiveStores = $countOfActiveStores;
    }

    public function getCountOfArticles(): ?int
    {
        return $this->countOfArticles;
    }

    public function setCountOfArticles(int $countOfArticles)
    {
        $this->countOfArticles = $countOfArticles;
    }

    public function getHost()
    {
        $url = new Url($this->domain);

        return str_replace('www.', '', $url->getHost());
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getParentWebsite(): Website
    {
       return $this->parentWebsite ?: $this;
    }
}
