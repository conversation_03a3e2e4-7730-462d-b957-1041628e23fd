<?php

namespace tipli\Model\LuckyShop;

use Nette\Http\Url;
use Nette\Localization\Translator;
use tipli\Model\Account\DeviceTokenFacade;
use tipli\Model\Account\Entities\User;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\NotificationFactory;
use tipli\Model\Inbox\NotificationManager;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\Templates\NotificationTemplateFacade;

class LuckyShopNotificationProvider
{
	public const EVENT_TYPE_LUCKY_SHOP_CREATED = 'luckyShopCreated';

	public function __construct(
		private LuckyShopFacade $luckyShopFacade,
		private NotificationFactory $notificationFactory,
		private NotificationManager $notificationManager,
		private NotificationTemplateFacade $notificationTemplateFacade,
		private DeviceTokenFacade $deviceTokenFacade,
		private Translator $translator,
	) {
	}

	public function scheduleLuckyShopCreatedNotification(LuckyShop $luckyShop, User $user): void
	{
		if (count($this->deviceTokenFacade->findValidUserDeviceTokens($user)) <= 0) {
			return;
		}

		if ($this->luckyShopFacade->findUserLuckyShopCheck($user, $luckyShop) !== null) {
			return;
		}

		$localization = $user->getLocalization();

		$link = new Url($this->translator->translate('newFront.links.luckyShops', [], null, $localization->getLocale()));

		$link->setQueryParameter('from', 'push');

#		if ($user->getAccessToken()) {
#			$link->setQueryParameter('at', $user->getAccessToken());
#		}

		$notificationTemplateDataObject = $this->notificationTemplateFacade->getNotificationTemplate(
			$localization,
			'lucky_shop',
			[
				'vocalName' => $user->getVocalFirstName(),
				'link' => $link->getAbsoluteUrl(),
			]
		);

		$notificationTemplateDataObject->setLink($link->getAbsoluteUrl());

		$notification = $this->notificationFactory->createNotificationFromNotificationTemplateDataObject(
			$user,
			$notificationTemplateDataObject,
			Notification::TYPE_LUCKY_SHOP,
			$luckyShop->getValidSince(),
			null,
			$luckyShop->getValidSince()
		);

		$this->notificationManager->saveNotification($notification);
	}
}
