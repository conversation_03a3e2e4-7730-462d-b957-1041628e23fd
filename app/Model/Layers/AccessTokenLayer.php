<?php

namespace tipli\Model\Layers;

use Nette\Application\Application;
use Nette\Application\Request;
use Nette\Security\AuthenticationException;
use Nette\Security\User;
use Nette\Utils\Strings;
use tipli\Model\Account\Entities\UserActivity;
use tipli\Model\Account\UserFacade;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Messages\EmailsInteractionManager;
use tipli\Security\AccessTokenAuthenticator;
use Tracy\Debugger;

class AccessTokenLayer
{
	/**
	 * @var Request
	 */
	private $request;

	public function __construct(
		Application $application,
		private User $user,
		private AccessTokenAuthenticator $accessTokenAuthenticator,
		private UserFacade $userFacade,
		private ClientLayer $clientLayer,
		private DatadogProducer $datadogProducer,
		private \Nette\Http\Request $httpRequest,
		private EmailsInteractionManager $emailsInteractionManager,
	) {
		$this->request = $application->getRequests()[0] ?? null;
	}

	public function listenRequest()
	{
		if (!$this->request || !$this->request instanceof Request) {
			return;
		}

		$accessToken = null;
		foreach (['at', 'uniqueHashId', 'userUniqueHash', 'userToken'] as $key) {
			if ($accessToken = $this->request->getParameter($key)) {
				break;
			}
		}

		if (!empty($accessToken)) {
			try {
				/** @var \tipli\Model\Account\Entities\User $user */
				$user = $this->accessTokenAuthenticator->authenticate($accessToken);
				if ($this->isAllowedToUserLogin($user)) {
					// email verification
					if (!$user->hasVerifiedEmail() && $this->request->getParameter('at') !== null) {
						$this->userFacade->verifyUserEmail($user);
					}

					// log in
					if ($this->user->isLoggedIn()) {
						$this->user->logout();
					}
					$this->user->login($user);
//					Debugger::log($this->clientLayer->getIp() . ' - ' . $accessToken, 'access-token-valid-request');
				}

				if (!$user && strlen($accessToken) > 6) {
					Debugger::log($this->clientLayer->getIp() . ' - ' . $accessToken, 'access-token-invalid-request');
				}

				$this->listenMailchimpClick($user);
			} catch (AuthenticationException $e) {
				$url = $this->httpRequest->getUrl();

				if (Strings::contains($url, 'api/')) {
					return;
				}

				Debugger::log($this->clientLayer->getIp() . ' - ' . $accessToken, 'access-token-bad-request');
				$this->datadogProducer->scheduleSendEvent('accessTokenBadLogin');
			}
		}
	}

	private function listenMailchimpClick(\tipli\Model\Account\Entities\User $user): void
	{
		if (
			$this->httpRequest->getQuery('utm_source') === 'newsletter'
			&& $this->httpRequest->getQuery('utm_medium') !== null
			&& $this->httpRequest->getQuery('utm_campaign') !== null
		) {
			$this->emailsInteractionManager->click($user, new \DateTime(), false, UserActivity::SOURCE_MAILCHIMP);
		}
	}

	private function isAllowedToUserLogin(\tipli\Model\Account\Entities\User $user): bool
	{
		return $user->isAdmin() === false;
//		if (!$this->user->isLoggedIn() || $this->user->getIdentity() === $user) {
//			return $user->isActive();
//		}
//
//		return !$this->user->getIdentity()->isAdmin();
	}
}
