<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use DateTime;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use <PERSON><PERSON><PERSON>\Model\Competitors\Entities\Coupon;
use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use Ka<PERSON>ino\Model\Images\ImageFilter;
use Ka<PERSON>ino\Model\Leaflets\Entities\Leaflet;
use Ka<PERSON>ino\Model\Leaflets\Entities\LeafletPage;
use Ka<PERSON>ino\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use Kaufino\Model\Products\ImportedProduct;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Competitors\CompetitorFacade;
use Nette\Http\Url;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;
use stdClass;
use Tracy\Debugger;

final class SyncManager
{
    /** @var EntityManager */
    private $entityManager;

    /** @var SteveClient */
    private $steveClient;

    /** @var LeafletFacade */
    private $leafletFacade;

    /** @var LocalizationFacade */
    private $localizationFacade;

    /** @var ShopFacade */
    private $shopFacade;

    /** @var CompetitorFacade */
    private $competitorFacade;

    /** @var TipliClient */
    private $tipliClient;

    /** @var GeoFacade */
    private $geoFacade;

    /** @var ImageFilter */
    private $imageFilter;

    /** @var City[] */
    private $cachedCities;

    private ITranslator $translator;

    public function __construct(
        LeafletFacade $leafletFacade,
        LocalizationFacade $localizationFacade,
        ShopFacade $shopFacade,
        OfferFacade $offerFacade,
        EntityManager $entityManager,
        SteveClient $steveClient,
        TipliClient $tipliClient,
        CompetitorFacade $competitorFacade,
        GeoFacade $geoFacade,
        ImageFilter $imageFilter,
        ITranslator $translator
    ) {
        $this->leafletFacade = $leafletFacade;
        $this->localizationFacade = $localizationFacade;
        $this->shopFacade = $shopFacade;
        $this->offerFacade = $offerFacade;
        $this->entityManager = $entityManager;
        $this->steveClient = $steveClient;
        $this->tipliClient = $tipliClient;
        $this->competitorFacade = $competitorFacade;
        $this->geoFacade = $geoFacade;
        $this->imageFilter = $imageFilter;
        $this->translator = $translator;
    }

    public function syncShops(): void
    {
        $shops = $this->steveClient->getShops();
        $shopsIds = [];

        foreach ($shops as $syncShop) {
            /** @var ?Shop $shop */
            $shop = $this->shopFacade->findShopByShopId($syncShop->id);

            /** @var ?Localization $localization */
            $localization = $this->localizationFacade->findLocalizationFromSteveLocale($syncShop->locale);

            if (!$localization) {
                continue;
            }

            if ($shop === null) {
                $shop = $this->shopFacade->findShopBySlug($localization, Strings::webalize($syncShop->name));
            }

            // Shop update is not allowed!
            if ($shop) {
                /*			    if ($shop->getLocalization()->getId() == 5) {
                                    $shop->setName(($syncShop->name));
                                    $shop->setSlug((Strings::webalize($syncShop->name)));
                                    $this->shopFacade->saveShop($shop);
                                }*/

                Debugger::dump("Edit shop");
                Debugger::dump($shop);

                if ($shop->getName() !== $syncShop->name) {
                    // $shop->setName($syncShop->name);
                    Debugger::log('oldName: ' . $shop->getName() . ' newName: ' . $syncShop->name, 'shopNameChanges');
                }

                $shopCities = $shop->getCities();

                $shopCitiesIds = [];
                foreach ($shopCities as $shopCity) {
                    $shopCitiesIds[] = $shopCity->getCityId();
                }

                $syncCitiesIds = [];
                foreach ($syncShop->cities as $syncCity) {
                    $syncCitiesIds[] = $syncCity->id;
                }

                /*                Debugger::dump("Shop cities");
                                Debugger::dump($shopCitiesIds);

                                Debugger::dump("API cities");
                                Debugger::dump($syncCitiesIds);*/

                // $removeIds = array_diff($shopCitiesIds, $syncCitiesIds);
                $removeIds = []; // temporary disable removing cities

                /*                Debugger::dump("Remove ids");
                                Debugger::dump($removeIds);*/

                $addIds = array_diff($syncCitiesIds, $shopCitiesIds);

                /*                Debugger::dump("Add ids");
                                Debugger::dump($addIds);

                                Debugger::dump("Remove ids");
                                Debugger::dump($removeIds);*/

                foreach ($removeIds as $cityId) {
                    $city = $this->findCityByCityIdCached($cityId);

                    if ($city) {
                        //Debugger::dump("Remove: " . $city->getId());
                        $shop->removeCity($city);
                    }
                }

                foreach ($addIds as $cityId) {
                    $city = $this->findCityByCityIdCached($cityId);

                    if ($city) {
                        //Debugger::dump("Add: " . $city->getId());
                        $shop->addCity($city);
                    }
                }

                $shop->setReward($syncShop->reward);

                $this->shopFacade->saveShop($shop, false);

                continue;
            }

            $search = strtolower($syncShop->name . $syncShop->locale);
            if (in_array($search, $shopsIds)) {
                continue;
            }

            $shopsIds[] = $search;
            $shop = new Shop($localization, $syncShop->name, Strings::webalize($syncShop->name), $syncShop->id, null, $syncShop->domain ?? null);
            $shop->setActiveLeaflets(false);
            $this->shopFacade->saveShop($shop);
        }
    }

    private function findCityByCityIdCached($cityId)
    {
        if (!isset($this->cachedCities[$cityId])) {
            $this->cachedCities[$cityId] = $this->geoFacade->findCityByCityId($cityId);
        }

        return $this->cachedCities[$cityId];
    }

    public function syncCities(): void
    {
        $cities = $this->steveClient->getCities();

        foreach ($cities as $syncCity) {
            /** @var ?City $city */
            $city = $this->geoFacade->findCityByCityId($syncCity->id);

            if ($city) {
                continue;
            }

            /** @var ?Localization $localization */
            $localization = $this->localizationFacade->findLocalizationFromSteveLocale($syncCity->locale);

            if (!$localization) {
                continue;
            }

            Debugger::dump($syncCity);

            $city = new City($localization, $syncCity->name, $syncCity->slug, $syncCity->id, $syncCity->district, $syncCity->lat, $syncCity->lng, $syncCity->population ?: 0);
            $this->geoFacade->saveCity($city);
        }
    }

    public function syncStores(): void
    {
        $stores = $this->steveClient->getStores();

        foreach ($stores as $store) {
            Debugger::dump($store);

            $storeId = $store->id;
            $storeName = $store->name;
            $storeCity = $store->city;
            $storeLat = $store->lat;
            $storeLng = $store->lng;
            $fullAddress = $store->fullAddress;

            break;
        }

        return;

        $shopsIds = [];

        foreach ($shops as $syncShop) {
            /** @var ?Shop $shop */
            $shop = $this->shopFacade->findShopByShopId($syncShop->id);

            // Shop update is not allowed!
            if ($shop) {
                continue;
            }

            /** @var ?Localization $localization */
            $localization = $this->localizationFacade->findLocalizationFromSteveLocale($syncShop->locale);

            if (!$localization) {
                continue;
            }

            $search = strtolower($syncShop->name . $syncShop->locale);
            if (in_array($search, $shopsIds)) {
                continue;
            }

            $shopsIds[] = $search;
            $shop = new Shop($localization, $syncShop->name, Strings::webalize($syncShop->name), $syncShop->id, null, $syncShop->domain ?? null);
            $shop->setActiveLeaflets(false);
            $this->shopFacade->saveShop($shop);
        }
    }

    public function syncCoupons(Localization $localization): void
    {
        $coupons = $this->steveClient->getDeals($localization);

        foreach ($coupons as $rawDeal) {
            if ($rawDeal->type !== 'coupon' && $rawDeal->type !== 'free_shipping' && $rawDeal->type !== 'sale') {
                continue;
            }

            $region = $rawDeal->locale;

            $localization = $this->localizationFacade->findLocalizationFromSteveLocale($region);

            if ($localization === null) {
                continue;
            }

            $shop = null;

            if ($rawDeal->shop) {
                $shop = $this->shopFacade->findShopByShopId($rawDeal->shop->id);

                if ($shop === null) {
                    $domain = $rawDeal->shop->domain;
                    $domainUrl = new Url($domain);
                    $searchDomain = str_replace('www.', '', $domainUrl->getHost());

                    if (!$searchDomain) {
                        continue;
                    }

                    $shop = $this->shopFacade->findShopByDomain($localization, $searchDomain);
                }
            }

            Debugger::log($rawDeal, 'sync-deals');

            if ($shop === null) {
                continue;
            }

            $offer = $this->offerFacade->findOfferByOfferId($rawDeal->id);

            $name = $rawDeal->type === 'sale' ? $rawDeal->generatedName : $rawDeal->name;
            $description = $rawDeal->type === 'sale' ? $rawDeal->generatedDescription : $rawDeal->description;

            if ($name === null) {
                continue;
            }

            if (!$offer) {
                if ($rawDeal->type === 'free_shipping') {
                    $discountType = Offer::DISCOUNT_TYPE_FREE_SHIPPING;
                } elseif ($rawDeal->unit === 'percentage') {
                    $discountType = Offer::DISCOUNT_TYPE_RELATIVE;
                } else {
                    $discountType = Offer::DISCOUNT_TYPE_ABSOLUTE;
                }

                $i = 0;
                $slug = $rawDeal->slug;

                while ($this->offerFacade->findOfferBySlug($localization, $slug)) {
                    $slug = $rawDeal->slug . '-' . ++$i;
                }

                $offer = $this->offerFacade->createOffer(
                    $localization,
                    $name,
                    $slug,
                    $shop,
                    $rawDeal->type === 'sale' ? Offer::TYPE_DEAL : Offer::TYPE_COUPON,
                    $discountType,
                    DateTime::createFromFormat('Y-m-d H:i:s', $rawDeal->validSince),
                    DateTime::createFromFormat('Y-m-d H:i:s', $rawDeal->validTill)
                );

                $offer->setOfferId($rawDeal->id);
                $offer->setCode($rawDeal->code);
                $offer->setDiscountAmount($rawDeal->value);
                $offer->setDescription($description);
            } else {
                $offer->setValidTill(DateTime::createFromFormat('Y-m-d H:i:s', $rawDeal->validTill));
                $offer->setName($name);
                $offer->setDescription($description);
                $offer->setDiscountAmount($rawDeal->value);
                $offer->setCode($rawDeal->code);
            }

            $offer->confirm();

            $this->offerFacade->saveOffer($offer);

            if ($rawDeal->type === 'sale') {
                continue;
            }

            $coupon = $this->competitorFacade->findCouponByCouponId($localization, 'Tipli', $rawDeal->id);
            if ($coupon) {
                $coupon->setShop($shop);
            } else {
                $coupon = new Coupon(
                    $localization,
                    $shop,
                    $rawDeal->shop->name,
                    $rawDeal->shop->domain,
                    'Tipli',
                    $rawDeal->id,
                    Coupon::DISCOUNT_TYPE_RELATIVE,
                    $rawDeal->value,
                    $rawDeal->name,
                    $rawDeal->description,
                    $rawDeal->code,
                    DateTime::createFromFormat('Y-m-d H:i:s', $rawDeal->validSince),
                    DateTime::createFromFormat('Y-m-d H:i:s', $rawDeal->validTill)
                );
            }

            $this->competitorFacade->saveCoupon($coupon);
        }
    }

    public function syncLeaflets(Localization $localization): void
    {
        $leaflets = $this->steveClient->getLeaflets($localization);
        $newsletters = $this->steveClient->getNewsletters($localization);

        $allLeaflets = array_merge($leaflets, $newsletters);

        /** @var stdClass $syncLeaflet */
        foreach ($allLeaflets as $syncLeaflet) {
            /** @var ?Shop $shop */
            $shop = $this->shopFacade->findShopByShopId($syncLeaflet->shopId);

            if (!$shop) {
                continue;
            }

            /** @var ?Leaflet $leaflet */
            $leaflet = $this->leafletFacade->findLeafletByLeafletId($syncLeaflet->uniqueId);

            $type = $syncLeaflet->type === 'newsletter' ? Leaflet::TYPE_NEWSLETTER : Leaflet::TYPE_LEAFLET;

            $saveNeeded = false;

            if ($syncLeaflet->isMerged === true) {
                if ($leaflet) {
                    $leaflet->setDeletedAt(new DateTime());
                    $this->leafletFacade->saveLeaflet($leaflet);
                }

                continue;
            }

            if ($leaflet) {
                if ($syncLeaflet->name !== null && $syncLeaflet->name !== $leaflet->getName()) {
                    $saveNeeded = true;
                    $leaflet->setName($syncLeaflet->name);
                }

                if ($leaflet->getType() !== $type) {
                    $saveNeeded = true;
                    $leaflet->setType($type);
                }

                if ($leaflet->isPrimary() !== $syncLeaflet->primary) {
                    $saveNeeded = true;
                    $leaflet->setPrimary($syncLeaflet->primary);
                }

                if ($leaflet->getOfferistaBrochureId() !== $syncLeaflet->offeristaId) {
                    $saveNeeded = true;
                    $leaflet->setOfferistaBrochureId($syncLeaflet->offeristaId);
                }

                $currentCitiesHash = $leaflet->getCitiesHash();

                $cities = [];
                foreach ($syncLeaflet->cities as $syncCity) {
                    $city = $this->geoFacade->findCityByCityId($syncCity->id);

                    if ($city) {
                        $cities[] = $city;
                    }
                }

                $leaflet->setCities($cities);

                if ($currentCitiesHash !== $leaflet->getCitiesHash()) {
                    $saveNeeded = true;
                }

                $validSince = DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->validSince);
                $validTill = DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->validTill);

                // Jirka ti to rikal
                if ($validSince != $leaflet->getValidSince()) {
                    $saveNeeded = true;
                    $leaflet->setValidSince($validSince);
                }

                if ($validTill != $leaflet->getValidTill()) {
                    $saveNeeded = true;
                    $leaflet->setValidTill($validTill);
                }

                $recalculatePageNumbers = false;

                $leafletPagesByPageNumber = [];
                $leafletPagesToSave = [];

                foreach ($leaflet->getPages() as $leafletPage) {
                    $leafletPagesByPageNumber[$leafletPage->getPageNumber()] = $leafletPage;
                }

                $realPageNumber = 1;
                foreach ($syncLeaflet->pages as $syncPage) {
                    if ($syncPage->isHidden) {
                        $leafletPage = $this->leafletFacade->findLeafletPageByFile($syncPage->file);

                        if ($leafletPage) {
                            $this->leafletFacade->removeLeafletPage($leafletPage);
                        }

                        $recalculatePageNumbers = true;

                        continue;
                    }

                    $leafletPageByPageNumber = $leafletPagesByPageNumber[$realPageNumber] ?? null;

                    if ($leafletPageByPageNumber && $leafletPageByPageNumber->getImageUrl() !== $syncPage->file) {
                        $leafletPageByPageNumber->setImageUrl($syncPage->file);
                        $leafletPagesToSave[$leafletPageByPageNumber->getId()] = $leafletPageByPageNumber;
                    }

                    if ($leafletPageByPageNumber && $leafletPageByPageNumber->getClickOuts() !== $syncPage->clickOuts) {
                        $leafletPageByPageNumber->setClickOuts($syncPage->clickOuts);
                        $leafletPagesToSave[$leafletPageByPageNumber->getId()] = $leafletPageByPageNumber;
                    }

                    $realPageNumber++;
                }

                if ($recalculatePageNumbers) {
                    $pageNumber = 1;
                    /** @var LeafletPage $page */
                    foreach ($leaflet->getPages() as $page) {
                        $page->setPageNumber($pageNumber);
                        $leafletPagesToSave[$page->getId()] = $page;

                        $pageNumber++;
                    }
                }

                if ($leafletPagesToSave) {
                    $this->leafletFacade->saveLeafletPages($leafletPagesToSave);
                }

                if ($saveNeeded === true) {
                    $this->leafletFacade->saveLeaflet($leaflet);
                }
            } else {
                $leafletPages = [];

                foreach ($syncLeaflet->pages as $syncPage) {
                    if ($syncPage->isHidden) {
                        continue;
                    }

                    $leafletPages[] = $syncPage;
                }

                if (empty($leafletPages)) {
                    continue;
                }

                if ($syncLeaflet->cancelledAt !== null) { // skip cancelled leaflets
                    continue;
                }

                if ($syncLeaflet->name === null) {
                    continue;
                }

                $validSince = DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->validSince);

                $slug = $this->translator->translate('kaufino.leaflet.brandLeafletFrom', ['brand' => $shop->getName()], null, $localization->getLocale());

                if ($slug !== 'leaflet.brandLeafletFrom') {
                    $slug .= '-' . $this->translator->translate('app.day.' . $validSince->format('N') . '.genitive', [],  null, $localization->getLocale());
                    $slug .= '-' .  $validSince->format('d.m');

                    $slug = Strings::webalize($slug);
                } else {
                    $slug = Strings::webalize($syncLeaflet->name);
                }

                $rootSlug = $slug;

                $i = 0;
                while ($this->leafletFacade->findLeafletBySlug($localization, $slug, false)) {
                    $i++;
                    $slug = $rootSlug . '-' . $i;
                }

                $leaflet = new Leaflet(
                    $localization,
                    $shop,
                    $syncLeaflet->uniqueId,
                    $syncLeaflet->name,
                    $type,
                    $slug,
                    $syncLeaflet->subject,
                    $syncLeaflet->primary,
                    $validSince,
                    DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->validTill)
                );

                $leaflet->setOfferistaBrochureId($syncLeaflet->offeristaId);

                $this->entityManager->persist($leaflet);
                $this->entityManager->flush($leaflet);

                $pageNumber = 1;
                foreach ($leafletPages as $syncPage) {
                    $leafletPage = new LeafletPage($leaflet, $syncPage->file, $pageNumber);

                    if ($syncPage->clickOuts) {
                        $leafletPage->setClickOuts($syncPage->clickOuts);
                    }

                    $this->entityManager->persist($leafletPage);
                    $this->entityManager->flush($leafletPage);

                    $pageNumber++;

                    if ($leaflet->isExpired() === false) {
                        foreach (
                            [
                                ($this->imageFilter)($syncPage->file, 768, null, ImageFilter::FLAG_FIT, 'webp'),
                                ($this->imageFilter)($syncPage->file, 768, null, ImageFilter::FLAG_FIT),
                                ($this->imageFilter)($syncPage->file, 1740, null, ImageFilter::FLAG_FIT, 'webp'),
                                ($this->imageFilter)($syncPage->file, 1740, null, ImageFilter::FLAG_FIT),
                            ] as $url
                        ) {
                            (new Client())->sendAsync(new Request('GET', $url));
                        }
                    }
                }

                $this->leafletFacade->saveLeaflet($leaflet);
            }

            $leaflet->setChecked($syncLeaflet->checked);

            if ($syncLeaflet->cancelledAt && $leaflet->isDeleted() === false) {
                $leaflet->setDeletedAt(new DateTime());
            } elseif ($syncLeaflet->cancelledAt === null && $leaflet->isDeleted() === true) {
                $leaflet->setDeletedAt(null);
            }

            if ($syncLeaflet->removedAt && !$leaflet->isDeleted()) {
                $leaflet->setDeletedAt(DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->removedAt));
            }

            if ($syncLeaflet->removeAt && !$leaflet->isDeleted()) {
                $leaflet->setDeletedAt(DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->removeAt));
            }

            if ($syncLeaflet->archivedAt && !$leaflet->isArchived()) {
                $leaflet->setArchiveAt(DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->archivedAt));
            }

            if ($syncLeaflet->archiveAt && !$leaflet->isArchived()) {
                $leaflet->setArchiveAt(DateTime::createFromFormat('Y-m-d H:i:s', $syncLeaflet->archiveAt));
            }

            $leaflet->setCountOfArchivedPages($syncLeaflet->archivationPages);

            $leaflet->setPartnerLink($syncLeaflet->partnerLink);

            $this->entityManager->flush($leaflet);
        }
    }

    public function syncProducts(Localization $localization)
    {
        $products = $this->steveClient->getProducts($localization);

        foreach ($products as $product) {
            if (Strings::length($product->name) <= 2) {
                continue;
            }

            $importedProduct = new ImportedProduct(
                $product->locale,
                $product->shop->id,
                $product->name,
                $product->productId,
                $product->description,
                $product->url,
                $product->imageUrl,
                $product->price,
                $product->oldPrice,
                $product->currency,
                (bool) $product->inStock,
                $product->position,
                $product->validTill,
                $product->removedAt
            );

            $offer = $this->offerFacade->findOfferByOfferId($importedProduct->getProductId());

            $shop = $this->shopFacade->findShopByShopId($importedProduct->getShopId());

            if (!$shop) {
                continue;
            }

            if (!$offer) {
                $rootSlug = $slug = Strings::webalize($importedProduct->getName());
                $i = 0;
                while ($this->offerFacade->findOfferBySlug($localization, $slug)) {
                    $i++;
                    $slug = $rootSlug . '-' . $i;
                }

                try {
                    $offer = $this->offerFacade->createOffer(
                        $localization,
                        $importedProduct->getName(),
                        $slug,
                        $shop,
                        Offer::TYPE_PRODUCT,
                        Offer::DISCOUNT_TYPE_ABSOLUTE,
                        new DateTime(),
                        $importedProduct->getValidTill()
                    );
                } catch (\Exception $e) {
                    Debugger::log($importedProduct, 'import-products-errors');
                    Debugger::log($e->getMessage(), 'import-products-errors');
                    break;
                }

                $offer->setOfferId($importedProduct->getProductId());
            } else {
                $offer->setValidTill($importedProduct->getValidTill());
            }

            $offer->setInStock($importedProduct->isInStock());
            $offer->setCommonPrice($importedProduct->getOldPrice());
            $offer->setCurrentPrice($importedProduct->getPrice());
            $offer->setImageUrl($importedProduct->getImageUrl());
            $offer->setExitUrl($importedProduct->getUrl());

            $offer->confirm();

            $this->offerFacade->saveOffer($offer);
        }
    }
}
