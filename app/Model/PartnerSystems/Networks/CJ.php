<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\DateTime;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use stdClass;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\ChatGPT\ChatGPTClient;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Producers\DealImporterProducer;
use tipli\Model\Files\Entities\File;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface ICJFactory
{
	/** @return CJ */
	public function create(PartnerSystem $partnerSystem);
}

class CJ extends Network
{
	/** @var DealImporterProducer $dealImporterProducer @inject */
	public $dealImporterProducer;

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	/** @var ChatGPTClient $chatGPTClient @inject */
	public ChatGPTClient $chatGPTClient;

	private const SALES_PAGE = 'cj-sales-pivot';
	private const SALES_LANGUAGE_CZECH = 'Czech';
	private const SALES_LANGUAGE_SLOVAK = 'Slovak';
	private const SALES_LANGUAGE_POLISH = 'Polish';
	private const SALES_LANGUAGE_HUNGARIAN = 'Hungarian';

	private const COUPON_NAME_REPLACEMENTS = [
		'cs' => ['Sleva', 'Kód'],
		'sk' => ['Zľava', 'Kod'],
		'pl' => ['Zniżka', 'Kod'],
		'ro' => ['Reducere', 'Cod'],
		'hu' => ['Kedvezmény', 'Kód'],
	];

	private const COUPON_NAME_REPLACE_WITH = [
		'cs' => 'Slevový kupon',
		'sk' => 'Zľavový kupón',
		'pl' => 'kupon rabatowy',
		'ro' => 'Cuponul de reducere',
		'hu' => 'Kedvezményes kupon',
	];

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);

		$url->setQueryParameter('sid', $userId . 'x' . $redirectionId);
		$url->setQueryParameter('cjgdprType', 7);
		$url->setQueryParameter('cjgdpr', 1);

		if ($deepUrl) {
			$url->setQueryParameter('url', $deepUrl);
		}

		$absoluteUrl = $url->getAbsoluteUrl();

		if ($url->getScheme() === 'http') {
			return substr($absoluteUrl, 5);
		} elseif ($url->getScheme() === 'https') {
			return substr($absoluteUrl, 6);
		}

		return $absoluteUrl;
	}

	public function import(File $file)
	{
		$currency = $this->partnerSystem->getOption('currency');

		if ($currency === 'PLK') {
			$currency = Currency::PLN;
		}

		if (!$currency || !Currency::getCurrencyByName($currency)) {
			throw new InvalidArgumentException('Undefined partner system currency.');
		}

		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$saleAmountKey = 'sale-amount-' . strtolower($currency);
		$publisherCommissionKey = 'publisher-commission-' . strtolower($currency);

		$requiredColumns = ['commission-id', 'order-id', 'advertiser-cid', 'event-date', 'event-date', 'sid', 'status', $saleAmountKey, $publisherCommissionKey];
		foreach ($requiredColumns as $requiredColumn) {
			if (!in_array($requiredColumn, $headers)) {
				throw new InvalidImportException('CJ import není ve správném formátu..');
			}
		}

		$results = $reader->getRecords($headers);
		$results = iterator_to_array($results);

		$records = $this->recomputeImportedRecords($results, $currency);

		foreach ($records as $index => $record) {
			if ($index === 0) {
				continue;
			}

			$transactionId = $record->id;
			$partnerSystemKey = $record->cid;
			$userId = $record->sid;
			$commissionAmount = $record->commissionAmount;
			$orderAmount = $record->orderAmount;
			$registeredAt = $record->eventDate;
			$lastUpdateAt = $record->lastUpdateDate;
			$status = $record->status;

			if ($orderAmount < 0) {
//				throw new InvalidStateException('Nalezena opravna transakce, kterou nelze zaregistrovat.');
				continue;
			}

			if ($commissionAmount <= 0) {
				$commissionAmount = 0;
			}

			if ($status === ImportedTransaction::STATUS_REGISTERED && $commissionAmount === 0) {
				continue;
			}

			if ($status === 'closed') {
				$status = ImportedTransaction::STATUS_CONFIRMED;
			} elseif ($status === 'locked' && $lastUpdateAt->modify('+ 10 days') < new \DateTime()) {
				$status = ImportedTransaction::STATUS_CONFIRMED;
			} else {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}

			if ($userId && $this->partnerSystem->getOption('ignorePartnerSystemKey') && in_array($partnerSystemKey, $this->partnerSystem->getOption('ignorePartnerSystemKey'))) {
				$partnerSystemKey = null;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			if ($status === ImportedTransaction::STATUS_REGISTERED && $record->status === 'locked' && $record->lockedDate) {
				$this->transactionFacade->logCjLockTransaction($this->partnerSystem->getId(), $transactionId, $record->lockedDate);
			}

//            if ($importedTransaction->getCommissionAmount() == 0 && $importedTransaction->getStatus() == ImportedTransaction::STATUS_CANCELLED) {
//                echo $importedTransaction->getTransactionId() . '<br />';
//                $this->cancelTransaction($importedTransaction);
//                continue;
//            }
			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function recomputeImportedRecords($importedRecords, $currency)
	{
		$currency = Strings::lower($currency);
		$records = [];
		$groupedRecords = [];

		foreach ($importedRecords as $importedRecord) {
			$importedRecord = (object) $importedRecord;
			if (is_array($this->partnerSystem->getOption('disableGroups')) && in_array($importedRecord->{'advertiser-cid'}, $this->partnerSystem->getOption('disableGroups'))) {
				$groupedRecords[][] = $importedRecord;
			} else {
				$uniqueOrderId = $importedRecord->{'advertiser-cid'} . '_' . $importedRecord->{'order-id'};
				$groupedRecords[$uniqueOrderId][] = $importedRecord;
			}
		}

		foreach ($groupedRecords as $orderId => $rawRecords) {
			$originalRecord = null;
			foreach ($rawRecords as $record) {
				$originalRecord = $record;
				break;
			}

			if ($originalRecord->{'commission-id'} === 'Commission ID') {
				continue;
			}

			$record = new stdClass();
			$record->id = (string) $originalRecord->{'commission-id'};
			$record->orderId = (string) $originalRecord->{'order-id'};
			$record->lockedDate = $originalRecord->{'locking-date'} ? new DateTime($originalRecord->{'locking-date'}) : null;
//            $record->websiteId = (string) $originalRecord->{'website-id'};
			$record->sid = (string) $originalRecord->{'sid'};
			$record->cid = (string) $originalRecord->{'advertiser-cid'};
			$record->eventDate = new \DateTime((string) $originalRecord->{'event-date'});
			$record->orderAmount = 0;
			$record->commissionAmount = (float) $originalRecord->{'publisher-commission-' . $currency};
			$record->status = strtolower((string) $originalRecord->{'status'});
			$record->lastUpdateDate = new \DateTime((string) $originalRecord->{'posting-date'});

			foreach ($rawRecords as $rawRecord) {
				$record->orderAmount += (float) $rawRecord->{'sale-amount-' . $currency};
				$record->commissionAmount += (float) $rawRecord->{'corrected-amount-' . $currency};
				$record->lastUpdateDate = new \DateTime((string) $rawRecord->{'posting-date'});
			}

			$records[] = $record;
		}

		return $records;
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		//        $from = (new \DateTime())->modify('-3 days');
//        $to = (new \DateTime())->modify('+1 day');

		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$rawRecords = [];
		if ($from && $to) {
			$rawRecords = array_merge($rawRecords, $this->getRecords($from, $to) ?: []);
		} else {
			$rawRecords = array_merge($rawRecords, $this->getRecords($nowFrom, $nowTo) ?: []);
		}

		$records = $this->recomputeRecords($rawRecords);
		$websiteIdFilter = $this->partnerSystem->getOption('websiteId');

		$currency = $this->partnerSystem->getOption('currency');

		if ($currency === 'PLK') {
			$currency = Currency::PLN;
		}

		foreach ($records as $record) {
			$partnerSystemKey = $record->cid;
			$transactionId = $record->id;
			$websiteId = $record->websiteId;
			$userId = $record->sid;
			$commissionAmount = $record->commissionAmount;
			$orderAmount = $record->orderAmount;
			$registeredAt = new \DateTime($record->eventDate);
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));

			$status = $this->resolveStatus($record->status, $partnerSystemKey, $record->lockedDate);

			if ($websiteIdFilter && $websiteIdFilter != $websiteId) {
				continue;
			}

			if ($commissionAmount <= 0) {
				$commissionAmount = 0;
			}

			if ($status === ImportedTransaction::STATUS_REGISTERED && $commissionAmount === 0) {
				// continue;
			}

//            if ($partnerSystemKey == '4854412') {
//            	if ($commissionAmount > 1000) {
//            		$commissionAmount = min($commissionAmount / 100, 512);
//            		$orderAmount = $commissionAmount * 10;
//				}
//			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			if ($status === ImportedTransaction::STATUS_REGISTERED && $record->status == 'locked' && $record->lockedDate) {
				# $this->transactionFacade->logCjLockTransaction($this->partnerSystem->getId(), $transactionId, $record->lockedDate);
			}

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function recomputeRecords($rawRecords)
	{
		$groupedRawRecords = [];
		$records = [];

		foreach ($rawRecords as $rawRecord) {
			if (is_array($this->partnerSystem->getOption('disableGroups')) && in_array($rawRecord->advertiserId, $this->partnerSystem->getOption('disableGroups'))) {
				$groupedRawRecords[][] = $rawRecord;
			} else {
				$uniqueOrderId =  $rawRecord->advertiserId . '_' . $rawRecord->orderId;
				$groupedRawRecords[$uniqueOrderId][] = $rawRecord;
			}
		}

		foreach ($groupedRawRecords as $orderId => $rawRecords) {
			$originalRecord = $this->findOriginalRecord($rawRecords);
			if (!$originalRecord) {
				continue;
			}

			$record = new stdClass();
			$record->id = (string) $originalRecord->commissionId;
			$record->lockedDate = new DateTime($originalRecord->lockingDate);
			$record->websiteId = (string) $originalRecord->websiteId;
			$record->sid = (string) $originalRecord->shopperId;
			$record->cid = (string) $originalRecord->advertiserId;
			$record->eventDate = (string) $originalRecord->eventDate;
			$record->actionTrackerName = (string) $originalRecord->actionTrackerName;
			$record->publisherId = (string) $originalRecord->publisherId;
			$record->commissionAmount = 0;
			$record->orderAmount = 0;
			$record->status = (string) $originalRecord->actionStatus;
			$record->type = $originalRecord->actionType;

			foreach ($rawRecords as $rawRecord) {
				$record->commissionAmount += (float) $rawRecord->pubCommissionAmountPubCurrency;
				$record->orderAmount += (float) $rawRecord->saleAmountPubCurrency;
			}

			if ($record->commissionAmount < 0) {
				$record->commissionAmount = 0;
			}

			if ($record->orderAmount < 0) {
				$record->orderAmount = 0;
			}

			$records[] = $record;
		}

		return $records;
	}

	private function findOriginalRecord($records)
	{
		foreach ($records as $record) {
			if ($record->original == 'true') {
				return $record;
			}
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$client = new \EUAutomation\GraphQL\Client('https://commissions.api.cj.com/query');

		$response = $client->raw('{
			publisherCommissions(forPublishers: ["' . $this->partnerSystem->getOption('publisherId') . '"], sinceEventDate:"' . $from->format('Y-m-d') . 'T' . $from->format('H:i:s') . 'Z",beforeEventDate:"' . $to->format('Y-m-d') . 'T' . $to->format('H:i:s') . 'Z", actionTypes:["item_sale","item_lead", "sim_sale", "sim_lead"])
			{	count payloadComplete records
				{	commissionId lockingDate websiteId publisherId actionStatus actionType actionTrackerName actionTrackerId orderId advertiserId shopperId eventDate saleAmountPubCurrency pubCommissionAmountPubCurrency original
				}
			}
			}', [
			'records' => 'actionTrackerName',
		], [
			'Authorization' => 'Bearer ' . $this->partnerSystem->getOption('apiToken'),
		]);

		$results = Json::decode($response->getBody()->getContents());

		return $results->data->publisherCommissions->records;
	}

	private function resolveStatus($actionStatus, $partnerSystemKey, $lockedDate)
	{
		if ($actionStatus === 'locked') {
			$lockedDateCloned = clone $lockedDate;
			$status = $lockedDateCloned->modify('+ 30 days') <= new DateTime() ? ImportedTransaction::STATUS_CONFIRMED : ImportedTransaction::STATUS_REGISTERED;
		} elseif ($actionStatus === 'closed') {
			$status = ImportedTransaction::STATUS_CONFIRMED;
		} elseif ($actionStatus === 'new') {
			$status = ImportedTransaction::STATUS_REGISTERED;
		} elseif ($actionStatus === 'extended') {
			$status = ImportedTransaction::STATUS_REGISTERED;
		} else {
			throw new InvalidArgumentException('unknown status ' . $actionStatus);
		}

		$lockExceptionShops = $this->partnerSystem->getOption('lockExceptionShops') ?: [];
		if ($partnerSystemKey && in_array($partnerSystemKey, $lockExceptionShops)) {
			$dateLimit = clone $lockedDate;
			$dateLimit->modify('+ 15 days');
			if ($dateLimit > new \DateTime()) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}
		}

		return $status;
	}

	public function processSales()
	{
		$salesUrl = $this->partnerSystem->getOption('salesUrl');
		Debugger::log('CJ processSales started - ' . $this->partnerSystem->getName());

		$salesUrl = str_replace('%page', 1, $salesUrl);

		$localizationIds = [
			Localization::LOCALE_CZECH => 6,
			Localization::LOCALE_SLOVAK => 6,
			Localization::LOCALE_POLISH => 24,
			Localization::LOCALE_ROMANIAN => 26,
			Localization::LOCALE_HUNGARIAN => 16,
			Localization::LOCALE_BULGARIA => 3,
			Localization::LOCALE_SLOVENIA => 28,
//			Localization::LOCALE_CROATIA => 13,
		];

		$localizations = [
			Localization::LOCALE_CZECH => $salesUrl . '&targeted-country=CZ,SK',
			//Localization::LOCALE_SLOVAK => $salesUrl . '&targeted-country=SK',
			Localization::LOCALE_POLISH => $salesUrl,
			Localization::LOCALE_ROMANIAN => $salesUrl,
			Localization::LOCALE_HUNGARIAN => $salesUrl,
			Localization::LOCALE_BULGARIA => $salesUrl,
			Localization::LOCALE_SLOVENIA => $salesUrl,
//			Localization::LOCALE_CROATIA => $salesUrl,
		];

		foreach ($localizations as $locale => $url) {
			if (!isset($localizationIds[$locale])) {
				Debugger::log('Unknown locale ' . $locale, 'cj-deals');
				continue;
			}

			$localization = $this->localizationFacade->findOneByLocale($locale);

			$languageId = $localizationIds[$locale];

			// pokud je lokalizace czech, tak se nefiltrujeme s parametrem language, nekteri inzerenti vyplnuji pouze targeted-country
			if ($localization->isCzech() === false) {
				$url = str_replace('%language%', $languageId, $url);
			}

			$locale = $localization->getLocale();

			$data = $this->fetchSales($url);

			$cjTranslatedIds = Json::decode(file_get_contents(__DIR__ . '/cj-translated-ids.json'), Json::FORCE_ARRAY);

			Debugger::log('URL: ' . $url, 'cj-deals');
			Debugger::log('Count of deals: ' . count($data->links->link), 'cj-deals');

			foreach ($data->links->link as $voucher) {
				$id = (int)$voucher->{'link-id'};
				$campaignId = $voucher->{'advertiser-id'};
				$language = $voucher->{'language'};
				$targetedCountriesArray = array_map(static function ($item) {
					return trim(strtolower($item));
				}, explode(',', $voucher->{'targeted-countries'}));
				$voucherName = (string)$voucher->{'link-name'};

				if ($locale === Localization::LOCALE_CZECH) {
					if (count($targetedCountriesArray) === 1) {
						$feedLocale = trim(strtolower($targetedCountriesArray[0]));
						if ($feedLocale === 'cz') {
							$feedLocale = 'cs';
						}
						$localization = $this->localizationFacade->findOneByLocale($feedLocale);

						if ($localization === null) {
							Debugger::log('Localization ' . $targetedCountriesArray[0] . ' not found', 'cj-deals');
							continue;
						}

						//Debugger::log('Localization ' . $targetedCountriesArray[0] . ' found. For Deal:' . $voucherName, 'cj-deals');
					} elseif (count($targetedCountriesArray) > 1) {
						if (isset($cjTranslatedIds[$id])) {
							$foundLocale = $cjTranslatedIds[$id];
						} else {
							$foundLocale = $this->chatGPTClient->detectLanguage($voucherName);
							$cjTranslatedIds[$id] = $foundLocale;
						}

						if ($foundLocale === null) {
							Debugger::log('Localization not found for Deal:' . $voucherName, 'cj-deals');
							continue;
						}

						$localeToCheck = $foundLocale === 'cs' ? 'cz' : $foundLocale;

						if (in_array($localeToCheck, $targetedCountriesArray) === false) {
							Debugger::log('Localization ' . $foundLocale . ' not found for Deal:' . $voucherName, 'cj-deals');
							continue;
						}

						$localization = $this->localizationFacade->findOneByLocale($foundLocale);

						if ($localization === null) {
							Debugger::log('Localization2 ' . $targetedCountriesArray[0] . ' not found', 'cj-deals');
							continue;
						}

						//Debugger::log('Localization2 ' . $localization->getLocale() . ' found. For Deal:' . $voucherName, 'cj-deals');
					} else {
						Debugger::log('Localization MISSING', 'cj-deals');
						continue;
					}
				} else {
					$localization = $this->localizationFacade->findOneByLocale($locale);
				}

				if (in_array($campaignId, [5886682])) {
					if (str_contains(strtolower($voucherName), 'salente') && $localization->isCzech()) {
						$shop = $this->shopFacade->find(6171);
						$id .= 6171;
					} elseif (str_contains(strtolower($voucherName), 'evolveo') && $localization->isCzech()) {
						$shop = $this->shopFacade->find(6170);
						$id .= 6170;
					} elseif (str_contains(strtolower($voucherName), 'salente') && $localization->isSlovak()) {
						$shop = $this->shopFacade->find(8281);
						$id .= 8281;
					} elseif (str_contains(strtolower($voucherName), 'evolveo') && $localization->isSlovak()) {
						$shop = $this->shopFacade->find(8280);
						$id .= 8280;
					} else {
						$shop = null;
					}
				} else {
					/** @var ?Shop $shop */
					$shop = $this->shopFacade->findByPartnerSystem($this->partnerSystem, $campaignId, $localization);
				}

				if ($shop === null) {
					Debugger::log('[' . $this->partnerSystem->getName() .  '][' . $voucher->{'advertiser-name'} . '] not found for campaignId ' . $campaignId, 'deals-not-paired-shops');
					Debugger::log('Shop ' . $voucher->{'advertiser-name'} .  '  not found for campaignId ' . $campaignId . ' and locale ' .  $localization->getLocale(), 'cj-deals');
					continue;
				}

				$name = Strings::firstUpper($voucherName);
				$slug = Strings::webalize($name);
				$code = (string)$voucher->{'coupon-code'};
				$validSince = (new \DateTime($voucher->{'promotion-start-date'}));
				$validTill = (new \DateTime($voucher->{'promotion-end-date'}));

				if ($validTill < new \DateTime()) {
					Debugger::log('Voucher expired ' . $id, 'cj-deals');
					continue;
				}

				if ((int)$validSince->format('H') > 12) {
					$validSince->setTime(23, 59, 59);
				} else {
					$validSince->setTime(0, 0, 0);
				}

				if ((int)$validTill->format('H') > 12) {
					$validTill->setTime(23, 59, 59);
				} else {
					$validTill->setTime(0, 0, 0);
				}

				$url = $voucher->{'destination'};
				$description = (string)$voucher->{'description'};

				if (empty(trim($description))) {
					$description = strip_tags((string)$voucher->{'description'});
				}

				$reward = $this->resolveRewardFromSale($name);

				$name = str_replace(self::COUPON_NAME_REPLACEMENTS[$locale], self::COUPON_NAME_REPLACE_WITH[$locale], $name);

				$importedDeal = new ImportedDeal($localization->getId(), $shop->getId(), null, $voucher->rules, $slug, $validSince, $validTill, null, null);
				$importedDeal->setUniqueId($id);
				$importedDeal->setDescription($description);
				$importedDeal->setSourceName($this->partnerSystem->getFeedName());
				$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
				$importedDeal->setValidSince($validSince);
				$importedDeal->setValidTill($validTill);
				$importedDeal->setName($name);
				$importedDeal->setDeepUrl($url);
				$importedDeal->setType(Deal::TYPE_COUPON);
				$importedDeal->setCode($code);
				$importedDeal->setUnit($reward !== null ? $reward['unit'] : null);
				$importedDeal->setValue($reward !== null ? $reward['value'] : null);
				$importedDeal->setExclusive(false);

				//$this->dealImporterProducer->scheduleImportedDeal($importedDeal);

				//Debugger::log('Deal ' . $localization->getLocale() . ' | ' . $importedDeal->getName() . ' imported', 'cj-deals');

				$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);
			}

			file_put_contents(__DIR__ . '/cj-translated-ids.json', Json::encode($cjTranslatedIds));
		}
	}

	private function fetchSales(string $url)
	{
		$client = new Client(['verify' => false]);

		$cacheKey = self::SALES_PAGE . $this->partnerSystem->getId() . $url;

		$page = $this->cache->load($cacheKey) ?: 1;
		$url = strtr($url, ['%page' => $page]);


		$response = $client->request('GET', $url, [
			'headers' => [
				'Authorization' => $this->partnerSystem->getOption('apiKey'),
			]]);

		$data = simplexml_load_string($response->getBody()->getContents());
		$page = (int) $data->links->attributes()->{'records-returned'} < 10 ? 1 : ++$page;

		$this->cache->save($cacheKey, $page);

		return $data;
	}

	public function resolveRewardFromSale($description)
	{
		preg_match('/(\d+\s?)(' . implode('|', Deal::getUnits()) . ')/', Strings::lower($description), $matches);

		if ($matches) {
			[$match, $value, $unitSymbol] = $matches;
			$unit = Deal::getUnitBySymbol($unitSymbol);

			return [
				'unit' => $unit,
				'value' => $value,
			];
		}

		return null;
	}

	public function importRefunds(File $file): array
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = Strings::lower($row['status']);

			if ($status === 'pending') {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				$row['order-id'],
				$this->resolveRefundSolutionStatus($status),
				$row['decline-reason']
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): string
	{
		switch ($status) {
			case 'declined':
				return RefundSolution::STATE_PARTNER_DECLINED;
			case 'accepted':
				return RefundSolution::STATE_PARTNER_APPROVED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}
}
