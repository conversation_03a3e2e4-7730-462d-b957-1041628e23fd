<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use InvalidArgumentException;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface ITradedoublerFactory
{
	/** @return Tradedoubler */
	public function create(PartnerSystem $partnerSystem);
}

class Tradedoubler extends Network
{
	private const SALES_URL = 'https://api.tradedoubler.com/1.0/vouchers.json?token=_TOKEN_';
	private const CLIENT_ID = '5041518a-07de-3cf3-8290-ca7e2606da0c';
	private const CLIENT_SECRET = '5fa0d288f1c5fd22';

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null): string
	{
		$url = new Url($url);
		$url->setQueryParameter('epi', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$url->setQueryParameter('url', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file, $offset = null): void
	{
		if ($offset === null) {
			$offset = 0;
		}

		$reader = Reader::createFromPath($this->fileStorage->getFile($file));
		$reader->setDelimiter(';');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return str_replace('"', '', $item);
		}, $headers);

		$requiredColumns = ['epi', 'status', 'orderValue', 'orderValue', 'publisher', 'commission', 'programId'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			if ($offset != 1) {
				$this->import($file, 1);

				return;
			}

			throw new InvalidImportException('Tradedoubler import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);
		$results = array_slice(iterator_to_array($results), $offset, 100000);

		$results = $this->recomputeTransactions($results);

		foreach ($results as $index => $row) {
			if (empty($row['programId'])) {
				continue;
			}

			if (Strings::contains($row['epi'], 'x') === false) {
				continue;
			}

			$partnerSystemKey = $row['programId'];

			$registeredAt = new \DateTime($row['timeOfTransaction']);

			$transactionId = $row['orderNumber'] ?: $row['leadNumber'];

			if (!$transactionId) {
				continue;
			}

			[$userId, $redirectionId] = explode('x', $row['epi']);

			$orderAmount = $this->cleanAmount($row['orderValue']);

			$commissionAmount = $this->cleanAmount($row['commission']);

			$status = $this->resolveStatus($row['status']);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::PLN, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function recomputeTransactions(array $records): array
	{
		$groupedRecords = [];

		foreach ($records as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$transactionId = $row['orderNumber'] ?: $row['leadNumber'];

			if (isset($groupedRecords[$transactionId])) {
				$parentTransaction = $groupedRecords[$transactionId];

				$currentCommissionAmount = $this->cleanAmount($row['commission']);
				$parentCommissionAmount = $this->cleanAmount($parentTransaction['commission']);

				$currentOrderAmount = $this->cleanAmount($row['orderValue']);
				$parentOrderAmount = $this->cleanAmount($parentTransaction['orderValue']);

				$groupedRecords[$transactionId]['commission'] = $currentCommissionAmount + $parentCommissionAmount;
				$groupedRecords[$transactionId]['orderValue'] = $currentOrderAmount + $parentOrderAmount;

				if ($parentTransaction['status'] !== $row['status']) {
					$groupedRecords[$transactionId]['status'] = 'P'; // registered
				}
			} else {
				$groupedRecords[$transactionId] = $row;
			}
		}

		return $groupedRecords;
	}

	private function cleanAmount($amount): float
	{
		$amount = preg_replace('/\s+/u', '', $amount);

		return (float) str_replace(',', '', $amount);
	}

	public function processWebhook($record): void
	{
		$record = (object) $record;

		foreach (['orderNumber', 'orderValue', 'commission', 'epi', 'timeOfEvent', 'status', 'advertiserId', 'programId'] as $field) {
			if (!isset($record->{$field})) {
				throw new InvalidArgumentException('Field ' . $field . ' is missing in webhook.');
			}
		}

		$transactionId = (string) $record->orderNumber;

		if (!$transactionId && $record->leadNumber) {
			return;
		}

		$userId = $record->epi;
		$orderAmount = $record->orderValue;
		$commissionAmount = $record->commission;

		$registeredAt = new \DateTime(date('Y-m-d H:i.s', strtotime($record->timeOfEvent)));

		$status = $this->resolveStatus($record->status);
		$partnerSystemKey = $record->programId;
		$currency = Currency::PLN;

		$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_WEBHOOK);

		$this->scheduleImportedTransaction($importedTransaction);
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): void
	{
		$nowFrom = (new \DateTime())->modify('- 1 day');
		$nowTo = (new \DateTime());

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}

		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $transaction) {
			if ($transaction->epi !== null && Strings::contains($transaction->epi, 'x')) {
				[$userId, $redirectionId] = explode('x', $transaction->epi);
			} else {
				$userId = (int) $transaction->epi;
			}

			$partnerSystemKey = $transaction->programId;
			$transactionId = $transaction->orderNumber;
			$commissionAmount = $transaction->commission;
			$orderAmount = $transaction->orderValue;
			$currency = Currency::PLN;
			$status = $this->resolveStatus($transaction->status);
			$registeredAt = new \DateTime($transaction->timeOfCreate);

			$importedTransaction = new ImportedTransaction(
				$this->partnerSystem,
				$partnerSystemKey,
				(int) $userId,
				$transactionId,
				$commissionAmount,
				$orderAmount,
				$currency,
				$status,
				$registeredAt,
				null,
				null,
				ImportedTransaction::CHANNEL_API
			);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to, $records = [], $pageNumber = 1, $credentials = null)
	{
		if ($credentials === null) {
			$credentials = $this->getCredentials();
		}

		$client = new Client(['verify' => false]);
		$response = $client->request('GET', 'https://connect.tradedoubler.com/publisher/report/transactions', [
			'headers' => [
				'Authorization' => 'Bearer ' . $credentials->access_token,
			],
			'query' => [
				'fromDate' => $from->format('Ymd'),
				'toDate' => $to->format('Ymd'),
				'limit' => 100,
				'offset' => $pageNumber * 100,
			],
		]);

		$records = Json::decode($response->getBody()->getContents())->items;

		if (count($records) >= 100 && $pageNumber <= 20) {
			$records = array_merge($records, $this->getRecords($from, $to, $records, $pageNumber + 1, $credentials));
		}

		return $records;
	}

	private function getCredentials()
	{
		$client = new Client(['verify' => false]);

		$response = $client->request('POST', 'https://connect.tradedoubler.com/uaa/oauth/token', [
			'headers' => [
				'Authorization' => 'Basic ' . base64_encode(self::CLIENT_ID . ':' . self::CLIENT_SECRET),
			],
			'form_params' => [
				'grant_type' => 'password',
				'username' => $this->partnerSystem->getOption('username'),
				'password' => $this->partnerSystem->getOption('password'),
			],
		]);

		return Json::decode($response->getBody()->getContents());
	}

	public function resolveStatus($status): string
	{
		if (!trim($status)) {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		if ($status == 'A') {
			return ImportedTransaction::STATUS_CONFIRMED;
		}

		if ($status == 'D') {
			return ImportedTransaction::STATUS_CANCELLED;
		}

		if ($status == 'P') {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		switch ($status) {
			case '3':
			case '2':
			case '4':
			case '5':
			case '6':
			case '1':
				return ImportedTransaction::STATUS_REGISTERED; //Tracked
			case '8':
			case '7':
				return ImportedTransaction::STATUS_CANCELLED; //Pause_deleted
			case '9':
				return ImportedTransaction::STATUS_CONFIRMED; //Paid
		}

		throw new \tipli\InvalidArgumentException('unknown status ' . $status);
	}

	public function processSales(): void
	{
		foreach ($this->partnerSystem->getOption('tokens') as $locale => $token) {
			$localization = $this->localizationFacade->findOneByLocale($locale);

			if ($localization === null) {
				Debugger::log('Localization not found for locale ' . $locale, 'tradedoubler');
				continue;
			}

			$vouchers = $this->getSales($token);

			foreach ($vouchers as $voucher) {
				$uniqueId = $voucher->id;

				$shop = $this->shopFacade->findByPartnerSystem($this->partnerSystem, $voucher->programId);

				if ($shop === null) {
					Debugger::log('Shop not found for programId ' . $voucher->programId, 'tradedoubler');
					continue;
				}

				if ($voucher->voucherTypeId === 1) {
					$type = Deal::TYPE_COUPON;
				} elseif ($voucher->voucherTypeId === 2) {
					$type = Deal::TYPE_SALE;
				} elseif ($voucher->voucherTypeId === 4) {
					$type = Deal::TYPE_FREE_SHIPPING;
				} else {
					$type = Deal::TYPE_TIP;
				}

				$validSince = (new \DateTime())->setTimestamp($voucher->startDate / 1000);
				$validTill = (new \DateTime())->setTimestamp($voucher->endDate / 1000);
				$name = $voucher->title;
				$description = $voucher->description ?? ($voucher->shortDescription ?? null);
				$slug = Strings::webalize($uniqueId . '-' . $voucher->programName . '-' . $name);
				$url = $voucher->defaultTrackUri;
				$value = $voucher->discountAmount;
				$unit = $voucher->isPercentage ? Deal::UNIT_PERCENTAGE : Currency::getCurrencyByName($voucher->currencyId);

				if ($validTill < new \DateTime()) {
					Debugger::log('Voucher expired ' . $voucher->id, 'tradedoubler');
					continue;
				}

				$importedDeal = new ImportedDeal($localization->getId(), $shop->getId(), null, $name, $slug, $validSince, $validTill, null, null);
				$importedDeal->setUniqueId($uniqueId);
				$importedDeal->setDescription($description);
				$importedDeal->setSourceName($this->partnerSystem->getFeedName());
				$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
				$importedDeal->setValidSince($validSince);
				$importedDeal->setValidTill($validTill);
				$importedDeal->setName($name);
				$importedDeal->setDeepUrl($url);
				$importedDeal->setType($type);
				$importedDeal->setCode($voucher->code);
				$importedDeal->setUnit($unit);
				$importedDeal->setValue($value);
				$importedDeal->setExclusive(false);

				Debugger::log('Voucher ' . $voucher->id . ' - ' . $voucher->programId, 'tradedoubler');

				$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);
			}
		}
	}

	private function getSales(string $token): array
	{
		$url = str_replace('_TOKEN_', $token, $this->partnerSystem->getOption('salesUrl'));

		$client = new Client();

		$response = $client->request('GET', $url);

		return Json::decode($response->getBody()->getContents());
	}

	public function importRefunds(File $file): array
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(';');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = $this->resolveRefundSolutionStatus(Strings::lower($row['statusstate']));

			if ($status === null) {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				$row['ordernumber'],
				$status,
				$row['statusmessage']
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): ?string
	{
		return match ($status) {
			'approved' => RefundSolution::STATE_PARTNER_APPROVED,
			'rejected' => RefundSolution::STATE_PARTNER_DECLINED,
			default => null,
		};
	}
}
