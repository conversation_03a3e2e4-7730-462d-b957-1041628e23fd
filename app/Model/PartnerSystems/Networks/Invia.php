<?php

namespace tipli\Model\PartnerSystems\Networks;

use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Transactions\ImportedTransaction;

interface IInviaFactory
{
	/**
	 * @return Invia
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Invia extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);

		if ($deepUrl) {
			$url = new Url($deepUrl);
		}

		$url->setQueryParameter('adata1', $userId . 'x' . $redirectionId);

		return $url;
	}

	private function resolveColumnNames(): array
	{
		$columnNames = [
			'cz' => [
				'orderId' => 'id-objednavky',
				'status' => 'stav',
				'price' => 'cena',
				'commission' => 'provize-celkem',
				'orderedAt' => 'objednano',
				'lastUpdatedAt' => 'naposledy-aktualizovano',
			],
			'sk' => [
				'orderId' => 'id-objednavky',
				'status' => 'stav',
				'price' => 'cena',
				'commission' => 'provizie-celkom',
				'orderedAt' => 'objednane',
				'lastUpdatedAt' => 'naposledy-aktualizovane',
			],
			'hu' => [
				'orderId' => 'order-id',
				'status' => 'state',
				'price' => 'price',
				'commission' => 'commission-total',
				'orderedAt' => 'ordered',
				'lastUpdatedAt' => 'last-update',
			],
		];

		if (!isset($columnNames[$this->partnerSystem->getOption('locale')])) {
			throw new InvalidArgumentException('unknown locale ' . $this->partnerSystem->getOption('locale'));
		}

		return $columnNames[$this->partnerSystem->getOption('locale')];
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));
		$reader->setDelimiter(';');

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$columnNames = $this->resolveColumnNames();
		$requiredColumns = array_values($columnNames);

		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Invia import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			if (!$row['data1']) {
				continue;
			}

			$status = $this->resolveStatus($row[$columnNames['status']]);

			$transactionId = $row[$columnNames['orderId']];
			$partnerSystemKey = 'invia_' . $this->partnerSystem->getOption('locale');
			[$userId, $redirectionId] = explode('x', $row['data1']);
			$commissionAmount = (float) $row[$columnNames['commission']];
			$orderAmount = (float) $row[$columnNames['price']];
			$registeredAt = new \DateTime($row[$columnNames['orderedAt']]);

			if ($commissionAmount == 0) {
				continue;
			}

			$currency = $this->partnerSystem->getOption('currency');

			$confirmedAt = null;
			if ($status == ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime($row[$columnNames['lastUpdatedAt']]);
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus(string $status)
	{
		switch ($status) {
			case 'Řeší se':
			case 'Rieši sa':
			case 'Prodáno, čeká se na návrat':
			case 'Predané, čaká sa na návrat':
			case 'Prodáno':
			case 'Predané':
			case 'In progress':
			case 'Sold, waiting for return':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'K fakturaci':
			case 'K fakturácii':
			case 'Fakturováno':
			case 'Fakturované':
			case 'To Invoice':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'Neprodáno':
			case 'Nepredané':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}
}
