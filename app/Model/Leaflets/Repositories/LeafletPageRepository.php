<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Leaflets\Repositories;

use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON>ino\Model\Websites\Entities\Website;
use Nette\Utils\Json;

class LeafletPageRepository extends EntityRepository
{
	public function getLeafletPages()
	{
		$qb = $this->createQueryBuilder('lp');

		return $qb;
	}

	public function findLeafletPagesToUseOcr(int $limit)
	{
		return $this->getLeafletPages()
			->andWhere('lp.useOcr = true')
			->andWhere('lp.ocrOutput IS NULL')
			->andWhere('lp.failedAt IS NULL')
			->getQuery();
	}

	public function findLeafletPagesToCalculateScore(int $limit)
	{
		return $this->getLeafletPages()
			->andWhere('lp.ocrOutput IS NOT NULL')
			->andWhere('lp.score IS NULL')
			->addOrderBy('lp.createdAt', 'DESC')
			->getQuery();
	}

	public function findLeafletPagesToUseDetectObjects(int $limit)
	{
		return $this->getLeafletPages()
			->andWhere('lp.useDetectObjects = true')
			->andWhere('lp.detectObjectsOutput IS NULL')
			->andWhere('lp.failedAt IS NULL')
			->getQuery();
	}

	public function findLeafletPagesToCreateOffers(int $limit)
	{
		$qb = $this->getLeafletPages()
			->andWhere('lp.annotatedAt IS NOT NULL')
			->andWhere('lp.processedAt IS NULL')
			->andWhere('lp.failedAt IS NULL')
			->andWhere('lp.annotationsOutput != :empty')
			->andWhere('lp.imageUrl IS NOT NULL')
			->setParameter('empty', '[]') // @todo Jirka: docasne preskakovat prazdne offers dokud se nevytvori moznost preskakovat letaky
			;

		return $qb->getQuery();
	}

    public function findLeafletPagesByFulltext($keyword, Localization $localization, ?string $websiteType = null, $limit = null, ?Tag $excludedTag = null, ?bool $onlyCurrent = true)
    {
        $keywordsInput = explode(';', $keyword);

        $qb = $this->getLeafletPages()
            ->innerJoin('lp.leaflet', 'l')
            ->innerJoin('l.shop', 's')
        ;

        $mainOrX = $qb->expr()->orX();

        foreach ($keywordsInput as $phraseIndex => $singlePhrase) {
            $singlePhrase = trim($singlePhrase);
            if (empty($singlePhrase)) {
                continue;
            }

            $wordsInPhrase = explode(' ', $singlePhrase);

            $phraseAndX = $qb->expr()->andX();
            $validWordsInPhrase = 0;

            foreach ($wordsInPhrase as $wordIndex => $word) {
                $word = trim($word);
                if (empty($word)) {
                    continue;
                }
                $placeholder = 'keyword_' . $phraseIndex . '_word_' . $wordIndex;
                $phraseAndX->add($qb->expr()->like('lp.ocrOutput', ':' . $placeholder));
                $qb->setParameter($placeholder, '%' . $word . '%');
                $validWordsInPhrase++;
            }

            if ($validWordsInPhrase > 0) {
                $mainOrX->add($phraseAndX);
            }
        }

        if ($mainOrX->count() > 0) {
            $qb->andWhere($mainOrX);
        } else {
            $qb->andWhere('1 = 0');
        }

        $qb->andWhere('l.localization = :localization')
            ->andWhere('l.validTill > :now')
            ->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
            ->andWhere('lp.ocrOutput IS NOT NULL')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
            ->addOrderBy('l.priority', 'DESC')
            ->addOrderBy('l.validTill', 'ASC')
            ->setParameter('localization', $localization)
            ->setParameter('now', new \DateTime())
        ;

        if ($onlyCurrent) {
            $qb->andWhere('l.validSince < :now');
        }

        if ($excludedTag) {
            $qb->andWhere('lp.excludedFromTags IS NULL OR JSON_CONTAINS(lp.excludedFromTags, :excludedTag) = 0')
                ->setParameter('excludedTag', Json::encode($excludedTag->getId()));
        }

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = 1');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_LETADO) {
            $qb->andWhere('s.activeLetado = 1');
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery();
    }

	public function findLeafletPagesToAnnotate(int $limit, ?Localization $localization = null)
	{
		$qb = $this->getLeafletPages()
			->innerJoin('lp.leaflet', 'l')
			->andWhere('l.validTill > :now')
			->andWhere('l.archivedAt IS NULL')
			->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
			->andWhere('lp.ocrOutput IS NOT NULL')
			->andWhere('lp.annotatedAt IS NULL')
			->andWhere('lp.failedAt IS NULL')
			->andWhere('lp.imageUrl IS NOT NULL')
			->andWhere('l.archivedAt IS NULL')
			->andWhere('lp.score IS NOT NULL')
            ->andWhere('l.primary = true')
			->andWhere('lp.score > 0')
			->addOrderBy('lp.score', 'DESC')
			->setParameter('now', new \DateTime())
        ;

        if ($localization) {
            $qb->andWhere('l.localization = :localization')
                ->setParameter('localization', $localization);
        }

        return $qb->getQuery();
	}

    public function findCountOfLeafletPagesToAnnotateByLocalization()
    {
        return $this->getLeafletPages()
            ->select('count(lp.id) as count, loc.id, loc.name')
            ->innerJoin('lp.leaflet', 'l')
            ->innerJoin('l.localization', 'loc')
            ->andWhere('l.validTill > :now')
            ->andWhere('l.archivedAt IS NULL')
            ->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
            ->andWhere('lp.ocrOutput IS NOT NULL')
            ->andWhere('lp.annotatedAt IS NULL')
            ->andWhere('lp.failedAt IS NULL')
            ->andWhere('lp.imageUrl IS NOT NULL')
            ->andWhere('lp.score IS NOT NULL')
            ->andWhere('l.primary = true')
            ->andWhere('lp.score > 0')
            ->addOrderBy('lp.score', 'DESC')
            ->addOrderBy('lp.id', 'DESC')
            ->groupBy('l.localization')
            ->setParameter('now', new \DateTime())
            ->andWhere('l.createdAt >= :createdAt')
            ->setParameter('createdAt', new \DateTime('-7 days'))
            ->getQuery()->getArrayResult();
    }

	public function getCountOfLeafletPagesToAnnotate(?Localization $localization = null): int
    {
		$qb = $this->getLeafletPages()
			->select('count(lp.id)')
			->innerJoin('lp.leaflet', 'l')
			->andWhere('l.validTill > :now')
			->andWhere('l.archivedAt IS NULL')
			->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
			->setParameter('now', new \DateTime())
			->andWhere('lp.ocrOutput IS NOT NULL')
			->andWhere('lp.annotatedAt IS NULL')
			->andWhere('lp.failedAt IS NULL')
			->andWhere('lp.imageUrl IS NOT NULL')
			->andWhere('lp.score IS NOT NULL')
			->andWhere('lp.score > 0')
            ->andWhere('l.createdAt >= :createdAt')
            ->setParameter('createdAt', new \DateTime('-7 days'))
            ->setParameter('now', new \DateTime())
        ;

        if ($localization) {
            $qb->andWhere('l.localization = :localization')
                ->setParameter('localization', $localization);
        }

        return (int) $qb->getQuery()->getSingleScalarResult();
	}

	public function findTopValidLeafletPagesByScore(Shop $shop, int $limit)
	{
		$qb = $this->getLeafletPages()
			->innerJoin('lp.leaflet', 'l')
			->andWhere('l.shop = :shop')
			->andWhere('l.validSince < :now')
			->andWhere('l.validTill > :now')
			->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
//			->andWhere('lp.ocrOutput IS NOT NULL')
			->andWhere('lp.score IS NOT NULL')
			->andWhere('lp.failedAt IS NULL')
			->andWhere('lp.imageUrl IS NOT NULL')
			->andWhere('lp.score > 0')
			->addOrderBy('lp.score', 'DESC')
			->addOrderBy('lp.id', 'DESC')
			->setParameter('shop', $shop)
			->setParameter('now', new \DateTime())
			->setMaxResults($limit)
		;

//		echo "\n\n";
//		echo $qb->getQuery()->getSQL();
//		echo "\n\n";

		return $qb->getQuery()->getResult();
	}

	public function findTrendingLeafletPages(Shop $shop)
	{
		$qb = $this->getLeafletPages()
			->innerJoin('lp.leaflet', 'l')
			->andWhere('l.shop = :shop')
			->andWhere('l.validSince < :now')
			->andWhere('l.validTill > :now')
			->andWhere('((l.deletedAt IS NOT NULL AND l.deletedAt > :now) OR l.deletedAt IS NULL)')
			->andWhere('lp.ocrOutput IS NOT NULL')
			->andWhere('lp.score IS NOT NULL')
			->andWhere('lp.failedAt IS NULL')
			->andWhere('lp.imageUrl IS NOT NULL')
			->andWhere('lp.isTrending = true')
			->addOrderBy('lp.score', 'DESC')
			->addOrderBy('lp.id', 'DESC')
			->setParameter('shop', $shop)
			->setParameter('now', new \DateTime())
		;

		return $qb->getQuery()->getResult();
	}
}
