<?php

namespace steve\Model\Leaflets\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Nette\Database\Explorer;
use Nette\InvalidArgumentException;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Spatie\PdfToImage\Exceptions\PdfDoesNotExist;
use steve\Model\Leaflets\Entities\Leaflet;
use steve\Model\Leaflets\LeafletFacade;
use steve\Model\Leaflets\Producers\LeafletPageProducer;
use steve\Model\RabbitMq\BaseConsumer;
use Tracy\Debugger;

class LeafletPageConsumer extends BaseConsumer implements IConsumer
{
    private LeafletFacade $leafletFacade;

	private Explorer $db;

	public function __construct(
		LeafletFacade $leafletFacade,
		Explorer $db,
	) {
        $this->leafletFacade = $leafletFacade;
		$this->db = $db;
	}

    /**
     * @throws JsonException
     */
    public function consume(Message $message): int
    {
        $data = Json::decode($message->content);

		try {
			/** @var Leaflet $leaflet */
			$leaflet = $this->leafletFacade->find($data->leafletId);

			if ($leaflet === null) {
				return IConsumer::MESSAGE_ACK;
			}

			if ($this->leafletFacade->findLeafletPageByPageNumber($leaflet, $data->pageNumber)) {
				return IConsumer::MESSAGE_ACK;
			}

			if ($leaflet->isCancelled() || $leaflet->isProcessed()) {
				return IConsumer::MESSAGE_ACK;
			}

			if ($data->type === LeafletPageProducer::TYPE_PDF_PAGE) {
				try {
					$startTime = time();
					$this->leafletFacade->processLeafletPdfPageType($leaflet, $data->file, $data->pageNumber, $data->options);
				} catch (PdfDoesNotExist|\ImagickException|\Exception $e) {
					$this->leafletFacade->cancelLeaflet($leaflet, Leaflet::CANCEL_REASON_PROCESS);

					return IConsumer::MESSAGE_ACK;
				}
			} else if ($data->type === LeafletPageProducer::TYPE_URL_PAGE) {
                $leafletPage = $this->leafletFacade->processLeafletUrlPageType($leaflet, $data->file, $data->pageNumber);
			} else if ($data->type === LeafletPageProducer::TYPE_IMAGE_PAGE) {
				$leafletPage = $this->leafletFacade->processLeafletImagePageType($leaflet, $data->file, $data->pageNumber);
			} else {
				throw new \InvalidArgumentException('Unknown leaflet type to process');
			}

            if (isset($leafletPage) && $data->clickOuts) {
                $leafletPage->setClickOuts($data->clickOuts);
                $this->leafletFacade->saveLeafletPage($leafletPage);
            }
		} catch (InvalidArgumentException|RequestException|ClientException|GuzzleException $e) {
			Debugger::log($e->getMessage(), 'leaflet-page-consumer-error');
		}

        return IConsumer::MESSAGE_ACK;
    }
}
