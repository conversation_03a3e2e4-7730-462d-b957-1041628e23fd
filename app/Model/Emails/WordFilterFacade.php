<?php

namespace steve\Model\Emails;

use Nette\SmartObject;
use steve\Model\Emails\Entities\Message;
use steve\Model\Emails\Entities\WordFilter;
use steve\Model\Emails\Managers\WordFilterManager;
use steve\Model\Emails\Repositories\MessageRepository;
use steve\Model\Emails\Repositories\EmailRepository;
use steve\Model\Emails\Repositories\WordFilterRepository;
use steve\Model\Shops\Entities\Shop;

class WordFilterFacade
{
    use SmartObject;

    /** @var WordFilterManager */
    private $wordFilterManager;

    /** @var WordFilterRepository */
    private $wordFilterRepository;

    public function __construct(WordFilterManager $wordFilterManager, WordFilterRepository $wordFilterRepository)
    {
    	$this->wordFilterManager = $wordFilterManager;
        $this->wordFilterRepository = $wordFilterRepository;
    }

    public function find($id)
    {
        return $this->wordFilterRepository->find($id);
    }

    public function findByShopAndLocale(Shop $shop)
	{
		return $this->wordFilterRepository->findByShopAndLocale($shop);
	}

    public function getWordFilters()
    {
        return $this->wordFilterRepository->getWordFilters();
    }

    public function createWordFilter($locale, $word)
    {
        return $this->wordFilterManager->createWordFilter($locale, $word);
    }

    public function saveWordFilter(WordFilter $wordFilter)
    {
        return $this->wordFilterManager->saveWordFilter($wordFilter);
    }

    public function filterMessage(Message $message)
	{
		return $this->wordFilterManager->filterMessage($message, $this->findByShopAndLocale($message->getShop()));
	}
}
