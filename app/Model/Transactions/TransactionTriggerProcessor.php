<?php

namespace tipli\Model\Transactions;

use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Campaign\CampaignFacade;
use tipli\Model\Campaign\Entities\CampaignSubscription;
use tipli\Model\Campaign\Entities\CampaignTransaction;
use tipli\Model\Currencies\CurrencyFacade;
use tipli\Model\Marketing\NewsletterFacade;
use tipli\Model\Messages\EmailTemplateFactory;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Rewards\Entities\ConstantReward;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Rewards\Entities\MoneyRewardCampaign;
use tipli\Model\Rewards\MoneyRewardFacade;
use tipli\Model\Rewards\RewardFacade;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\Entities\TransactionProcess;
use tipli\Model\Transactions\Repositories\TransactionRepository;
use tipli\Model\Vouchers\Entities\UserVoucher;
use tipli\Model\Vouchers\Entities\UserVoucherTransaction;
use tipli\Model\Vouchers\Entities\VoucherCampaign;
use tipli\Model\Vouchers\VoucherFacade;
use Tracy\Debugger;

class TransactionTriggerProcessor
{
	public const TRIGGER_CHECK_BONUSES = 'check_bonuses';
	public const TRIGGER_CHECK_SUSPECTED_TRANSACTION = 'check_suspected_transaction';
	public const TRIGGER_PAIR_REDIRECTION = 'pair_redirection';
	public const TRIGGER_BONUS_REGISTRATION_EMAIL = 'bonus_registration_email';
	public const TRIGGER_CAMPAIGN_ON_REGISTER = 'campaign_on_register';
	public const TRIGGER_CAMPAIGN_ON_CONFIRM = 'campaign_on_confirm';
	public const TRIGGER_VOUCHER_ON_REGISTER = 'voucher_on_register';
	public const TRIGGER_VOUCHER_ON_CONFIRM = 'voucher_on_confirm';

	public function __construct(
		private TransactionManager $transactionManager,
		private RewardFacade $rewardFacade,
		private SuspectedTransactionFacade $suspectedTransactionFacade,
		private MessageFacade $messageFacade,
		private CurrencyFacade $currencyFacade,
		private CampaignFacade $campaignFacade,
		private MoneyRewardFacade $moneyRewardFacade,
		private TransactionRepository $transactionRepository,
		private VoucherFacade $voucherFacade,
		private UserFacade $userFacade,
		private NewsletterFacade $newsletterFacade,
		private SqlQueryScheduler $sqlQueryScheduler,
		private EmailTemplateFactory $emailTemplateFactory,
	) {
	}

	public function processTrigger(string $trigger, Transaction $transaction, Redirection $redirection = null)
	{
		switch ($trigger) {
			case self::TRIGGER_CHECK_BONUSES:
				$this->checkUserRecommendation($transaction);
				$this->checkConfirmationTresholds($transaction);
				$this->flagProcess(TransactionProcess::TYPE_CHECK_BONUSES, $transaction);
				break;
			case self::TRIGGER_CHECK_SUSPECTED_TRANSACTION:
				$this->suspectedTransactionFacade->analyzeTransaction($transaction);
				$this->flagProcess(TransactionProcess::TYPE_CHECK_SUSPECTED_TRANSACTION, $transaction);
				break;
			case self::TRIGGER_PAIR_REDIRECTION:
				$this->pairRedirection($transaction, $redirection);
				$this->flagProcess(TransactionProcess::TYPE_PAIR_REDIRECTION, $transaction);
				break;
			case self::TRIGGER_BONUS_REGISTRATION_EMAIL:
				$this->sendBonusRegistrationEmail($transaction);
				$this->flagProcess(TransactionProcess::TYPE_BONUS_REGISTRATION_EMAIL, $transaction);
				break;
			case self::TRIGGER_CAMPAIGN_ON_REGISTER:
				$this->campaignCreateBonusTransaction($transaction);
				$this->flagProcess(TransactionProcess::TYPE_CAMPAIGN_CREATE_BONUS, $transaction);
				break;
			case self::TRIGGER_CAMPAIGN_ON_CONFIRM:
				$this->campaignConfirmBonus($transaction);
				$this->flagProcess(TransactionProcess::TYPE_CAMPAIGN_CONFIRM_BONUS, $transaction);
				$this->campaignFinishCampaign($transaction);
				$this->flagProcess(TransactionProcess::TYPE_CAMPAIGN_FINISH, $transaction);
				break;
			case self::TRIGGER_VOUCHER_ON_REGISTER:
				$this->voucherCreateBonusTransaction($transaction);
				$this->flagProcess(TransactionProcess::TYPE_VOUCHER_CREATE_BONUS, $transaction);
				break;
			case self::TRIGGER_VOUCHER_ON_CONFIRM:
				$this->voucherConfirmBonusTransaction($transaction);
				$this->flagProcess(TransactionProcess::TYPE_VOUCHER_CONFIRM_BONUS, $transaction);
				break;
			default:
				throw new InvalidArgumentException('Unknown trigger: '  . $trigger);
		}
	}

	private function checkUserRecommendation(Transaction $transaction)
	{
		if (!$transaction->getUser()) {
			return;
		}

		if (!$transaction->isCommission()) {
			return;
		}

		$relatedUser = $transaction->getUser();
		if (!$parentUser = $relatedUser->getParentUser()) {
			return;
		}

		$this->checkRecommendationBonus($parentUser, $relatedUser);
	}

	public function checkRecommendationBonus(User $parentUser, User $relatedUser)
	{
		/** @var MoneyReward $moneyReward */
		$moneyReward = null;

		/** @var MoneyReward[] $moneyRewards */
		$moneyRewards = $this->moneyRewardFacade->findValidMoneyRewardsByUser($parentUser, $relatedUser->getCreatedAt(), MoneyRewardCampaign::GOAL_RECOMMENDATION);
		foreach ($moneyRewards as $moneyReward) {
			if ($moneyReward->getAmount() <= 0) {
				continue;
			}
			break;
		}

		/** @var ConstantReward $constantReward */
		$constantReward = $this->rewardFacade->findConstantRewardByType($parentUser->getLocalization(), ConstantReward::TYPE_BONUS_RECOMMENDATION, $relatedUser->getCreatedAt());

		$amount = $constantReward->getAmount();
		$confirmationTreshold = $constantReward->getConfirmationTreshold();

		if ($moneyReward !== null && $this->transactionRepository->findRecommendationBonus($relatedUser) === null) { // existuje money reward a zaroven jeste uzivatel nedostal bonus za doporuceni
			$amount = $moneyReward->getAmount();
			$confirmationTreshold = $moneyReward->getConfirmationTreshold();

			$this->moneyRewardFacade->useMoneyReward($moneyReward); // bonus se "vyexpiruje"
		}

		// VOUCHERS
		/** @var UserVoucher|null $validUserVoucher */
		$validUserVoucher = null;
		$userVouchers = $this->voucherFacade->findValidActiveUserVouchers($parentUser, $relatedUser->getCreatedAt(), [VoucherCampaign::REWARD_TYPE_RECOMMENDATION_BONUS_BOOST]);
		/** @var UserVoucher $userVoucher */
		foreach ($userVouchers as $userVoucher) {
			if ($validUserVoucher === null || $validUserVoucher->getVoucherCampaign()->getBonusAmount() < $userVoucher->getVoucherCampaign()->getBonusAmount()) {
				$validUserVoucher = $userVoucher;
			}
		}

		if ($validUserVoucher !== null) {
			$amount = $validUserVoucher->getVoucherCampaign()->getBonusAmount();
		}
		// VOUCHERS END

		// create or return bonus
		$transaction = $this->transactionManager->createOrReturnRecommendationBonus(
			$parentUser,
			$relatedUser,
			$constantReward->getName(),
			$constantReward->getCurrency(),
			$amount,
			$confirmationTreshold,
			$constantReward->getRecommendationBonusTreshold()
		);

		// USE VOUCHER
		if ($validUserVoucher !== null) {
			$this->voucherFacade->createUserVoucherTransaction($validUserVoucher, $transaction);
			$this->voucherFacade->finishUserVoucher($validUserVoucher);
		}

		if (!$transaction->isConfirmed()) {
			$balance = $this->transactionManager->getConfirmedLifetimeCommissionBalance($relatedUser);

			if ($balance >= $transaction->getRecommendationBonusTreshold()) {
				$this->transactionManager->confirmTransaction($transaction);
			}
		}

		$this->checkConfirmationTresholds($transaction);
	}

	public function checkConfirmationTresholds(Transaction $transaction)
	{
		if (!$transaction->getUser()) {
			return;
		}

		$campaignTransaction = $this->campaignFacade->findCampaignTransactionForActiveUser($transaction->getUser());

		if (
			$transaction->isBonus()
			&& !$transaction->isBonusVoucher()
			&& (!$transaction->isBonusCampaign() || ($campaignTransaction && !$campaignTransaction->getTransaction()))
			&& $transaction->getConfirmationTreshold() === (float) 0
			&& !$transaction->isConfirmed()
		) {
			$this->transactionManager->confirmTransaction($transaction);
			bdump("a1");
		}

		if (
			$transaction->isBonus()
			&& !$transaction->isBonusVoucher()
			&& !$transaction->isBonusRecommendation()
			&& $transaction->getUser()
			&& $transaction->getConfirmationTreshold() < $this->transactionManager->getConfirmedCommissionBalance($transaction->getUser())
		) {
			$transaction->prepareForConfirm();
			$this->transactionManager->saveTransaction($transaction);
			bdump("b1");
		}

		if ($transaction->isCommissionOrRefund() && $transaction->getUser()) {
			$this->transactionManager->confirmPreparedForConfirmTransactions($transaction->getUser());
			bdump("c1");
		}

		$this->campaignConfirmBonusActiveUser($transaction);
	}

	private function pairRedirection(Transaction $transaction, Redirection $redirection)
	{
		$transaction->setPlatform($redirection->getPlatform());

		if ($deal = $redirection->getDeal()) {
			$transaction->setDeal($deal);
		}

		$this->transactionManager->saveTransaction($transaction);
	}

	private function sendBonusRegistrationEmail(Transaction $transaction)
	{
		if ($transaction->isBonusRecommendation()) {
			$user = $transaction->getUser();
			$recommendedUser = $transaction->getRelatedRecommendedUser();

			if ($user && $recommendedUser && ($user->isSlovak() || $user->isCzech())) {
				$emailDataObject = $this->emailTemplateFactory->buildRecommendedUserFirstPurchase($user, $recommendedUser);

				$duplicityHash = substr(sha1('recommend_bonus_registered' . $user->getId()) . $recommendedUser->getId(), -32);

				$this->messageFacade->sendEmailByDTO($user, $emailDataObject, $duplicityHash);
			} else {
				$this->messageFacade->sendTransactionRecommendationBonusRegistrationEmail($transaction);
			}
		} else {
			$this->messageFacade->sendBonusTransactionRecommendationBonusRegistrationEmail($transaction);
		}
	}

	public function computeAccounting(Transaction $transaction)
	{
		if (!$transaction->isCommission()) {
			return;
		}

		$currency = $transaction->getCurrency();
		$commissionAmount = $transaction->getCommissionAmount();
		$userCommissionAmount = $transaction->getUserCommissionAmount();

		$turnover = $this->currencyFacade->convertToCZK($commissionAmount, $currency);
		$income = $turnover - $this->currencyFacade->convertToCZK($userCommissionAmount, $currency);

		$transaction->setAccounting($turnover, $income);

		if (!$transaction->isConfirmed()) {
			$transaction->setOriginalAccounting($turnover, $income);

			if ($transaction->hasShop()) {
				$transaction->setShopConfirmationRate($transaction->getShop()->getConfirmationRate());
			}
		}

		$this->transactionManager->saveTransaction($transaction);
	}

	public function campaignSubscribeNewUser(Transaction $transaction)
	{
		$user = $transaction->getUser();

		if (!$user) {
			return;
		}

		if ($user->isWhiteLabelUser()) { // twisto rondo nedostavaji double transakce
			return;
		}

		if (!$user || !$transaction->isCommissionOrRefund()) { // pouze nakupy
			return;
		}

		if ($user->isActiveUser()) { // pouze pro nove
			return;
		}

		if (!CampaignSubscription::canNewUserSubscribeCampaign($user)) {
			return;
		}

		$this->campaignFacade->createOrReturnCamapignSubscription($user, $transaction->getUtm(), CampaignSubscription::SOURCE_TRANSACTION);
	}

	public function campaignCreateBonusTransaction(Transaction $transaction)
	{
		$user = $transaction->getUser();
		$userId = $transaction->getUser() ? $transaction->getUser()->getId() : 0;
		$transactionId = $transaction->getId();

		if (!$user) {
			bdump("cl 1");
			Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 1', 'TriggerProcessor__campaign');
			return;
		}

		if (!$transaction->isCommissionOrRefund() || !$transaction->hasUser()) {
			bdump("cl 2");
			Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 2', 'TriggerProcessor__campaign');
			return;
		}

		/** @var CampaignSubscription|null $campaignSubscription */
		$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($user);

		if (!$campaignSubscription || $campaignSubscription->isFinished() || !$campaignSubscription->isValid()) {
			bdump("cl 3");
			Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 3', 'TriggerProcessor__campaign');
			if (!$campaignSubscription) {
				Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 3a', 'TriggerProcessor__campaign');
			}
			if ($campaignSubscription && $campaignSubscription->isFinished()) {
				Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 3b', 'TriggerProcessor__campaign');
			}
			if ($campaignSubscription && !$campaignSubscription->isValid()) {
				Debugger::log($userId . ' ### ' . $transactionId . ' ### cl 3c', 'TriggerProcessor__campaign');
			}
			return;
		}

		$this->campaignFacade->setSubscriptionValidationForFirstTransaction($campaignSubscription, $transaction); // pocatek kampane je od prvni transakce

		if (!$campaignSubscription->isNewUser()) {
			return null;
		}

		/** @var CampaignTransaction $campaignTransaction */
		$campaignTransaction = $this->campaignFacade->findCampaignTransactionByTransaction($transaction);

		if (!$campaignTransaction) { // transakce jeste neexistuje
			$this->campaignFacade->createCampaignBonusTransactionForNewUser($campaignSubscription, $transaction);
		}
	}

	public function voucherCreateBonusTransaction(Transaction $transaction)
	{
		$user = $transaction->getUser();
		$userId = $transaction->getUser() ? $transaction->getUser()->getId() : 0;
		$user = $user === null && !empty($userId) ? $this->userFacade->find($userId) : $user;

		if (!$user) {
			return;
		}

		if (!$transaction->isCommissionOrRefund() || !$transaction->hasUser()) {
			return;
		}

		/** @var CampaignTransaction $campaignTransaction */
		$userVoucherTransaction = $this->voucherFacade->findUserVoucherTransactionByTransaction($transaction);

		if ($userVoucherTransaction !== null) {
			return;
		}

		$closestPurchasedAt = min(array_filter([$transaction->getRegisteredAt(), $transaction->getCreatedAt(), $transaction->getRedirectedAt()]));

		/** @var UserVoucher $validUserVoucher */
		$userVouchers = $this->voucherFacade->findValidActiveUserVouchers(
			$user,
			$closestPurchasedAt,
			[VoucherCampaign::REWARD_TYPE_STATIC_BONUS, VoucherCampaign::REWARD_TYPE_DYNAMIC_BONUS],
			$transaction->getShop(),
			$transaction->getOrderAmount()
		);

		/** @var UserVoucher|null $userVoucher */
		$userVoucher = !empty($userVouchers) ? $userVouchers[0] : null;

		if ($userVoucher === null) {
			return;
		}

		$voucherCampaign = $userVoucher->getVoucherCampaign();

		if ($voucherCampaign->getRewardType() === VoucherCampaign::REWARD_TYPE_STATIC_BONUS) {
			$bonusAmount = $voucherCampaign->getBonusAmount();
		} elseif ($voucherCampaign->getRewardType() === VoucherCampaign::REWARD_TYPE_DYNAMIC_BONUS) {
			$totalBudget = $this->voucherFacade->getAvailableBudget($userVoucher);
			$userCommissionAmount = $transaction->getUserCommissionAmount();

			if ($voucherCampaign->getBonusMultiplier() !== null) {
				$bonusAmount = $userCommissionAmount * $voucherCampaign->getBonusMultiplier();
			} else {
				$bonusAmount = $voucherCampaign->getBonusAmount();
			}

			$bonusAmount = min($bonusAmount, $totalBudget);
		}

		if (!isset($bonusAmount)) {
			return;
		}

		$bonusTransaction = $this->transactionManager->createVoucherBonusTransaction(
			$user,
			'Bonus',
			$bonusAmount
		);

		$this->voucherFacade->createUserVoucherTransaction($userVoucher, $bonusTransaction, $transaction);

		if ($voucherCampaign->getIsOneTimeBonus()) {
			$this->voucherFacade->finishUserVoucher($userVoucher);
		} elseif ($voucherCampaign->getRewardType() === VoucherCampaign::REWARD_TYPE_STATIC_BONUS) {
			$this->voucherFacade->finishUserVoucher($userVoucher);
		} elseif ($voucherCampaign->getRewardType() === VoucherCampaign::REWARD_TYPE_DYNAMIC_BONUS) {
			$totalBudget = $this->voucherFacade->getAvailableBudget($userVoucher);
			if ($totalBudget === (float) 0) {
				$this->voucherFacade->finishUserVoucher($userVoucher);
			}
		}
	}

	public function voucherConfirmBonusTransaction(Transaction $transaction)
	{
		if ($transaction->isCommissionOrRefund() === false) {
			return;
		}

		if ($transaction->hasUser() === false) {
			return;
		}

		if ($transaction->isConfirmed() === false) {
			return;
		}

		/** @var UserVoucherTransaction|null $userVoucherTransaction */
		$userVoucherTransaction = $this->voucherFacade->findUserVoucherTransactionByTransaction($transaction);

		if ($userVoucherTransaction === null) {
			return;
		}

		$bonusTransaction = $userVoucherTransaction->getBonusTransaction();

		if ($bonusTransaction->isConfirmed() === true) {
			return;
		}

		/** @var UserVoucher $userVoucher */
		$userVoucher = $userVoucherTransaction->getUserVoucher();

		/** @var VoucherCampaign $voucherCampaign */
		$voucherCampaign = $userVoucher->getVoucherCampaign();

		if ($voucherCampaign->getRewardType() === VoucherCampaign::REWARD_TYPE_DYNAMIC_BONUS) {
			$totalBudget = $this->voucherFacade->getAvailableBudget($userVoucher, $bonusTransaction);
			$userCommissionAmount = $transaction->getUserCommissionAmount();

			if ($voucherCampaign->getBonusMultiplier() !== null) {
				$bonusAmount = $userCommissionAmount * $voucherCampaign->getBonusMultiplier();
			} else {
				$bonusAmount = $voucherCampaign->getBonusAmount();
			}

			$bonusAmount = min($bonusAmount, $totalBudget);
		} else {
			$bonusAmount = $voucherCampaign->getBonusAmount();
		}

		$this->transactionManager->confirmTransaction($bonusTransaction, $bonusAmount, $transaction->getCurrency());
	}

	private function campaignConfirmBonus(Transaction $transaction)
	{
		bdump(1);
		if (!$transaction->isCommissionOrRefund()) {
			Debugger::log($transaction->getId() . ' ### 325a', 'TriggerProcessor__campaign');
			return;
		}

		if (!$transaction->hasUser()) {
			Debugger::log($transaction->getId() . ' ### 325b', 'TriggerProcessor__campaign');
			return;
		}

		if (!$transaction->isConfirmed()) {
			Debugger::log($transaction->getId() . ' ### 325c', 'TriggerProcessor__campaign');
//			return;
		}

		/** @var CampaignTransaction $campaignTransaction */
		$campaignTransaction = $this->campaignFacade->findCampaignTransactionByTransaction($transaction);

		/** @var CampaignSubscription|null $campaignSubscription */
		$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($transaction->getUser());

		if (!$campaignTransaction || !$campaignSubscription || $campaignSubscription->isFinished()) {
			Debugger::log($transaction->getId() . ' ### 336', 'TriggerProcessor__campaign');
			return;
		}

		/** @var Transaction $bonusTransaction */
		$bonusTransaction = $campaignTransaction->getBonusTransaction();

		$amount = $transaction->getAmount() < $bonusTransaction->getAmount() ? $transaction->getAmount() : $bonusTransaction->getAmount();
		bdump("bude potvrzeno s castkou: " . $amount);

		Debugger::log($transaction->getId() . ' ### 346', 'TriggerProcessor__campaign');
		$this->transactionManager->confirmTransaction($bonusTransaction, $amount, $transaction->getCurrency());
	}

	private function campaignConfirmBonusActiveUser(Transaction $transaction)
	{
		if (!$transaction->isCommissionOrRefund() || !$transaction->hasUser() || !$transaction->isConfirmed()) {
			return;
		}

		/** @var CampaignSubscription|null $campaignSubscription */
		$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($transaction->getUser());

		if (!$campaignSubscription || $campaignSubscription->isNewUser() || !$campaignSubscription || $campaignSubscription->isFinished()) {
			return;
		}

		/** @var CampaignTransaction|null $campaignTransaction */
		$campaignTransaction = $this->campaignFacade->findCampaignTransactionForActiveUser($transaction->getUser());

		if (!$campaignTransaction) {
			return;
		}

		$this->transactionManager->confirmTransaction(
			$campaignTransaction->getBonusTransaction()
		);
	}

	private function campaignFinishCampaign(Transaction $transaction)
	{
		bdump(2);
		if (!$transaction->isBonusCampaign() || !$transaction->hasUser()) {
			return;
		}

		$user = $transaction->getUser();

		/** @var CampaignSubscription|null $campaignSubscription */
		$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($user);

		if (!$campaignSubscription || $campaignSubscription->isFinished() || !$campaignSubscription->isValid()) {
			bdump("kampan neni validni");
			return;
		}

		$confirmedBonus = $this->campaignFacade->getUsedBonus($transaction->getUser(), true);
		bdump("confirmed bns: " . $confirmedBonus);
		if ($confirmedBonus >= CampaignSubscription::getBonusForUser($user)) {
			$this->campaignFacade->finishCampaignSubscription($campaignSubscription);
		}
	}

	private function flagProcess(string $type, Transaction $transaction)
	{
		$this->sqlQueryScheduler->updateTransactionProcess(
			$transaction->getId(),
			$type
		);
	}
}
