<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlock;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlockType;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\Repositories\ContentBlockRepository;
use Ka<PERSON>ino\Model\Shops\Repositories\ContentBlockTypeRepository;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Users\Entities\User;

class ContentBlockFacade
{
	/** @var ContentBlockRepository */
	private $contentBlockRepository;

	/** @var ContentBlockManager */
	private $contentBlockManager;

	/** @var ContentBlockTypeRepository */
	private $contentBlockTypeRepository;

	/** @var ContentBlockTypeManager */
	private $contentBlockTypeManager;

	public function __construct(
		ContentBlockRepository $contentBlockRepository,
		ContentBlockManager $contentBlockManager,
		ContentBlockTypeRepository $contentBlockTypeRepository,
		ContentBlockTypeManager $contentBlockTypeManager
	) {
		$this->contentBlockRepository = $contentBlockRepository;
		$this->contentBlockManager = $contentBlockManager;
		$this->contentBlockTypeRepository = $contentBlockTypeRepository;
		$this->contentBlockTypeManager = $contentBlockTypeManager;
	}

	/** @return ContentBlock|object|null */
	public function find(int $id): ?object
	{
		return $this->contentBlockRepository->find($id);
	}

    public function findContentBlockTypeById(int $id): ContentBlockType
    {
        return $this->contentBlockTypeRepository->find($id);
    }

	public function findContentBlockTypesByWebsiteType(string $websiteType, ?string $entity = null)
	{
		return $this->contentBlockTypeRepository->findContentBlockTypesByWebsiteType($websiteType, $entity);
	}

	public function findContentBlockType(string $websiteType, string $type)
	{
		return $this->contentBlockTypeRepository->findOneBy(['websiteType' => $websiteType, 'type' => $type]);
	}

	public function createContentBlock(ContentBlockType $contentBlockType, ?Shop $shop, ?Tag $tag, ?User $user, ?string $heading, ?string $content, bool $generatedByAi = false): Entities\ContentBlock
	{
		return $this->contentBlockManager->createContentBlock($contentBlockType, $shop, $tag, $user, $heading, $content, $generatedByAi);
	}

	public function findContentBlocksByShop(Shop $shop, string $websiteType)
	{
		$contentBlocks = [];

		/** @var ContentBlock $contentBlock */
		foreach ($this->contentBlockRepository->findContentBlocksByShop($shop, $websiteType) as $contentBlock) {
			$contentBlocks[$contentBlock->getType()] = $contentBlock;
		}

		return $contentBlocks;
	}

    public function getContentBlocks(): QueryBuilder
    {
        return $this->contentBlockRepository->getContentBlocks();
    }

    public function findContentBlocksByTag(Tag $tag, string $websiteType)
    {
        $contentBlocks = [];

        /** @var ContentBlock $contentBlock */
        foreach ($this->contentBlockRepository->findContentBlocksByTag($tag, $websiteType) as $contentBlock) {
            $contentBlocks[$contentBlock->getType()] = $contentBlock;
        }

        return $contentBlocks;
    }

	public function saveContentBlock(ContentBlock $contentBlock): ContentBlock
	{
		return $this->contentBlockManager->saveContentBlock($contentBlock);
	}

	public function saveContentBlockType(ContentBlockType $contentBlockType): ContentBlockType
	{
		return $this->contentBlockTypeManager->saveContentBlockType($contentBlockType);
	}

	public function findContentBlockForShopByContentBlockType(Shop $shop, ContentBlockType $contentBlockType)
	{
		return $this->contentBlockRepository->findOneBy(['shop' => $shop, 'contentBlockType' => $contentBlockType]);
	}

	public function findContentBlocksToGenerate(int $limit = 1)
	{
		return $this->contentBlockRepository->findContentBlocksToGenerate($limit);
	}

	public function findFaqContentBlocks(Shop $shop, string $websiteType)
	{
		return $this->contentBlockRepository->findFaqContentBlocks($shop, $websiteType);
	}

    public function findContentBlockByTypeForShop(Shop $shop, ContentBlockType $contentBlockType): ?object
    {
        return $this->contentBlockRepository->findContentBlockByTypeForShop($shop, $contentBlockType);
    }

    public function findContentBlockByTypeForTag(Tag $tag, ContentBlockType $contentBlockType): ?object
    {
        return $this->contentBlockRepository->findContentBlockByTypeForTag($tag, $contentBlockType);
    }

    public function findArchivedContentBlocksByShop(Shop $shop, string $websiteType): array
    {
        $archivedBlocks = $this->contentBlockRepository->findArchivedContentBlocksByShop($shop, $websiteType);

        $result = [];
        foreach ($archivedBlocks as $archivedBlock) {
            $result[$archivedBlock->getType()][] = $archivedBlock;
        }

        return $result;
    }

    public function findArchivedContentBlocksByTag(Tag $tag, string $websiteType): array
    {
        $archivedBlocks = $this->contentBlockRepository->findArchivedContentBlocksByTag($tag, $websiteType);

        $result = [];
        foreach ($archivedBlocks as $archivedBlock) {
            $result[$archivedBlock->getType()][] = $archivedBlock;
        }

        return $result;
    }
}
