<?php

namespace tipli\Model\Shops;

use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopExternalData;
use tipli\Model\Shops\Repositories\ShopExternalDataRepository;

class ShopExternalDataFacade
{
	public function __construct(
		private ShopExternalDataRepository $shopExternalDataRepository,
		private ShopExternalDataManager $shopExternalDataManager,
		private ShopExternalDataProcessor $shopExternalDataProcessor
	) {
	}

	public function find(int $id): ?ShopExternalData
	{
		return $this->shopExternalDataRepository->find($id);
	}

	public function findByShop(Shop $shop): ?ShopExternalData
	{
		return $this->shopExternalDataRepository->findByShop($shop);
	}

	public function findShopsReadyToProcess(int $limit = 5): array
	{
		return $this->shopExternalDataRepository->findShopsReadyToProcess($limit);
	}

	public function createShopExternalData(Shop $shop): ShopExternalData
	{
		return $this->shopExternalDataManager->createShopExternalData($shop);
	}

	public function saveShopExternalData(ShopExternalData $shopExternalData): ShopExternalData
	{
		return $this->shopExternalDataManager->saveShopExternalData($shopExternalData);
	}

	public function processShopExternalData(ShopExternalData $shopExternalData): void
	{
		$shopExternalData = $this->shopExternalDataProcessor->processShopExternalData($shopExternalData);

		$this->saveShopExternalData($shopExternalData);
	}
}
