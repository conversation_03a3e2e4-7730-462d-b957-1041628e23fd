<?php

declare(strict_types=1);

namespace Kaufino\Model\Shops\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Kaufino\Model\Geo\Entities\City;
use Kaufino\Model\Localization\Entities\Localization;
use Nette\Utils\Json;
use Nette\Utils\Strings;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Shops\Repositories\StoreRepository")
 * @ORM\Table(name="kaufino_shops_store")
 */
class Store
{
    /**
     * @var int
     * @ORM\Column(type="integer", nullable=FALSE)
     * @ORM\Id
     * @ORM\GeneratedValue
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
     * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
     */
    protected $localization;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Shops\Entities\Shop", inversedBy="stores")
     * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
     */
    protected $shop;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Geo\Entities\City", inversedBy="stores")
     * @ORM\JoinColumn(name="city_id", referencedColumnName="id")
     */
    protected $city;

    /**
     * @ORM\Column(type="integer", unique=true)
     */
    protected $storeId;

    /**
     * @ORM\Column(type="string")
     */
    protected $name;

    /**
     * @ORM\Column(type="string")
     */
    protected $fullAddress;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $type;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $municipality;

    /**
     * @ORM\Column(type="string")
     */
    protected $street;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $houseNumber;

    /**
     * @ORM\Column(type="integer")
     */
    protected $zipCode;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $lat;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $lng;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $slug;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $openingHours;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private $phoneNumber;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private $email;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private $storeUrl;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private $websiteUrl;

    /**
     * @ORM\Column(type="boolean")
     */
    protected $active = false;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $activatedAt;

    /**
     * @ORM\Column(type="boolean")
     */
    protected $activeOferto = false;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $activatedOfertoAt;

    /**
     * @ORM\Column(type="datetime")
     */
    private $updatedAt;

    /**
     * @ORM\Column(type="datetime")
     */
    private $createdAt;

    public function __construct(
        Localization $localization,
        Shop         $shop,
        City         $city,
        string       $name,
        string       $fullAddress,
        ?int         $storeId,
        ?string      $type,
        ?string      $municipality,
        ?string      $street,
        ?string      $houseNumber,
        ?string      $zipCode,
        ?string      $lat,
        ?string      $lng,
        ?string      $slug
    )
    {
        $this->localization = $localization;
        $this->shop = $shop;
        $this->city = $city;
        $this->name = $name;
        $this->fullAddress = $fullAddress;
        $this->storeId = $storeId;
        $this->type = $type;
        $this->municipality = $municipality;
        $this->street = $street;
        $this->houseNumber = $houseNumber;
        $this->zipCode = $zipCode;
        $this->lat = $lat;
        $this->lng = $lng;
        $this->slug = $slug;
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function update(): void
    {
        $this->updatedAt = new DateTime();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCity(): City
    {
        return $this->city;
    }

    public function getOpeningHours(): ?array
    {
        if ($this->openingHours === null) {
            return null;
        }

        return Json::decode($this->openingHours, Json::FORCE_ARRAY);
    }

    public function getDays(): array
    {
        return [
            'MO' => 1,
            'TU' => 2,
            'WE' => 3,
            'TH' => 4,
            'FR' => 5,
            'SA' => 6,
            'SU' => 7,
        ];
    }

    public function isOpen(): bool
    {
        $todayShortcut = Strings::upper(substr(date('D'), 0, 2));

        $openingHours = $this->getOpeningHours();

        if (!isset($openingHours[$todayShortcut])) {
            return false;
        }

        $now = date('H:i');

        foreach ($openingHours[$todayShortcut] as $todayOpeningHours) {
            $today = [
                'open' => $todayOpeningHours['open'],
                'close' => $todayOpeningHours['close'],
            ];

            if ($now >= $today['open'] && $now <= $today['close']) {
                return true;
            }
        }

        return false;
    }

    public function getAddress(): string
    {
        return $this->street . ' ' . $this->houseNumber . ', ' . $this->zipCode . ' ' . $this->city->getName();
    }

    public function getMapUrl()
    {

    }

    public function getShop(): Shop
    {
        return $this->shop;
    }

    public function getLat()
    {
        return $this->lat;
    }

    public function getLng()
    {
        return $this->lng;
    }

    public function getLocalization(): Localization
    {
        return $this->localization;
    }

    public function setOpeningHours(?string $openingHours): void
    {
        $this->openingHours = $openingHours;
    }

    public function setPhoneNumber(?string $phoneNumber): void
    {
        $this->phoneNumber = $phoneNumber;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setStoreUrl(?string $storeUrl): void
    {
        $this->storeUrl = $storeUrl;
    }

    public function getStoreUrl(): ?string
    {
        return $this->storeUrl;
    }

    public function setWebsiteUrl(?string $websiteUrl): void
    {
        $this->websiteUrl = $websiteUrl;
    }

    public function getWebsiteUrl(): ?string
    {
        return $this->websiteUrl;
    }

    public function getFullAddress(): string
    {
        return preg_replace('/,([^,]+)$/', '', $this->fullAddress);
    }

    public function setFullAddress(string $fullAddress): void
    {
        $this->fullAddress = $fullAddress;
    }

    public function getFullStreet(): string
    {
        return $this->street . ' ' . $this->houseNumber;
    }

    public function setSlug(string $slug): void
    {
        $this->slug = $slug;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function isActiveOferto(): bool
    {
        return $this->activeOferto;
    }

    public function getStreet()
    {
        return $this->street;
    }

    public function activate()
    {
        $this->active = true;
        $this->activatedAt = new DateTime();
    }

    public function deactivate()
    {
        $this->active = false;
        $this->activatedAt = null;
    }

    public function activateOferto()
    {
        $this->activeOferto = true;
        $this->activatedOfertoAt = new DateTime();
    }

    public function deactivateOferto()
    {
        $this->activeOferto = false;
        $this->activatedOfertoAt = null;
    }

    public function getMunicipality(): ?string
    {
        return $this->municipality;
    }

    public function getHouseNumber(): ?string
    {
        return $this->houseNumber;
    }
}
