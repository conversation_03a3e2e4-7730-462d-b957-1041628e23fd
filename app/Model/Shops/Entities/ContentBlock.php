<?php

declare(strict_types=1);

namespace Kaufino\Model\Shops\Entities;

use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Users\Entities\User;
use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Strings;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Shops\Repositories\ContentBlockRepository")
 * @ORM\Table(name="kaufino_shops_content_block")
 */
class ContentBlock
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=false)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="Shop", inversedBy="contentBlocks")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private $shop;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Tags\Entities\Tag", inversedBy="contentBlocks")
     * @ORM\JoinColumn(name="tag_id", referencedColumnName="id")
     */
    private $tag;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $createdByUser;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="updated_user_id", referencedColumnName="id")
	 */
	private $updatedByUser;

	/**
	 * @ORM\ManyToOne(targetEntity="ContentBlockType")
	 * @ORM\JoinColumn(name="content_block_type_id", referencedColumnName="id")
	 */
	private $contentBlockType;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	private $websiteType;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $heading;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $content;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $newContent;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $generatedContent;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $generatedByAi;

	/**
	 * @ORM\Column(type="datetime", nullable=false)
	 */
	private $updatedAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?\DateTime $archivedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=false)
	 */
	private $createdAt;

	public function __construct(ContentBlockType $contentBlockType, ?Shop $shop, ?Tag $tag, ?User $user, ?string $heading, ?string $content, bool $generatedByAi = false)
	{
		$this->shop = $shop;
        $this->tag = $tag;
		$this->contentBlockType = $contentBlockType;
		$this->websiteType = $contentBlockType->getWebsiteType();
		$this->createdByUser = $user;
		$this->updatedByUser = $user;
		$this->heading = $heading;
		$this->content = $content;
		$this->generatedByAi = $generatedByAi;

		$this->updatedAt = new \DateTime();
		$this->createdAt = new \DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getContentBlockType(): ContentBlockType
	{
		return $this->contentBlockType;
	}

	public function getType(): string
	{
		return $this->contentBlockType->getType();
	}

	public function getWebsiteType()
	{
		return $this->websiteType;
	}

	public function getHeading(): ?string
	{
		return $this->heading;
	}

	public function getContent(): ?string
	{
		return $this->content;
	}

	public function getPriority(): int
	{
		return $this->priority;
	}

	public function isActive(): bool
	{
		return $this->active;
	}

	public function setUpdatedByUser(User $updatedByUser): void
	{
		$this->updatedByUser = $updatedByUser;
	}

	public function setHeading(?string $heading): void
	{
		$this->heading = $heading;
	}

	public function setContent(?string $content): void
	{
		$this->content = $content;
	}

	public function update(User $user)
	{
		$this->updatedAt = new \DateTime();
		$this->updatedByUser = $user;
	}

	public function isGeneratedByAi(): bool
	{
		return $this->generatedByAi;
	}

	public function setGeneratedByAi(bool $generatedByAi): void
	{
		$this->generatedByAi = $generatedByAi;
	}

	/**
	 * @return ?string
	 */
	public function getGeneratedContent(): ?string
	{
		return $this->generatedContent;
	}

	/**
	 * @param ?string $generatedContent
	 */
	public function setGeneratedContent(?string $generatedContent): void
	{
		$this->generatedContent = $generatedContent;
	}

	public function getShop(): ?Shop
	{
		return $this->shop;
	}

    public function getTag(): ?Tag
    {
        return $this->tag;
    }

    public function setNewContent(?string $newContent): void
    {
        $this->newContent = $newContent;
    }

    public function getNewContent()
    {
        return $this->newContent;
    }

    public function archive()
    {
        $this->archivedAt = new \DateTime();
    }

    public function isArchived(): bool
    {
        return $this->archivedAt !== null;
    }

    public function isFaqType(): bool
    {
        return Strings::contains($this->getType(), 'faq_');
    }
}