<?php

namespace tipli\Model\Shops;

use Nette\Utils\Strings;
use Nette\Utils\Validators;
use tipli\Model\Shops\Entities\ShopExternalData;
use tipli\Model\WebCrawler\Exceptions\WebCrawlerException;
use tipli\Model\WebCrawler\WebCrawlerClient;

class ShopExternalDataProcessor
{
	public function __construct(
		private WebCrawlerClient $webCrawlerClient
	) {
	}

	public function processShopExternalData(ShopExternalData $shopExternalData): ShopExternalData
	{
		try {
			$domain = $shopExternalData->getShop()->getDomain();

			if ($domain === null || empty(trim($domain)) === true) {
				throw new WebCrawlerException('Unrecognized domain.');
			}

			if (Validators::isUrl($domain) === false) {
				throw new WebCrawlerException('Invalid URL');
			}

			$webResult = $this->webCrawlerClient->get($domain);

			if (Strings::length($webResult->getTitle()) > 250) {
				throw new WebCrawlerException('Title too long.');
			}

			$shopExternalData->setTitle($webResult->getTitle());
			$shopExternalData->setDescription($webResult->getDescription());
			$shopExternalData->setKeywords($webResult->getKeywords());

			$shopExternalData->process();
		} catch (WebCrawlerException | \Exception $e) {
			$shopExternalData->fail($e->getMessage());
		}

		return $shopExternalData;
	}
}
