<?php

namespace tipli\Model\Shops;

use DateTime;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Nette\Utils\ArrayHash;
use tipli\Model\Doctrine\EntityManager;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Repositories\PartnerSystemRepository;
use tipli\Model\Shops\Entities\CashbackCorrectionRule;
use tipli\Model\Shops\Entities\DescriptionBlock;
use tipli\Model\Shops\Entities\ForeignShop;
use tipli\Model\Shops\Entities\RelatedShop;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopChange;
use tipli\Model\Shops\Entities\ShopNote;
use tipli\Model\Shops\Entities\ShopQuestion;
use tipli\Model\Shops\Entities\ShopReport;
use tipli\Model\Shops\Events\ForeignShopUpdatedEvent;
use tipli\Model\Shops\Repositories\CashbackCorrectionRuleRepository;
use tipli\Model\Shops\Repositories\DescriptionBlockRepository;
use tipli\Model\Shops\Repositories\ForeignShopRepository;
use tipli\Model\Shops\Repositories\RedirectionRepository;
use tipli\Model\Shops\Repositories\RelatedShopRepository;
use tipli\Model\Shops\Repositories\ShopChangeRepository;
use tipli\Model\Shops\Repositories\ShopNoteRepository;
use tipli\Model\Shops\Repositories\ShopPartnerSystemRepository;
use tipli\Model\Shops\Repositories\ShopPauseLogRepository;
use tipli\Model\Shops\Repositories\ShopQuestionRepository;
use tipli\Model\Shops\Repositories\ShopReportRepository;
use tipli\Model\Shops\Repositories\ShopRepository;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;
use tipli\Shops\Queries\ShopsQuery;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;
use Tracy\Debugger;

class ShopFacade
{
	/** @var EntityManager */
	private $em;

	/** @var ShopRepository */
	private $shopRepository;

	/** @var ShopManager */
	private $shopManager;

	/** @var TagFacade */
	private $tagFacade;

	/** @var RelatedShopManager */
	private $relatedShopManager;

	/** @var ForeignShopRepository */
	private $foreignShopRepository;

	/** @var RelatedShopRepository */
	private $relatedShopRepository;

	/** @var ShopPartnerSystemRepository */
	private $shopPartnerSystemRepository;

	/** @var Cache */
	private $cache;

	/** @var RedirectionRepository */
	private $redirectionRepository;

	/** @var DescriptionBlockRepository */
	private $descriptionBlockRepository;

	/** @var DescriptionBlockManager */
	private $descriptionBlockManager;

	/** @var ShopPauseLogManager */
	private $shopPauseLogManager;

	/** @var ShopPauseLogRepository */
	private $shopPauseLogRepository;

	/** @var CashbackCorrectionRuleRepository */
	private $cashbackCorrectionRuleRepository;

	/** @var ShopCashbackCorrectionRuleManager */
	private $shopCashbackCorrectionRuleManager;

	/** @var ShopChangeManager */
	private $shopChangeManager;

	/** @var ShopChangeRepository */
	private $shopChangeRepository;

	/** @var PartnerSystemRepository */
	private $partnerSystemRepository;

	/** @var ShopReportManager */
	private $shopReportManager;

	/** @var ShopReportRepository */
	private $shopReportRepository;

	/** @var ShopReportCalculator */
	private $shopReportCalculator;

	/** @var ShopNoteRepository */
	private $shopNoteRepository;

	/** @var ShopQuestionRepository */
	private $shopQuestionRepository;

	/** @var ShopQuestionManager */
	private $shopQuestionManager;

	/** @var EventDispatcher */
	private $eventDispatcher;

	public function __construct(EntityManager $em, ShopRepository $shopRepository, ShopManager $shopManager, TagFacade $tagFacade, RelatedShopManager $relatedShopManager, RelatedShopRepository $relatedShopRepository, ShopPartnerSystemRepository $shopPartnerSystemRepository, Storage $storage, RedirectionRepository $redirectionRepository, DescriptionBlockRepository $descriptionBlockRepository, DescriptionBlockManager $descriptionBlockManager, ShopPauseLogManager $shopPauseLogManager, ShopPauseLogRepository $shopPauseLogRepository, CashbackCorrectionRuleRepository $cashbackCorrectionRuleRepository, ShopCashbackCorrectionRuleManager $shopCashbackCorrectionRuleManager, ShopChangeManager $shopChangeManager, ShopChangeRepository $shopChangeRepository, PartnerSystemRepository $partnerSystemRepository, ShopReportManager $shopReportManager, ShopReportRepository $shopReportRepository, ShopReportCalculator $shopReportCalculator, ShopNoteRepository $shopNoteRepository, ShopQuestionRepository $shopQuestionRepository, ShopQuestionManager $shopQuestionManager, ForeignShopRepository $foreignShopRepository, EventDispatcher $eventDispatcher)
	{
		$this->em = $em;
		$this->shopRepository = $shopRepository;
		$this->shopManager = $shopManager;
		$this->tagFacade = $tagFacade;
		$this->relatedShopManager = $relatedShopManager;
		$this->relatedShopRepository = $relatedShopRepository;
		$this->shopPartnerSystemRepository = $shopPartnerSystemRepository;
		$this->cache = new Cache($storage, self::class);
		$this->redirectionRepository = $redirectionRepository;
		$this->descriptionBlockRepository = $descriptionBlockRepository;
		$this->descriptionBlockManager = $descriptionBlockManager;
		$this->shopPauseLogManager = $shopPauseLogManager;
		$this->shopPauseLogRepository = $shopPauseLogRepository;
		$this->cashbackCorrectionRuleRepository = $cashbackCorrectionRuleRepository;
		$this->shopCashbackCorrectionRuleManager = $shopCashbackCorrectionRuleManager;
		$this->shopChangeManager = $shopChangeManager;
		$this->shopChangeRepository = $shopChangeRepository;
		$this->partnerSystemRepository = $partnerSystemRepository;
		$this->shopReportManager = $shopReportManager;
		$this->shopReportRepository = $shopReportRepository;
		$this->shopReportCalculator = $shopReportCalculator;
		$this->shopNoteRepository = $shopNoteRepository;
		$this->foreignShopRepository = $foreignShopRepository;
		$this->shopQuestionRepository = $shopQuestionRepository;
		$this->shopQuestionManager = $shopQuestionManager;
		$this->eventDispatcher = $eventDispatcher;
	}

	public function fetch(ShopsQuery $query)
	{
		return $this->shopRepository->fetch($query);
	}

	public function scheduleUpdateIndex(Shop $shop): Shop
	{
		$shop->indexUpdated();
		return $this->shopManager->saveShop($shop, false);
	}

	public function scheduleShopChecker(Shop $shop, ?DateTime $scheduleAt = null): Shop
	{
		$shop->scheduleShopChecker($scheduleAt);
		return $this->shopManager->saveShop($shop, false);
	}

	public function scheduleTransactionChecker(Shop $shop, ?DateTime $scheduleAt = null): Shop
	{
		$shop->scheduleTransactionChecker($scheduleAt);
		return $this->shopManager->saveShop($shop, false);
	}

	public function saveShop(Shop $shop): Shop
	{
		return $this->shopManager->saveShop($shop);
	}

	public function saveShops(array $shops): void
	{
		$this->shopManager->saveShops($shops);
	}

	public function getShops(Localization $localization = null, $sortByLocale = true): QueryBuilder
	{
		return $this->shopRepository->getShops($localization, $sortByLocale);
	}

	public function createShopsQuery(Localization $localization = null)
	{
		$query = new ShopsQuery();

		if ($localization) {
			$query->withLocalization($localization);
		}

		return $query;
	}

	public function createShopsWithTagQuery(Localization $localization = null, Tag $tag = null)
	{
		$query = new ShopsQuery();

		if ($localization) {
			$query->withLocalization($localization);
		}

		if ($tag) {
			$query->withTag($tag);
		}

		return $query;
	}

	public function getPublishedShops(Localization $localization = null)
	{
		return $this->shopRepository->getPublishedShops($localization);
	}

	public function getActiveAndVisibleShops(Localization $localization = null)
	{
		return $this->shopRepository->getActiveAndVisibleShops($localization);
	}

	public function getCountOfActiveAndVisibleShops(Localization $localization = null): int
	{
		return $this->shopRepository->getCountOfActiveAndVisibleShops($localization);
	}

	public function getPublishedShopsByTag(Tag $tag)
	{
		return $this->shopRepository->getPublishedShopsByTags($this->tagFacade->getVisibleTagsTreeLeavesIds($tag));
	}

	public function getPublishedShopsByTags($tags, $onlyWithCashbackAllowed = false)
	{
		return $this->shopRepository->getPublishedShopsByTags($tags, $onlyWithCashbackAllowed);
	}

	public function findTopShops(int $maxResults = 3, Localization $localization = null, $optimized = false, ?array $exceptShops = null, $onlyWithCashbackAllowed = false, $exceptPaused = false, bool $onlyWithoutCashback = false, bool $withoutGamble = false)
	{
		$query = (new ShopsQuery())
			->onlyActive()
			->withLocalization($localization)
			->onlyPublished()
			->sortTop();

		if ($optimized) {
			$query->optimized();
		}

		if ($exceptShops) {
			$query->exceptShops($exceptShops);
		}

		if ($exceptPaused) {
			$query->exceptPaused();
		}

		if ($onlyWithCashbackAllowed) {
			$query->onlyWithCashbackAllowed();
		} elseif ($onlyWithoutCashback) {
			$query->onlyWithCashbackDisabled();
		}

		if ($withoutGamble) {
			$query->exceptGamble();
		}

		return $this->shopRepository->fetch($query)->applyPaging(0, $maxResults);
	}

	public function findNewestShops($maxResults = 3, Localization $localization = null, $optimized = false, $onlyWithCashbackAllowed = false)
	{
		$query = (new ShopsQuery())
			->withLocalization($localization)
			->onlyPublished()
			->onlyVisible()
			->sortNewest();

		if ($onlyWithCashbackAllowed) {
			$query->onlyWithCashbackAllowed();
		}

		if ($optimized) {
			$query->optimized();
		}

		return $this->shopRepository->fetch($query)->applyPaging(0, $maxResults);
	}

	public function findShopsForAddonSerp($localization)
	{
		$query = (new ShopsQuery())
			->withLocalization($localization)
			->exceptPaused()
			->onlyForAddon();

		return $this->shopRepository->fetch($query);
	}

	public function findShopListForAddon($localization)
	{
		return $this->getShopsForAddon($localization)
			->getQuery()
			->getArrayResult();
	}

	public function getShopsForAddon(Localization $localization)
	{
		return $this->shopRepository->getShopsForAddon($localization);
	}

	public function findByIds(array $ids): array
	{
		return $this->shopRepository->findByIds($ids);
	}

	public function findShopsByIds(array $ids, $forAddon = false, $sortByField = false, ?bool $onlyWithCashbackAllowed = false)
	{
		$query = (new ShopsQuery())
			->in($ids, $sortByField);

		if ($forAddon) {
			$query->onlyForNewAddon();
		}

		if ($onlyWithCashbackAllowed) {
			$query->onlyWithCashbackAllowed();
		}

		return $this->shopRepository->fetch($query);
	}

	public function findSortedShops(Localization $localization, string $sort, ?int $offset = null, ?int $limit = null): array
	{
		return $this->shopRepository->findSortedShops($localization, $sort, $offset, $limit);
	}

	public function getShopsSelectList(Localization $localization = null, $onlyWithCashbackAllowed = true)
	{
		$cacheKey = (($localization ? $localization->getLocale() : '') . '-' . ($onlyWithCashbackAllowed ? 1 : 0));

		if ($shops = $this->cache->load($cacheKey)) {
			return $shops;
		}

		$shops = [];

		$query = $this->createShopsQuery($localization);
		$query->optimized();
		$query->sortAlphabetically();

		if ($onlyWithCashbackAllowed) {
			$query->onlyWithCashbackAllowed();
		}

		/** @var Shop $shop */
		foreach ($this->fetch($query) as $shop) {
			$name = !$localization ? $shop->getLocalization()->getLocale() . ' ' : '';
			$name .= $shop->getName();

			if (!$shop->isActive()) {
				$name .= ' (neaktivní)';
			}
			if (!$shop->isPublished()) {
				$name .= ' (nepublikován)';
			}
			if (!$shop->isVisible()) {
				$name .= ' (skrytý!)';
			}

			$shops[$shop->getId()] = $name;
		}

		$this->cache->save($cacheKey, $shops, [Cache::EXPIRATION => '30 minutes']);

		return $shops;
	}

	/**
	 * @param int $id
	 * @return null|Shop|object
	 */
	public function find($id)
	{
		return $this->shopRepository->find($id);
	}

	public function findPairs(Localization $localization = null)
	{
		$criteria = $localization ? ['localization' => $localization] : [];
		return $this->shopRepository->findPairs($criteria, 'name', 'name');
	}

	public function findBy($criteria, $orderBy = null, $limit = null, $offset = null)
	{
		return $this->shopRepository->findBy($criteria, $orderBy, $limit, $offset);
	}

	public function findBySlug($slug, Localization $localization = null)
	{
		return $this->shopRepository->findBySlug($slug, $localization);
	}

	public function findPartnerSystemsShops()
	{
		return $this->shopPartnerSystemRepository->findAll();
	}

	public function findByPartnerSystem(PartnerSystem $partnerSystem, $partnerSystemKey = null, Localization $localization = null)
	{
		return $this->shopPartnerSystemRepository->findShopByPartnerSystem($partnerSystem, $partnerSystemKey, $localization);
	}

	public function findAll()
	{
		return $this->shopRepository->findAll();
	}

	public function findShopChange($id): ?ShopChange
	{
		return $this->shopChangeRepository->find($id);
	}

	public function getShopRedirections()
	{
		return $this->redirectionRepository->getRedirections();
	}

	/**
	 * @param Shop $shop
	 * @param DateTime $redirectedSince
	 * @param DateTime $redirectedTill
	 * @param array<string> $excludeIds
	 * @return User[]
	 * @throws Exception
	 */
	public function findUsersRedirectedInPeriod(Shop $shop, DateTime $redirectedSince, DateTime $redirectedTill, array $excludeIds)
	{
		return $this->redirectionRepository->findUsersRedirectedInPeriod($shop, $redirectedSince, $redirectedTill, $excludeIds);
	}

	public function findShopsWhereUserHasRedirection(User $user, DateTime $dateFrom = null, $sortByPriority = false)
	{
		return $this->shopRepository->findShopsWhereUserHasRedirection($user, $dateFrom, $sortByPriority);
	}

	public function findShopsFromUserLastRedirections(User $user, array $exceptShops = null, int $maxResults = 10)
	{
		return $this->shopRepository->findShopsFromUserLastRedirections($user, $exceptShops, $maxResults);
	}

	public function findLastUserRedirectionInShop(User $user, Shop $shop, DateTime $redirectedSince, DateTime $redirectedTill)
	{
		return $this->redirectionRepository->findLastUserRedirectionInShop($user, $shop, $redirectedSince, $redirectedTill);
	}

	public function findShopsFromUserLastTransactions(User $user, int $maxResults = 10)
	{
		return $this->shopRepository->findShopsFromUserLastTransactions($user, $maxResults);
	}

	public function findUserFirstShopRedirection(User $user)
	{
		return $this->redirectionRepository->findUserFirstShopRedirection($user);
	}

	public function findShopRedirection($id)
	{
		return $this->redirectionRepository->find($id);
	}

	public function findShopRedirectionByUniqueId($uniqueId)
	{
		return $this->redirectionRepository->findOneBy(['uniqueId' => $uniqueId]);
	}

	public function findCountOfShops(Localization $localization = null, bool $onlyCashback = false)
	{
		return $this->shopRepository->findCountOfShops($localization, $onlyCashback);
	}

	public function findTotalCountOfShops(Localization $localization = null)
	{
		return $this->shopRepository->findTotalCountOfShops($localization);
	}

	public function getCountOfShopsWithoutDeals(Localization $localization = null)
	{
		return $this->shopRepository->getCountOfShopsWithoutDeals($localization);
	}

	public function getCountOfShopsWithoutCoupons(Localization $localization = null)
	{
		return $this->shopRepository->getCountOfShopsWithoutCoupons($localization);
	}

	public function createShop(Localization $localization, $name, $slug, $priority, DateTime $publishedAt, $active, $author = null, $visible = true)
	{
		return $this->shopManager->createShop($localization, $name, $slug, $priority, $publishedAt, $active, $author, $visible);
	}

	public function addRelatedShop(Shop $parentShop, Shop $relatedShop, $priority)
	{
		$this->relatedShopManager->addRelatedShop($parentShop, $relatedShop, $priority);
	}

	public function addForeignShop(Shop $parentShop, Shop $foreignShop, $priority)
	{
		$foreignShop = new ForeignShop($parentShop, $foreignShop, $priority);

		$this->em->persist($foreignShop);
		$this->em->flush($foreignShop);
		$this->em->refresh($parentShop);

		$this->eventDispatcher->dispatch(
			new ForeignShopUpdatedEvent($foreignShop)
		);
	}

	public function assignPartnerSystemToShop(PartnerSystem $partnerSystem, Shop $shop, $key = null)
	{
		return $this->shopManager->assignPartnerSystemToShop($partnerSystem, $shop, $key);
	}

	public function unAssignPartnerSystemToShop(Entities\PartnerSystem $partnerSystem)
	{
		$this->shopManager->unAssignPartnerSystemToShop($partnerSystem);
	}

	public function removeRelatedShop(RelatedShop $relatedShop)
	{
		$this->relatedShopManager->removeRelatedShop($relatedShop);
	}

	public function removeForeignShop(ForeignShop $foreignShop)
	{
		$this->em->remove($foreignShop);
		$this->em->flush($foreignShop);

		$this->eventDispatcher->dispatch(
			new ForeignShopUpdatedEvent($foreignShop)
		);
	}

	public function removeShopCover(Shop $shop)
	{
		$shop->setCover(null);
		$this->em->flush($shop);
	}

	public function removeShopMobileCover(Shop $shop)
	{
		$shop->setMobileCover(null);
		$this->em->flush($shop);
	}

	public function findShopPartnerSystem($id)
	{
		return $this->shopPartnerSystemRepository->find($id);
	}

	public function findRelatedShop($id)
	{
		return $this->relatedShopRepository->find($id);
	}

	public function hasShopRelatedShop(Shop $shop, Shop $relatedShop): bool
	{
		$ids = [];
		/** @var RelatedShop $related */
		bdump($this->findRelatedShops($shop));
		foreach ($this->findRelatedShops($shop) as $related) {
			$ids[] = $related->getRelatedShop()->getId();
		}

		return in_array($relatedShop->getId(), $ids);
	}

	public function findForeignShop($id)
	{
		return $this->foreignShopRepository->find($id);
	}

	public function getShopPartnerSystems(Shop $shop)
	{
		return $this->shopPartnerSystemRepository->getShopPartnerSystems($shop);
	}

	public function verifySeo(Shop $shop)
	{
		$shop->verifySeo();

		$this->shopManager->saveShop($shop);
	}

	public function findShopByDomain(Localization $localization, $shopDomain): ?Shop
	{
		return $this->shopRepository->findShopByDomain($localization, $shopDomain);
	}

	public function findShopsByDomain($shopDomain)
	{
		return $this->shopRepository->findShopsByDomain($shopDomain);
	}

	public function findShopsByTag(Tag $tag): array
	{
		return $this->shopRepository->findShopsBytag($tag);
	}

	public function findGambleShops(): array
	{
		$tag = $this->tagFacade->find(Tag::GAMBLE_TAG_ID);

		if ($tag === null) {
			return [];
		}

		return $this->findShopsByTag($tag);
	}

	public function findPublishedShopsByTag(Tag $tag, bool $onlyCashback = false, int $maxResults = 12, ?Shop $exceptShop = null)
	{
		return $this->shopRepository->findPublishedShopsByTag($tag, $onlyCashback, $maxResults, $exceptShop);
	}

	public function findCountOfPublishedShopsByTag(Tag $tag, bool $onlyCashback = false)
	{
		return $this->shopRepository->findCountOfPublishedShopsByTag($tag, $onlyCashback);
	}

	public function findPairsLocalizationShop()
	{
		$data = [];

		$query = $this->createShopsQuery();
		$query->sortAlphabetically();
		$query->onlyWithCashbackAllowed();

		$result = $this->shopRepository->fetch($query);

		foreach ($result as $shop) {
			$data[$shop->getLocalization()->getId()][$shop->getId()] = $shop->getName();
		}

		return $data;
	}

	public function findShopByPartnerSystemKey(string $partnerSystemKey, ?PartnerSystem $partnerSystem = null): ?Shop
	{
		// $partnerSystem = $this->shopPartnerSystemRepository->findOneBy(['partnerSystemKey' => $partnerSystemKey]);

		$shopPartnerSystem = $this->shopPartnerSystemRepository->getShopPartnerSystems()
			->andWhere('sp.partnerSystemKey = :partnerSystemKey')
			->setParameter('partnerSystemKey', $partnerSystemKey);

		if ($partnerSystem) {
			$shopPartnerSystem->andWhere('sp.partnerSystem = :partnerSystem')
				->setParameter('partnerSystem', $partnerSystem);
		}

		if ($shopPartnerSystem) {
			$shopPartnerSystem = $shopPartnerSystem->getQuery()
				->setMaxResults(1)
				->getOneOrNullResult();

			if ($shopPartnerSystem) {
				return $shopPartnerSystem->getShop();
			}
		}

		return null;
	}

	public function findShopsByPartnerSystemKey(string|int $partnerSystemKey, ?PartnerSystem $partnerSystem = null): array
	{
		return $this->shopPartnerSystemRepository->findShopsByPartnerSystemKey($partnerSystemKey, $partnerSystem);
	}

	public function searchShops(Localization $localization = null, $term): ShopsQuery
	{
		return $this->shopRepository->searchShops($this->createShopsQuery($localization), $term);
	}

	public function findShopsWithActiveBanner(Localization $localization)
	{
		return $this->shopRepository->findShopsWithActiveBanner($this->createShopsQuery($localization));
	}

	public function getShopDescriptionBlocks(Shop $shop)
	{
		return $this->descriptionBlockRepository->getBlocks($shop);
	}

	public function createDescriptionBlock(Shop $shop, $type, $description, $title = null, $priority = 0)
	{
		return $this->descriptionBlockManager->createDescriptionBlock($shop, $type, $description, $title, $priority);
	}

	public function saveDescriptionBlock(DescriptionBlock $descriptionBlock)
	{
		return $this->descriptionBlockManager->saveDescriptionBlock($descriptionBlock);
	}

	/**
	 * @param int $id
	 * @return object|null|DescriptionBlock
	 */
	public function findDescriptionBlock($id)
	{
		return $this->descriptionBlockRepository->find($id);
	}

	public function removeDescriptionBlock(DescriptionBlock $descriptionBlock)
	{
		$this->shopManager->removeDescriptionBlock($descriptionBlock);
	}

	public function findShopQuestion($id)
	{
		return $this->shopQuestionRepository->find($id);
	}

	public function getShopQuestions(Shop $shop)
	{
		return $this->shopQuestionRepository->getQuestions($shop);
	}

	public function removeShopQuestion(ShopQuestion $shopQuestion): void
	{
		$shopQuestion->getShop()->getShopQuestions()->removeElement($shopQuestion);
		$this->shopManager->removeShopQuestion($shopQuestion);
	}

	public function createShopQuestion(Shop $shop, User $user, string $question, string $answer)
	{
		return $this->shopQuestionManager->createShopQuestion($shop, $user, $question, $answer);
	}

	public function saveShopQuestion(ShopQuestion $shopQuestion)
	{
		return $this->shopQuestionManager->saveShopQuestion($shopQuestion);
	}

	/**
	 * @param Shop $shop
	 * @param User $admin
	 * @param string $action
	 * @param DateTime|null $pauseAt
	 * @param string|null $reason
	 * @return Entities\ShopPauseLog
	 */
	public function createShopPauseLog(Shop $shop, User $admin, string $action, ?DateTime $pauseAt = null, ?string $reason = null)
	{
		return $this->shopPauseLogManager->createShopPauseLog($shop, $admin, $action, $pauseAt, $reason);
	}

	public function getShopPauseLog()
	{
		return $this->shopPauseLogRepository->getShopPauseLog();
	}

	public function processProductData(Shop $shop)
	{
		$shop->getShopProductData()->process();
		$this->saveShop($shop);
	}

	public function findShopsToProcessProducts()
	{
		return $this->shopRepository->findShopsToProcessProducts();
	}

	public function findShopsWithProductFeed(Localization $localization)
	{
		return $this->shopRepository->findShopsWithProductFeed($localization);
	}

	public function getShopsWithProductFeed(Localization $localization)
	{
		return $this->shopRepository->getShopsWithProductFeed($localization);
	}

	public function findShopPartnerSystemKey(Shop $shop, PartnerSystem $partnerSystem)
	{
		return $this->shopPartnerSystemRepository->findShopPartnerSystemKey($shop, $partnerSystem);
	}

	public function findRelatedShops(Shop $shop, bool $onlyWithCashbackAllowed = false, ?int $limit = null)
	{
		return $this->relatedShopRepository->findRelatedShops($shop, $onlyWithCashbackAllowed, $limit);
	}

	public function saveRelatedShop(RelatedShop $relatedShop): void
	{
		$this->relatedShopManager->saveRelatedShop($relatedShop);
	}

	public function findTopShopsWithProducts(Localization $localization, ?int $limit = null)
	{
		return $this->shopRepository->findTopShopsWithProducts($localization, $limit);
	}

	public function getShopCashbackCorrectionRules(?Shop $shop)
	{
		return $this->cashbackCorrectionRuleRepository->getCashbackCorrectionRules($shop);
	}

	public function findShopCashbackCorrectionRule($id)
	{
		return $this->cashbackCorrectionRuleRepository->find($id);
	}

	public function removeShopCashbackCorrectionRule(CashbackCorrectionRule $cashbackCorrectionRule)
	{
		$this->shopCashbackCorrectionRuleManager->removeCashbackCorretionRule($cashbackCorrectionRule);
	}

	public function createCashbackCorrectionRule(Shop $shop, DateTime $validSince, DateTime $validTill, float $cashbackFrom, float $cashbackTo, float $multiplicator, ?string $name): CashbackCorrectionRule
	{
		return $this->shopCashbackCorrectionRuleManager->createCashbackCorrectionRule($shop, $validSince, $validTill, $cashbackFrom, $cashbackTo, $multiplicator, $name);
	}

	public function createShopChange(Shop $shop, User $user, string $change, $oldValue, $newValue): ShopChange
	{
		return $this->shopChangeManager->createShopChange($shop, $user, $change, $oldValue, $newValue);
	}

	public function createShopChanges(Shop $shop, User $user, ArrayHash $values): void
	{
		$changes = [];

		if (isset($values->partnerSystemUrl) && $shop->getPartnerSystemRedirectUrl() != $values->partnerSystemUrl) {
			$changes['partnerSystemUrl'] = [
				'old' => $shop->getPartnerSystemRedirectUrl(),
				'new' => $values->partnerSystemUrl,
			];
		}

		if ($shop->getPartnerSystem() && $values->partnerSystemId && $shop->getPartnerSystem()->getId() !== $values->partnerSystemId) {
			/** @var Entities\PartnerSystem $shopPartnerSystem */
			$partnerSystem = $this->partnerSystemRepository->find($values->partnerSystemId);

			$changes['partnerSystem'] = [
				'old' => $shop->getPartnerSystem()->getName() . ' (' . $shop->getPartnerSystem()->getId() . ')',
				'new' => $partnerSystem->getName() . ' (' . $partnerSystem->getId() . ')',
			];
		}

		if ($shop->getMonetization() !== $values->monetization) {
			$changes['monetization'] = [
				'old' => $shop->getMonetization(),
				'new' => $values->monetization,
			];
		}

		if ($shop->isActive() !== (bool) $values->active) {
			$changes['visibility'] = [
				'old' => $shop->isActive() === true ? 'Public' : 'Hidden',
				'new' => (bool) $values->active === true ? 'Public' : 'Hidden',
			];
		}

		if ($shop->isCashbackActive() !== (bool) $values->cashbackActive) {
			$changes['cashbackActive'] = [
				'old' => $shop->isCashbackActive() ? 'Active' : 'Inactive',
				'new' => (bool) $values->cashbackActive ? 'Active' : 'Inactive',
			];
		}

		if ($shop->getName() !== $values->name) {
			$changes['shopName'] = [
				'old' => $shop->getName(),
				'new' => $values->name,
			];
		}

		if ($shop->isDeepUrlAllowed() !== (bool) $values->deepUrlAllowed) {
			$changes['deepUrlAllowed'] = [
				'old' => $shop->isDeepUrlAllowed(),
				'new' => (bool) $values->deepUrlAllowed,
			];
		}

		if ($shop->isCashbackWithCouponAllowed() !== (bool) $values->cashbackWithCouponAllowed) {
			$changes['cashbackWithCouponAllowed'] = [
				'old' => $shop->isCashbackWithCouponAllowed(),
				'new' => (bool) $values->cashbackWithCouponAllowed,
			];
		}

		if ($shop->getConditions() !== $values->cashbackConditions) {
			$changes['cashbackConditions'] = [
				'old' => $shop->getConditions(),
				'new' => $values->cashbackConditions,
			];
		}

		if ($shop->getShopData()->isOnlyFirstPurchaseCondition() !== (bool) $values->onlyFirstPurchaseCondition) {
			$changes['onlyFirstPurchaseCondition'] = [
				'old' => $shop->getShopData()->isOnlyFirstPurchaseCondition(),
				'new' => (bool) $values->onlyFirstPurchaseCondition,
			];
		}

		if ($shop->isCouponConfirmationRequired() !== (bool) $values->couponConfirmationRequired) {
			$changes['couponConfirmationRequired'] = [
				'old' => $shop->isCouponConfirmationRequired(),
				'new' => (bool) $values->couponConfirmationRequired,
			];
		}

		foreach ($changes as $change => $values) {
			$this->shopChangeManager->createShopChange($shop, $user, $change, $values['old'], $values['new']);
		}
	}

	public function getShopChanges(Shop $shop = null, ?DateTime $changedSince = null, ?DateTime $changedTill = null)
	{
		return $this->shopChangeRepository->getShopChanges($shop, $changedSince, $changedTill);
	}

	public function findShopsWithProducts(Localization $localization, ?int $limit = null)
	{
		return $this->shopRepository->findShopsWithProducts($localization, $limit);
	}

	public function getShopsWithProducts()
	{
		return $this->shopRepository->getShopsWithProducts();
	}

	public function findShopReportByDay($shop, $startedAt, $endedAt): ?ShopReport
	{
		return $this->shopReportRepository->findShopReportByDay($shop, $startedAt, $endedAt);
	}

	public function createOrReturnShopReport(Shop $shop, DateTime $startedAt, DateTime $endedAt): ShopReport
	{
		/** @var ShopReport $shopReport */
		if (!$shopReport = $this->shopReportRepository->findShopReportByDay($shop, $startedAt, $endedAt)) {
			$shopReport = new ShopReport($shop, $startedAt, $endedAt);
		}

		return $this->shopReportManager->saveShopReport($shopReport);
	}

	public function recalculateShopReport(ShopReport $shopReport, DateTime $startedAt, DateTime $endedAt)
	{
		$this->shopReportCalculator->recalculateShopReport($shopReport, $startedAt, $endedAt);

		$this->shopReportManager->saveShopReport($shopReport);
	}

	public function findLastShopReport(Shop $shop): ?ShopReport
	{
		return $this->shopReportRepository->findLastShopReport($shop);
	}

	public function findShopsInProductTag(Localization $localization, Tag $tag)
	{
		return $this->shopRepository->findShopsInProductTag($localization, $tag);
	}

	public function findShopsToCalculateReports(?Localization $localization = null)
	{
		return $this->shopRepository->findShopsToCalculateReports($localization);
	}

	public function findShopsToReports(?Localization $localization = null)
	{
		return $this->shopRepository->findShopsToReports($localization);
	}

	public function findShopReportData(array $shops, DateTime $dateFrom, DateTime $dateTo)
	{
		return $this->shopReportRepository->findShopReportData($shops, $dateFrom, $dateTo);
	}

	public function getShopsWithMessage(Localization $localization = null)
	{
		return $this->shopRepository->getShopsWithMessage($localization);
	}

	/**
	 * @return array<Shop>
	 */
	public function findShopsWithCouponForHomepage(?Localization $localization = null, bool $withCashback = true): array
	{
		$shopsQuery = $this->createShopsQuery($localization)
			->onlyPublished()
			->optimized()
			->onlyWithSomeDealsOrCoupons()
			->sortByCountOfCouponDeals()
			->sortTop();

		$shopsWithoutCashbackQuery = clone ($shopsQuery);
		$shopsWithoutCashbackQuery->onlyWithCashbackDisabled();

		$shopsWithoutCashback = $this->fetch($shopsWithoutCashbackQuery)->applyPaging(0, 16);

		if ($withCashback) {
			$shopsWithCashbackQuery = clone ($shopsQuery);
			$shopsWithCashbackQuery->onlyWithCashbackAllowed();

			$shopsWithCashback = $this->fetch($shopsWithCashbackQuery)->applyPaging(0, 16);

			$shops = array_merge($shopsWithCashback->toArray(), $shopsWithoutCashback->toArray());
		} else {
			$shops = $shopsWithoutCashback->toArray();
		}

		if (count($shops) < 32) {
			$shops = array_merge(
				$shops,
				$this->findTopShops(32 - count($shops), $localization, true, $shops, false, true, $withCashback === false)->toArray()
			);
		}

		return $shops;
	}

	/**
	 * @return array<Shop>
	 */
	public function findTopShopsWithCoupon(?Localization $localization, int $maxResults = 24): array
	{
		return $this->getShops($localization)
			->setMaxResults($maxResults)
			->andWhere('s.visible = 1')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
			->andWhere('s.active = 1')
			->addOrderBy('s.priority', 'DESC')
			->addSelect('sd')
			->leftJoin('s.shopData', 'sd')
			->addOrderBy('s.priority', 'DESC')
			->getQuery()
			->getResult();
	}

	/**
	 * @return array<Shop>
	 */
	public function findBoostedShopsWithCoupon(?Localization $localization, array $exceptShops): array
	{
		$query = $this->createShopsQuery($localization)
			->onlyPublished()
			->optimized()
			->onlyWithSomeCoupons()
			->sortBoosted()
			->sortTop()
			->exceptShops($exceptShops);

		return $this->fetch($query)->toArray();
	}

	public function findByName(Localization $localization, string $name)
	{
		return $this->shopRepository->findOneBy(['name' => $name, 'localization' => $localization]);
	}

	public function saveShopNote(ShopNote $shopNote): ShopNote
	{
		$this->em->persist($shopNote);
		$this->em->flush($shopNote);

		return $shopNote;
	}

	public function addShopNoteToShop(Shop $shop, string $note, $user)
	{
		$shopNote = new ShopNote($shop, $note, $user);

		$this->saveShopNote($shopNote);
	}

	public function removeShopNote($note)
	{
		$this->em->remove($note);
		$this->em->flush($note);
	}

	public function removeShopNoteById($id)
	{
		$note = $this->shopNoteRepository->find($id);

		$this->removeShopNote($note);
	}

	public function getShopNotes(Shop $shop)
	{
		return $this->shopNoteRepository->getShopNotes($shop);
	}

	public function findShopNotes(): array
	{
		return $this->shopNoteRepository->findAll();
	}

	public function getRelatedShops(Shop $shop)
	{
		return $this->relatedShopRepository->findRelatedShops($shop);
	}

	public function findForeignShops(Shop $shop)
	{
		return $this->foreignShopRepository->findForeignShops($shop);
	}

	public function hasShopForeignShop(Shop $shop, Shop $foreignShop): bool
	{
		$ids = [];
		foreach ($this->findForeignShops($shop) as $oneOfForeignShops) {
			$ids[] = $oneOfForeignShops->getShop()->getId();
		}

		return in_array($foreignShop->getId(), $ids);
	}

	public function findShopByPartnerSystem(PartnerSystem $partnerSystem)
	{
		return $this->shopRepository->findByPartnerSystem($partnerSystem);
	}

	public function getShopsWithoutDescriptionBlock(string $type): QueryBuilder
	{
		return $this->shopRepository->getShopsWithoutDescriptionBlock($type);
	}

	public function findShopsReadyToGenerateDescription(string $type): array
	{
		return $this->shopRepository->findShopsReadyToGenerateDescription($type);
	}

	public function findShopsReadyToProcessExternalData(int $limit = 5): array
	{
		return $this->shopRepository->findShopsReadyToProcessExternalData($limit);
	}

	public function sortShopsByTurnover(Localization $localization): void
	{
		echo "--- DEFAULT --- " . $localization->getLocale() . ' ' . PHP_EOL;

		Debugger::log('--- DEFAULT --- ' . $localization->getLocale(), 'sort-shops-command');

		$topShops = $this->findTopShops(16, $localization, false, [], true);

		$shops = $this->findTopShops(9999, $localization, false, $topShops->toArray(), true);

		$shopData = [];
		$currentPriorities = [];

		$i = 1;
		/** @var Shop $shop */
		foreach ($shops as $shop) {
			$shopData[] = [
				'shop' => $shop,
				'turnover' => $shop->getTotalCommissionAmountInLast30Days(),
				'priority' => $shop->getPriority(),
			];
			$currentPriorities[] = $shop->getPriority();

			echo $i . '. ' . $shop->getName() . ' | ' . $shop->getPriority() . ' | ' . $shop->getTotalCommissionAmountInLast30Days() . PHP_EOL;
			Debugger::log($i . '. ' . $shop->getName() . ' | ' . $shop->getPriority() . ' | ' . $shop->getTotalCommissionAmountInLast30Days(), 'sort-shops-command');
			$i++;
		}

		usort($shopData, static function ($a, $b) {
			return $b['turnover'] <=> $a['turnover'];
		});

		echo "--- SORTED  --- " . $localization->getLocale() . ' ' . PHP_EOL;
		Debugger::log('--- SORTED  --- ' . $localization->getLocale(), 'sort-shops-command');

		$i = 1;
		foreach ($shopData as $index => $shopData) {
			/** @var Shop $shop */
			$shop = $shopData['shop'];

			$newPriority = $currentPriorities[$index];

			echo $i . '. ' . $shop->getName() .  ' | ' . $shop->getPriority() . ' -> ' . $newPriority . ' | ' . $shop->getTotalCommissionAmountInLast30Days() . PHP_EOL;
			Debugger::log($i . '. ' . $shop->getName() .  ' | ' . $shop->getPriority() . ' -> ' . $newPriority . ' | ' . $shop->getTotalCommissionAmountInLast30Days(), 'sort-shops-command');
			$i++;

			$shop->setPriority($newPriority);

			$this->saveShop($shop);
		}
	}

	public function findDescriptionBlockByType(Shop $shop, string $type): ?DescriptionBlock
	{
		return $this->descriptionBlockRepository->findDescriptionBlockByType($shop, $type);
	}

	public function findUsersRedirectionsCount(User $user): int
	{
		return $this->redirectionRepository->findShopRedirectionsCount($user);
	}

	public function findShopsForLuckyShopPicker(Localization $localization): array
	{
		$shopIds = Shop::LUCKY_SHOP_IDS[$localization->getId()] ?? [];

		$shops = $this->shopRepository->findByIds($shopIds);

		if (count($shops) < 50) {
			$shops = $this->findTopShops(100, $localization, false, [], true)
				->toArray()
			;

			$totalShops = count($shops);
			$perCategory = intdiv($totalShops, 3);

			return [
				'very_popular' => array_slice($shops, 0, $perCategory),
				'popular' => array_slice($shops, $perCategory, $perCategory),
				'unpopular' => array_slice($shops, 2 * $perCategory),
			];
		}

		$shops = array_filter($shops, static function (Shop $shop) {
			return $shop->isActive() && $shop->isPublished() && $shop->isVisible() && $shop->isCashbackAllowed();
		});

		dumpe($shops);

		return [
			'very_popular' => array_slice($shops, 0, 20),
			'popular' => array_slice($shops, 20, 50),
			'unpopular' => array_slice($shops, 70),
		];
	}
}
