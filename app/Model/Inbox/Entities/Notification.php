<?php

namespace tipli\Model\Inbox\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use DateTime;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Inbox\Repositories\NotificationRepository")
 * @ORM\Table(name="tipli_inbox_notification", uniqueConstraints={@ORM\UniqueConstraint(name="user_campaign_unique", columns={"user_id", "notification_campaign_id"})},
 *    indexes={
 *     @ORM\Index(name="scheduled_atx", columns={"scheduled_at"})
 *    }
 * )
 */
class Notification
{
	public const TYPE_TRANSACTION_REGISTRATION = 'transaction_registration';
	public const TYPE_PAYOUT_CREATION = 'payout_creation';
	public const TYPE_PAYOUT_CONFIRMATION = 'payout_confirmation';
	public const TYPE_LUCKY_SHOP = 'lucky_shop';
	public const TYPE_DEAL = 'deal';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Inbox\Entities\NotificationCampaign")
	 * @ORM\JoinColumn(name="notification_campaign_id", referencedColumnName="id")
	 */
	private ?NotificationCampaign $notificationCampaign = null;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Inbox\Entities\NotificationBody", fetch="EAGER", cascade={"PERSIST"})
	 * @ORM\JoinColumn(name="body_id", referencedColumnName="id")
	 * @var NotificationBody
	 */
	private $body;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 * @var User
	 */
	private $user;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $type;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $scheduledAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $validTill = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $pushScheduledAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $pushEnqueuedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $pushedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $pushFailedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $openedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $mobileOpenedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $clickedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $mobileClickedAt = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(User $user, ?NotificationCampaign $notificationCampaign, \DateTime $scheduledAt)
	{
		$this->user = $user;
		$this->notificationCampaign = $notificationCampaign;
		$this->scheduledAt = $scheduledAt;
		$this->createdAt = new \DateTime();
	}

	public static function getTypes(): array
	{
		return [
			self::TYPE_TRANSACTION_REGISTRATION => 'Transaction registration',
			self::TYPE_PAYOUT_CREATION => 'Payout creation',
			self::TYPE_PAYOUT_CONFIRMATION => 'Payout confirmation',
			self::TYPE_LUCKY_SHOP => 'Lucky shop',
			self::TYPE_DEAL => 'Deal',
		];
	}

	/**
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return NotificationBody
	 */
	public function getBody()
	{
		return $this->body;
	}

	/**
	 * @param NotificationBody $body
	 */
	public function setBody($body)
	{
		$this->body = $body;
	}

	/**
	 * @return NotificationCampaign
	 */
	public function getNotificationCampaign(): ?NotificationCampaign
	{
		return $this->notificationCampaign;
	}

	/**
	 * @return User
	 */
	public function getUser(): User
	{
		return $this->user;
	}

	/**
	 * @return \DateTime|null
	 */
	public function getOpenedAt()
	{
		return $this->openedAt;
	}

	/**
	 * @return \DateTime|null
	 */
	public function getClickedAt()
	{
		return $this->clickedAt;
	}

	/**
	 * @return \DateTime
	 */
	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	public function getScheduledAt(): \DateTime
	{
		return $this->scheduledAt;
	}

	/**
	 * @param \DateTime $clickedAt
	 * @return Notification
	 */
	public function setClickedAt(\DateTime $clickedAt): Notification
	{
		$this->clickedAt = $clickedAt;
		return $this;
	}

	public function setValidTill(?DateTime $validTill = null): void
	{
		$this->validTill = $validTill;
	}

	public function isValid()
	{
		return $this->notificationCampaign && $this->notificationCampaign->isValid();
	}

	/**
	 * @return string|null
	 */
	public function getType(): ?string
	{
		return $this->type;
	}

	/**
	 * @param string|null $type
	 */
	public function setType(?string $type): void
	{
		$this->type = $type;
	}

	public function schedulePush(\DateTime $pushScheduledAt = null)
	{
		$this->pushScheduledAt = $pushScheduledAt ?? new \DateTime();
	}

	/**
	 * @return \DateTime|null
	 */
	public function getMobileClickedAt(): ?\DateTime
	{
		return $this->mobileClickedAt;
	}

	/**
	 * @param \DateTime|null $mobileClickedAt
	 */
	public function setMobileClickedAt(?\DateTime $mobileClickedAt): void
	{
		$this->mobileClickedAt = $mobileClickedAt;
	}

	/**
	 * @return \DateTime|null
	 */
	public function getMobileOpenedAt(): ?\DateTime
	{
		return $this->mobileOpenedAt;
	}

	public function openMobile(): void
	{
		$this->mobileOpenedAt = new \DateTime();
	}

	public function open(): void
	{
		$this->openedAt = new \DateTime();
	}
}
