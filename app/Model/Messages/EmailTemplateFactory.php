<?php

namespace tipli\Model\Messages;

use Contributte\Translation\Translator;
use tipli\Model\Account\Entities\User;
use tipli\Model\Currencies\Currency;
use tipli\Model\Marketing\DataObjects\EmailDataObject;
use tipli\Model\Marketing\NewsletterFacade;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Reviews\Entities\ReviewRequest;
use tipli\Model\Rewards\Entities\ConstantReward;
use tipli\Model\Rewards\RewardFacade;

class EmailTemplateFactory
{
	public function __construct(
		public EmailFactory $emailFactory,
		public NewsletterFacade $newsletterFacade,
		public RewardFacade $rewardFacade,
		public Translator $translator,
	) {
	}

	public function buildNewPasswordEmail(User $user): ?EmailDataObject
	{
		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'new-password',
			[
				'vocalName' => $user->getVocalFirstName(),
				'url' => $this->translator->translate('front.links.sign.newPassword', ['hash' => $user->getNewPasswordRequestHash()], [], null, $user->getLocale()),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}

	public function buildAccountNumberVerificationEmail(User $user): ?EmailDataObject
	{
		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'account-number-verification',
			[
				'url' => $this->translator->translate('front.links.account.accountNumber.verification', ['token' => $user->getAccountNumberHash()], [], null, $user->getLocale()),
				'vocalName' => $user->getVocalFirstName(),
				'name' => $user->getFirstName(),
				'surname' => $user->getLastName(),
				'email' => $user->getEmail(),
				'bankAccount' => $user->getAccountNumber(),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}

	public function buildRegistrationRecommenderEmail(User $user, User $recommendedUser): ?EmailDataObject
	{
		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'registration-recommender-email',
			[
				'userId' => $user->getId(),
				'currency' => Currency::getCurrencySymbol($user->getLocalization()->getCurrency()),
				'bonusAmount' => $this->rewardFacade->findConstantRewardByType($user->getLocalization(), ConstantReward::TYPE_BONUS_RECOMMENDATION, $recommendedUser->getCreatedAt())->getAmount(),
				'referralEmail' => $recommendedUser->getEmail(),
				'vocalName' => $user->getVocalFirstName(),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}

	public function buildRecommendedUserFirstPurchase(User $user, User $recommendedUser): ?EmailDataObject
	{
		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'transaction-rb-registration',
			[
				'currency' => Currency::getCurrencySymbol($user->getLocalization()->getCurrency()),
				'bonusAmount' => $this->rewardFacade->findConstantRewardByType($user->getLocalization(), ConstantReward::TYPE_BONUS_RECOMMENDATION, $recommendedUser->getCreatedAt())->getAmount(),
				'referralEmail' => $recommendedUser->getEmail(),
				'vocalName' => $user->getVocalFirstName(),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}

	public function buildRefundReviewRequestEmail(ReviewRequest $reviewRequest): ?EmailDataObject
	{
		$params = [
			'vocalName' => $reviewRequest->getUser()->getVocalFirstName(),
			'refundDate' => $reviewRequest->getRefund()->getCreatedAt(),
			'storeName' => $reviewRequest->getRefund()->getShop() ? $reviewRequest->getRefund()->getShop()->getName() : null,
			'requestType' => $this->translator->translate('newFront.account.refundsList.titleByRefundType.' . $reviewRequest->getRefund()->getType(), null, [], null, $reviewRequest->getUser()->getLocale()),
			'accessToken' => $reviewRequest->getUser()->getAccessToken(),
		];

		for ($i = 1; $i <= 5; $i++) {
			$params['ratingLink' . $i] = $this->generateRatingLink($reviewRequest, $i);
		}

		$emailDataObject = $this->newsletterFacade->getEmail(
			$reviewRequest->getUser()->getLocalization(),
			'feedback-satisfaction-guarantee',
			$params
		);

		return $emailDataObject;
	}

	public function buildPayoutReviewRequestEmail(ReviewRequest $reviewRequest): ?EmailDataObject
	{
		$params = [
			'vocalName' => $reviewRequest->getUser()->getVocalFirstName(),
			'payoutDate' => $reviewRequest->getPayout()->getCreatedAt(),
			'payoutAmount' => round($reviewRequest->getPayout()->getAmount(), 2),
			'currency' => Currency::getCurrencySymbol($reviewRequest->getUser()->getLocalization()->getCurrency()),
			'accessToken' => $reviewRequest->getUser()->getAccessToken(),
		];

		for ($i = 1; $i <= 5; $i++) {
			$params['ratingLink' . $i] = $this->generateRatingLink($reviewRequest, $i);
		}

		$emailDataObject = $this->newsletterFacade->getEmail(
			$reviewRequest->getUser()->getLocalization(),
			'feedback-payout',
			$params
		);

		return $emailDataObject;
	}

	private function generateRatingLink(ReviewRequest $reviewRequest, int $rating): string
	{
		return $this->translator->translate('front.links.review.addTipli', [
			'uniqueId' => $reviewRequest->getUniqueId(),
			'rating' => $rating,
		], [], $reviewRequest->getLocalization()->getLocale());
	}

	public function buildPayoutCreationEmail(Payout $payout): ?EmailDataObject
	{
		$user = $payout->getUser();

		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'payout-creation',
			[
				'vocalName' => $user->getVocalFirstName(),
				'currency' => Currency::getCurrencySymbol($user->getLocalization()->getCurrency()),
				'bankAccount' => $payout->getAccountNumber(),
				'payoutAmount' => abs($payout->getTransaction()->getAmount()),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}

	public function buildPayoutConfirmationEmail(Payout $payout): ?EmailDataObject
	{
		$user = $payout->getUser();

		$emailDataObject = $this->newsletterFacade->getEmail(
			$user->getLocalization(),
			'payout-confirmation',
			[
				'vocalName' => $user->getVocalFirstName(),
				'currency' => Currency::getCurrencySymbol($user->getLocalization()->getCurrency()),
				'bankAccount' => $payout->getAccountNumber(),
				'payoutAmount' => abs($payout->getTransaction()->getAmount()),
				'accessToken' => $user->getAccessToken(),
			]
		);

		return $emailDataObject;
	}
}
