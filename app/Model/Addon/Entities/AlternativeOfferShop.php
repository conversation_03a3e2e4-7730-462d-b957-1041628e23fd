<?php

namespace tipli\Model\Addon\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Addon\Repositories\AlternativeOfferShopRepository")
 * @ORM\Table(name="tipli_addon_alternative_offer_shop")
 */
class AlternativeOfferShop
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Addon\Entities\AlternativeOffer", inversedBy="shops")
	 * @ORM\JoinColumn(name="alternative_offer_id", referencedColumnName="id")
	 */
	private AlternativeOffer $alternativeOffer;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private Shop $shop;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $skipIfUserHasTransaction;

	/**
	 * @ORM\Column(type="string")
	 */
	private ?string $deepUrl;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $priority = 0;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(AlternativeOffer $alternativeOffer, Shop $shop, bool $skipIfUserHasTransaction, ?string $deepUrl = null, int $priority = 0)
	{
		$this->alternativeOffer = $alternativeOffer;
		$this->shop = $shop;
		$this->skipIfUserHasTransaction = $skipIfUserHasTransaction;
		$this->deepUrl = $deepUrl;
		$this->priority = $priority;
		$this->createdAt = new \DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getShop(): Shop
	{
		return $this->shop;
	}

	public function shouldSkipIfUserHasTransaction(): bool
	{
		return $this->skipIfUserHasTransaction;
	}

	public function setSkipIfUserHasTransaction(bool $skipIfUserHasTransaction): void
	{
		$this->skipIfUserHasTransaction = $skipIfUserHasTransaction;
	}

	public function getDeepUrl(): ?string
	{
		return $this->deepUrl;
	}

	public function setDeepUrl(?string $deepUrl): void
	{
		$this->deepUrl = $deepUrl;
	}

	public function getPriority(): int
	{
		return $this->priority;
	}

	public function setPriority(int $priority): void
	{
		$this->priority = $priority;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}
}
