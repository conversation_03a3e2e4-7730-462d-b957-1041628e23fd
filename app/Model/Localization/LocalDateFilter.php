<?php

namespace tipli\Model\Localization;

class LocalDateFilter
{
	public function __construct(
		private DateTimeZoneResolver $dateTimeZoneResolver,
		private LocalizationFacade $localizationFacade
	) {
	}

	public function __invoke(?\DateTime $dateTime, string $format = 'd.m.Y', ?string $locale = null)
	{
		if ($dateTime === null) {
			return '';
		}

		if ($locale === null) {
			$localization = $this->localizationFacade->getCurrentLocalization();
		} else {
			$localization = $this->localizationFacade->findOneByLocale($locale);

			if ($localization === null) {
				$localization = $this->localizationFacade->getCurrentLocalization();
			}
		}

		if ($localization->isHungarian()) {
			if ($format === 'd.m.Y') {
				$format = 'Y. n. j';
			} elseif ($format === 'd.m.Y H:i') {
				$format = 'Y. n. j H:i';
			}
		}

		return $this->dateTimeZoneResolver->resolve($dateTime, $localization)->format($format);
	}
}
