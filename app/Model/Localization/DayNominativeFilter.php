<?php

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use Nette\Localization\ITranslator;

class DayNominativeFilter
{
    /**
     * @var ITranslator
     */
    private $translator;

    public function __construct(ITranslator $translator)
    {
        $this->translator = $translator;
    }

    public function __invoke($date)
    {
        return $this->translator->translate(sprintf('app.day.%s.nominative', $date->format('w')));
    }
}
