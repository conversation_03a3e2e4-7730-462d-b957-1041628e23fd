<?php

namespace tipli\Model\Localization;

use Contributte\Translation\LocalesResolvers\ResolverInterface;
use Contributte\Translation\Translator;
use Nette\Http\Request;
use Nette\Http\UrlScript;
use Nette\Utils\Strings;
use tipli\Model\Configuration;
use tipli\Model\Localization\Entities\Localization;
use Tracy\Debugger;

class WebsiteResolver implements ResolverInterface
{
	/**
	 * @var Request
	 */
	private $request;

	/**
	 * @var array
	 */
	private $websites;

	public function __construct(
		Request $request,
		Configuration $configuration
	) {
		$this->request = $request;
		$this->websites = $configuration->getWebsites();
	}

	/**
	 * @param Translator $translator
	 */
	public function resolve(Translator $translator): ?string
	{
		/** @var UrlScript $url */
		$url = $this->request->getUrl();

		/** @var string $host */
		$host = $this->request->getUrl()->getHost();

		if (
			Strings::startsWith($url->getAbsoluteUrl(), 'https://www.tiplino.hu')
			|| Strings::startsWith($url->getAbsoluteUrl(), 'http://www.tiplino.hu')
		) {
			return Localization::LOCALE_HUNGARIAN;
		}

		foreach ($this->websites as $locale => $domains) {
			if (in_array($host, $domains)) {
				return $locale;
			}
		}

		if (
			PHP_SAPI !== 'cli'
			&& !Strings::contains($host, '.czdev')
			&& !Strings::contains($host, 'localhost')
			&& !Strings::contains($host, '127.0.0.1')
			&& !Strings::contains($host, 'tipli.czlocal')
			&& !Strings::contains($host, 'tipli.hulocal')
			&& (!Strings::contains($host, 'tipli.cz') && $this->request->getPost() !== 81)
		) { // .czdev -> testy v buddym
			Debugger::log('unknown website', 'websites-resolver');
			throw new \InvalidArgumentException('Unknown domain.');
		}

		return $this->getDefaultLocale();
	}

	/**
	 * @return string
	 */
	public function getDefaultLocale(): string
	{
		return 'cs';
	}
}
