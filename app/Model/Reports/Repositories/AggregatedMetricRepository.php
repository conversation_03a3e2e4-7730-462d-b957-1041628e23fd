<?php

namespace tipli\Model\Reports\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;
use DateTime;
use tipli\Model\Reports\Entities\AggregatedMetric;

class AggregatedMetricRepository extends BaseRepository
{
	public function findAggregatedMetricBy(Localization $localization, string $intervalName, DateTime $startedAt): ?AggregatedMetric
	{
		return $this->createQueryBuilder('am')
			->andWhere('am.localization = :localization')
			->andWhere('am.intervalName = :intervalName')
			->andWhere('am.startedAt >= :startedAt')
			->setParameters([
				'localization' => $localization,
				'intervalName' => $intervalName,
				'startedAt' => $startedAt,
			])->getQuery()->getOneOrNullResult();
	}

	public function findAggregatedMetrics(Localization $localization = null, $intervalName, \DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('a')
			->addSelect('sum(a.countOfUsers) AS countOfUsers')
			->addSelect('a.startedAt')
			->addSelect('a.endedAt')
			->andWhere('a.intervalName = :intervalName')
			->andWhere('a.intervalName = :intervalName')
			->setParameter('intervalName', $intervalName)
			->andWhere('a.startedAt >= :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('a.endedAt <= :endedAt')
			->setParameter('endedAt', $endedAt)
			->orderBy('a.startedAt');

		if ($localization) {
			$qb->andWhere('a.localization = :localization')
				->setParameter('localization', $localization);
		} else {
			$qb->addGroupBy('a.startedAt');
		}

		return $qb->getQuery()->getResult();
	}

	public function findAggregatedMetricsInTotals(Localization $localization = null, $intervalName, \DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('a')
			->addSelect('sum(a.commissionAmount) AS commissionAmount')
			->addSelect('sum(a.userCommissionAmount) AS userCommissionAmount')
			->addSelect('sum(a.bonusAmount) AS bonusAmount')
			->addSelect('sum(a.turnover) AS turnover')
			->addSelect('sum(a.originalTurnover) AS originalTurnover')
			->addSelect('sum(a.confirmedTurnover) AS confirmedTurnover')
			->addSelect('sum(a.income) AS income')
			->addSelect('sum(a.countOfTransactions) AS countOfTransactions')
			->addSelect('sum(a.countOfActiveUsers) AS countOfActiveUsers')
			->addSelect('sum(a.countOfUsers) AS countOfUsers')
			->addSelect('sum(a.countOfActivations) AS countOfActivations')
			->addSelect('sum(a.countOfReactivations) AS countOfReactivations')
			->addSelect('sum(a.countOfPurchases) AS countOfPurchases')
			->addSelect('sum(a.countOfUsersIos) as countOfUsersIos')
			->addSelect('sum(a.countOfUsersAndroid) as countOfUsersAndroid')
			->addSelect('sum(a.countOfUsersWeb) as countOfUsersWeb')
			->addSelect('sum(a.countOfAddonInstalls) as countOfAddonInstalls')
			->addSelect('sum(a.countOfUsersUsingAddon) as countOfUsersUsingAddon')
			->addSelect('sum(a.countOfMobileAppInstalls) as countOfMobileAppInstalls')
			->addSelect('sum(a.countOfUsersUsingMobileApp) as countOfUsersUsingMobileApp')
			->addSelect('sum(a.countOfActiveUsersMobileApp) as countOfActiveUsersMobileApp')
			->addSelect('IFNULL(NULLIF(a.confirmedTurnover,0) / NULLIF(a.turnover, 0),0)  as confirmedTransactionsRate')
			->andWhere('a.intervalName = :intervalName')
			->setParameter('intervalName', $intervalName)
			->andWhere('a.startedAt >= :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('a.endedAt <= :endedAt')
			->setParameter('endedAt', $endedAt)
			->orderBy('a.startedAt');

		if ($localization !== null) {
			$qb->andWhere('a.localization = :localization')
				->setParameter('localization', $localization);
		} else {
			$qb->andWhere('a.localization IS NULL');
		}

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findAggregatedMetric(?Localization $localization, $intervalName, \DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('a')
			->select('a')
			->andWhere('a.intervalName = :intervalName')
			->setParameter('intervalName', $intervalName)
			->andWhere('a.startedAt = :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('a.endedAt = :endedAt')
			->setParameter('endedAt', $endedAt);

		if ($localization !== null) {
			$qb->andWhere('a.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->andWhere('a.localization IS NULL');
		}

		$qb->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findAggregatedMetricsByDateTime(\DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('a')
			->select('a')
			->andWhere('a.startedAt >= :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('a.endedAt <= :endedAt')
			->setParameter('endedAt', $endedAt);

		return $qb->getQuery()->getResult();
	}
}
