<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Model\Seo;

use Kaufino\Model\Seo\Entities\PageExtension;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Websites\Entities\Website;
use Latte\CompileException;
use Latte\Engine;
use Latte\Loaders\StringLoader;
use Latte\Sandbox\SecurityPolicy;
use Nette\Application\LinkGenerator;
use Nette\Http\Url;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;
use Tracy\Debugger;

class SeoGenerator
{
	/**
	 * @var ITranslator
	 */
	private $translator;

	/**
	 * @var LinkGenerator
	 */
	private $linkGenerator;

	public function __construct(ITranslator $translator, LinkGenerator $linkGenerator)
	{
		$this->translator = $translator;
		$this->linkGenerator = $linkGenerator;
	}

	public function generateCanonicalUrl(string $rawUrl)
	{
		$url = new Url($rawUrl);

		$queriesBlacklist = [
			'utm_campaign',
			'utm_source',
			'utm_medium',
			'utm_content',
			'utm_keyword',
			'utm_id',
			'backLink',
			'returnUrl',
			'requireLogin',
			'shortcut',
			'_fid',
			'oid',
		];

		if (Strings::contains($rawUrl, 'kaufino') || Strings::contains($rawUrl, 'mrofert')) {
			$queriesBlacklist[] = 'page';
		}

		foreach ($queriesBlacklist as $query) {
			$this->removeQuery($url, $query);
		}

		foreach ($url->getQueryParameters() as $parameter => $value) {
			if (Strings::contains($parameter, 'sorter')) {
				$this->removeQuery($url, $parameter);
			}
		}

		$url->setScheme('https');
		return $url->getAbsoluteUrl();
	}

	private function removeQuery(Url &$url, $query)
	{
		if ($url->getQueryParameter($query)) {
			$url->setQueryParameter($query, null);
		}
	}

	public function generateShopMetaTitle(Shop $shop, Website $website): string
	{
		$titleTemplate = $shop->getTitle();

        bdump($shop);
        bdump($titleTemplate);

		if (!$titleTemplate && $shop->isStore()) {
			$titleTemplate = $this->translator->translate($website->getLocaleFileName() . '.shop.type.shopTitle');
		}

		return $this->renderShopTitle($titleTemplate, $shop, $website);
	}

	public function generateShopHeading1(Shop $shop, Website $website): string
	{
		return $this->renderShopTitle($shop->getHeading(), $shop, $website);
	}

	public function generateShopHeadingFromPageExtension(PageExtension $pageExtension, Shop $shop, Website $website): string
	{
		return $this->renderShopTitle($pageExtension->getHeading(), $shop, $website);
	}

	private function renderShopTitle(?string $template, Shop $shop, Website $website): string
	{
		$websiteType = $website->getLocaleFileName();

        $currentLeaflet = $shop->getCurrentLeaflet();
        $nextLeaflet = $shop->getNextLeaflet();

        $params = [
            'shopName' => $shop->getName(),
            'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
            'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
        ];

        if ($template === null && $shop->isStore() && $website->isKaufino() && $shop->getLocalization()->isGeneratedMetaDataAllowed()) {
            if ($nextLeaflet !== null) {
                $template = $this->translator->translate($websiteType . '.shop.metaTitles.withFutureLeaflet', $params);
            } else if ($currentLeaflet !== null) {
                $template = $this->translator->translate($websiteType . '.shop.metaTitles.withCurrentLeaflet', $params);
            } else {
                $template = $this->translator->translate($websiteType . '.shop.metaTitles.withoutCurrentAndFutureLeaflet', $params);
            }
        }

		if ($template) {
			$latte = new Engine();

			$policy = new SecurityPolicy();
			$latte->setPolicy($policy);
			$policy->allowFilters($policy::ALL);
			$latte->addFilter('dayGenitive', function ($date): string {
				return $this->translator->translate(sprintf('app.day.%s.genitive', $date->format('w')));
			});
            $latte->addFilter('dayNominative', function ($date): string {
                return $this->translator->translate(sprintf('app.day.%s.nominative', $date->format('w')));
            });
			$policy->allowMacros(['if', '=', 'else', 'elseif']);
			$latte->setSandboxMode();

			$latte->setLoader(new StringLoader());

            bdump($template);

			try {
				return html_entity_decode($latte->renderToString($template, $params));
			} catch (CompileException $e) {
                Debugger::log($e, 'seo-generator-error');
			}
		}

		if ($shop->isStore()) {
			return $this->translator->translate($websiteType . '.shop.defaultTitleSuffic', ['shopName' => $shop->getName()]);
		}

		if ($shop->isEshop()) {
			return $this->translator->translate($websiteType . '.shop.type.eshopTitle', ['brand' => $shop->getName()]);
		}

		// isService
		return $this->translator->translate($websiteType . '.shop.defaultTitleSuffic', ['shopName' => $shop->getName()]);
	}

	public function generateShopMetaDescription(Shop $shop, ?Website $website = null): string
	{
		$websiteType = $website->getLocaleFileName();

		if ($shop->isStore()) {
			if ($website !== null && $website->isKaufino() && $shop->getLocalization()->isGeneratedMetaDataAllowed()) {
				$currentLeaflet = $shop->getCurrentLeaflet();
				$nextLeaflet = $shop->getNextLeaflet();

				$params = [
					'shopName' => $shop->getName(),
					'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
					'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
				];

				if ($nextLeaflet !== null) {
					$template = $this->translator->translate($websiteType . '.shop.metaDescriptions.withFutureLeaflet', $params);
				} else if ($currentLeaflet !== null) {
					$template = $this->translator->translate($websiteType . '.shop.metaDescriptions.withCurrentLeaflet', $params);
				} else {
					$template = $this->translator->translate($websiteType . '.shop.metaDescriptions.withoutCurrentAndFutureLeaflet', ['brand' => Strings::upper($shop->getName())]);
				}

				$latte = new Engine();
				$policy = new SecurityPolicy();
				$latte->setPolicy($policy);
				$policy->allowFilters($policy::ALL);
				$latte->addFilter('dayGenitive', function ($date): string {
					return $date ? $this->translator->translate(sprintf('app.day.%s.genitive', $date->format('w'))) : '';
				});
				$policy->allowMacros(['if', '=', 'else', 'elseif']);
				$latte->setSandboxMode();

				$latte->setLoader(new StringLoader());

				return $latte->renderToString($template, $params);
			}

			return $this->translator->translate($websiteType . '.shop.text', ['brand' => $shop->getName()]);
		}

		if ($shop->isEshop()) {
			return $this->translator->translate($websiteType . '.shop.type.eshop', ['brand' => $shop->getName()]);
		}

		// isService
		return $this->translator->translate($websiteType . '.shop.text', ['brand' => $shop->getName()]);
	}

	public function renderInSandbox(string $template, array $params): ?string
	{
		$latte = new Engine();
		//$template = "KAUFLAND leták{if \$currentLeafletFromDate} od {\$currentLeafletFromDate|dayGenitive} {\$currentLeafletFromDate|date:'d.m.Y'}{/if} + akční příští týden";

		$policy = new SecurityPolicy();
		$latte->setPolicy($policy);
		$policy->allowFilters($policy::ALL);
		$latte->addFilter('dayGenitive', function ($date): string {
			return $date ? $this->translator->translate(sprintf('app.day.%s.genitive', $date->format('w'))) : '';
		});
		$latte->addFilter('monthName', function ($date): string {
			return $date ? $this->translator->translate(sprintf('app.months.%s', $date->format('n'))): '';
		});
		$policy->allowMacros(['if', '=', 'else', 'elseif']);
		$latte->setSandboxMode();

		$latte->setLoader(new StringLoader());

		//return $string = $latte->renderToString($template, $params);

		try {
			return html_entity_decode($latte->renderToString($template, $params));
		} catch (CompileException $e) {
			return null;
		}
	}
}
