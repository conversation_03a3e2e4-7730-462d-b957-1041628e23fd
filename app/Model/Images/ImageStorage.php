<?php

namespace tipli\Model\Images;

use GuzzleHttp\Client;
use Nette\Utils\ImageException;
use tipli\Model\Doctrine\EntityManager;
use Nette\Http\FileUpload;
use Nette\Utils\FileSystem;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Configuration;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Images\Repositories\ImageRepository;
use Tracy\Debugger;
use Nette\Utils\Image as NetteImage;

class ImageStorage
{
	public const IMAGE_OUTPUT_JPEG = 'jpeg';
	public const IMAGE_OUTPUT_PNG = 'png';
	public const IMAGE_OUTPUT_JPEG_PNG = 'jpeg_png';
	public const IMAGE_OUTPUT_GIF = 'gif';

	/**
	 * @var EntityManager
	 */
	private $em;

	/**
	 * @var ImageRepository
	 */
	private $imageRepository;

	/**
	 * @var string
	 */
	private $imagesPath;

	/**
	 * @var string
	 */
	private $thumbnailsPath;

  /**
   * @var Configuration
   */
	private $configuration;

	public function __construct(EntityManager $em, ImageRepository $imageRepository, Configuration $configuration)
	{
		$this->em = $em;
		$this->imageRepository = $imageRepository;
		$this->imagesPath = $configuration->getImagesPath();
		$this->thumbnailsPath = $configuration->getThumbnailsPath();
		$this->configuration = $configuration;
	}

	private function getMimeTypeByOutput(string $type): ?int
	{
		switch ($type) {
			case self::IMAGE_OUTPUT_JPEG:
				return NetteImage::JPEG;
			case self::IMAGE_OUTPUT_PNG:
				return NetteImage::PNG;
			case self::IMAGE_OUTPUT_GIF:
				return NetteImage::GIF;
			case self::IMAGE_OUTPUT_JPEG_PNG:
				return NetteImage::JPEG;
			default:
				return null;
		}
	}

	private function getExtensionByType(string $type): ?string
	{
		switch ($type) {
			case self::IMAGE_OUTPUT_JPEG:
				return 'jpeg';
			case self::IMAGE_OUTPUT_JPEG_PNG:
				return 'jpeg';
			case self::IMAGE_OUTPUT_PNG:
				return 'png';
			case self::IMAGE_OUTPUT_GIF:
				return 'gif';
			default:
				return null;
		}
	}

	public function saveImage(FileUpload $imageFile, $namespace, $resizeWidth = null, $resizeHeight = null, string $outputType = null)
	{
		try {
			if ($imageFile->isOk() === false) {
				throw new ImageException('Image is broken.');
			}

			if (file_exists($this->imagesPath . $namespace) === false) {
				mkdir($this->imagesPath . $namespace);
			}

			// svg file
			if ($imageFile->getContentType() === 'image/svg+xml' || str_ends_with($imageFile->getName(), '.svg')) {
				$extension = 'svg';

				$fileName = sha1($imageFile->getContents()) . $extension;

				$dir = $this->imagesPath . $namespace . '/';

				$temporaryName = $dir . $fileName;

				$imageFile->move($temporaryName);
			} else {
				if ($outputType !== null) {
					if (in_array($outputType, [self::IMAGE_OUTPUT_JPEG, self::IMAGE_OUTPUT_PNG, self::IMAGE_OUTPUT_JPEG_PNG, self::IMAGE_OUTPUT_GIF]) === false) {
						throw new ImageException('Unknown output type.');
					}

					$type = $this->getMimeTypeByOutput($outputType);

					if ($type === null) {
						throw new ImageException('Unknown type.');
					}

					$extension = $this->getExtensionByType($outputType);

					if ($extension === null) {
						throw new ImageException('Unknown extension.');
					}
				} else {
					$type = NetteImage::detectTypeFromFile($imageFile->getTemporaryFile());

					if ($type === null) {
						throw new ImageException('Image type not detected.');
					}

					$extension = NetteImage::typeToExtension($type);
				}

				$fileName = sha1($imageFile->getContents()) . '.' . $extension;

				$dir = $this->imagesPath . $namespace . '/';

				$temporaryName = $dir . $fileName;

				$imageFile->move($temporaryName);
//				if ($outputType !== null) {
//					if ($type === NetteImage::GIF || $type === NetteImage::WEBP || $type === NetteImage::AVIF) {
//						$imageFile->move($temporaryName);
//					} else {
//						$imageFile->save($temporaryName, 100, $type);
//					}
//				} else {
//					$imageFile->move($temporaryName);
//				}

				if ($resizeWidth || $resizeHeight) {
					$image = NetteImage::fromFile($temporaryName);
					$image->resize($resizeWidth, $resizeHeight);
					$image->save($temporaryName, 100);
				}
			}

			if (file_exists($temporaryName)) {
				$image = $this->createImage($fileName, Image::STORAGE_TYPE_FILESYSTEM, $namespace);

				FileSystem::rename($temporaryName, $dir . $image->getId() . '.' . $extension);
				$image->setIdentifier($image->getId() . '.' . $extension);
				$this->em->flush($image);

				return $image;
			}
		} catch (ImageException $e) {
			throw $e;
		}

		return false;
	}

	public function uploadImage(FileUpload $imageFile, $namespace, $resizeWidth = null, $resizeHeight = null, $outputType = self::IMAGE_OUTPUT_JPEG_PNG)
	{
		switch ($outputType) {
			case self::IMAGE_OUTPUT_JPEG:
				$type = \Nette\Utils\Image::JPEG;
				break;
			case self::IMAGE_OUTPUT_PNG:
				$type = \Nette\Utils\Image::PNG;
				break;
			case self::IMAGE_OUTPUT_JPEG_PNG:
				$type = null;
				break;
			default:
				throw new \InvalidArgumentException('Neznámý typ výstupu.');
		}

		if (!($imageFile->isOk())) {
			throw new \InvalidArgumentException('Obrázek je poškozený.');
		}

		if ($type) {
			$extension = $type === \Nette\Utils\Image::PNG ? '.png' : '.jpg';
		} else {
			switch ($imageFile->getContentType()) {
				case 'image/png':
					$extension = '.png';
					break;
				case 'image/jpeg':
				case 'image/jpg':
					$extension = '.jpg';
					break;
				default:
					throw new \InvalidArgumentException('Neznámý typ souboru.');
			}
		}

		$imageFile = $imageFile->toImage();
		if ($resizeWidth || $resizeHeight) {
			$imageFile->resize($resizeWidth, $resizeHeight, \Nette\Utils\Image::EXACT);
		}

		if (!file_exists($this->imagesPath . $namespace)) {
			mkdir($this->imagesPath . $namespace);
		}

		$fileName = sha1($imageFile->toString()) . $extension;
		$dir = $this->imagesPath . $namespace . '/';
		$temporaryName = $dir . $fileName;

		$imageFile->save($temporaryName, 100, $type);

		return '/upload/images/' . $namespace . '/' . $fileName;
	}

	public function createImage($identifier, $storageType, $namespace)
	{
		$image = new Image($identifier, $storageType, $namespace);

		$this->em->persist($image);
		$this->em->flush($image);

		return $image;
	}

	public function saveImageFromUrl($url, $namespace, $resizeWidth = null, $resizeHeight = null, $outputType = self::IMAGE_OUTPUT_JPEG, $useFileGetContents = false)
	{
		switch ($outputType) {
			case self::IMAGE_OUTPUT_JPEG:
				$extension = '.jpg';
				$type = \Nette\Utils\Image::JPEG;
				break;
			case self::IMAGE_OUTPUT_PNG:
				$extension = '.png';
				$type = \Nette\Utils\Image::PNG;
				break;
			default:
				throw new \InvalidArgumentException('Neznámý typ výstupu.');
		}

		if (!file_exists($this->imagesPath . $namespace)) {
			mkdir($this->imagesPath . $namespace);
		}

		$headers = @get_headers($url);
		if (Strings::contains($headers[0], '404')) {
			throw new InvalidArgumentException('The file does not exist at url: ' . $url);
		}

		if ($useFileGetContents) {
			$content = file_get_contents($url);
		} else {
			$client = new Client();
			$response = $client->request('GET', $url, [
				'headers' => [
					'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36',
				],
			]);
			$content = $response->getBody()->getContents();
		}

		$imageFile = \Nette\Utils\Image::fromString($content);

		if ($resizeWidth || $resizeHeight) {
			$imageFile->resize($resizeWidth, $resizeHeight, \Nette\Utils\Image::EXACT);
		}

		$fileName = substr(sha1($url), 0, 16) . $extension;
		$dir = $this->imagesPath . $namespace . '/';
		$temporaryName = $dir . $fileName;

		$imageFile->save($temporaryName, 100, $type);

		$image = new Image($fileName, Image::STORAGE_TYPE_FILESYSTEM, $namespace);
		$this->em->persist($image);
		$this->em->flush($image);

		FileSystem::rename($temporaryName, $dir . $image->getId() . $extension);
		$image->setIdentifier($image->getId() . $extension);

		$this->em->flush($image);

		return $image;
	}

	public function getImageFile(Image $image)
	{
		$path = $this->imagesPath . $image->getNamespace() . '/' . $image->getIdentifier();

		if (!file_exists($path)) {
			return null;
		}

		return \Nette\Utils\Image::fromFile($path);
	}

	public function getRealImagePath(Image $image): ?string
	{
		return '/upload/images/' . $image->getNamespace() . '/' . $image->getIdentifier();
	}

	public function getImagePath(Image $image): ?string
	{
		$path = $this->imagesPath . $image->getNamespace() . '/' . $image->getIdentifier();

		if (!file_exists($path)) {
			return null;
		}

		return $path;
	}

	public function getImageContentType(Image $image): ?string
	{
		$path = $this->getImagePath($image);

		if ($path === null) {
			return null;
		}

		return mime_content_type($path);
	}

	public function isSvg(FileUpload $file): bool
	{
		return $file->getContentType() === 'image/svg+xml';
	}

	public function deleteImage(Image $image)
	{
		$path = $this->imagesPath . $image->getNamespace() . '/' . $image->getIdentifier();

		if (file_exists($path) === true) {
			FileSystem::delete($path);
		}

		$this->em->remove($image);
		$this->em->flush($image);
	}

	public function deleteImageFiles(Image $image)
	{
		if ($image->getStorageType() === Image::STORAGE_TYPE_FILESYSTEM) {
			FileSystem::delete($this->imagesPath . $image->getNamespace() . '/' . $image->getIdentifier());
		}

		FileSystem::delete($this->thumbnailsPath . $this->getThumbnailPath($image));

		$image->delete();
		$this->em->persist($image);
		$this->em->flush($image);
	}

	public function deleteImageFromPath(string $namespace, string $identifier)
	{
		$path = $this->imagesPath . $namespace . '/' . $identifier;

		if (file_exists($path) === false) {
			Debugger::log($path, 'imageStorage-nonexisting-file-to-remove');
		} else {
			FileSystem::delete($path);
		}
	}

	public function resetCompressionMark(Image $image)
	{
		$image->setCompressed(false);

		$this->em->persist($image);
		$this->em->flush($image);
	}

	public function getThumbnailPath(Image $image)
	{
		return floor($image->getId() / 1000) . DIRECTORY_SEPARATOR . $image->getId() . DIRECTORY_SEPARATOR;
	}

	/**
	 * @param int $id
	 * @return object|null|Image
	 */
	public function findImage(int $id)
	{
		return $this->imageRepository->findImage($id);
	}

	public function createImageForSample(string $namespace, string $identifier): Image
	{
		$namespace = 'samples/' . $namespace;
		$image = new Image($identifier, Image::STORAGE_TYPE_FILESYSTEM, $namespace);

		if (!file_exists($this->imagesPath . $namespace)) {
			mkdir($this->imagesPath . $namespace);
		}

		copy(__DIR__ . '/../../../www/images/' . $namespace . '/' . $identifier, $this->imagesPath . $namespace . '/' . $identifier);

		$this->em->persist($image);
		$this->em->flush($image);

		return $image;
	}
}
