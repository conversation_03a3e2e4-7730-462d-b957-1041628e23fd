<?php

namespace tipli\Model\Account\Subscribers;

use Contributte\Events\Extra\Event\Application\RequestEvent;
use Contributte\Events\Extra\Event\Security\LoggedInEvent;
use Nette\Application\LinkGenerator;
use Nette\Caching\Cache;
use Nette\Caching\IStorage;
use Nette\Http\Request;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Events\UserCreatedEvent;
use tipli\Model\Account\Events\UserRemovedEvent;
use tipli\Model\Account\Events\UserRequestedNewPasswordEvent;
use tipli\Model\Account\PhoneNumberVerificationManager;
use tipli\Model\Account\Producers\UserUtmChangeProducer;
use tipli\Model\Account\UserFacade;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Layers\ParentReferencingLayer;
use tipli\Model\Layers\RondoDataLayer;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Marketing\NewsletterFacade;
use tipli\Model\Messages\EmailTemplateFactory;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Rewards\RewardFacade;

final class UserSubscriber implements EventSubscriberInterface
{
	/** @var MessageFacade */
	private $messageFacade;

	/** @var UserFacade */
	private $userFacade;

	/** @var UtmLayer */
	private $utmLayer;

	/** @var ClientLayer */
	private $clientLayer;

	/** @var ParentReferencingLayer */
	private $parentReferencingLayer;

	/** @var Request */
	private $httpRequest;

	/** @var PayoutFacade */
	private $payoutFacade;

	/** @var PhoneNumberVerificationManager */
	private $phoneNumberVerificationManager;

	/** @var UserUtmChangeProducer */
	private $userUtmChangeProducer;

	/** @var \Nette\Security\User */
	private $netteUser;

	/** @var RondoDataLayer */
	private $rondoDataLayer;

	private Cache $cache;

	public function __construct(
		MessageFacade $messageFacade,
		UserFacade $userFacade,
		private RewardFacade $rewardFacade,
		private NewsletterFacade $newsletterFacade,
		UtmLayer $utmLayer,
		ClientLayer $clientLayer,
		ParentReferencingLayer $parentReferencingLayer,
		Request $httpRequest,
		PayoutFacade $payoutFacade,
		PhoneNumberVerificationManager $phoneNumberVerificationManager,
		UserUtmChangeProducer $userUtmChangeProducer,
		\Nette\Security\User $netteUser,
		RondoDataLayer $rondoDataLayer,
		IStorage $storage,
		private SqlQueryScheduler $sqlQueryScheduler,
		private LinkGenerator $linkGenerator,
		private EmailTemplateFactory $emailTemplateFactory,
	) {
		$this->messageFacade = $messageFacade;
		$this->userFacade = $userFacade;
		$this->utmLayer = $utmLayer;
		$this->clientLayer = $clientLayer;
		$this->parentReferencingLayer = $parentReferencingLayer;
		$this->httpRequest = $httpRequest;
		$this->payoutFacade = $payoutFacade;
		$this->phoneNumberVerificationManager = $phoneNumberVerificationManager;
		$this->userUtmChangeProducer = $userUtmChangeProducer;
		$this->netteUser = $netteUser;
		$this->rondoDataLayer = $rondoDataLayer;
		$this->cache = new Cache($storage, self::class);
	}

	public static function getSubscribedEvents(): array
	{
		return [
			UserCreatedEvent::class => [
				['initSettings', 100],
//				['sendVerificationEmail', 20],
				['sendRecommenderNotification', 10],
			],
			UserRemovedEvent::class => [
				['clearUserPayouts', 100],
				['clearUserEmails', 100],
				['clearUserSms', 100],
			],
			UserRequestedNewPasswordEvent::class => [
				['sendRequestNewPasswordEmail', 20],
			],
			LoggedInEvent::class => [
				['trackLogIn', 20],
				['verifyIfFacebookIsConnected', 30],
				['assignStoredUtms', 10],
			],
			RequestEvent::class => [
				['trackLastVisit', 20],
			],
		];
	}

	public function initSettings(UserCreatedEvent $userCreatedEvent)
	{
		$user = $userCreatedEvent->getUser();

		if (!$user->getUtm()) {
			$user->setUtm($this->utmLayer->getUtm());
		}

		if ($this->parentReferencingLayer->getParentUser()) {
			$user->setParentUser($this->parentReferencingLayer->getParentUser());
		}

		if ($partnerOrganization = $this->parentReferencingLayer->getPartnerOrganization()) {
			$user->setPartnerOrganization($partnerOrganization);
		}

		if ($this->rondoDataLayer->getData1() !== null) {
			$user->getSegmentData()->setRondoData1($this->rondoDataLayer->getData1());
		}

		if ($this->rondoDataLayer->getData2() !== null) {
			$user->getSegmentData()->setRondoData2($this->rondoDataLayer->getData2());
		}

		$user->setIp($this->clientLayer->getIp());
		$user->setReferer($this->clientLayer->getReferer());
		$user->setUserAgent($this->clientLayer->getUserAgent());
		$user->setFirstVisitPage($this->parentReferencingLayer->getEntrancePage());
		$user->setRegistrationPage(trim($this->httpRequest->getUrl()->getPath(), '/'));

		$this->userFacade->saveUser($user, false);
	}

	public function verifyIfFacebookIsConnected()
	{
		/** @var User $user */
		$user = $this->netteUser->getIdentity();

		if ($user->isFbConnected()) {
			$this->userFacade->verifyUserEmail($user);
		}
	}

	public function sendRecommenderNotification(UserCreatedEvent $userCreatedEvent)
	{
		$user = $userCreatedEvent->getUser();

		if ($user->getParentUser()) {
			if ($user->isSlovak() || $user->isCzech() || $user->isPolish() || $user->isRomanian() || $user->isHungarian() || $user->isSlovenian()) {
				$recommendedUser = $user;
				$user = $user->getParentUser();

				$emailDataObject = $this->emailTemplateFactory->buildRegistrationRecommenderEmail($user, $recommendedUser);

				$duplicityHash = substr(sha1('recommender' . $user->getId()) . $recommendedUser->getId(), -32);

				if ($emailDataObject !== null) {
					$this->messageFacade->sendEmailByDTO($user, $emailDataObject, $duplicityHash);
				}
			} else {
				$this->messageFacade->sendRegistrationRecommenderEmail($user->getParentUser(), $user);
			}
		}
	}

	public function assignStoredUtms()
	{
		/** @var User $user */
		$user = $this->netteUser->getIdentity();

		foreach ($this->utmLayer->pickupStoredUtms() as $utmData) {
			$utm = $utmData['utm'];
			$changedAt = $utmData['storedAt'];

			$this->userUtmChangeProducer->scheduleTrackUserUtmChange($user, $utm, $changedAt);
		}
	}

	public function trackLogIn()
	{
		/** @var User $user */
		$user = $this->netteUser->getIdentity();

		if ($user) {
			$this->sqlQueryScheduler->trackLastLoggedInAt($user, new \DateTime());
		}
	}

	public function sendRequestNewPasswordEmail(UserRequestedNewPasswordEvent $userRequestedNewPasswordEvent)
	{
		$this->messageFacade->sendNewPasswordEmail($userRequestedNewPasswordEvent->getUser());
	}

	public function clearUserPayouts(UserRemovedEvent $userRemovedEvent)
	{
		$this->payoutFacade->clearUserPayouts($userRemovedEvent->getUser());
	}

	public function clearUserEmails(UserRemovedEvent $userRemovedEvent)
	{
		$this->messageFacade->clearUserEmails($userRemovedEvent->getUser());
	}

	public function clearUserSms(UserRemovedEvent $userRemovedEvent)
	{
		$user = $userRemovedEvent->getUser();

		$this->messageFacade->clearUserSms($user);

		$this->phoneNumberVerificationManager->clearUserData($user);
	}

	public function trackLastVisit(RequestEvent $requestEvent)
	{
		if ($this->netteUser->isLoggedIn() === false) {
			return;
		}

		/** @var User $user */
		$user = $this->netteUser->getIdentity();

		$userLastWebVisitedAt = $user->getSegmentData()->getLastWebVisitAt();

		$cacheKey = 'lastWebVisitAt_' . $user->getId();

		if ($this->cache->load($cacheKey) === null && ($userLastWebVisitedAt === null || $userLastWebVisitedAt < new \DateTime('-1 hour'))) {
			$this->sqlQueryScheduler->scheduleUpdateLastWebVisitAt($user->getId());
			$this->cache->save($cacheKey, true, ['expire' => '1 hour']);
		}
	}
}
