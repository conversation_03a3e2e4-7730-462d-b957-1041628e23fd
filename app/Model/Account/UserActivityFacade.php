<?php

namespace tipli\Model\Account;

use Nette\SmartObject;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserActivity;
use tipli\Model\Account\Producers\UserActivityProducer;
use tipli\Model\Account\Repositories\UserActivityRepository;
use tipli\Model\Inbox\Entities\NotificationCampaign;

class UserActivityFacade
{
	use SmartObject;

	public function __construct(
		private UserActivityManager $userActivityManager,
		private UserActivityRepository $userActivityRepository,
		private UserActivityProducer $userActivityProducer
	) {
	}

	public function trackUserActivity(
		User $user,
		string $eventName,
		?string $campaign,
		string $source,
		\DateTime $dateTime,
		?string $data = null,
		?NotificationCampaign $notificationCampaign = null
	): UserActivity {
		$uniqueId = UserActivity::generateUniqueId($user, $eventName, $source, $dateTime, $notificationCampaign);

		$userActivity = $this->userActivityRepository->findByUniqueId($uniqueId);
		if ($userActivity !== null) {
			return $userActivity;
		}

		return $this->userActivityManager->createUserActivity($user, $eventName, $campaign, $source, $dateTime, $data, $notificationCampaign);
	}

	public function scheduleUserActivity(User $user, string $eventName, ?string $campaign, string $source, \DateTime $dateTime, ?string $data = null, ?NotificationCampaign $notificationCampaign = null): void
	{
		$this->userActivityProducer->scheduleUserActivity($user, $eventName, $campaign, $source, $dateTime, $data, $notificationCampaign);
	}
}
