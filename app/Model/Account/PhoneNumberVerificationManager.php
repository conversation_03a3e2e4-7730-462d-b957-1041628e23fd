<?php

namespace tipli\Model\Account;

use Nette\Utils\Strings;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Account\Entities\PhoneNumberVerificationCode;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Repositories\PhoneNumberVerificationCodeRepository;
use tipli\Model\Configuration;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Session\SectionFactory;
use tipli\TooManyRequestsException;
use Tracy\Debugger;

class PhoneNumberVerificationManager
{
	/** @var EntityManager */
	private $em;

	/** @var PhoneNumberVerificationCodeRepository */
	private $phoneNumberVerificationCodeRepository;

	/** @var MessageFacade */
	private $messageFacade;

	/** @var SectionFactory */
	private $sectionFactory;

	/** @var Configuration */
	private $configuration;

	public function __construct(
		EntityManager $em,
		PhoneNumberVerificationCodeRepository $phoneNumberVerificationCodeRepository,
		MessageFacade $messageFacade,
		SectionFactory $sectionFactory,
		Configuration $configuration
	) {
		$this->em = $em;
		$this->phoneNumberVerificationCodeRepository = $phoneNumberVerificationCodeRepository;
		$this->messageFacade = $messageFacade;
		$this->sectionFactory = $sectionFactory;
		$this->configuration = $configuration;
	}

	public function createPhoneNumberVerificationCode(User $user, string $ip)
	{
		$code = new PhoneNumberVerificationCode($user, $ip);

		if ($this->configuration->getMode() === 'test' || $user->getEmail() === '<EMAIL>') {
			$code->setVerificationCode('1234');
		}

		return $this->savePhoneNumberVerificationCode(
			$code
		);
	}

	public function savePhoneNumberVerificationCode(PhoneNumberVerificationCode $phoneNumberVerificationCode)
	{
		$this->em->persist($phoneNumberVerificationCode);
		$this->em->flush($phoneNumberVerificationCode);

		return $phoneNumberVerificationCode;
	}

	public function findValidVerificationCode(User $user, $phoneNumber, $verificationCode = null)
	{
		return $this->phoneNumberVerificationCodeRepository->findValidVerificationCode($user, $phoneNumber, $verificationCode);
	}

	public function useVerificationCode(PhoneNumberVerificationCode $verificationCode)
	{
		$verificationCode->useVerificationCode();

		$this->savePhoneNumberVerificationCode($verificationCode);
	}

	public function requestVerification(User $user, bool $requestAgain, string $ip)
	{
		if ($this->hasTooManyRequests($user, $ip)) {
			throw new TooManyRequestsException('Too many requests!');
		}

		$verificationCode = $this->findValidVerificationCode($user, $user->getPhoneNumber());

		// Request new verification code - invalidate old code
		if ($verificationCode && $requestAgain) {
			$this->useVerificationCode($verificationCode);
			$verificationCode = null;
		}

		if (!$verificationCode || $requestAgain) {
			$verificationCode = $this->createPhoneNumberVerificationCode($user, $ip);

			$this->messageFacade->sendVerificationSms(
				$user,
				$verificationCode->getVerificationCode(),
				$verificationCode->getValidTill()
			);
		}
	}

	private function hasTooManyRequests(User $user, string $ip): bool
	{
		if ($this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestsForUser($user, (new \DateTime())->modify('- 1 hour')) > 10) {
			return true;
		}

		if ($this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestsForUser($user, (new \DateTime())->modify('- 24 hours')) > 20) {
			return true;
		}

		if ($this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestsForIp($ip, (new \DateTime())->modify('- 1 hour')) > 10) {
			return true;
		}

		if ($this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestsForIp($ip, (new \DateTime())->modify('- 24 hours')) > 20) {
			return true;
		}

		// TODO rename
		if (
			$user->getPhoneNumber()
			&& $this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestForPhoneNumber($user->getPhoneNumber(), (new \DateTime())->modify('- 3 days')) >= 3
		) {
			Debugger::log($user->getPhoneNumber() . '- Too many requests for phone number', 'sms-warning');
			return true;
		}

		if (
			$user->getPhoneNumber()
			&& (Strings::startsWith($user->getPhoneNumber(), '+386') || Strings::startsWith($user->getPhoneNumber(), '+359'))
			&& $user->getCreatedAt() > (new \DateTime())->modify('- 2 minutes')
		) {
			Debugger::log($user->getPhoneNumber() . ' - User registered within 2 minutes', 'sms-warning');
			return true;
		}

		if (
			$user->getPhoneNumber()
			&& Strings::startsWith($user->getPhoneNumber(), '+386')
			&& $this->phoneNumberVerificationCodeRepository->findCountOfVerificationRequestsForPhoneNumberCountryCode('+386', (new \DateTime())->modify('- 30 minutes')) > 20
		) {
			Debugger::log($user->getPhoneNumber() . '- Too many requests for country code +386', 'warning');
			return true;
		}

		$section = $this->sectionFactory->getResendVerificationSmsSecuritySection();
		$count = $section->count ? : 0;
		$count += 1;

		if ($count > 5 && !$section->blockedTill) {
			$section->blockedTill = (new \DateTime())->modify('+ 5 minutes');
		}

		if ($section->blockedTill > new \DateTime()) {
			return true;
		}

		if ($section->blockedTill && $section->blockedTill < new \DateTime()) {
			$section->blockedTill = null;
			$section->count = 0;
			$count = 0;
		}

		$section->blockedTill = null;
		$section->count = $count;

		return false;
	}

	public function clearUserData(User $user)
	{
		$codes = $this->phoneNumberVerificationCodeRepository->findCodesByUser($user);

		/** @var PhoneNumberVerificationCode $sms */
		foreach ($codes as $code) {
			$code->setPhoneNumber(null);

			$this->savePhoneNumberVerificationCode($code);
		}
	}
}
