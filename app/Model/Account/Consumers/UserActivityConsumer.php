<?php

namespace tipli\Model\Account\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Account\Messages\UserActivityMessage;
use tipli\Model\Account\UserActivityFacade;
use tipli\Model\Account\UserFacade;
use tipli\Model\Inbox\NotificationFacade;
use tipli\Model\Marketing\BannerFacade;
use tipli\Model\RabbitMq\BaseConsumer;

class UserActivityConsumer extends BaseConsumer implements IConsumer
{
	public function __construct(
		private NotificationFacade $notificationFacade,
		private UserFacade $userFacade,
		private UserActivityFacade $userActivityFacade,
		private BannerFacade $bannerFacade
	) {
	}

	public function consume(Message $data): int
	{
		$message = UserActivityMessage::fromJson($data->content);

		$user = $this->userFacade->find($message->userId);
		$campaign = $message->notificationCampaignId !== null ? $this->notificationFacade->findNotificationCampaign($message->notificationCampaignId) : null;
		$banner = isset($message->bannerId) ? $this->bannerFacade->find($message->bannerId) : null;

		$this->userActivityFacade->trackUserActivity(
			$user,
			$message->eventName,
			$message->campaign,
			$message->source,
			$message->dateTime,
			$message->data,
			$campaign,
			$banner
		);

		return IConsumer::MESSAGE_ACK;
	}
}
