<?php

namespace tipli\Model\Refunds\Entities;

use tipli\Model\Account\Entities\User;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Refunds\Repositories\RefundSolutionRepository")
 * @ORM\Table(name="tipli_refunds_refund_solution")
 */
class RefundSolution
{
	public const STATE_PARTNER_APPROVED = 'partner_approved';
	public const STATE_PARTNER_DECLINED = 'partner_declined';
	public const STATE_PARTNER_PENDING = 'partner_pending';
	public const STATE_OUR_FAULT = 'our_fault';
	public const STATE_OPENED = 'opened';
	public const STATE_WAITING = 'waiting';
	public const STATE_IGNORED = 'ignored';
	public const STATE_CANNOT_RESOLVE = 'cannot_resolve';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="Refund", inversedBy="refundSolution")
	 * @ORM\JoinColumn(name="refund_id", referencedColumnName="id")
	 * @var Refund
	 */
	private $refund;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 * @var User|null
	 */
	private $user;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $exportedAt;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $state = self::STATE_OPENED;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $note;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $reason;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $stateUpdatedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $inAffiliateRevisedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	public function __construct(Refund $refund)
	{
		$this->refund = $refund;
		$this->createdAt = new \DateTime();
	}

	/**
	 * @return User|null
	 */
	public function getUser(): ?User
	{
		return $this->user;
	}

	/**
	 * @return string|null
	 */
	public function getState(): ?string
	{
		return $this->state;
	}

	/**
	 * @return \DateTime|null
	 */
	public function getExportedAt(): ?\DateTime
	{
		return $this->exportedAt;
	}

	public function export(): void
	{
		$this->exportedAt = new \DateTime();
	}

	/**
	 * @param User|null $user
	 */
	public function setUser(?User $user): void
	{
		$this->user = $user;
	}

	/**
	 * @param string|null $state
	 */
	public function setState(?string $state): void
	{
		$this->state = $state;
		$this->stateUpdatedAt = new \DateTime();
	}

	/**
	 * @return string|null
	 */
	public function getReason(): ?string
	{
		return $this->reason;
	}

	/**
	 * @param string|null $reason
	 */
	public function setReason(?string $reason): void
	{
		$this->reason = $reason;
	}

	/**
	 * @return string|null
	 */
	public function getNote(): ?string
	{
		return $this->note;
	}

	/**
	 * @param string|null $note
	 */
	public function setNote(?string $note): void
	{
		$this->note = $note;
	}

	public static function getStates(): array
	{
		return [
			self::STATE_PARTNER_APPROVED => 'Partner uznal',
			self::STATE_PARTNER_DECLINED => 'Partner neuznal',
			self::STATE_OUR_FAULT => 'Naše chyba',
			self::STATE_OPENED => 'čekající',
			self::STATE_WAITING => 'v řešení',
			self::STATE_IGNORED => 'Neřešíme',
			self::STATE_CANNOT_RESOLVE => 'Nelze zadat',
		];
	}

	public function getStateUpdatedAt(): ?\DateTime
	{
		return $this->stateUpdatedAt;
	}

	public function getInAffiliateRevisedAt(): ?\DateTime
	{
		return $this->inAffiliateRevisedAt;
	}

	public function setInAffiliateRevisedAt(?\DateTime $inAffiliateRevisedAt): void
	{
		$this->inAffiliateRevisedAt = $inAffiliateRevisedAt;
	}
}
