<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Offers\Repositories;

use DateTime;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Offers\Entities\Offer;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Websites\Entities\Website;

class OfferRepository extends EntityRepository
{
	public function getOffers(Localization $localization = null, $current = true): QueryBuilder
	{
		$qb = $this->createQueryBuilder('o');

		if ($localization) {
			$qb->andWhere('o.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('o.localization', 'l');
		}

		if ($current) {
			$qb->andWhere('o.validTill > :validTill')
				->setParameter('validTill', new DateTime());
		}

		if ($current == false) {
			$qb->andWhere('o.validTill < :validTill')
				->setParameter('validTill', new DateTime());
		}

		return $qb;
	}

	public function findOfferBySlug(Localization $localization, string $slug)
	{
		$qb = $this->getOffers($localization)
			->andWhere('o.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findOffersToConfirm(?Localization $localization = null)
	{
		$qb = $this->getOffers()
			->andWhere('o.confirmedAt IS NULL')
			->andWhere('o.archivedAt IS NULL')
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_LEAFLET)
        ;

        if ($localization) {
            $qb->andWhere('o.localization = :localization')
                ->setParameter('localization', $localization);
        }

        return $qb->getQuery();
	}

	public function getCountOfOffersToConfirm(?Localization $localization = null): int
    {
		$qb = $this->getOffers()
			->select('count(o.id)')
			->andWhere('o.confirmedAt IS NULL')
			->andWhere('o.archivedAt IS NULL')
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_LEAFLET)
        ;

        if ($localization) {
            $qb->andWhere('o.localization = :localization')
                ->setParameter('localization', $localization);
        }

        return (int) $qb->getQuery()
            ->getSingleScalarResult();
	}

    public function findCountOfOffersToAnnotateByLocalization()
    {
        return $this->getOffers()
            ->select('count(o.id) as count, l.name, l.id')
            ->innerJoin('o.localization', 'loc')
            ->andWhere('o.confirmedAt IS NULL')
            ->andWhere('o.archivedAt IS NULL')
            ->andWhere('o.type = :type')
            ->setParameter('type', Offer::TYPE_LEAFLET)
            ->addGroupBy('loc.id')
            ->getQuery()
            ->getArrayResult();
    }

	public function findOfferToAssignProduct(Localization $localization)
	{
		return $this->getOffers($localization)
			->andWhere('o.confirmedAt IS NOT NULL')
			->andWhere('o.archivedAt IS NULL')
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_LEAFLET)
			->setMaxResults(1)
			->getQuery();
	}

	public function getCountOfOffersToAssignProduct(Localization $localization)
	{
		return (int) $this->getOffers($localization)
			->select('count(o.id)')
			->andWhere('o.confirmedAt IS NOT NULL')
			->andWhere('o.archivedAt IS NULL')
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_LEAFLET)
			->getQuery()->getSingleScalarResult();
	}

	public function getOffersByShop(Shop $shop, $current, $type)
	{
		$qb = $this->getOffers(null, $current)
			->andWhere('o.confirmedAt IS NOT NULL')
			->andWhere('o.shop = :shop')
			->setParameter('shop', $shop)
        ;

		if ($type) {
			$qb->andWhere('o.type = :type')
				->setParameter('type', $type);

            if ($type === Offer::TYPE_COUPON) {
                $qb->andWhere('o.discountAmount > 0');
            }

            if ($type === Offer::TYPE_LEAFLET) {
                $qb->leftJoin('o.tags', 't')
                    ->addOrderBy('t.priority', 'DESC')
                ;

                $qb->innerJoin('o.leafletPage', 'lp')
                    ->innerJoin('lp.leaflet', 'leaflet')
                    ->addOrderBy('leaflet.priority', 'DESC')
                ;
            }
		}

        $qb->addOrderBy('o.priority', 'DESC')
            ->addOrderBy('o.validTill', 'DESC');

		return $qb->getQuery();
	}

    public function getOffersByShops(array $shops, $current, $type): Query
    {
        $qb = $this->getOffers(null, $current)
            ->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.shop IN (:shops)')
            ->setParameter('shops', $shops)
            ->addOrderBy('o.priority', 'DESC')
            ->addOrderBy('o.validTill', 'DESC');

        if ($type) {
            $qb->andWhere('o.type = :type')
                ->setParameter('type', $type);

            if ($type === Offer::TYPE_COUPON) {
                $qb->andWhere('o.discountAmount > 0');
            }
        }

        return $qb->getQuery();
    }

	public function findOffersByLeaflet(Leaflet $leaflet, int $limit = 10, $onlyCurrent = true, string $type = null)
	{
		$qb = $this->getOffers(null, $onlyCurrent)
			->innerJoin('o.leafletPage', 'lp')
			->andWhere('lp.leaflet = :leaflet')
			->setParameter('leaflet', $leaflet)
			->addOrderBy('o.priority', 'DESC')
			->addOrderBy('o.validTill', 'DESC')
			->andWhere('o.confirmedAt IS NOT NULL')
			->setMaxResults($limit);

		if ($type) {
			$qb->andWhere('o.type = :type')
				->setParameter('type', $type);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findOffersByNames(array $names, Localization $localization)
	{
		$qb = $this->getOffers($localization, false)
			->andWhere('o.name IN (:names)')
			->setParameter('names', $names);

		return $qb->getQuery();
	}

    public function getOffersByFulltext($keyword, Localization $localization, $current = true, bool $onlyWithLeafletPage = false, ?string $websiteType = null): QueryBuilder
    {
        $keywords = explode(';', $keyword);

        $qb = $this->createQueryBuilder('o')
            ->innerJoin('o.shop', 's')
            ->where('o.localization = :localization')
            ->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.leafletPage IS NOT NULL')
            ->andWhere('o.validSince BETWEEN :since AND :now')
            ->setParameter('localization', $localization)
            ->setParameter('since', new \DateTimeImmutable('-12 months'))
            ->setParameter('now', new \DateTimeImmutable());

        // fulltext match (name or OCR)
        $orX = $qb->expr()->orX();
        foreach ($keywords as $index => $keywordPart) {
            $paramName = 'keyword_' . $index;
            $orX->add($qb->expr()->like('o.name', ':' . $paramName));
            $orX->add($qb->expr()->like('o.ocrOutput', ':' . $paramName));
            $qb->setParameter($paramName, '%' . $keywordPart . '%');
        }
        $qb->andWhere($orX);

        // filters by website type
        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = true');
        } elseif (in_array($websiteType, [Website::MODULE_KAUFINO, Website::MODULE_KAUFINO_SUBDOMAIN], true)) {
            $qb->andWhere('s.activeKaufino = true');
        }
        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = false');
        }

        return $qb;
    }

	public function findOffersByFulltext($keyword, Localization $localization, $current = true, bool $onlyWithLeafletPage = false, ?string $websiteType = null)
	{
		$qb = $this->getOffers($localization, $current)
            ->innerJoin('o.shop', 's')
			->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.archivedAt IS NULL')
			->andWhere('o.name LIKE :keyword OR o.ocrOutput LIKE :keyword')
			->setParameter('keyword', '%' . $keyword . '%')
		;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = true');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = true');
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

		if ($onlyWithLeafletPage) {
			$qb->andWhere('o.leafletPage IS NOT NULL');
		}

		return $qb->getQuery();
	}

    public function findCurrentOffersByFulltext($keyword, Localization $localization, ?string $websiteType = null)
    {
        $keywords = explode(';', $keyword);

        $qb = $this->getOffers($localization, true)
            ->innerJoin('o.shop', 's')
            ->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.archivedAt IS NULL');

        $orX = $qb->expr()->orX();
        foreach ($keywords as $index => $keyword) {
            $keyword = trim($keyword);
            $placeholder = 'keyword_' . $index;
            $orX->add($qb->expr()->like('o.name', ':' . $placeholder));
            $orX->add($qb->expr()->like('o.ocrOutput', ':' . $placeholder));
            $qb->setParameter($placeholder, '%' . $keyword . '%');
        }
        $qb->andWhere($orX);

        $qb->andWhere('o.leafletPage IS NOT NULL')
            ->andWhere('o.validSince <= :now')
            ->setParameter('now', new DateTime());

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = true');
        } elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('s.activeKaufino = true');
        }

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.hidden = 0');
        }

        $qb->addOrderBy('o.currentPrice', 'ASC');

        return $qb->getQuery()
            ->getResult();
    }

    public function findFutureOffersByFulltext($keyword, Localization $localization, ?string $websiteType = null)
    {
        $keywords = explode(';', $keyword);

        $qb = $this->getOffers($localization, true)
            ->innerJoin('o.shop', 's')
            ->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.archivedAt IS NULL')
            ->andWhere('o.leafletPage IS NOT NULL')
            ->andWhere('o.validSince >= :now')
            ->setParameter('now', new DateTime())
        ;

        $orX = $qb->expr()->orX();
        foreach ($keywords as $index => $keyword) {
            $keyword = trim($keyword);
            $placeholder = 'keyword_' . $index;
            $orX->add($qb->expr()->like('o.name', ':' . $placeholder));
            $orX->add($qb->expr()->like('o.ocrOutput', ':' . $placeholder));
            $qb->setParameter($placeholder, '%' . $keyword . '%');
        }
        $qb->andWhere($orX);

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = true');
        } elseif ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.activeKaufino = true');
        }

        return $qb->getQuery()
            ->getResult();
    }

    public function findAvgPriceFromOffers(string $keyword, Localization $localization, ?string $websiteType = null): array
    {
        $qb = $this->getOffersByFulltext($keyword, $localization, $websiteType);
        $qb->select("CONCAT(YEAR(o.validSince), '-', LPAD(MONTH(o.validSince), 2, '0')) AS month")
            ->addSelect("AVG(o.currentPrice) AS avgPrice")
            ->
            ->andWhere('o.commonPrice > 0')
            ->groupBy('month')
            ->orderBy('month', 'ASC');

        return $qb->getQuery()->getArrayResult();
    }

    public function findAvgDiscountFromOffers(string $keyword, Localization $localization, ?string $websiteType = null)
    {
        $qb = $this->getOffersByFulltext($keyword, $localization, $websiteType);
        $qb->select("CONCAT(YEAR(o.validSince), '-', LPAD(MONTH(o.validSince), 2, '0')) AS month")
            ->addSelect("AVG((1 - (o.currentPrice / o.commonPrice)) * 100) AS avgDiscountPercent")
            ->groupBy('month')
            ->orderBy('month');

        return $qb->getQuery()->getArrayResult();
    }

    public function findCountOfOffersByShop(string $keyword, Localization $localization, ?string $websiteType = null)
    {
        $qb = $this->getOffersByFulltext($keyword, $localization, $websiteType);
        $qb->select("CONCAT(YEAR(o.validSince), '-', LPAD(MONTH(o.validSince), 2, '0')) AS month")
            ->addSelect("s.name AS shopName")
            ->addSelect("COUNT(o.id) AS offerCount")
            ->groupBy('month')
            ->addGroupBy('s.name')
            ->orderBy('month', 'ASC')
            ->addOrderBy('shopName', 'ASC');

        return $qb->getQuery()->getArrayResult();
    }

    public function findTopOffers(Localization $localization, ?int $limit, ?string $type = null, ?array $shops = null, ?bool $withLeafletPage = null, ?array $exceptOffers = [], ?Tag $tag = null, bool $groupByShop = false): Query
    {
		$qb = $this->getOffers($localization, true)
            ->innerJoin('o.shop', 's')
			->andWhere('o.confirmedAt IS NOT NULL')
            ->andWhere('o.archivedAt IS NULL')
            ->addOrderBy('s.priorityLeaflets', 'DESC')
		;

        if ($withLeafletPage === true) {
            $qb
                ->andWhere('o.leafletPage IS NOT NULL');
        }

        if ($shops) {
            $qb->andWhere('o.shop IN (:shops)')
                ->setParameter('shops', $shops);
        }

		if ($type) {
			$qb->andWhere('o.type = :type')
				->setParameter('type', $type);
		}

        if ($exceptOffers) {
            $qb->andWhere('o NOT IN (:exceptOffers)')
                ->setParameter('exceptOffers', $exceptOffers);
        }

        if ($tag) {
            $qb->innerJoin('o.tags', 't')
                ->andWhere('t = :tag')
                ->setParameter('tag', $tag)
            ;
        }

        if ($groupByShop) {
            $qb->addGroupBy('o.shop');
        }

		return $qb
            ->setMaxResults($limit)
            ->getQuery();
	}

	public function findTopCouponOffers(Localization $localization, int $limit, int $offset)
	{
		return $this->getOffers($localization)
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_COUPON)
			->setMaxResults($limit)
			->setFirstResult($offset)
			->getQuery()
			->getResult();
	}

	public function findProducts(Localization $localization): Query
	{
		return $this->getOffers($localization)
			->andWhere('o.type = :product')
			->setParameter('product', Offer::TYPE_PRODUCT)
			->getQuery();
	}

	public function findTopCoupons(Localization $localization, ?int $limit = null): array
	{
		$qb = $this->getOffers($localization)
			->innerJoin('o.shop', 's')
			->andWhere('s.activeCoupons = true')
			->andWhere('o.type = :type')
			->setParameter('type', Offer::TYPE_COUPON)
            ->andWhere('o.discountAmount > 0')
			->addOrderBy('o.id', 'DESC')
			->addGroupBy('o.shop')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()->getResult();
	}

	public function findTopOfferByShop(Shop $shop, ?string $discountType = null, string $type = null)
	{
		$qb = $this->getOffers()
			->andWhere('o.shop = :shop')
			->setParameter('shop', $shop)
            ->andWhere('o.discountAmount > 0')
			->addOrderBy('o.discountAmount', 'DESC')
			->setMaxResults(1)
        ;

        if ($discountType) {
            $qb->andWhere('o.discountType = :discountType')
                ->setParameter('discountType', $discountType);
        }

        if ($type) {
            $qb->andWhere('o.type = :type')
                ->setParameter('type', $type);
        }

        return $qb
            ->getQuery()
            ->getOneOrNullResult();
	}

    public function findOffersToRemove()
    {
        return $this->getOffers(null, null)
            ->andWhere('o.validTill < :validTill')
            ->setParameter('validTill', new DateTime('-30 days'))
            ->andWhere('o.removedAt IS NULL')
            ->addOrderBy('o.validTill', 'ASC')
            ->setMaxResults(1000)
            ->getQuery()
            ->getResult()
        ;
    }
}
