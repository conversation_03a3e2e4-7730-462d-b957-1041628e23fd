<?php

namespace tipli\Commands;

use Nette\Database\Context;
use Nette\Database\Row;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\Model\Inbox\Producers\NotificationsProducer;

class PushNotifications extends Job
{
	private const MAX_NOTIFICATIONS = 10000;

	public function __construct(
		private Context $context,
		private NotificationsProducer $notificationsProducer,
	) {
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:push-notifications:run');
	}

	public function start($hardRun = false, $modulo = null)
	{
		$log = $this->onStart();

		$countOfPushedNotifications = $this->pushNotifications();

		$this->onFinish($log, 'Pushnuto ' . $countOfPushedNotifications . ' notifikaci');

		if ($countOfPushedNotifications === 0) {
			$this->sleepAndShutdown(20);
		}
	}

	private function pushNotifications(): int
	{
		$notifications = $this->context->query('
			SELECT
				n.id,
				n.user_id,
				n.notification_campaign_id,
				b.title,
				b.content,
				COALESCE(c.url, b.url) AS url,
				us.access_token AS user_access_token
			FROM
				tipli_inbox_notification n
				INNER JOIN tipli_inbox_notification_body b ON b.id = n.body_id
				LEFT JOIN tipli_inbox_notification_campaign c ON c.id = n.notification_campaign_id
				INNER JOIN tipli_account_user u ON u.id = n.user_id
				INNER JOIN tipli_account_user_security us ON u.id = us.user_id
			WHERE
				n.push_scheduled_at IS NOT NULL
				AND n.push_scheduled_at <= NOW()
				AND n.push_enqueued_at IS NULL
				AND n.push_failed_at IS NULL
				AND n.scheduled_at >= ?
			ORDER BY
				 n.scheduled_at DESC
			LIMIT ?
		', (new \DateTime())->modify('- 24 hours'), self::MAX_NOTIFICATIONS)->fetchPairs('id');

		$usersIds = array_map(static function (Row $row) {
			return $row->user_id;
		}, $notifications);

		if (count($usersIds) === 0) {
			return 0;
		}

//		/** @var Row[] $usersDeviceTokens */
//		$usersDeviceTokensWithVersion = $this->context->query('
//			SELECT dt.user_id, GROUP_CONCAT(dt.token) AS device_tokens
//			FROM tipli_account_user_device_token dt
//			WHERE dt.user_id IN (?) AND dt.valid_till >= NOW() AND dt.version IS NOT NULL AND platform = "android"
//			GROUP BY dt.user_id
//		', $usersIds)->fetchPairs('user_id');
//
//		$this->sendMultiPushNotifications($notifications, $usersDeviceTokensWithVersion, 'android');

//		/** @var Row[] $usersDeviceTokens */
//		$usersDeviceTokensWithVersion = $this->context->query('
//			SELECT dt.user_id, GROUP_CONCAT(dt.token) AS device_tokens
//			FROM tipli_account_user_device_token dt
//			WHERE dt.user_id IN (?) AND dt.valid_till >= NOW() AND dt.version IS NOT NULL AND platform = "iOS"
//			GROUP BY dt.user_id
//		', $usersIds)->fetchPairs('user_id');

//		$this->sendMultiPushNotifications($notifications, $usersDeviceTokensWithVersion, 'iOS');

//		WHERE dt.user_id IN (?) AND dt.valid_till >= NOW() AND (dt.version IS NULL OR (dt.version IS NOT NULL AND platform = "android"))
		/** @var Row[] $usersDeviceTokens */
		$usersDeviceTokens = $this->context->query('
			SELECT dt.user_id, GROUP_CONCAT(dt.token) AS device_tokens
			FROM tipli_account_user_device_token dt
			WHERE dt.user_id IN (?) AND dt.valid_till >= NOW()
			GROUP BY dt.user_id
		', $usersIds)->fetchPairs('user_id');

		/** @var Row $notification */
		foreach ($notifications as $notification) {
			if (isset($usersDeviceTokens[$notification->user_id]) === false) {
				continue;
			}

			$this->scheduleSendPushNotification($notification, explode(',', $usersDeviceTokens[$notification->user_id]->device_tokens));
		}

		$this->context->query('UPDATE tipli_inbox_notification SET push_enqueued_at = NOW() WHERE id IN (?)', array_keys($notifications));

		return count($notifications);
	}

	public function scheduleSendPushNotification($notification, array $deviceTokens): void
	{
		if (count($deviceTokens) === 0) {
			return;
		}

		$url = $notification->url ?: null;

		if ($url !== null && Strings::contains($url, 'tipli')) {
			$urlObject = new Url($url);
			$urlObject->setQueryParameter('at', $notification->user_access_token);

			/** @var string $url */
			$url = $urlObject->getAbsoluteUrl();
		}

		$this->notificationsProducer->schedulePushNotifications(
			$notification->title,
			$notification->content,
			$deviceTokens,
			(string)$notification->id,
			(bool)$notification->url,
			$url,
			false,
			null,
			null,
			$notification->notification_campaign_id
		);
	}

	public function sendMultiPushNotifications(array $notifications, array $userDeviceTokens, string $platform = 'android'): void
	{
		$campaigns = [];

		foreach ($notifications as $notification) {
			if (isset($userDeviceTokens[$notification->user_id]) === false) {
				continue;
			}

			$deviceTokens = explode(',', $userDeviceTokens[$notification->user_id]->device_tokens);

			if ($notification->notification_campaign_id !== null) {
				if (isset($campaigns[$notification->notification_campaign_id]) === false) {
					$campaigns[$notification->notification_campaign_id] = [
						'title' => $notification->title,
						'body' => $notification->content,
						'deviceTokens' => $deviceTokens,
						'notificationId' => 'c' . $notification->notification_campaign_id,
						'webviewAllowed' => (bool) $notification->url,
						'url' => $notification->url,
						'showReviewPopup' => false,
						'reviewPopupTitle' => null,
						'reviewPopupText' => null,
						'notification_campaign_id' => $notification->notification_campaign_id,
						'platform' => $platform,
					];
				} else {
					$campaigns[$notification->notification_campaign_id]['deviceTokens'] = array_merge($campaigns[$notification->notification_campaign_id]['deviceTokens'], $deviceTokens);
				}
			} else {
				$this->scheduleSendPushNotification($notification, $deviceTokens);
			}
		}

		foreach ($campaigns as $campaign) {
			foreach (array_chunk($campaign['deviceTokens'], 100) as $deviceTokens) {
				// reset indexes
				$deviceTokens = array_values($deviceTokens);

				$this->notificationsProducer->schedulePushNotifications(
					$campaign['title'],
					$campaign['body'],
					$deviceTokens,
					$campaign['notificationId'],
					$campaign['webviewAllowed'],
					$campaign['url'],
					$campaign['showReviewPopup'],
					$campaign['reviewPopupTitle'],
					$campaign['reviewPopupText'],
					$campaign['notification_campaign_id'],
					$campaign['platform']
				);
			}
		}
	}
}
