<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Triggers\Entities\Trigger;
use tipli\Model\Triggers\TriggerFacade;
use Tracy\Debugger;

class ScheduleAutoresponders extends Job
{
	// type => $days
	private const AUTORESPONDERS = [
		'howToVideo' => 10,
		//'howToGetRewards' => 3,
		'mobileApp' => 7,
		'addon' => 13,
		//'shops' => 21,
		//'electro' => 28,
		//'travel' => 35,
		//'aliexpress' => 42,
		//'fashion' => 49,
		//'health' => 56,
		'feedbackAfterRedirection' => 3,
		'deals' => 7,
		'bonusAfterRegistration' => 4,
		'bonusAfterRegistrationLastDay' => 6,
		'luckyShop' => 10,
		'doubleExtraReward' => 16,
		//'reactivationCampaign' => 0,
	];

	/** @var TriggerFacade */
	private $triggerFacade;

	/** @var Context */
	private $context;

	public function __construct(TriggerFacade $triggerFacade, Context $context)
	{
		parent::__construct();

		$this->triggerFacade = $triggerFacade;
		$this->context = $context;
	}

	protected function configure()
	{
		parent::configure();

		$this->setName('tipli:schedule-autoresponders:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->scheduleAutoresponders();

		$this->onFinish($log);
	}

	private function scheduleAutoresponders()
	{
		foreach (self::AUTORESPONDERS as $type => $daysDelay) {
			switch ($type) {
				case 'howToVideo':
					$this->scheduleHowToVideoAutoresponder($daysDelay);
					break;
				case 'howToGetRewards':
					$this->scheduleHowToGetRewardsAutoresponder($daysDelay);
					break;
				case 'mobileApp':
					$this->scheduleMobileAppAutoresponder($daysDelay);
					break;
				case 'addon':
					$this->scheduleAddonAutoresponder($daysDelay);
					break;
				case 'shops':
					$this->scheduleShopsAutoresponder($daysDelay);
					break;
				case 'electro':
					$this->scheduleElectroAutoresponder($daysDelay);
					break;
				case 'travel':
					$this->scheduleTravelAutoresponder($daysDelay);
					break;
				case 'aliexpress':
					$this->scheduleAliexpressAutoresponder($daysDelay);
					break;
				case 'fashion':
					$this->scheduleFashionAutoresponder($daysDelay);
					break;
				case 'health':
					$this->scheduleHealthAutoresponder($daysDelay);
					break;
				case 'feedbackAfterRedirection':
					$this->scheduleFeedbackAfterRedirection($daysDelay);
					break;
				case 'deals':
					$this->scheduleDealsAutoResponder($daysDelay);
					break;
				case 'bonusAfterRegistration':
					$this->scheduleBonusAfterRegistrationAutoResponder($daysDelay);
					break;
				case 'bonusAfterRegistrationLastDay':
					$this->scheduleBonusAfterRegistrationLastDayAutoResponder($daysDelay);
					break;
				case 'luckyShop':
					$this->scheduleLuckyShopAutoResponder($daysDelay);
					break;
				case 'doubleExtraReward':
					$this->scheduleDoubleExtraRewardAutoResponder($daysDelay);
					break;
				case 'reactivationCampaign':
					$this->scheduleReactivationCampaignAutoResponder();
					break;
				default:
					throw new \InvalidArgumentException('Neznámý typ autoresponderu.');
			}
		}
	}

	public function startAutoresponder(string $type)
	{
		if ($type === 'reactivationCampaign') {
			return $this->scheduleReactivationCampaignAutoResponder();
		}
	}

	private function scheduleReactivationCampaignAutoResponder()
	{
		$users = $this->context->query(
			"
				SELECT u.id
				FROM `tipli_account_user` u
				JOIN tipli_account_segment_data sd ON sd.user_id = u.id
				WHERE u.localization_id = 1
				AND u.active = 1
				AND u.email NOT LIKE '%rondo%'
				AND u.email NOT LIKE '%twisto%'
				AND u.email NOT LIKE '%homecredit%'
				AND u.email NOT LIKE '%@tipli.cz%'
				AND u.emails_unsubscribed_at IS NULL
				AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = ? AND content_type = ?)
				AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = ?)
				AND sd.last_interaction_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)
				AND u.id NOT IN
				(
				SELECT user_id
				FROM tipli_account_user_mailkit
				WHERE status = 'subscribed'
				)
				ORDER BY sd.last_interaction_at DESC
				LIMIT 20000
            ",
			'email',
			'newsletter',
			Trigger::TYPE_AUTORESPONDER_REACTIVATION_CAMPAIGN
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_REACTIVATION_CAMPAIGN
			);
		}

		return count($users);
	}

	private function scheduleBonusAfterRegistrationAutoResponder(int $daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = ?)
		  	AND NOT EXISTS (
				SELECT 1 FROM tipli_transactions_transaction t
				WHERE t.user_id = u.id AND t.type = ?
			);
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59),
			Trigger::TYPE_AUTORESPONDER_BONUS_AFTER_REGISTRATION,
			Transaction::TYPE_COMMISSION
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_BONUS_AFTER_REGISTRATION
			);
		}

		return count($users);
	}

	private function scheduleBonusAfterRegistrationLastDayAutoResponder(int $daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = ?)
		  	AND NOT EXISTS (
				SELECT 1 FROM tipli_transactions_transaction t
				WHERE t.user_id = u.id AND t.type = ?
			);
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59),
			Trigger::TYPE_AUTORESPONDER_BONUS_AFTER_REGISTRATION_LAST_DAY,
			Transaction::TYPE_COMMISSION
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_BONUS_AFTER_REGISTRATION_LAST_DAY
			);
		}

		return count($users);
	}

	private function scheduleDoubleExtraRewardAutoResponder(int $daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = ?)
		  	AND NOT EXISTS (
				SELECT 1 FROM tipli_transactions_transaction t
				WHERE t.user_id = u.id AND t.type = ?
			);
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59),
			Trigger::TYPE_AUTORESPONDER_DOUBLE_EXTRA_REWARD,
			Transaction::TYPE_COMMISSION
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_DOUBLE_EXTRA_REWARD
			);
		}

		return count($users);
	}

	private function scheduleLuckyShopAutoResponder(int $daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = ?)
            AND u.id NOT IN (SELECT user_id FROM tipli_lucky_shop_user_lucky_shops)
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59),
			Trigger::TYPE_AUTORESPONDER_LUCKY_SHOP
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_LUCKY_SHOP
			);
		}

		return count($users);
	}

	private function scheduleHowToVideoAutoresponder($daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (3,4)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'how_to_video\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_HOW_TO_VIDEO
			);
		}

		return count($users);
	}

	private function scheduleHowToGetRewardsAutoresponder($daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.segment = \'new\'
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'how_to_get_rewards\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_HOW_TO_GET_REWARDS
			);
		}

		return count($users);
	}

	private function scheduleMobileAppAutoresponder($daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            LEFT JOIN tipli_account_user_mobile_device md ON md.user_id = u.id
            WHERE u.localization_id IN (3,4)
            AND md.id IS NULL
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'mobile_app\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_MOBILE_APP
			);
		}

		return count($users);
	}

	private function scheduleAddonAutoresponder($daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            INNER JOIN tipli_account_segment_data sd ON sd.user_id = u.id
            INNER JOIN tipli_account_user_data ud ON ud.user_id = u.id
            WHERE u.localization_id IN (1,2,3,4)
            AND sd.addon_installed = 0
            AND ud.platform = \'web\'
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'addon\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_ADDON
			);
		}

		return count($users);
	}

	private function scheduleShopsAutoresponder($daysDelay): int
	{
		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.segment = \'new\'
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'shops\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_SHOPS
			);
		}

		return count($users);
	}

	private function scheduleElectroAutoresponder($daysDelay)
	{
		if ((new \DateTime())->format('w') != 2) {
			return 0;
		}

		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'electronics\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_ELECTRONICS
			);
		}

		return count($users);
	}

	private function scheduleTravelAutoresponder($daysDelay)
	{
		if ((new \DateTime())->format('w') != 2) {
			return 0;
		}

		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'travel\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_TRAVEL
			);
		}

		return count($users);
	}

	private function scheduleAliexpressAutoresponder($daysDelay)
	{
		if ((new \DateTime())->format('w') != 2) {
			return 0;
		}

		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'aliexpress\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_ALIEXPRESS
			);
		}

		return count($users);
	}

	private function scheduleFashionAutoresponder($daysDelay)
	{
		if ((new \DateTime())->format('w') != 2) {
			return 0;
		}

		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'fashion\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_FASHION
			);
		}

		return count($users);
	}

	private function scheduleHealthAutoresponder($daysDelay)
	{
		if ((new \DateTime())->format('w') != 2) {
			return 0;
		}

		$users = $this->context->query(
			'
            SELECT u.id
            FROM tipli_account_user u
            WHERE u.localization_id IN (1,2,3,4,6)
            AND u.created_at >= ? AND u.created_at <= ?
            AND u.emails_unsubscribed_at IS NULL
            AND u.id NOT IN (SELECT user_id from tipli_account_sending_policy WHERE message_type = \'email\' AND content_type = \'autoresponder\')
            AND u.id NOT IN (SELECT user_id FROM tipli_messages_user_auto_responder WHERE `type` = \'health\')
            ',
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(0, 0, 0),
			(new \DateTime('- ' . $daysDelay . ' days'))->setTime(23, 59, 59)
		)->fetchAll();

		foreach ($users as $user) {
			$this->triggerFacade->scheduleCreateTrigger(
				$user->id,
				Trigger::TYPE_AUTORESPONDER_HEALTH
			);
		}

		return count($users);
	}

	private function scheduleFeedbackAfterRedirection($daysDelay): int
	{
		if ((new \DateTime())->format('H') !== '07') {
			return 0;
		}

		$results = $this->context->query(
			'
				SELECT u.id AS userId, r.id AS redirectionId
				FROM tipli_account_user u
				LEFT JOIN tipli_shops_redirection r ON r.user_id = u.id
				LEFT JOIN tipli_shops_shop s ON r.shop_id = s.id
				LEFT JOIN tipli_transactions_transaction t ON t.user_id = u.id AND t.type = ?
				LEFT JOIN tipli_messages_user_auto_responder a ON a.user_id = u.id AND a.type = ? AND a.created_at > ?
				WHERE u.localization_id = 1 AND
				t.id IS NULL AND
				a.id IS NULL AND
				r.created_at >= ? AND
				r.created_at <= ? AND
				u.email NOT LIKE ? AND
				u.email NOT LIKE ? AND
				s.cashback_allowed = ?
            ',
			Transaction::TYPE_COMMISSION,
			Trigger::TYPE_AUTORESPONDER_FEEDBACK_AFTER_REDIRECTION,
			(new \DateTime('-1 month')),
			(new \DateTime('-10 days')),
			(new \DateTime('- ' . $daysDelay . ' days')),
			"%TWISTO%",
			"%RONDO%",
			true
		)->fetchAll();

		echo count($results);

		$userIds = [];

		foreach ($results as $result) {
			if (in_array($result->userId, $userIds)) {
				continue;
			}

			if ($result->redirectionId === null || $result->userId === null) {
				Debugger::log('Feedback after redirection skip ' . $result->userId . ' | ' . $result->redirectionId, 'autoresponders');
				continue;
			}

			Debugger::log('Feedback after redirection ' . $result->userId . ' | ' . $result->redirectionId, 'autoresponders');
			$this->triggerFacade->scheduleCreateTrigger(
				$result->userId,
				Trigger::TYPE_AUTORESPONDER_FEEDBACK_AFTER_REDIRECTION,
				['redirectionId' => $result->redirectionId]
			);

			$userIds[] = $result->userId;
		}

		return count($results);
	}

	private function scheduleDealsAutoResponder($daysDelay): int
	{
//		Debugger::log('Started', 'autoresponders-deals');

		$deals = $this->context->query(
			'
				SELECT d.id, d.shop_id
				FROM tipli_deals_deal d
				WHERE d.valid_since < ? AND
					  d.valid_till > ? AND
				      d.removed_at IS NULL AND
				      d.shop_id IS NOT NULL AND
				      d.promoted_at IS NOT NULL
				ORDER BY d.promoted_at DESC
            ',
			(new \DateTime()),
			(new \DateTime())
		)->fetchAll();

		if (count($deals) === 0) {
//			Debugger::log('No deals found', 'autoresponders-deals');
			return 0;
		}

		$shops = [];
		foreach ($deals as $deal) {
			$shops[$deal->shop_id][] = $deal->id;
		}

		$userShops = [];

		$transactionShops = $this->context->query(
			'
				SELECT DISTINCT t.user_id, t.shop_id
				FROM tipli_account_user u
				LEFT JOIN tipli_transactions_transaction t ON t.user_id = u.id AND t.type = ? AND t.created_at > ?
				LEFT JOIN tipli_account_user_favorite_shop fs ON fs.shop_id = t.shop_id
				LEFT JOIN tipli_messages_user_auto_responder a ON a.user_id = u.id AND a.type = ? AND a.created_at > ?
				WHERE t.shop_id IN (?)
				  AND fs.shop_id IS NOT NULL
				  AND a.user_id IS NULL
            ',
			Transaction::TYPE_COMMISSION,
			(new \DateTime('-1 year')),
			Trigger::TYPE_AUTORESPONDER_DEALS,
			(new \DateTime('-7 days')),
			array_keys($shops)
		)->fetchAll();

		$userShops = $this->mergeUserShops($userShops, $transactionShops);

		$redirectionShops = $this->context->query(
			'
				SELECT DISTINCT r.user_id, r.shop_id
				FROM tipli_account_user u
				LEFT JOIN tipli_shops_redirection r ON r.user_id = u.id AND r.created_at > ?
				LEFT JOIN tipli_account_user_favorite_shop fs ON fs.shop_id = r.shop_id
				LEFT JOIN tipli_messages_user_auto_responder a ON a.user_id = u.id AND a.type = ? AND a.created_at > ?
				WHERE r.shop_id IN (?)
				  AND fs.shop_id IS NOT NULL
				  AND a.user_id IS NULL
            ',
			(new \DateTime('-90 days')),
			Trigger::TYPE_AUTORESPONDER_DEALS,
			(new \DateTime('-7 days')),
			array_keys($shops)
		)->fetchAll();

		$userShops = $this->mergeUserShops($userShops, $redirectionShops);

		$userIds = [];
		foreach ($userShops as $userId => $userShopsIds) {
			if (in_array($userId, $userIds)) {
				continue;
			}

			$deals = [];

			foreach ($shops as $shopId => $dealIds) {
				if (in_array($shopId, $userShopsIds)) {
					$deals = array_merge($deals, $dealIds);
				}
			}

//			Debugger::log('Sending ' . $userId . ' | ' . implode(',', $deals), 'autoresponders-deals');

			$this->triggerFacade->scheduleCreateTrigger(
				$userId,
				Trigger::TYPE_AUTORESPONDER_DEALS,
				['deals' => $deals]
			);
			$userIds[] = $userId;
		}

//		Debugger::log('Ended', 'autoresponders-deals');

		return 1;
	}

	private function mergeUserShops($sourceUserShops, $targetUserShops): array
	{
		foreach ($targetUserShops as $targetUserShop) {
			if ($targetUserShop->user_id === null || $targetUserShop->shop_id === null) {
				continue;
			}

			if (isset($sourceUserShops[$targetUserShop->user_id]) === true) {
				if (in_array($targetUserShop->shop_id, $sourceUserShops[$targetUserShop->user_id]) === false) {
					$sourceUserShops[$targetUserShop->user_id][] = $targetUserShop->shop_id;
				}
			} else {
				$sourceUserShops[$targetUserShop->user_id] = [
					$targetUserShop->shop_id,
				];
			}
		}

		return $sourceUserShops;
	}
}
