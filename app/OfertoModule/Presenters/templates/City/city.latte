{capture $stores}
    {var $uniqueStores = []}
    {foreach $shops as $shop}
        {skipIf !$shop->hasCity($city)}
        {var $uniqueStores[] = $shop}
        {breakIf $iterator->counter > 3}
    {/foreach}


    {foreach $uniqueStores as $store}
        {if $city->isActiveBrandsOferto()}
            <a n:href="City:shop $city, $store">{_"$websiteType.city.city.leafletStores.storeWithCity", [city => $city->getName(), brand => $store->getName()]}</a>{sep}, {/sep}
        {else}
            <a n:href="Shop:shop $store">{_"$websiteType.city.city.leafletStores.title", [brand => $store->getName()]}</a>{sep}, {/sep}
        {/if}
    {/foreach}
{/capture}

{capture $storesInText}
    {var $uniqueStores = []}
    {foreach $leaflets as $leaflet}
        {skipIf !$leaflet->getShop()->hasCity($city)}
        {var $uniqueStores[$leaflet->getShop()->getId()] = $leaflet->getShop()}
        {breakIf $iterator->counter > 5}
    {/foreach}

    {var $countOfStores = count($uniqueStores)}
    {foreach $uniqueStores as $store}
        {if $iterator->isLast() && $countOfStores > 1} {_kaufino.city.city.generatedText.and} {/if}
        <a n:href="Shop:shop $store" title="{_"$websiteType.city.city.leafletStores.title", [brand => $store->getName()]}">{$store->getName()}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
    {/foreach}
{/capture}

{capture $cities}
    {var $cities = []}
    {foreach $nearestCities as $nearestCity}
        {var $cities[] = $nearestCity}
        {breakIf $iterator->counter == 4}
    {/foreach}

    {foreach $cities as $item}
        {if $iterator->isLast() && count($cities) > 1} {_kaufino.city.city.generatedText.and} {/if}
        <a n:href="City:city $item">{$item->getName()}</a>{if $iterator->getCounter() < count($cities)-1 && count($cities) > 2}, {/if}
    {/foreach}
{/capture}

{capture $shopsLink}
<a n:href="Shops:shops">{_"$websiteType.navbar.shops"}</a>
{/capture}

{var $month = ((new DateTime())|monthName|lower)}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.city.title, [city => $city->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.city.city.text, [city => $city->getName(), stores => trim($stores)]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <span class="color-grey">{$city->getName()}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">                 
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_"$websiteType.city.city.title", [city => $city->getName()]}
                            {/if}
                        </h1>
						<p class="page-header__text ml-0">
                            {if $pageExtension && $pageExtension->getShortDescription()}
                                {$pageExtension->getShortDescription()}
                            {else}
                                {_"$websiteType.city.city.text", [city => $city->getName(), stores => trim($stores)]|noescape}
                            {/if}
                        </p>
					</div>					
				</div>                

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">                        
                        {foreach $leaflets as $leaflet}
                            {if false && $iterator->counter == 1}
                                <div class="k-leaflets__item k-leaflets__item--first mb-3">                                                                        
                                    <!-- Vypis mesta - Responsive - 2 -->
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="2849716931" data-ad-format="auto" data-full-width-responsive="true"></ins>
                                    
                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}                        
                    </div>

                    <p n:if="count($leaflets) > 18" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
                    </p>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                <div n:if="count($shops) > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.city.otherShops, [city => $city->getName()]}</h2>
                    <div class="k-shop">    
                        {foreach $shops as $shop}
                            {if $city->isActiveBrandsOferto()}
                                <a n:href="City:shop $city, $shop" class="k-shop__item {$iterator->counter > 24 ? 'hidden' : ''}">
                            {else}
                                <a n:href="Shop:shop $shop" class="k-shop__item {$iterator->counter > 24 ? 'hidden' : ''}">
                            {/if}
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                                    </picture>
                                </span>
                                <small class="k-shop__title">{$shop->getName()}</small>
                            </a>
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 24" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                {*
                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCity}
                    </h2>

                    <div class="k-tag mb-5">
                        {foreach $nearestCities as $nearestCity}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:city $nearestCity" class="k-tag__item">{$nearestCity->getName()}</a>
                            </span>
                        {/foreach}
                        
                        <p n:if="count($nearestCities) > 11" class="d-flex w100">
                            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                        </p>
                    </div>
                </div>
                *}

                {if false && ($localization->isCzech() || $localization->isItaly() || $localization->isHungarian())}
                    <h2>{_"$websiteType.city.city.titleH2", [city => $city->getName()]}</h2>

                    <div class="k-content">
                        <p>{_"$websiteType.city.city.textBottom", [city => $city->getName(), 'population' => (number_format($city->getPopulation(), 0, ',', ' ')), stores => trim($storesInText), 'month' => $month, 'shopsLink' => $shopsLink]|noescape}</p>

                        <ul>
                            <li n:foreach="$leaflets as $leaflet">
                                {capture $validSince}{$leaflet->getValidSince()|localDate} {/capture}
                                {capture $validTill}{$leaflet->getValidTill()|localDate}{/capture}

                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet">{_kaufino.city.city.generatedText.leaflet, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validTill => $validTill]}</a>
                                {breakIf $iterator->getCounter() > 2}
                            </li>
                        </ul>

                    
                        <p>{_"$websiteType.city.city.textBottom2", [city => $city->getName(), 'cities' => $cities] |noescape}</p>
                    </div>
                {/if}
            </div>        			            
        </div>	

    </div>    

	<div class="float-wrapper__stop"></div>	
</div>
