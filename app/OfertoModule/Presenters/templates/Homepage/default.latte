{block scripts}
    {include parent}

    <script type="application/ld+json" n:if="count($leaflets)">
        {
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            "name": "{_"$websiteType.homepage.h1"}",
        "url": {$presenter->link('//this')},
        "itemListElement": [
            {foreach $leaflets as $key => $leaflet}
                {
                    "@type": "SaleEvent",
                    "name": {$leaflet->getName()},
                    "url": {$presenter->link('Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                    "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                    "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                    "location": {
                        "@type": "Place",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {$presenter->link('Shop:shop', $leaflet->getShop())}
                    }
                }{sep},{/sep}
        {/foreach}
        ]
    }
    </script>
{/block}

{block title}{if $website->hasAdSense()}{_"$websiteType.homepage.title"}{else}{_"$websiteType.homepage.article.title"}{/if}{/block}

{block description}{_"$websiteType.homepage.text"}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {continueIf $otherWebsite->getType() !== 'oferto'}

        <link rel="alternate" n:attr="hreflang: $otherWebsite->getLocalization()->getFullLocale('-')" href={$otherWebsite->getDomain()} />
    {/foreach}
{/block}

{block content}
<div class="container">
    {if $website->hasAdSense()}       
        
        <div class="mb-6">
            <h1 class="k__title ta-center mt-5 mb-4">{if $localization->isCzech()}{_"$websiteType.homepage.h1"}{else}{_"$websiteType.homepage.title"}{/if}</h1>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.homepage.text"}</p>
        </div>

        <div class="k-leaflets__wrapper  k-leaflets__wrapper--xs-mx">
            {foreach $leaflets as $leaflet}
                <div class="k-leaflets__item mb-5">                
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                        <picture n:if="$leaflet->getFirstPage()">
                            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                        </picture>                    
                    </a>
                    <div class="k-leaflets__title mt-0 mb-0">
                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                            {if $leaflet->isChecked() === false}
                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                            {else}
                                {$leaflet->getName()}
                            {/if}
                        </a>
                    </div>                
                    <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                </div>   

                {if ($localization->isNorwaian()  || $localization->isPolish()  || $localization->isRomanian()) && $iterator->getCounter() == 4}         
                    <!-- start div_mobile_1 slot /***********/mroferto/in_page_m_1 -->
                    <div id="div_mobile_1">
                        <script type="text/javascript">
                        googletag.cmd.push(function () {
                            var mobileR = googletag
                            .sizeMapping()
                            .addSize(
                                [0, 0],
                                [[300, 250], [300, 600], [300, 100], [300, 50], [300, 60], "fluid"]
                            )
                            .addSize(
                                [340, 0],
                                [
                                [300, 600],
                                [300, 250],
                                [336, 280],
                                [320, 100],
                                [320, 50],
                                [300, 100],
                                [300, 50],
                                [320, 60],
                                [320, 50],
                                "fluid",
                                ]
                            )
                            .addSize([768, 0], [])
                            .addSize([1024, 0], [])
                            .build();
                            googletag
                            .defineSlot(
                                "/***********/mroferto/in_page_m_1",
                                [
                                [300, 600],
                                [300, 250],
                                [336, 280],
                                [320, 100],
                                [320, 50],
                                [300, 100],
                                [300, 50],
                                [320, 60],
                                [320, 50],
                                "fluid",
                                ],
                                "div_mobile_1"
                            )
                            .defineSizeMapping(mobileR)
                            .addService(googletag.pubads());
                            googletag.display("div_mobile_1");
                        });
                        </script>
                    </div>
                    <!-- end div_mobile_1 slot-->
                {/if}
            {/foreach}
        </div>

        <div class="ta-center">
            <a href="{link Leaflets:leaflets}" class="color-grey td-hover-underline">
                {_"$websiteType.homepage.allLeaflets"}
            </a>
        </div>         

        <div class="mb-4">
            <h2 class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.shops.title"}</h2>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.shops.text"}</p>
        </div>

        <div class="k-shop">    
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" class="k-shop__item">
                    <span class="k-shop__image-wrapper">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                        </picture>                    
                    </span>
                    <small class="k-shop__title">{$shop->getName()}</small>
                </a>
                            
            {/foreach}
        </div>

        <div class="ta-center mb-6">
            <a href="{link Shops:shops}" class="color-grey td-hover-underline">
                {_"$websiteType.homepage.allShops"}
            </a>
        </div>            

        <div class="mb-4">
            <h2 class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.article.title"}</h2>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.homepage.article.text"}</p>
        </div>

        <div class="mb-5">
            {include "../components/article-list.latte", articles => $articles}    
        </div>

        <div class="ta-center mb-3">
            <a n:href="Articles:articles" class="color-grey td-hover-underline">{_"$websiteType.homepage.article.button"}</a>
        </div>

        <div n:if="count($cities) > 0">
            <h2 class="k__title ta-center">
                {_"$websiteType.homepage.city"}
            </h2>

            <div class="k-tag mb-3">
                {foreach $cities as $city}
                    <div class="k-tag__inner">
                        <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                    </div>
                {/foreach}
            </div>

            <div class="ta-center mb-5">
                <a n:href="Cities:cities" class="color-grey td-hover-underline">
                    {_'kaufino.showMore.cities'}
                </a>
            </div>
        </div>
    {else}
        <div class="mb-4">
            <h1 class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.article.title"}</h1>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.homepage.article.text"}</p>
        </div>

        <div class="mb-5">
            {include "../components/article-list.latte", articles => $articles}    
        </div>

        <div class="ta-center mb-3">
            <a n:href="Articles:articles" class="color-grey td-hover-underline">{_"$websiteType.homepage.article.button"}</a>
        </div>

        <div class="mb-6">
            <h2 class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.title"}</h2>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.homepage.text"}</p>
        </div>

        <div class="k-leaflets__wrapper  k-leaflets__wrapper--xs-mx">
            {foreach $leaflets as $leaflet}
                <div class="k-leaflets__item mb-5">                
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                        <picture n:if="$leaflet->getFirstPage()">
                            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                        </picture>                    
                    </a>
                    <div class="k-leaflets__title mt-0 mb-0">
                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                            {if $leaflet->isChecked() === false}
                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                            {else}
                                {$leaflet->getName()}
                            {/if}
                        </a>
                    </div>                
                    <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                </div>            
            {/foreach}
        </div>

        <div class="ta-center">
            <a href="{link Leaflets:leaflets}" class="color-grey td-hover-underline">
                {_"$websiteType.homepage.allLeaflets"}
            </a>
        </div>

        <div class="mb-4">
            <h2 class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.shops.title"}</h2>
            <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.shops.text"}</p>
        </div>

        <div class="k-shop">    
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" class="k-shop__item">
                    <span class="k-shop__image-wrapper">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                        </picture>                    
                    </span>
                    <small class="k-shop__title">{$shop->getName()}</small>
                </a>
            {/foreach}
        </div>

        <div class="ta-center mb-6">
            <a href="{link Shops:shops}" class="color-grey td-hover-underline">
                {_"$websiteType.homepage.allShops"}
            </a>
        </div>   
    {/if}          
</div>