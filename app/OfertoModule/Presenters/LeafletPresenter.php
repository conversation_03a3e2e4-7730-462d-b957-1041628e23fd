<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Tracy\Debugger;

final class LeafletPresenter extends BasePresenter
{
	/** @persistent */
	public $detail;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, true, null, $this->website->getModule());

		$this->template->leaflets = $leaflets;
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet, $page = 1, $detail = false): void
	{
		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveOferto() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

        if ($leaflet->isArchived()) {
            $this->redirectPermanent("Shop:shop", ['shop' => $shop]);
        }

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

        $countOfPages = $leaflet->getCountOfPages();

        $isVirtualPage = $page === $leaflet->getCountOfPages() + 1;

        if ($isVirtualPage) {
            $countOfPages = $countOfPages + 1;
        }

        if ($isVirtualPage === false && $page > $countOfPages) {
            $this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
        }

        $this->template->isVirtualPage = $isVirtualPage;

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;
		$this->template->similarLeaflets = $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		$this->template->recommendedLeaflets = $this->leafletFacade->findLeaflets($this->localization, true, 5, Website::MODULE_OFERTO);

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, true, null, $this->website->getModule());
		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

		$this->template->articles = $this->articleFacade->findArticles($this->website, 4);

        if ($leaflet->isExpired()) {
            $validLeaflet = $this->leafletFacade->findLeafletsByShop($shop, 1, true, true, null, false);
            $this->template->validLeaflet = $validLeaflet ? $validLeaflet[0] : null;
        }

        $cookieName = 'relatedLeafletOpened_' . $leaflet->getId();
        $this->template->openRelatedLeafletAllowed = $this->getHttpRequest()->getCookie($cookieName) === null;

        if ($isVirtualPage) {
            $this->template->topLeaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 9, Website::MODULE_OFERTO);
            $this->template->topShops =  $this->shopFacade->findTopLeafletShops($this->localization, true, 12, Website::MODULE_OFERTO);
            Debugger::log($this->website->getDomain() . ': virtual page visited: ' . $leaflet->getId(), 'virtual-page-visits');
        }

        dumpe($this->website);

        $this->template->relatedLeaflets = $this->leafletFacade->findRelatedLeaflets($leaflet);
	}

    public function handleOpenRelatedLeaflet(int $leafletId): void
    {
        $currentLeaflet = $this->leafletFacade->findLeaflet($leafletId);

        $relatedLeaflet = $this->leafletFacade->findRelatedLeaflet($currentLeaflet);

        if ($relatedLeaflet === null) {
            $topLeaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, $this->website->getModule());

            foreach ($topLeaflets as $topLeaflet) {
                if ($topLeaflet->getId() !== $currentLeaflet->getId()) {
                    $relatedLeaflet = $topLeaflet;
                    break;
                }
            }
        }

        $this->getHttpResponse()->setCookie('relatedLeafletOpened_' . $currentLeaflet->getId(), '1', '1 day');

        if ($relatedLeaflet === null) {
            $this->redirect('Shop:shop', ['shop' => $currentLeaflet->getShop()]);
        }

        Debugger::log($this->website->getDomain() . ': redirect from: ' . $leafletId . ' to: ' . $relatedLeaflet->getId(), 'related-leaflet-redirects');

        $this->redirect('this', ['shop' => $relatedLeaflet->getShop(), 'leaflet' => $relatedLeaflet, 'page' => 1]);
    }
}
