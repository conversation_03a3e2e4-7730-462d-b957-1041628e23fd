head:
	title: Tiplino - A legjobb cashbackjutalmak Magyarországon
	description: 'A Tiplino cashbackjutalmakat, kedvezményeket és kuponokat kínál. Szerezzen ön is jutalmakat a vásárlásaiért!'
	twitterUsername: @tiplino_hu
	image: https://www.tiplino.hu/images/content/share/OG_image_Tiplino_HU.png

homepage:
	login: Bejelentkezés
	register: Fiók létrehozása
	sign:
		signTitle: Van már fiókja?
		signIn: Bejelentkezés

	header:
		title: Pénzvisszaszerzés
		titleSuffix: ''
		subTitle: online vásárlásokból
		block1: <span>Több mint <strong>%shopCount% webshopból</strong> szerezhet vissza akár <strong>%percent% %</strong>-ot a vásárlásból</span>
		block2: Ráadásul hozzáférést kap exkluzív ajánlatokhoz és kedvezményes kuponokhoz
		block3: Egyszerű és teljesen ingyenes
		bonusAmount: %bonus% Ft
		bonus: 'BÓNUSZ ÚJ FIÓKHOZ '
		form:
			title: Kezdjen el pénzt visszaszerezni
			input: Az Ön e-mailje
			or: vagy
			submit: Ingyenes fiók létrehozása

		beginButton: Kezdjen el pénzt visszaszerezni
		rating: '4,8/5'
		reviewCount: '1 ezer értékelés'

	partners:
		text: 'Partnerek, akik pénzt térítenek vissza:'
		rest: +%rest% további

	howDoesItWork:
		title: Hogyan működik a Tiplino?
		step1:
			title: '<span>Regisztráljon ingyen </span> <span class="text-primary-orange font-medium cursor-pointer xl:hover:underline js-form-input-focus">itt</span>'
			description: 'Hozzon létre egy felhasználói fiókot a jutalmak gyűjtéséhez '

		step2:
			title: Vásároljon a Tiplinóval
			description: 'Keresse meg a webshopot, kattintson és vásároljon, ahogy szokott.'

		step3:
			title: Szerezze vissza a pénzt
			description: A Tiplino visszatérít egy részt a vásárlás összegéből a számlájára.

		beginButton: Szerezzen jutalmakat

	shops:
		title: 'Vásároljon kedvenc webshopjaiban <br> <span class="font-bold">és kapjon vissza pénzt</span>'
		subTitle: és kapjon vissza pénzt
		back: vissza a vásárlásból
		rest: +%rest% további webshop
		allShops: Minden bolt megtekintése
		moneyBackCount: 'Az elmúlt hónapban <span class="font-bold">%count% vásárlás</span> után térítettünk vissza pénzt'

	review:
		title: Az emberek szeretnek minket
		facebook:
			name: Juraj V.
			description: 'Imádom ezt a cashbackportált. Rengeteg a részt vevő bolt. Az első <span class="text-base md:text-lg font-medium">hónapokban kétszeres jutalmakat</span>, max. %bonus% Ft bónuszt szereztem, így sokat spóroltam a lakás berendezésével.'
			count: '1,1 ezer értékelés'
			rating: '4,8/5'

		apple:
			name: Ján D.
			description: 'Minden úgy működik, ahogy kell. Még a kis vásárlásoknál is lehet spórolni. Nem sok, de örömet okoz. És semmibe sem kerül. Csak néhány kattintás vásárlás közben. A többit a Tiplino intézi.'
			count: '1 ezer értékelés'

		google:
			name: Denisa M.
			description: 'Két éve használom a Tiplino szolgáltatást, és elégedett vagyok. Sok bolt közül választhat. Bármilyen "összegyűjtött" összeget kifizetnek, és a pénz néhány nap alatt a számlán van.'
			count: '3,4 ezer értékelés'

	cashback:
		title: Kérjen pénzvisszatérítést
		titleSuffix: még ma
		titleMobile: Kérjen pénzvisszatérítést
		description: 'és szerezzen bónuszt a vásárlásokért összesen akár <span class="bg-orange-gradient text-white p-1">%bonus% Ft</span>'
		beginButton: Kezdje el megszerezni a jutalmakat

	favoriteShops:
		title: Kedvenc boltok
		description: 'Tudja meg, mely boltokban kap jutalmakat és kedvezményes kuponokat.'

headerAddonPromo:
	exclusiveBonusText: 'Ma éjfélig <span class="underline font-bold">kapod meg a %bonus% hozzáadásért</span>'
	bonusText: Éjfél után már csak %bonus%

homepageLogged:
	titleShops: Legnépszerűbb boltok
	titleBanners: Jelenleg folyamatban lévő akciók
	titleDeals: Kedvezményes kuponok és kedvezmények
	otherDeals: Minden kupon megtekintése
	addonPromo:
		title: Később készül vásárolni?
		text: 'Ne felejtsen el jutalmakat és kedvezményes kuponokat szerezni vásárlásához. A Tiplino megjeleníti őket a böngészőben akár <span class="font-bold">%count% webshopban.</span>'
		button: Tiplino hozzáadása a böngészőhöz
		fakeBrowserMetaTitle: Tiplino.hu - Cashback
		fakeBrowserUrl: Tiplino.hu
		reward: 'éjfélig 1 000 Ft, utána csak 100 Ft'

	firstPurchasePopup:
		title: Várjuk az első vásárlását
		cta: 'Megerősítem, hogy éppen befejeztem a vásárlást'

	firstPurchase:
		title: Várjuk az első vásárlását
		text: '<span class="font-bold">Minden vásárlásból származó jutalomhoz</span> hozzáadunk egy bónuszt is <br> ugyanolyan értékben. Így összesen akár <span class="font-bold">%bonus% Ft bónuszt</span> szerezhet.'
		cta: 'Megerősítem, hogy éppen befejeztem a vásárlást'
		showGuide: Útmutató megjelenítése
		closeGuide: Útmutató elrejtése
		how:
			title: Hogyan szerezhet
			rewards: Jutalmakat
			withTipli: a Tiplinóval?

		steps:
			first:
				title: Válassza ki a WEBÁRUHÁZAT
				text: 'Mielőtt vásárolna, keresse meg kedvenc üzletét a Tiplinón.'

			second:
				title: Látogassa meg az üzletet
				text: 'Kattintson a linkre, és látogassa meg az üzletet a Tiplinón keresztül.'

			third:
				title: Szerezze meg a jutalmat
				text: 'Vásároljon, ahogy szokott. A sikeres vásárlás után pénzvisszatérítést kap.'

		thanks:
			title: 'Köszönjük, hogy tájékoztatott vásárlásáról'
			text1: A Tiplinón keresztüli üzletlátogatást rögzítettük
			text2: 'Most várunk az üzlet visszajelzésére a vásárlásáról (általában 48 órán belül)'
			text3: A jutalmat azonnal regisztráljuk és e-mailben értesítjük

firstPurchasePromo:
	title1: Várjuk
	title2: az első vásárlásod

navbar:
	title: A Tiplinón több mint %count% webshopot talál
	text: Szerezzen pénzvisszatérítést a vásárlásokból akár %percent% %-ig
	link: Minden webáruház megtekintése
	cta: Tekintse meg mind a %count% webáruházat
	shopsTitle: Népszerű webáruházak jutalmakkal
	shops: Webáruházak jutalmakkal
	coupons: Kedvezményes kuponok
	tellFriend: 'Ajánlja és <span class="font-medium">szerezzen akár %upTo% Ft-ot</span>'
	firstPurchase: Szerezd meg az első jutalmadat
	refunds: Elégedettségi garancia
	search: Webáruház keresése ...
	addon: Tiplino hozzáadása a böngészőhöz
	howItWorks: Hogyan szerezzen jutalmakat?
	howItWorksWithTipli: Hogyan szerezzen jutalmakat a Tiplinóval?
	luckyShops:
		name: Tiplino ajándékozás
		title: Tiplino ajándékozás
		new: Újdonság
		mobileText: 'Minden nap kiválasztunk egy szerencsés áruházat. Azok, akik az adott napon eltalálják és felfedik azt, pénzjutalomban részesülnek.'
		nonPlayer:
			text: 'Nyerjen <span class="font-bold">4 000 Ft</span> minden nap'
			cta: Nyerni szeretnék

		newWindow:
			text: Van 1 új ablak|Vannak %count% új ablakok|Vannak %count% új ablakai

		reveal:
			text: Leplezze le a mai áruházat
			mobile: Leleplezés múlva
			mobileText: 'Siessen, csak ma éjfélig van ideje.'
			badge: '1 új'

		futureReveal:
			text: Leleplezés múlva
			cta: Átlépés a Tiplino ajándékozáshoz

		everyDay: minden nap!
		amount: '4 000 Ft'

accountNavbar:
	rewards: Saját jutalmaim áttekintése
	payouts: Jutalmak kifizetése
	settings: Fiók beállítása
	redirections: Legutóbbi átirányítások
	refunds: Elégedettségi garancia
	notifications: Értesítések
	addon: Tiplino a böngészőhöz
	signOut: Kijelentkezés
	partner: Magasabb jutalmakat szerezhet a partnernek köszönhetően
	tipliExtra: Tipli extra

deals:
	title: Kedvezményes kuponok és kedvezmények Magyarországon
	couponsTitle: Kedvezményes kuponok és kódok
	couponsTitleWithTag: 'Kedvezményes kuponok a(z) %tag% kategóriából'
	topCouponsTitle: 'Kuponok, amelyek most népszerűek'
	otherDealsTitle: További kedvezmények és akciók
	shop:
		countOfCoupons: %count% kupon|%count% kuponok|%count% kuponok
		countOfSales: %count%x kedvezmény

	deal:
		tip: Tipp
		sale: Kedvezmény
		product: Termék
		cashback: Kedvezmény
		freeShipping: Ingyenes szállítás
		free_shipping: Ingyenes szállítás
		coupon: Kupon
		newsletter: Hírlevél
		useCode: Kupon használata
		getCode: Kupon megszerzése
		getSale: Kedvezmény megszerzése
		validTill: Érvényes %date%-ig
		validTillDays: Csak ma|Még %count% nap|Még %count% nap van hátra
		validTillToday: Csak ma
		expiredAt: Lejárt %date%
		conditions: Feltételek
		closeConditions: Feltételek bezárása
		cashbackDealPromoText: Kedvezmény minden vásárlásból
		couponWithoutCode: Kód nem szükséges
		copied: Másolva

nocashbackShop:
	title: Ebben az üzletben csak kedvezményeket és kuponokat kínálunk
	btn: Használja kedvezményes kuponjainkat
	info: Vásárlásból származó jutalom nélkül
	goToShop: Menjen az üzletbe
	goToShopGamble: Menjen
	registerShopGamble: Regisztrálj

shops:
	btnHowItWorks: Hogyan működik a Tiplino?
	shopCardCta: Vásárolni
	title: 'Webáruházak, ahol jutalmakat kap'
	allShops: Mind
	favoriteShops: Legnépszerűbb boltok
	newestShops: Legújabb üzletek
	dealsTitle: 'Kedvezményes kuponok, szórólapok vagy más akciók'
	nonCashbackShops:
		title: 'Boltok kedvezményes kuponokkal vagy más akciókkal|Boltok kedvezményes kuponokkal vagy más akciókkal a(z) %category% kategóriából'
		shopLabel: Kedvezmények és kuponok

	shop:
		backToAllCategory: Minden webáruház
		salesCouponsLeafletDeals: 'Kedvezményes kuponok, szórólapok vagy más akciók'
		cashbackShops: Szerezzen jutalmat minden vásárlásért
		cashbackGambleShops: Szerezzen jutalmat
		search:
			title: Keresés
			noResult: 'Akárhogy is keresünk, a „%search%” kifejezést nem találtuk.'

		signUpModal:
			title: 'Vásároljon a(z) %shop%-ban és szerezzen <br> akár <span class="bg-orange-gradient text-white px-5 py-1">%bonus% Ft</span>'
			bonusText: bónusz az új fiókhoz
			bonusReward: %bonus% Ft
			registrationTitle: Kezdjen el pénzt visszaszerezni
			registrationCta: Ingyenes fiók létrehozása
			or: vagy

		allShops:
			title: 'Webáruházak, ahol jutalmakat kap'
			subtitle: 'Válasszon a %count% webáruházból, ahol minden vásárlásért jutalmakat kap. Használja ki a <a href="%saleLink%" class=" xl:hover:underline">kedvezményeket és kedvezményes kódokat, hogy még többet spóroljon minden vásárlásnál</a>.'
			keywords: 'lista, boltok, webshopok, ajánlatok, TOP boltok'
			description: Az összes együttműködő webáruház listája a Tiplino.hu oldalon

		sortedShops:
			title: 'Legújabb üzletek, ahol jutalmakat szerezhet|Legújabb üzletek, ahol jutalmakat szerezhet a kategóriából %category%'
			subtitle: 'Tekintse meg a legújabb webáruházakat a Tiplinón. Szerezzen jutalmat minden vásárlás után, és spóroljon még többet a kedvezményes kódokkal.'

		shop:
			tooltip: 'Kattintson erre a szívre, hogy hozzáadja ezt a webáruházat a kedvenc boltok szekcióhoz'
			aboutShopTitle: 'A(z) %name% webáruházról'
			couponsDescriptionTitle: 'Hogyan használhatja fel a kedvezményes kuponokat a(z) %name% webáruházban'
			paymentMethodsTitle: 'Fizetési módok a(z) %shopName% webáruházban'
			expiredCouponsTitle: Lejárt %shop% kedvezményes kódok
			expiredCouponsTitleDecathlon: Lejárt %shop% ajánlatok
			expiredCouponsTitleCedok: Lekésett ajánlatok
			similarShopsTitle: Hasonló webáruházak
			similiarShopsTitleForCashbackShop: 'Webáruházak, ahol szintén jutalmakat kap'
			similiarShopsTitleForNonCashbackShop: 'Webáruházak, ahol jutalmakat kap'
			similiarShopsTitleForCashbackShopGamble: 'Partnerek, ahol szintén jutalmakat kap'
			similiarShopsTitleForNonCashbackShopGamble: 'Partnerek, ahol jutalmakat kap'
			noDeals: 'Jelenleg nincsenek kedvezményes kuponok a(z) %brand% webáruházban.'
			couponsFromSimilarShopsTitle: 'Tekintse meg további kedvezményes kuponokat a(z) %brands% vagy %brand% webáruházakból.'
			salesFromSimilarShopsTitle: 'Tekintse meg további kedvezményeket a(z) %brands% vagy %brand% webáruházakból.'
			couponsAndSalesFromSimilarShopsTitle: 'Tekintse meg további kedvezményes kuponokat és kedvezményeket a(z) %brands% vagy %brand% webáruházakból.'
			couponsFromSimilarShopsTitleOneBrand: 'Tekintse meg további kedvezményes kuponokat a(z) %brand% webáruházból.'
			salesFromSimilarShopsTitleOneBrand: 'Tekintse meg további kedvezményeket a(z) %brand% webáruházból.'
			peopleAboutTipliTitle: Mit mondanak az emberek a Tiplinóról
			numberOfPurchases: Megvalósított vásárlások
			averagePayForPurchases: Átlagos jutalom vásárlásonként
			averageTurnaroundTimeRewards: A jutalmak átlagos regisztrációs ideje
			btnHowItWorks: 'Tudja meg, hogyan működik a Tiplino'
			countOfCouponsText: %count%&nbsp;kupon|%count%&nbsp;kuponok|%count%&nbsp;kupon
			countOfSalesText: és&nbsp;%count%&nbsp;kedvezmény|és&nbsp;%count%&nbsp;kedvezmények|és&nbsp;%count%&nbsp;kedvezmények
			fromShop: 'a(z) %brand%-tól'
			aliexpressDayInfoMessage: 'Az AliExpress days során leadott rendelések nagy száma miatt előfordulhat, hogy az AliExpress később regisztrálja a jutalmát. Kérjük türelmét.'
			contactTitle: Kapcsolat %shopName%
			showAllText: Teljes szöveg megjelenítése
			showAllDeals: További kedvezmények és kuponok megtekintése|További %count% kedvezmény és kupon megtekintése|További %count% kedvezmény és kupon megtekintése
			navigation: Navigáció
			categoryTitle: Webáruház kategóriák
			ctaBox:
				title: Elege van a kedvezményes kuponok kereséséből?
				text: A cashbackkel mindig kedvezményt kap
				more: Tudjon meg többet

			couponInstructions:
				step1: Válasszon kedvezménykupont a Tipli-n.
				step2: Másolja vagy jegyezze fel a kedvezménykódot.
				step3: Adja meg a kedvezménykódot az áruházban.
				step4: ''

			favorites:
				add: Hozzáadás a kedvencekhez
				remove: Eltávolítás a kedvencek közül

			addon:
				title: Szerezzen kuponokat egy kattintással
				titleSuffix: 'a(z) %shopName%-nál és további %count% webáruházban.'
				cta: Tiplino hozzáadása a böngészőhöz

			deals:
				title: Kedvezményes kódok és kuponok

			shippingDescription:
				title: %shop% ingyenes szállítás
				phone: Telefon
				email: E-mail
				address: Cím

			metaTitle:
				sales: %brand% kedvezmény akár %value%
				salesCode: %brand% kedvezményes kód %value%
				salesAndCode: kedvezmények és kedvezményes kódok

			metaDescription:
				sales: '👉 %brand% kedvezmények itt! Használja ki a lehetőséget, hogy akár %value%-ot spóroljon a következő vásárlásán. ✌ Az aktuális %brand% kedvezmények korlátozott ideig érvényesek.'
				salesCode: 'Használja ki az aktuális  %brand% kedvezményes kódot, 👉 és spóroljon akár %value%-ot a következő vásárlásán. Tekintse meg az összes %brand% kedvezményes kódot és kupont ✅'
				salesAndCode: Szeretné használni a legújabb %brand% kedvezményes kódokat 👌 vagy akciósan vásárolni? Ne hagyjon ki egyetlen lehetőséget sem a megtakarításra a Tiplinóval. ✅

			metaKeywords:
				cashback: 'cashback, kedvezmények, kedvezményes kuponok, jutalmak'
				withoutCashback: 'akciós vásárlások, akciók, kedvezmények, kedvezményes kuponok'

			blackFridayTitle: %shop% Black Friday
			sidebarMenu:
				howApply: 'Hogyan használja a kedvezményes kuponokat a(z) %name%-nál'
				review: 'Vélemények a(z) %shop%-ról'
				expired: Lejárt %shop% kedvezményes kódok

			btnGetCashback:
				title: Szerezzen vissza %cashbackValue% a vásárlásból
				tooltip: A gombra kattintva átirányítjuk az üzletbe. A vásárlás után általában 48 órán belül regisztráljuk a jutalmat.

			guide:
				title: Hogyan szerezze meg az első jutalmát a vásárlásból
				step1: 'Kattintson a %shop% üzletre a <span class="font-bold leading-[24.5px]">Vásárlás</span> gombon keresztül a Tiplinón.'
				step2: 'Vásároljon, ahogy szokott. A többit bízza ránk.'
				step3: A vásárlás egy részét jutalomként regisztráljuk.

			allCoupons: %countOfCoupons% kedvezményes kupon|%countOfCoupons% kedvezményes kupon|%countOfCoupons% kedvezményes kupon
			allShops: %countOfShops% webáruházban jutalmakkal
			allShopsCouponCta: Az összes webáruház megtekintése
			btnContinueToShop:
				title: Vásárolni

			btnNoCashback: Jelenleg nem lehet cashbackjutalmat szerezni
			benefitMessage:
				days: nap|nap|nap
				hours: óra|óra|óra
				minutes: perc|perc|perc

			offers:
				more: Az összes ajánlat megtekintése

			sales:
				title: Kedvezményes ajánlatok
				validTill: Érvényes eddig

			leaflets:
				title: Aktuális szórólapok
				cta: Az összes %shop% szórólap »
				header: Akciós szórólap %shop%
				descriptionBeginning: Tekintse meg
				descriptionLink: %shop% akciós szórólap
				descriptionEnd: és válasszon a széles kínálatból.
				descriptionAddition: 'Az aktuális %shop% akciós szórólapot megtalálja oldalunkon, így nem marad le egyetlen érdekes ajánlatról sem. Tekintse meg a kiválasztott termékeket akciós áron. A %shop% szórólapokat ad ki széles márkaválasztékkal és érdekes kedvezményekkel.'

			products:
				title: 'Vásárlási tippek a(z) %shop% webáruházban'
				cta: További termékek »
				save: Megtakarítás %amount% %currency%
				button: Vásárolni

			articles:
				title: Vásárlási tippek
				titleAbout: 'Cikkek a(z) %shop%-ról'

			conditions:
				title: Feltételek
				conditionsTitle: Általános feltételek
				text: 'Ha más kedvezményes kuponokat használ, mint amelyek a Tiplinón elérhetők, előfordulhat, hogy a vásárlás után járó jutalma nem kerül regisztrációra.'
				heading: Jutalomszerzés feltételei
				more: Összes feltétel megtekintése
				calculation: 'A jutalma az összeg alapján kerül kiszámításra, amelyet a rendeléséért fizet. Ebből az összegből általában levonásra kerül az áfa, a szállítási díj és a felhasznált hűségjutalmak. Külföldi boltokban történő vásárlás esetén árfolyam-ingadozás miatt kisebb eltérések lehetnek a jutalom kiszámításában. Jutalmat akciós árakon történő vásárlásért is kap. Azonban előfordulhat, hogy a jutalom nem kerül regisztrálásra, ha olyan kedvezményes kupont használ, amely nem a mi oldalunkról származik.'
				success: 'A jutalom regisztrálásának sikeressége majdnem 100%. Csak kivételes esetekben nem regisztráljuk a jutalmat, például a böngésző technikai problémái miatt. Ezeket a helyzeteket az ügyfélszolgálat gyorsan megoldja Önnek, csak töltse ki az <a href="%guaranteeUrl%">Eelégedettségi garancia űrlapot</a>.'
				closePopup: Bezárás

			panels:
				reward:
					description: 'vásárláskor a(z) %name%-ban'
					redirectButton: Vásárolni

				recommendedShops:
					title: Ajánlott webáruházak

			sign:
				email: Adja meg e-mail-címét
				button: Regisztráció
				or: vagy
				facebook: Bejelentkezés Facebookkal
				showConditions: Általános szerződési feltételek megtekintése
				timeUntil: az akció lejáratáig

			condition: Üzleti feltételek
			reviews:
				title: Vélemények %name%-ról
				empty: Ezt a webáruházat még senki nem értékelte.
				panel:
					showAll: 'Minden vélemény megtekintése (%count%)'
					add: Új vélemény hozzáadása

			favoriteShop:
				add: Hozzáadás a kedvenc webáruházakhoz
				remove: Eltávolítás a kedvenc webáruházakból

			shopDetail:
				title: Kedvezményes kód %shop%
				shopCategoryTitle: Webáruház kategóriák
				shopProductsCategoryTitle: 'A(z) %shop% termékei ezekben a kategóriákban találhatók'
				recommendedOrganicShops: Mások ez után is érdeklődtek
				btntitle: Vásárolni
				btntitleGamble: Jutalom felvétele
				nav:
					sale: Kedvezmények
					shop: Boltok
					aboutShop: 'A(z) %shop%-ról'
					condition: Cashback és feltételek
					leaflet: Szórólapok

				referralTitle: 'Küldje el a linket, és szerezzen jutalmat a vásárlásért'
				referralText: 'Ha elküldi ezt a linket, jutalmat szerezhet ismerőse vásárlásáért.'
				referralCopyLink: Kattintson ide a link másolásához
				referralCopiedLink: Másolva
				facebook: %brand% Facebook-oldala
				cashbackCheck:
					title: 'Ellenőrizze, hogy kap-e jutalmat a termékért:'
					yesCashback: Ezzel a termékkel a Tiplino jutalmat kínál.
					noCashback: Ezzel a termékkel nem szerezhető jutalom.
					form:
						input: A termék hivatkozása az aliexpress.com-on
						submit: Ellenőrzés

				recommendedShops: Kedvezmények más webáruházakban
				discountCode: kedvezményes kód
				foreignShops: %brand% más országokban
				alternativeNames: Mások ez után is érdeklődnek
				promo:
					title: Kezdjen el jutalmat szerezni a vásárlásokból
					promoTextMobile1: 'Hozzon létre egy fiókot a Tiplinón, és kezdjen el pénzt visszakapni <span class="font-normal">a(z) %shop%-nál és további %count% webáruháznál.</span>'
					promoText1: 'A(z) %shop%-nál minden vásárlásból akár %cashback% visszatérítés jár'
					promoText2: További %count% webshop is pénzt térít vissza
					promoText3: Egyszerű és teljesen ingyenes
					bonus: bónusz az új fiókhoz
					bonusAmount: +%amount% Ft
					cta: Ingyenes regisztráció

				otherDeals: 'Tekintse meg a kuponokat más webáruházakból a(z) %tag% kategóriában'
				faq: Gyakran ismételt kérdések

			virtualCoupon:
				title: 'Hogyan szerezhet <span class="font-bold">vissza pénzt a Tiplinóval?</span>'
				steps:
					step1:
						title: Regisztráljon a Tiplino portálon.
						text: A regisztráció ingyenes.

					step2:
						text: Tovább a %shop% webáruházba a Tiplino profilon található “Vásárlás” gombbal.

					step3:
						text: 'Fejezze be a vásárlást a %shop% webáruházban, és a Tiplino regisztrálja a jutalmát.'

					reward: 'Akár <span class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%reward%</span><span class="font-bold"> összeget is visszaszerez <br> minden vásárlás után.</span>'

				cta: Regisztrálni

			shopRedirectionPopup:
				title: 'Figyelem! Elveszíthet akár <span class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%reward%</span> <span class="font-bold">összeget a vásárlásból</span> a %shop% webáruházban.'
				cta: Jutalmat szeretnék szerezni
				continueToShop: Folytatás jutalom nélkül

			addonPopup:
				meta: Tiplino.hu - Cashback
				address: Tiplino.hu
				title: Elmulasztott már jutalmat a <br>%shop% webáruházban?
				text: 'Adja hozzá a<span class="font-bold">Tiplinót a böngészőhöz,</span> és mi mindig figyelmeztetjük, ha <br> jutalmat szerezhet az adott webáruházban.'
				cta: Tiplino hozzáadása a böngészőhöz
				continueToShop: Tovább szeretnék menni a webáruházba
				rating: '<span class="font-bold">60 ezer</span> felhasználó'

			generatedDescription:
				coupons:
					title: Ne hagyja ki a kedvezményes kuponokat
					p1: 'Ha inkább <strong>kedvezményes kuponokat és kódokat</strong> szeretne használni, tekintse meg a %couponLinks%-ot.'
					coupon1: kedvezményes kupon
					coupon2: kedvezményes kupon
					couponsAndSales: kedvezményes kupon és akció|kedvezményes kuponok és akciók|kedvezményes kuponok és akciók
					p2: Érdekelheti a %couponLinks% is.
					or: vagy
					or2: és vagy
					code: kedvezményes kód
					codeFrom: kedvezményes kód ettől
					sale: kedvezmény eddig
					separator: ', '
					codeFromSuffix: ''
					saleSuffix: ''

				cashback:
					title: 'Szerezzen jutalmat a(z) %shopName% webáruházban'
					p1: 'A(z) <strong>%shopName%</strong> webáruházban történő vásárláskor <strong>%reward% jutalmat </strong> kap.'
					p2: 'Pénzt is visszakaphat a vásárláskor ismert webáruházakban, mint például %shops%, vagy tekintse meg a többi webáruházat a(z) %tagName% kategóriában.'
					p3: 'Újdonságként megtalálhatja nálunk a(z) %newestShops% webáruházakat.'

				nonCashback:
					title: 'További kedvezmények és kuponok a(z) %shopName% webáruházban'
					p1: 'A(z) <strong>%shopName%</strong> webáruházban kedvezményes kuponokat, kódokat használhat fel, vagy érdekes kedvezményeket szerezhet.'
					p2: 'Érdekes kedvezményeket kínálnak más webáruházak is a(z) %tagName% kategóriában.'
					p3: 'Ezekben az webáruházakban nemcsak pénzt térítenek vissza a vásárlásból, hanem spórolhat is ismert üzletekben, mint például %recommendedShopLinks%.'
					p4: 'Újdonságként megtalálhatja nálunk a(z) %newestShopLinks% webáruházakat.'

			promoDeal:
				title: 'Szerezzen %bonus% Ft-ot még a(z) %brand% webáruházban történő vásárlás előtt'
				text: 'Regisztráljon a Tiplinóra, és szerezzen vissza pénzt az online vásárlásokból. Minden vásárlásból szerzett jutalomhoz kap egy bónuszjutalmat is ugyan olyan értékben. Összesen akár %bonus% Ft bónuszjutalmat is szerezhet. Ez a Tiplino portál ajánlata, nem a(z) %brand% üzleté.'
				amount: %bonus% Ft
				button: Szerezzen %bonus% Ft-ot

			signUpModal:
				title: 'Vásároljon a(z) %shop%-ban, és szerezzen <br> akár <span class="bg-orange-gradient text-white px-5 py-1">%bonus% Ft</span> bónuszjutalmat'
				bonusText: bónusz az új fiókhoz
				bonusReward: %bonus% Ft
				registrationTitle: Kezdjen el pénzt visszaszerezni
				registrationCta: Ingyenes fiók létrehozása
				or: vagy

			favoriteShops:
				title: Ajánlott üzletek jutalmakkal
				text: 'A(z) %shop% vásárlói kedvezményeket és jutalmakat kapnak ezekben a webáruházakban is'

			recommendedShops:
				title: Üzletek kedvezményes kuponokkal
				text: Aktuális kuponok és kedvezmények %month% %year%
				alt: %brand% kedvezményes kuponok

			tips:
				title: Tippek a megtakarításhoz
				subtitle: 'Vásárláskor a(z) %shop% webshopban'

		shopsList:
			sort:
				newest: Legújabb webáruházak
				favourites: Legnépszerűbbek
				alphabetically: Ábécé sorrendben
				back: Vissza az összes webáruházhoz

			emptyList:
				btnLabel: Az összes webáruház megtekintése
				text: 'Jelenleg nincs olyan webáruház, amely megfelel a keresésének. Próbálja megtekinteni az <a href="%link%">összes webáruházat</a>.'

		recommendedArticles:
			title: 'Cikkek, amelyek érdekelhetik'
			subtitle: 'Inspirálódjon érdekes tippekkel, és szerezzen jutalmat a vásárlásai után.'

	sale:
		allSales:
			title: A legjobb kedvezményes ajánlatok Magyarországon
			subtitle: Válasszon a legjobb ajánlatok közül Magyarországon. Minden vásárlás olcsóbb lehet.
			keywords: 'kedvezmények, kuponok, jutalmak, voucherek, ajánlatok'
			description: Az általunk kínált kedvezmények és kuponok listája partnereinknél.

		shopSales:
			title: %shop% kedvezmények és kuponok
			titlePart1: %shop% kedvezményes kuponok
			titlePart2: Akár %topSale% kedvezmény
			keywords: '%shop%, %shop% kedvezmény, %shop% kedvezményes kupon, %shop% kedvezményes kód, %shop% akciók, %shop% kiárusítás'
			description: 'A(z) %shop% által kínált kedvezmények és kuponok listája.'

		sale:
			markups:
				sale: Kedvezmény
				coupon: Kupon
				free_shipping: Ingyenes szállítás
				gift: Ajándék
				relative: Kedvezmény

			saleCopy: Másolja a kódot kattintással
			saleCta:
				unLogged:
					sale: Kedvezmény megszerzése
					coupon: Kedvezménykód megszerzése
					free_shipping: Ingyenes szállítás megszerzése
					gift: Ajándék megszerzése

	redirection:
		title: Átirányítás folyamatban...
		redirectnow: Átirányítás most
		reward: visszatérítés a vásárlásból
		rewardInShop: 'a(z) %shop% webáruházban'
		rewardInGambleShop: a %shop% regisztrációjáért
		conditions:
			title: Milyen jutalmat kapsz
			showMore: Több megjelenítése

		registrationTime:
			minutes: A jutalmat ebben a webáruházban általában <strong>1 percen</strong> belül regisztráljuk a vásárlás után.|A jutalmat ebben a webáruházban általában <strong>%count% percen</strong> belül regisztráljuk a vásárlás után.|A jutalmat ebben a webáruházban általában <strong>%count% percen</strong> belül regisztráljuk a vásárlás után.
			hours: A jutalmat ebben a webáruházban általában <strong>1 órán</strong> belül regisztráljuk a vásárlás után.|A jutalmat ebben a webáruházban általában <strong>%count% órán</strong> belül regisztráljuk a vásárlás után.|A jutalmat ebben a webáruházban általában <strong>%count% órán</strong> belül regisztráljuk a vásárlás után.
			nextDay: A jutalmat ebben a webáruházban általában a vásárlást <strong>követő napon</strong> regisztráljuk.

		nonProfit:
			text: 'A(z) %brand% webáruházban történt vásárlásból származó jutalmak jótékonysági célokra vannak szánva, és nem válthatók ki.<br><br>Köszönjük, hogy a Tiplinón keresztül történő vásárlásával segít.'
			button: Értem és folytatni szeretném
			buttonBack: Vissza a Tiplinóhoz

		recaptcha:
			title: 'Kérjük, erősítse meg, hogy nem robot.'
			submit: Folytatás

	redirectionUnLogged:
		titleShop: 'Átirányítjuk a(z) %name% oldalra, ahol <strong>akár %reward%-kal olcsóbban</strong> vásárolhat!'
		titleWithoutCashback: 'A(z) %name% üzletben nem jár cashback, de biztosan talál ott érdekes akciókat és kedvezményeket.'
		subtitle: 'Regisztráljon ingyenesen, és kezdjen el kedvezményes vásárolni ma!'
		signUp:
			email: Adja meg e-mail-címét
			button: Ingyenes regisztráció

		alreadyRegistered:
			title: Már van fiókja?
			text: '<a href="%link%" class="redirect-unloged__login">Jelentkezzen be</a>'

		howItWorks: 'Tudja meg, hogyan szerezhet jutalmat a vásárlásból'
		continue: Folytatás a webáruházhoz <strong>jutalom nélkül</strong>

sign:
	up:
		title: Új fiók
		create: Fiók létrehozása
		subtitle: 'Hozzon létre egy új fiókot, és<br class="lg:hidden"> <span class="font-bold">szerezzen akár <span class="text-white px-2 py-1 rounded-md bg-orange-gradient">%bonus% Ft</span> bónuszt</span>'
		accountExists: Már van fiókja?
		signInLink: Jelentkezzen be
		form:
			acceptAllConditions: 'A regisztrációval elfogadja a <a target="_blank" href="%condition%">felhasználási feltételeket</a> és&nbsp;a&nbsp;<a target="_blank" href="%privacy%">személyes adatok feldolgozását</a>.'
			sendAcceptPrivacy: 'Az űrlap elküldésével elfogadja a&nbsp;<a target="_blank" href="%privacy%">személyes adatok feldolgozását</a>.'
			iHaveCode: Van promóciós kódom
			code: az ön kódja
			submit: Ingyenes regisztráció
			validator:
				passwordNotValid: A jelszónak legalább %count% karakter hosszúnak kell lennie.
				codeRequired: Adja meg a promóciós kódot

		addonPromoPage:
			user: 'Profil <span class="font-bold">%email%</span> létrehozva'
			title: Fejezze be a regisztrációt
			addon: a Tiplino böngésző
			reward: hozzáadásával és szerezzen további 1 000 Ft
			rewardAmount: '1000 Ft'
			text: 'A Tiplino bővítmény figyelmeztetni fogja, <br> ha az adott webáruházban jutalmat szerezhető.'
			countDownText: A különleges ajánlat csak ma éjfélig érvényes.
			cta: Tiplino hozzáadása a böngészőhöz
			remindLater: Emlékeztessen később
			availableOn: Elérhető itt
			review: '<span class="font-bold text-dark-1">4,7/5</span> (60 ezer aktív felhasználó)'
			demo:
				inteligent: intelligens <br> értesítés
				cta: Szerezzen jutalmat
				coupons: Mutassa a kuponokat
				back: vissza a vásárláshoz

	in:
		title: Bejelentkezés
		subtitle: Üdvözöljük újra itt! Jelentkezzen be a fiókjába.
		forgottenPasswordLabel: Elfelejtette a jelszavát?
		forgottenPasswordLink: Kattintson ide
		or: vagy
		form:
			captcha: 'Kérjük, erősítse meg, hogy nem robot.'
			submit: Bejelentkezés
			email: E-mail
			password: Jelszó
			error:
				info: Ha jelszóra van szüksége
				link: kattintson ide

		addonPromoPage:
			title: A
			addon: Tiplino a böngészőbe
			suffix: nem felejted el a jutalmakat
			bullet:
				first: Emlékeztet a jutalmakra akár <strong>%countOfCashbackShops% webáruházban</strong>
				second: Megmutatja a jutalom lehetőségét <strong>közvetlenül a vásárlás során</strong>
				third: Több mint 60 ezer elégedett felhasználó emlékszik jutalmaira a Tiplino böngészőbővítményének köszönhetően

			cta: Add hozzá a Tiplino a böngészőbe
			remindLater: Emlékeztessen később
			availableOn: Elérhető itt
			review: '<span class="font-bold text-dark-1">4,7/5</span> (60 ezer aktív felhasználó)'
			demo:
				inteligent: intelligens <br> értesítések
				cta: Szerezd meg a jutalmat
				coupons: Tekintsd meg a kuponokat
				back: vissza a vásárláshoz

		signUpPromo:
			title: Még nincs fiókja?
			text: 'Regisztráljon, és fedezze fel az összes előnyt még ma!'
			cta: Fiók létrehozása INGYENESEN

	forgottenPassword:
		title: Elfelejtett jelszó
		text: Az új jelszó beállításához szükséges utasításokat elküldjük az e-mail-címére.
		back: Vissza a bejelentkezéshez

	forgottenPasswordSent:
		title: Az új jelszó kérése elküldve
		text: 'Ha az e-mail-cím létezik, elküldjük Önnek a jelszó beállításához szükséges utasításokat az e-mail-fiókjába. Ha kérdése van, <a href="%link%">lépjen velünk kapcsolatba</a>.'

	newPassword:
		title: Új jelszó
		text: 'A jelszó sikeres megváltoztatása után bejelentkezhet új jelszavával, és folytathatja a fiókja használatát.'
		form:
			password: Új jelszó
			passwordVerify: Új jelszó újra
			submit: Jelszó megváltoztatása
			validator:
				passwordRequired: Adja meg az új jelszavát.
				passwordValid: Az új jelszónak legalább %count% karakter hosszúnak kell lennie.
				passwordsEqual: A jelszavak nem egyeznek.

	messages:
		signUpSuccess: A regisztráció sikeres volt.
		signInSuccess: Sikeresen bejelentkezett.
		signOutSuccess: Sikeresen kijelentkezett.
		newPasswordSuccess: Az új jelszó elmentve. Most már bejelentkezhet.
		requireToBeLoggedIn: Ehhez a szekcióhoz be kell jelentkeznie.

footer:
	copyright: © %year% Tiplino.hu | Minden jog fenntartva
	help: Miben segíthetünk?
	helpEmail: <EMAIL>
	badge: Jutalom regisztrálásának garanciája
	app: Tiplino alkalmazás
	promo:
		text: Élvezze a jutalmakat minden online vásárlásból
		cta: Ingyenes fiók létrehozása

	navbar:
		shopSection:
			title: Top boltok

		about:
			title: A Tiplinóról
			howItWorks: Hogyan szerezzen jutalmakat?
			guarantee: Elégedettségi garancia
			faq: Gyakran ismételt kérdések
			addon: Tiplino böngészőbővítmény
			app: Mobilalkalmazás
			tips: Vásárlási tippek
			articles: Cikkek
			sales: Kedvezményes kuponok
			leaflets: Szórólapok

		info:
			title: Információk
			career: Karrier
			privacyPolicy: Adatvédelmi irányelvek
			cookies: Sütik
			conditions: Felhasználási feltételek

		application:
			title: Tiplino alkalmazás

		support:
			title: Segítségre van szükségem
			reward: Nem került a jutalmam regisztrációra
			payout: Kérdésem van a jutalmak kifizetéséről
			answer: Más kérdésem van
			forgottenPassword: Elfelejtett jelszó
			contact: Kapcsolat

	aboutProject:
		title: A projektről
		description: 'Végre olyan vásárlás, ami mindenkit örömmel tölt el! A Tiplino segítségével az online vásárlások mindig előnyösebbek. Vásároljon kedvenc webáruházaiban, és kapjon vissza akár %maxReward% jutalmat az összesített rendelési értékből. Ha elégedett velünk, ajánljon minket másoknak is, és keressen további pénzt.'

	otherCountries:
		title: Tiplino más országokban

	addonPromo:
		title: Tiplino a böngészőbe
		text: Szerezzen jutalmakat a vásárlásokból és kedvezményes kódokat egyetlen kattintással
		cta: Tiplino hozzáadása a böngészőhöz
		sale:
			value: '10 %'
			text: vissza a vásárlásból
			getReward: Szerezzen jutalmat
			show: Kedvezményes kuponok megjelenítése

		coupon:
			title: '1 Kupon'
			value: Kedvezményes kupon 200 ft
			text: a Tiplinótól
			getReward: Kupon felhasználása
			show: További kedvezményes kuponok

account:
	navigation: Navigáció
	userCard:
		lifetimeBalance: Eddig összesen ennyit szerzett
		currentBalance: Jutalmaim

	myAccount: Saját fiókom
	myApprovals:
		'2fa': Állítsa be a 2FA-t
		title: Hozzájárulásaim
		link: Hozzájárulásaim áttekintése
		text: Az adatkezeléshez adott hozzájárulások áttekintése.
		table:
			document: Dokumentum
			date: Dátum

	accountDeletionRequest:
		title: A fiók és a személyes adatok törlésének kérvényezése
		submit: Fiók törlésének kérvényezése
		subject: Fiók törlésének kérvényezése
		message: Kérem a fiókom és a személyes adataim törlését
		link: Fiók törlése
		success: 'Köszönjük, hamarosan kapcsolatba lépünk Önnel a fiókja törlésével kapcsolatban.'
		emailDoesntMatch: A megadott e-mail-címnek meg kell egyeznie a bejelentkezett felhasználó e-mail-címével.
		unsignedUser: 'Ha törölni szeretné fiókját, kérjük, jelentkezzen be'
		gdpr:
			title: A fiók és a személyes adatok törlése
			text: 'Amennyiben úgy dönt, hogy törölni szeretné fiókját vagy személyes adatait, az alábbi hivatkozáson megteheti'

	addonPromo:
		title: Soha ne feledkezzen meg a <br> jutalmakról és kuponokról!
		text: 'A <b>Tiplino böngészőbővítmény</b> emlékeztetni fogja, amikor a több mint %count% webáruház valamelyikét látogatja meg.'
		cta: Tiplino hozzáadása a böngészőhöz

	user:
		changeVerifiedMessage: A módosítást sikeresen igazoltuk. Köszönjük.
		settings:
			guide:
				title: Profilja %score%%-ban kitöltve
				filled_personal_data:
					label: Személyes adatok kitöltése
					description: 'Személyes adatait azért használjuk, hogy jobban célozhassuk az exkluzív ajánlatokat a Tiplinón. Ezeket az adatokat a jutalmak kifizetésének ellenőrzéséhez is használjuk. Felhasználóink személyes adatai biztonságban vannak nálunk, és nem osztjuk meg őket harmadik féllel.'
					link: https://www.tiplino.hu/fiok-beallitasai

				filled_bank_details:
					label: Töltse ki a banki adatokat
					description: A jövőbeni jutalmak kifizetését a bankszámlájára küldjük.
					link: ''

				has_transaction:
					label: Vásároljon először a Tiplinón keresztül
					description: 'Vásároljon először a Tiplinón keresztül, és győződjön meg róla, hogy valóban működik!'
					link: ''

				installed_addon:
					label: Adja hozzá a Tiplinót a böngészőhöz
					description: 'Soha nem marad le jutalomról. A Tiplino böngészőbővítmény ügyesen figyelmezteti, ha jutalomra jogosult a látogatott üzletben.'
					link: https://www.tiplino.hu/tipli-a-bongeszobe

				recommended_user:
					label: Ajánlja a Tiplinót egy barátjának
					description: 'Ajánlja a Tiplinót ismerőseinek! Minden új felhasználóért, akit ajánl, bónuszjutalmat kap %recommendationBonusAmount% Ft értékben.'
					link: https://www.tiplino.hu/ajanlas-baratoknak

			title: Fiók beállítása
			personalData: Az Ön személyes adatai
			form:
				firstName: Keresztnév
				lastName: Vezetéknév
				gender: Nem
				birthdate: Születési dátum
				phoneNumber: Telefonszám
				accountNumber: Számlaszám
				accountNumberTooltip: 'Erre az adatra azért van szükségünk, hogy a jutalmakat a bankszámlájára küldhessük. Ezt az adatot senkivel nem osztjuk meg.'
				bankCode: Bankkód
				iban: IBAN
				waiting: Várjuk az e-mailes megerősítést
				confirmed: Megerősítve
				submit: Mentés
				sendingPolicy:
					emailLabel: 'Válassza ki, milyen típusú információkat szeretne kapni az e-mail-címére'
					smsLabel: 'Válassza ki, milyen típusú információkat szeretne SMS-ben fogadni'

				validator:
					birthdatePattern: Érvénytelen születési dátum
					phoneNumber: 'Sajnáljuk, a telefonszám formátuma érvénytelen.'
					invalidCode: 'A kód helytelen, vagy már nem érvényes.'
					accountNumber: 'Sajnáljuk, a számlaszám formátuma érvénytelen.'
					accountNumberPrefix: A bankszámlaszám előtagjának kötőjelet kell tartalmaznia.
					bankCode: Írja be a bankkódot a perjel után.
					bankCodeNotExists: 'Sajnáljuk, a megadott bankkód nem létezik.'
					iban: 'Sajnáljuk, de az IBAN nem adható meg. Kérjük, írja be a számlaszámot a következő formátumban: 123-**********/1231.'

				messages:
					success: A profil módosítása sikeresen megtörtént.

			unsubscribeEmail:
				description: Már nem kérek %contentType% az e-mail-címemre.
				btnUnsubscribe: Leiratkozás
				messages:
					success: Az e-mail-címe sikeresen leiratkozott.

			unsubscribeModal:
				text: 'Éppen most készül leiratkozni az e-mailekről:'
				areYouSure: Biztosan le szeretne iratkozni?
				cta: Leiratkozás

		changePassword:
			title: Jelszó módosítása
			form:
				oldPassword: Régi jelszó
				newPassword: Új jelszó
				newPasswordVerify: Új jelszó újra
				submit: Jelszó megváltoztatása
				validator:
					oldPasswordRequired: Adja meg a régi jelszavát.
					oldPasswordIncorrect: Hibás régi jelszó.
					newPasswordNotValid: Az új jelszónak legalább %d karakter hosszúnak kell lennie.
					newPasswordRequired: Adja meg az új jelszavát.
					newPasswordVerifyRequired: Adja meg új jelszavát még egyszer az ellenőrzéshez.
					passwordsNotEqual: 'Sajnáljuk, az új jelszó nem egyezik a megerősítéssel.'
					passwordContainsInvalidCharacters: Az megadott jelszó érvénytelen karaktereket tartalmaz.

				messages:
					success: A jelszó sikeresen megváltozott.

		accountNumber:
			title: Bankszámla a jutalmak kifizetéséhez
			link: Bankszámlaszám beállítása
			form:
				accountNumber: Számlaszám
				accountNumberTooltip: 'Erre az adatra azért van szükségünk, hogy a jutalmakat a bankszámlájára küldhessük. Ezt az adatot senkinek nem adjuk tovább. A számlaszámot a következő formátumban adja meg: 123-**********/1231'
				accountPlaceholder: 'A számlaszámot a következő formátumban adja meg: 123-**********/1231'
				iban: IBAN
				waitingForVerification: E-mailes megerősítésre várunk

			verification:
				heading: Ellenőrizze a bankszámláját
				title: 'E-mail címére elküldtünk egy megerősítő linket, amellyel befejezheti a bankszámlaszám beállítását'
				validation: A link érvényessége 24 óra.
				contactUs: 'Nem kapott megerősítő linket? <a href="%link%">Lépjen kapcsolatba velünk</a>!'
				cta: Vissza a fiók beállításaihoz

		iHaveCode:
			title: Promóciós kód beváltása
			label: Promóciós kód
			placeholder: Adja meg a promóciós kódot
			submit: Alkalmazás
			messages:
				success: Jutalma mostantól aktív.

		completeProfile:
			recommendText: 'Segítsen barátainak kedvezményesen vásárolni, és szerezzen akár <strong>%upTo%&nbsp;Ft</strong> a Tiplinótól!'
			recommendButton: Ajánljon és szerezzen akár <strong>%upTo%&nbsp;Ft</strong>
			first: Most már csak az <strong>első vásárlást</strong> kell elvégeznie
			firstButton: Webáruházak áttekintése

		guarantee:
			title: Elégedettségi garancia
			faq:
				question1: 'Vásároltam, de semmi nem történt.'
				answer1: 'A leadott rendelések általában <strong>48&nbsp;órán</strong> belül kerülnek regisztrációra. A vásárlásból származó cashbackjutalmat az üzlet, ahol vásárolt, erősíti meg. Ha több, mint 3 napja várja a jutalom rögzítését, kérjük, lépjen kapcsolatba velünk az oldal alján található űrlapon keresztül. Azonnal ellenőrizzük a vásárlását és a hozzá rendelt jutalmat.'
				question2: Mikor kerülnek megerősítésre a jutalmaim?
				answer2: 'A vásárlásból származó jutalmak megerősítése általában 12&nbsp;től&nbsp;70 napig tart. A jutalom megerősítési ideje az adott webshopon múlik, ahol vásárolt. További részletekért látogassa meg a <a href="%link%">Hogyan működnek a jutalmak</a> szekciót.'
				question3: Mikor kérhetem a megerősített jutalmak kifizetését?
				answer3: 'Amint a megerősített jutalmak összege meghaladja a 0 Ft-ot. Ez azt jelenti, hogy akár 1 Ft-ot is kifizethet. :-)'
				question4: Mikor kérhetem a bónuszjutalmak kifizetését?
				answer4: 'A bónuszjutalmak akkor fizethetők ki, ha a felhasználónak legalább 1 500 Ft megerősített jutalom van a számláján. Ha a jutalom kifizetésre kerül, akkor a következő bónuszjutalmak kifizetéséhez ismét el kell érni a 1 500 Ft-ot a felhasználó számláján.'
				question5: 'Már van 1 500 Ft megerősített jutalmam, de a barátom ajánlása után járó bónuszjutalmak még mindig zárolva vannak. Miért?'
				answer5: 'Mert a barátjának is kell velünk barátkoznia. A barát ajánlása után járó bónuszjutalmakat akkor lehet kifizetni, ha a barátja legalább 750 Ft-ot összegyűjtött velünk.'

			howUseDescription: 'Célunk, hogy minden felhasználó elégedett legyen a Tiplino portállal. Ha a jutalma nem került regisztrálásra, törölték a jutalmat, vagy más problémát észlelt, itt vagyunk Önnek, ne habozzon kapcsolatba lépni velünk.'
			form:
				declaration: 'A űrlap elküldésével elfogadja, hogy az összes megadott adata valós.'
				orderConfirmation: 'Kérjük, csatolja az eredeti rendelési visszaigazolást a kifizetett összeggel, a rendelési számmal és a vásárlás időpontjával. A vásárlás dátuma és ideje megtalálható például az első e-mail visszaigazolás fejlécében.'
				submit: Beadás megoldásra
				question:
					label: Kérdés típusa
					q1: 'Helytelen a jutalom összege<span>A cashbackjutalmat általában az ÁFA, szállítás és esetleges kedvezmények (hűségprogram, születésnapi kedvezmény stb.) nélküli összeg alapján számítjuk.</span>'
					q2: 'Jutalom nem került regisztrálásra<span>Ha több mint 48 óra telt el a rendelés leadása óta, és a cashbackjutalom még mindig nem került regisztrálásra.</span>'
					q3: 'Jutalom nincs jóváhagyva<span>Ha a cashbackjutalom már hozzá van adva a Tiplino fiókjához, de a jóváhagyásra több mint 70 napja vár.</span>'
					q4: 'Kifizetési probléma<span>Ha kifizetést kért, de a pénz 3 napon belül nem került a bankszámlájára.</span>'
					q5: Egyéb ok<span>Valami más aggasztja? Írjon nekünk bátran. Megoldjuk a helyzetet együtt.</span>

				device:
					label: Telefonon vagy számítógépen vásárolt?
					d1: A vásárlást <strong>számítógépen</strong> végeztem
					d2: A vásárlást <strong>mobil eszközön / tableten</strong> végeztem

				browser:
					label: Melyik böngészőt használta a vásárlás során?
					b1: Google Chrome
					b2: Mozilla Firefox
					b3: Opera
					b4: Safari
					b5: Internet Explorer / Edge
					b6: Egyéb

				adblock:
					label: Nem volt bekapcsolva az AdBlock vagy más hirdetésblokkoló vásárlás közben?
					ab1: A vásárlás közben <strong>be volt kapcsolva a hirdetésblokkoló</strong>
					ab2: A vásárlás közben <strong>ki volt kapcsolva a hirdetésblokkoló</strong>
					ab3: 'Nem tudtam, mi az az AdBlock'

				issue:
					label: 'Kényelmesen, komplikációk nélkül vásárolt?'
					i1: 'Igen, a vásárlás a tervek szerint zajlott'
					i2: 'Nem, a vásárlás során komplikációk merültek fel'

				coupon:
					label: Használt valamilyen kedvezménykódot vagy hűségprogramot a vásárlás során?
					c1: 'Igen, a vásárlás során kedvezménykódot vagy hűségprogramot használtam'
					c2: 'Nem, a vásárlás során nem használtam semmilyen kedvezménykódot vagy hűségprogramot'

				couponCode:
					label: A vásárlás során használt kedvezménykód

				attachments:
					label: Vásárlási bizonylat

				message:
					label: Üzenete
					placeholder: Üzenet szövege

				validator:
					questionRequired: 'Kérjük, válassza ki a kérdés típusát.'
					deviceRequired: 'Kérjük, válassza ki az eszközt, amelyet használt.'
					browserRequired: 'Kérjük, válassza ki a böngészőt.'
					issueRequired: 'Kérjük, válassza ki, hogy voltak-e problémái a vásárlás során.'
					couponRequired: 'Kérjük, válassza ki, hogy használt-e kedvezménykupont.'
					couponCodeRequired: 'Kérjük, adja meg a használt kedvezménykódot.'
					messageRequired: 'Kérjük, írja meg üzenetét.'
					adblockRequired: 'Kérjük, válassza ki, hogy használt-e AdBlock-ot.'
					attachmentsRequired: 'Kérjük, csatolja a vásárlási bizonylatot vagy más anyagot.'
					attachmentsWrongFiles: 'Nem töltheti fel ezt a fájlt. Engedélyezett formátumok: doc, docx, gif, jpeg, jpg, png, pdf, txt'

				messages:
					success: Az üzenet sikeresen elküldve. Köszönjük.

		accountFriend:
			title: '<strong>%amount% Ft</strong><br> minden barát után,<br> aki a Tiplinón keresztül vásárol'
			subtitle: Ajánljon akár <strong>%userLimit% ismerőst</strong> és <strong>szerezzen %upTo% Ft</strong>.
			addition: 'Csak annyi szükséges, hogy mindegyikük legalább 1 vásárlást végezzen a Tiplinón keresztül.'
			sign: Szerezzen<br><strong>%amount% Ft</strong>
			header:
				title1: Küldjön meghívót barátjának e-mailben
				title2: Másolja le a linkjét és ossza meg barátaival
				emailPlaceholder: Adja meg az e-mail-címeket
				step1: '<strong>Másolja le a linket</strong> és küldje el barátjának, vagy <strong>ossza meg</strong> a Tiplinót a közösségi médiában.'
				step2: 'Ha a barátja <strong>átkattint</strong> az Ön által megosztott linkre és <strong>regisztrál</strong>, jutalmat kap.'
				step3: '<strong>A %amount% Ft</strong> jutalom az ajánlásért Önnek lesz regisztrálva <strong>, miután a jutalmak regisztrálva lettek</strong> a barátja számláján.'
				or: vagy
				link: 'A megosztással elfogadja az <a href="%link%">Ajánlj és szerezz</a> feltételeit'

			friend:
				title: 'Barátok, akiknek ajánlotta a Tiplinót'
				registration: regisztráció
				bonus: bónusz %bonus%
				bothMsg: 'Ahhoz, hogy megkapja a bónuszát %bonus%, még össze kell gyűjtenie <strong>%userMissingToConfirm%</strong> megerősített cashbackjutalmat. A barátjának még össze kell gyűjtenie <strong>%recommendedUserMissingToConfirm%</strong> megerősített cashbackjutalmat.'
				userMsg: 'Ahhoz, hogy megkapja a bónuszát %bonus%, még össze kell gyűjtenie <strong>%userMissingToConfirm%</strong> megerősített cashbackjutalmat.'
				recommendedUserMsg: 'Ahhoz, hogy megkapja a bónuszát %bonus%, a barátjának még össze kell gyűjtenie <strong>%recommendedUserMissingToConfirm%</strong> megerősített cashbackjutalmat.'

			favorite:
				title: Kedvenc webáruházak

		tellFriend:
			title:
				part1: Szerezzen %amount% Ft-ot
				part2: 'minden egyes barátért,'
				part3: akit ajánl

			condition: 'Csak annyit kell tennie, hogy legalább 780 Ft-ot szerezzen a vásárlásai után'
			steps:
				title: Ossza meg a jutalom örömét barátaival
				step1:
					title: Ossza meg a Tiplinót barátaival
					text: 'Másolja le a linket, és küldje el barátjának, vagy ossza meg a Tiplinót a közösségi médiában.'

				step2:
					title: %amount% Ft ajánlásért járó bónusz
					text: 'Ha a barátja az Ön által megosztott linkre kattint és regisztrál, akkor egy várakozó bónuszt kap %amount% Ft értékben.'

				step3:
					title: A bónusz feloldása
					text: 'Az ajánlásért járó %amount% Ft bónusz akkor kerül megosztásra, ha barátja legalább 750 Ft-ot szerez a jutalmakból.'

			recommendLink:
				title: Ossza meg egyedi regisztrációs linkjét barátaival
				conditions: 'A megosztással elfogadja az <a class="underline" href="%link%">Ajánlj és szerezz</a> feltételeit'
				copy: Másolás
				copied: Másolva

			promo:
				reward: '<span class="font-bold">%amount% Ft-ot szerezhet</span> 30 ajánlásért'
				smalltext: Megéri ajánlani a Tiplinót

			warning:
				title: Fontos figyelmeztetés
				text1: 'Szigorúan <span class="font-bold">tilos megosztási linkeket közzétenni partnerek weboldalain vagy profiljain</span>. E szabályok megszegése esetén a felhasználó fiókját zároljuk és nem jogosult a jutalmak kifizetésére.'
				text2: 'Tilos ajánlani és bónuszokat felhasználni olyan felhasználói fiókok között, amelyek ugyanazt az eszközt használják (számítógép, tablet, mobil stb.) a Tiplinóhoz való hozzáféréshez. Családtagok ajánlása ezen feltételek teljesítése esetén engedélyezett.'

			faq:
				title: Gyakran ismételt kérdések
				question1: Hogyan ajánlhatom a Tiplinót barátaimnak?
				answer1: 'Barátainak az alábbi módokon ajánlhatja a Tiplinót:'
				list1: 'Az ajánlási link másolásával és elküldésével barátainak <strong>e-mailben, Facebookon, WhatsAppon, Viberen</strong> vagy bárhol, ahol kapcsolatban állnak'
				list3: 'Megosztás a közösségi médiában - helyezze el az ajánlási linket a Facebookon, vagy akár tweeteljen a Tiplino előnyeiről, és szerezzen %upTo% Ft jutalmat'
				question2: Milyen jutalmat kapok barátaim ajánlásáért?
				answer2: 'Ajánlásért bónuszjutalmat kap <strong>%amount% Ft értékben</strong>. A bónuszjutalmat regisztráljuk a számláján, amint barátja <strong>megvásárolja első termékét a Tiplinón keresztül</strong>.'
				question3: 'Kap jutalmat a barátom is, akit ajánlottam?'
				answer3: 'Sajnos a barátja nem kap kezdeti jutalmat tőlünk. Ha azonban ő is ajánl másokat, akkor ő is, mint Ön, bónuszjutalmat szerez <strong>%amount% Ft értékben</strong>.'
				question4: 'Ajánlottam egy barátot, és az ajánlási jutalmak áttekintésében a "jóváhagyásra vár" információ látható. Mikor lesz jóváhagyva az ajánlási jutalom? Mikor kérhetem a kifizetést?'
				answer4: 'Az ajánlási bónusz <strong>%amount% Ft</strong> akkor kerül jóváhagyásra, amikor barátja <strong>legalább 750 Ft megerősített jutalmat</strong> szerez a Tiplinón (az átlagos jutalom körülbelül 375 Ft, tehát 2 vásárlás szükséges a barátjától a Tiplinón keresztül). A bónuszjutalmakat akkor lehet kifizetni, ha a felhasználónak legalább 1500 Ft megerősített jutalma van a vásárlásokból. Ha a jutalom kifizetésre kerül, a következő bónuszjutalom kifizetéséhez ismét el kell érni a 1500 Ft-ot a felhasználó számláján.'
				question5: Hány barátot ajánlhatok?
				answer5: 'Ajánljon nekünk 30 barátot, és <strong>keressen akár %upTo% Ft-ot</strong>. Minden ajánlásért %amount% Ft-ot szerezhet.'

			recommendedUsers:
				title: Az Ön által ajánlott barátok

		notification:
			title: Üzenetek
			subtitle: Legújabb értesítések és akciók
			notFound: Még nem érkezett üzenet.
			cta: Folytatás

	transaction:
		default:
			title: Jutalmak és előnyök
			tellFriend: Összesen akár <strong>%amount% Ft-ot</strong> szerezhet az ajánlásával <strong>%recommendation% ismerősének</strong>|Összesen akár <strong>%amount% Ft-ot</strong> szerezhet az ajánlásával <strong>%recommendation% ismerősének</strong>|Összesen akár <strong>%amount% Ft-ot</strong> szerezhet az ajánlásával <strong>%recommendation% ismerősének</strong>
			tellFriendLink: Ajánlott ismerősök áttekintése
			registeredBonusBalance:
				title: Bónuszjutalmak
				tooltip: Minden jóváhagyásra váró bónuszjutalom összértéke. A részletekért nézze meg a jutalmak áttekintését.

			registeredCommissionBalance:
				title: Függőben lévő tranzakciók
				tooltip: 'A jóváhagyásra váró jutalmak összege az üzletből (például az üzlet várja a 14 napos törvényes visszatérítési határidő lejártát).'

			confirmedBalance:
				title: Kifizethető jutalmak
				tooltip: 'Az összeg, amelyet kifizetésre kérhet.'

			btnPay: Jutalmak kifizetése
			share:
				title: 'Ossza meg velünk a közösségi médiában, és keressen több pénzt'
				description: 'Ha egy felhasználó a közösségi médiában közzétett státusza révén érkezik a Tiplinóra, akkor ajánlási jutalmat kap %amount% Ft összegben. Ezt a jutalmat bónuszként regisztráljuk számlájára.'

		benefitProgress:
			title: Szerezzen ajándékot tőlünk %value% Ft értékben
			remain: 'Ahhoz, hogy <strong>ajándékot</strong> kapjon, még <strong>%value% Ft</strong> összeget kell összegyűjtenie'
			time: Hátra van még <strong>%days% nap</strong>|Hátra van még <strong>%days% nap</strong>|Hátra van még <strong>%days% nap</strong>
			help:
				one: 'Vásároljon először a Tiplinón keresztül, és szerezze meg első jutalmát a vásárlásból.'
				two: Szerezzen összesen %amount% Ft jutalmat cashback formájában a következő napon.|Szerezzen összesen %amount% Ft jutalmat cashback formájában a következő %days% napban.|Szerezzen összesen %amount% Ft jutalmat cashback formájában a következő %days% napban.
				three: 'Teljesítette? Gratulálunk, szerzett <strong>%amount% Ft-ot a Tiplinótól</strong>.'
				hide: Súgó elrejtése
				show: Súgó megjelenítése

	payout:
		request:
			forPayout: '<span class="font-bold text-primary-orange">%amount% Ft</span> kifizetésre vár'
			accountNumber: 'A jutalom kifizetése erre a számlára lesz átutalva: <span class="text-dark-1 font-bold">%accountNumber%</span>'
			noReward:
				title: 'Jelenleg <span class="font-bold text-primary-orange">nincs kifizethető jutalma</span>'
				waitingForFirstOrder: 'Várjuk az első vásárlását,'
				waitingForFirstOrder2: hogy regisztrálhassuk a jutalmát
				how: Hogyan szerezzen
				how2: jutalmakat
				steps:
					first:
						title: Válassza ki a webáruházat
						text: Vásárlás előtt keresse meg kedvenc üzletét a Tiplinón.

					second:
						title: Látogassa meg az üzletet
						text: 'Kattintson a linkre, és látogassa meg az üzletet a Tiplinón keresztül'

					third:
						title: Szerezze meg a jutalmat
						text: 'Vásároljon, ahogy szokott, és a vásárlás után kapja vissza a pénz egy részét.'

					cta: Az összes webáruház megtekintése

			payout: <strong>%count% Ft</strong> már kifizetve
			waiting: <strong>%count% Ft</strong> jóváhagyásra vár
			bankAccount: 'A jutalom kifizetése erre a számlára lesz átutalva: <strong>%number%</strong>.'
			condition: 'Kijelentem, hogy a Tiplino szolgáltatást egyedülállóan használom, személyes adataim valósak, és a megadott bankszámla <strong>%number%</strong> helyes.'
			submit: Jutalom kifizetése
			minimalReward: A megerősített jutalom minimális összege a kifizetéshez 1 Ft
			noFullProfile:
				title: Profilja nincs teljesen kitöltve
				subTitle: 'Kérjük, töltse ki az alábbi adatokat:'
				button: Fiók beállítása
				accountNumber: bankszámla
				firstName: keresztnév
				lastName: vezetéknév
				gender: nem
				phoneNumberVerificationTitle: 'Kérjük, erősítse meg telefonszámát'
				phoneNumberVerificationText: 'Ahhoz, hogy pénzt küldhessünk a számlájára, először meg kell erősítenünk a telefonszámát.'
				phoneNumberVerificationButton: Szám ellenőrzése
				phoneNumber: telefonszám
				accountNumberVerificationTitle: 'Kérjük, erősítse meg bankszámláját'
				accountNumberVerificationText: 'Ahhoz, hogy pénzt küldhessünk a számlájára, először meg kell erősítenünk a bankszámláját.'
				accountNumberVerificationButton: Számla ellenőrzése

			errors:
				uncheckedConditions: El kell fogadnia a feltételeket.

			waitingRewards:
				title: 'A jutalmak <span class="font-bold text-primary-orange">jóváhagyásra várnak</span> a webáruház által'
				heading: 'Jutalmai, amelyek megerősítésre várnak'
				faq:
					title: Mennyi ideig tart a jutalom megerősítése?
					text: A megerősített jutalmakat azonnal kifizethetjük bankszámlájára.
					commissions:
						title: Vásárlásokból származó jutalmak
						text: A vásárlásokból származó jutalmak jóváhagyása általában 30-70 napot vesz igénybe az eredeti jutalom hozzáadásától számítva. Ez az időszak a reklamációk vagy a termék visszaküldésének esetére van meghatározva.

					bonus:
						title: Bónuszjutalmak
						text: 'A bónuszjutalmak akkor lesznek jóváhagyva, ha elérte a meghatározott számú vásárlásból származó jutalmat. Például a bónusz megerősítése akkor történik meg, amikor 1500 Ft megerősített jutalmat szerez.'

		default:
			title: Jutalmak kifizetése
			payouts:
				title: Függőben lévő kifizetési kérelmek és már kifizetett jutalmak
				description: 'Az alábbiakban láthatja a jutalmai összes kifizetését és függőben lévő kifizetési kérelmeit, amelyek jóváhagyásra várnak. A kérelem jóváhagyása általában nem tart tovább 2 munkanapnál.'
				table:
					amount: Kifizetés összege
					accountNumber: Számlaszám
					iban: IBAN
					createdAt: Kérelem dátuma
					confirmedAt: Kifizetés dátuma

		thankYou:
			title: Köszönjük!
			subTitle: A kifizetési kérelmet sikeresen megkaptuk
			text: 'A jutalmak kifizetése a kérelem jóváhagyása után a következő 3 munkanapban megtörténik. Amint a kifizetést jóváhagyjuk, értesíteni fogjuk.'
			small: 'Ha bármilyen kérdése van a kérelem státuszával kapcsolatban, kérjük, vegye fel velünk a kapcsolatot a <a href="mailto:<EMAIL>"><EMAIL></a> címen'

	transactions:
		box1:
			title: Jutalomra vársz?
			descriptionBold: A jutalmad általában a vásárlást követő 48 órán belül megjelenik a jutalmak áttekintésében.
			description: '''
				Ha még ezután sem látod a jutalmadat, jelezd nekünk.
				<span class="italic">Várjuk a vásárlásodról szóló információt az alábbi üzletekből:</span>
			'''
			guarantee: Jutalom jóváírási garancia
			moreSingular: +%amount% további
			morePlural: +%amount% továbbiak
			button: Hiányzó vásárlás bejelentése

		box2:
			title: Várjuk az első vásárlásodat
			descriptionBold: A vásárlás után a jutalmad általában 48 órán belül megjelenik az áttekintésben.
			description: 'Próbáld ki az első vásárlásodat a Tiplinón keresztül, és szerezd meg a jutalmadat.'
			descriptionItalic: 'Jutalmat szerezhetsz több mint 500 üzletben:'
			guarantee: Jutalom jóváírási garancia
			table:
				cashback: '<span class="_upTo">akár</span> <span class="_value">%cashback%</span>&nbsp;<span class="_symbol">%</span> <span class="_suffix">vissza a vásárlásból</span>'
				buttonShops: Minden üzlet megtekintése
				buttonHelp: Segítségre van szükséged?

	lastRedirections:
		default:
			title: Legutóbbi átirányítások
			withoutRedirections:
				title: Várjuk az első vásárlásodat
				desctiption: 'A vásárlás után a jutalmad általában 48 órán belül megjelenik a jutalmak áttekintésében. <span class="font-bold">Fejezd be az első vásárlásodat a Tiplinón keresztül, és szerezd meg a jutalmat.</span>'
				guarantee: Jutalom jóváírási garancia
				table:
					title: 'Jutalmat szerezhetsz több mint 500 üzletben:'
					allShops: Összes üzlet megtekintése
					help: Segítségre van szükséged?
					tooltipContent: 'Várunk a(z) %shopName% vásárlási információira. Hátralévő idő: %hours% óra %minutes% perc'
					tooltipContentSuccess: 'span class="font-bold text-secondary-green">Megkaptad a jutalmadat a(z) %shop% üzlettől.</span><br> A részleteket a jutalmak áttekintésében találod.'

			redirections:
				title: Legutóbbi átirányítások
				titleLink: ''
				dectiption: 'Itt láthatod az összes üzletet, ahová a Tiplinón keresztül kattintottál. Ha 48 órán belül nem kaptál jutalmat a vásárlás után, jelezd nekünk. Mindent ellenőrzünk, és örömmel jóváírjuk a jutalmat.'
				guarantee: Jutalom jóváírási garancia
				table:
					title: Legutóbbi átirányításaid a Tiplinón keresztül
					shop: Üzlet
					redirectionTime: Átirányítás ideje
					information: Információ a jutalomról
					report: Hiányzó jutalom bejelentése
					reward: Jutalom jóváírva

	personalization:
		skip: Ezt a lépést kihagyom
		skipWithoutMoney: 'Nem akarom a 100 Ft-ot, és ki szeretném hagyni ezt a lépést'
		whyPanel:
			title: Miért van szükség ilyen sok információra?
			description: Lorem ipsum dolor sit amet consectetuer adipiscing adipiscing sociis felis lorem. Ligula quis enim nisl Duis Curabitur.
			reason1: Tiplino az Ön igényeire szabva
			reason2: Születésnapi ajándék
			reason3: %amount% a bankszámlára teljes regisztrációért

		favouriteShops:
			thanks: Köszönjük regisztrációját!
			fill: 'Töltse ki profilját, és szerezzen 100 Ft bónuszt.'
			title: Válassza ki kedvenc boltjait
			description: Lorem ipsum dolor sit amet consectetuer adipiscing adipiscing sociis felis lorem. Ligula quis enim nisl Duis Curabitur.
			submit: Folytatás

		personalData:
			title: Töltse ki személyes adatait
			submit: Regisztráció befejezése és %amount% megszerzése
			line1: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation'
			line2: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation'
			line3: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation'

		bankDetails:
			title: 'Köszönjük, hogy kitöltötte profilját'
			description: A bizalom erősítése érdekében azonnal elküldjük Önnek a <strong>%amount%</strong>-ot.
			submit: %amount% elküldése a bankszámlára

		information:
			title: Hogyan működik?
			description: Lorem ipsum dolor sit amet consectetuer adipiscing adipiscing sociis felis lorem. Ligula quis enim nisl Duis Curabitur.
			btnOK: Értem
			btnHelp: Segítségre van szükségem

		form:
			firstName:
				label: Keresztnév

			lastName:
				label: Vezetéknév

			phoneNumber:
				label: Telefonszám

			birthdate:
				label: Születési dátum

			gender: 'Válassza ki a nemét:'
			bankAccount: Adja meg bankszámlaszámát.
			iban: Adja meg az IBAN-t.
			validator:
				birthdate: 'Kérjük, adja meg születési dátumát.'
				birthdatePattern: 'A születési dátum formátuma nem megfelelő. A helyes formátum: 01.01.2016'

	recommendedUsersList:
		empty: Egyelőre senki sem regisztrált az Ön linkjén keresztül.
		table:
			user: Felhasználó
			date: Regisztráció dátuma

	transactionsList:
		title: Jutalom áttekintése
		empty: Egyelőre nem regisztráltunk jutalmat nálad.
		nonProfit: 'Összesen <span class="font-bold">%amount% Ft-ot</span> adományoztál vásárlásaiddal jótékony célokra. <span class="text-primary-orange font-medium">Köszönjük! 🧡</span>'
		unknownCommissionAmount: A jutalom összege pontosítva lesz
		table:
			name: Név
			amount: Jutalom összege
			registeredAt: Regisztráció dátuma
			confirmedAt: Visszaigazolás dátuma
			type: Jutalom típusa
			moreInfo: Több információ a jutalomról
			lessInfo: Részletek bezárása
			orderAmount: Rendelés összege
			userCommission: Vásárlás visszatérítés
			statusWFC: Várakozás visszaigazolásra
			estimatedConfirmation: Visszaigazolás várhatóan %date%-ig
			confirmationInDelay: Az állapot ellenőrzése folyamatban van az eladónál
			isCancelled: 'A jutalmat az eladó elutasította, például kupon használata vagy a rendelés módosítása miatt.'
			infoValueHelp: 'A rendelés összege tartalmazhatja az ÁFA-t, a szállítási költséget és a felhasznált kedvezményeket.'
			state: Állapot
			confirmed: Visszaigazolt
			canceled: Elutasított
			estimatedConfirmationMobile: Várható visszaigazolás

		filter:
			all: Minden jutalom
			wait: Várakozó jutalmak
			confirm: Megerősített jutalmak
			lostReward: Nem került regisztrációra a jutalma?
			report: Hiányzó vásárlás bejelentése

	expireTransactionsList:
		title: Lejárt jutalmak
		text: 'Ha a Tiplino-fiókban több mint két évig nem történt vásárlás, a korábban nem kifizetett jutalmak már nem fizethetők ki.'
		button: Jutalmak megtekintése
		table:
			name: Név
			amount: Jutalom összege
			moreInfo: Több információ a jutalomról
			registeredAt: Regisztráció dátuma
			expiredAt: Lejárat dátuma

	refundsList:
		title: Kérelmek áttekintése
		empty: Egyelőre nincs nyilvántartott reklamációja.
		table:
			createdAt: Létrehozás dátuma
			type: Kérelem típusa
			shop: Üzlet
			state: Állapot
			closedAt: Zárás dátuma
			orderNumber: Rendelési szám

		stateForCustomer:
			approved: Jóváhagyva
			declined: Elutasítva
			open: Nyitott

		titleByRefundType:
			missing_commission: Nem regisztrált jutalom
			missing_bonus: Van egy kérdésem a bónusszal kapcsolatban
			incorrect_amount: Helytelen jutalom összege
			unconfirmed_transaction: Nem megerősített jutalom
			canceled_commission: Törölt jutalom

	shareRewardsList:
		title: Előnyök áttekintése
		table:
			name: Név
			shop: Webáruház
			allShops: Minden webáruház
			validity: Érvényesség
			validForever: örökké

	reviews:
		default:
			title: Értékeléseim
			description: Itt találja meg az összes értékelését a Tiplinón.
			tipliReview:
				title: Tiplino értékelése
				name: Tiplino.hu
				review: Értékelje a Tiplinót

			shopsToReview:
				title: Webáruházak értékelése
				review: Értékelje a webáruházat

			shopsReviews:
				title: Értékelt webáruházak

		share:
			title: Köszönjük az értékelést!
			rate: Ossza meg értékelését a Facebookon.
			rateBtn: Értékelés a Facebookon
			back: Vissza az értékeléseimhez

		review:
			title: Értékelés hozzáadása
			titleShop: 'Értékelje a(z) %shop% webáruházat'
			userName: Teljes név
			userNameWillBePublished: '(Ez a név meg fog jelenni az értékeléssel együtt)'
			allowUserPicture: Profilkép megjelenítésének engedélyezése
			opinion: Az Ön értékelése
			text: Mennyire volt elégedett?
			submit: Értékelés hozzáadása
			nameRequired: 'Kérjük, adja meg a nevét'
			textRequired: 'Kérjük, adja meg az értékelés szövegét'
			invalidName: Az e-mail-cím nem használható fel az értékeléshez
			emojiNotAllowed: 'Emoji karakterek nem megengedettek, kérjük, adjon meg szöveges értékelést'
			shopDoesNotExists: A webáruház nem létezik
			done: Az értékelést sikeresen elmentettük.
			tips:
				shop:
					title: Mi érdekel minket
					text1: 'Termék kézbesítése, gyorsaság és minőség'
					text2: Az üzlet ügyfélszolgálatának minősége
					text3: Az Ön tapasztalata a cashbackkel a webáruházban

				tipli:
					title: Mi érdekel minket
					text1: Az Ön tapasztalatai a vásárlásból származó jutalmak megszerzésével
					text2: Tapasztalatok az ügyfélszolgálattal kapcsolatban
					text3: A weboldal áttekinthetősége
					text4: Ötletek

			globalTips:
				title: Mit kerüljünk el
				text1: Trágár szavak és sértések használata
				text2: Értékelés használata fórumhozzászólásként
				text3: Linkek és reklámok elhelyezése
				text4: Személyes adatok megadása
				text5: Az értékelés tartalmának nagybetűkkel írása

		reviewRequest:
			description: 'Kérjük, ossza meg velünk, min kellene legközelebb javítanunk.'
			descriptionFilled: <b>Kész!</b> Az értékelést kiegészítettük.
			submit: Értékelés beküldése

	feedback:
		form:
			feedbackMessage: Üzenete
			submit: Beküldés

		default:
			thankYou: 'Köszönjük az értékelését, hagyhat nekünk üzenetet is.'
			backToTipli: 'Vissza a <a href="https://www.tiplino.hu">Tiplino</a> oldalára'

		thankYou:
			positive: 'Köszönjük, hogy elküldte értékelését. Értékelhet minket a <a href="https://www.facebook.com/tiplinohu/reviews">Facebookon</a> is.'
			neutralOrNegative: Köszönjük a visszajelzést. Dolgozunk a javításán.

refund:
	guaranteeTitle: Elégedettségi garancia
	title: A jutalom regisztrálása akár 48 órát is igénybe vehet
	text: 'Ha ez idő elteltével sem látja a jutalmat regisztrálva vagy más problémát észlel, itt vagyunk Önnek, hogy megoldjuk a helyzetet az Ön elégedettsége érdekében.'
	refundsText: Tekintse meg az összes követelmény áttekintését
	refundsCta: Áttekintés megtekintése
	existingRefund:
		allClose: 'Tekintse meg az <a href="%refundLink%">összes kérelmet</a>.'
		inProgress: 'Tekintse meg az <a href="%refundLink%">összes kérelmet</a>.'

	back: Vissza a választáshoz
	chooseButton: Kiválasztás
	step1:
		title: Miben segíthetünk?
		choice1:
			title: Kérdésem van a jutalommal kapcsolatban
			text: 'Előfordul, hogy technikai okok miatt valami nem sikerül. Nem került regisztrációra a jutalma vagy a bónusza, helytelen a jutalom összege vagy régóta megerősítésre vár a jutalma - mindezt segítünk megoldani.'

		choice2:
			title: Kérdésem van a kifizetéssel kapcsolatban
			text: Eltévedt a jutalma kifizetése a számlájára vezető úton vagy kérni szeretné a számlaszám módosítását? Itt segítünk Önnek.

		choice3:
			title: Más kérdésem van
			text: 'Ha nem tudja hová irányítsa kérdését, itt mindig segítünk Önnek.'

	step2:
		title: Kérdésem van a jutalommal kapcsolatban
		text: 'Előfordul, hogy technikai probléma miatt valami nem sikerül. Nem került regisztrációra a jutalma vagy a bónusza, helytelen a jutalom összege vagy régóta jóváhagyásra vár a jutalma - mindezt segítünk megoldani.'
		choice1:
			title: Nem regisztrált jutalom

		choice2:
			title: Van egy kérdésem a bónusszal kapcsolatban

		choice3:
			title: Helytelen jutalom összege

		choice4:
			title: Nem megerősített jutalom

		choice5:
			title: Törölt jutalom

		choice6:
			title: Más kérdésem van
			text: 'Ha nem tudja, hová irányítsa kérdését, itt mindig segítünk Önnek.'

	missingCommission:
		title: Nem regisztrált jutalom
		bookingInfo: 'Jelenleg késések tapasztalhatók a <strong>Booking.com</strong> jutalmak regisztrálásában. Nem kell aggódnia a jutalmak miatt, mindent regisztrálunk később. Intenzíven dolgozunk a helyzet megoldásán. Köszönjük megértését.'
		refundsFormInfo: 'Az utólag regisztrált jutalmak <a href="feltetelek#bonus"><u>bónuszként</u></a> kerülnek regisztrálásra.'

	missingBonus:
		title: Van egy kérdésem a bónusszal kapcsolatban

	incorrectAmount:
		title: Helytelen jutalom összege

	unconfirmedTransaction:
		title: Nem megerősített jutalom
		disabledTemuTransaction: A Temu webáruház általában a vásárlástól számított 90 nap elteltével erősíti meg a jutalmakat.
		disabledSheinTransaction: A Shein webáruház általában 120 nappal a vásárlás után erősíti meg a jutalmakat.

	canceledCommission:
		title: Törölt jutalom

	payout:
		title: Kérdésem van a kifizetéssel kapcsolatban
		text: 'Kérjük, válassza ki azt a kifizetést, amelyikhez kérdése van, vagy hagyja kiválasztva az alapértelmezett opciót, ha nincs konkrét kifizetési probléma:'
		subject: Probléma a kifizetéssel

	other:
		title: Más kérdésem van
		text: 'Ha nem tudja, hová irányítsa kérdését, itt mindig segítünk Önnek.'
		subject: Egyéb

	form:
		time: Vásárlás ideje
		date: Vásárlás dátuma
		dateAlert: 'A jutalmat általában <strong>48 órán belül</strong> regisztráljuk a vásárlás után. Kérjük, várjon, és ha a jutalom még nem került regisztrálásra, nyújtsa be kérelmét. Köszönjük.'
		shops: 'Jelölje be, melyik webáruházban vásárolt'
		shopsSearch: Keressen másik webáruházat
		noShops: Nem találhatóak webáruházak
		invoice: Számla vagy rendelési bizonylat
		secondInvoice: További bizonylat a rendeléshez
		orderId: Rendelési szám
		orderAmount: Rendelési összeg
		orderCurrency: Rendelés pénzneme
		couponUsed: Kedvezménykódot használtam a vásárláskor
		couponCode: Kuponkód
		chooseReward: Válassza ki a jutalmat
		chooseProblemReward: Válassza ki a jutalmat
		chooseProblemPayout: 'Válassza ki a kifizetést, amelyhez a probléma kapcsolódik'
		otherProblemPayout: Nincs probléma a felsorolt kifizetésekkel
		showMoreRewards: további jutalmak megjelenítése
		showMorePayouts: további kifizetések megjelenítése
		message: Üzenet tartalma
		allTransactionsConfirmed: Minden tranzakciója meg van már megerősítve.
		reward: Jutalom
		expectedAmount: A jutalom várható összege
		noTransactionsWaitingToConfirmed: 'Nincsenek olyan tranzakciói, amelyek megerősítésre várnának.'
		transactionsWaitingToConfirm: Jutalma jelenleg a webáruház megerősítésére vár. A jutalom megerősítése általában 70 napot vesz igénybe a Tiplino-fiókjába való regisztrációtól számítva.
		noCanceledCommissions: Nincsenek törölt jutalmai.
		productUrls: 'Link a termékhez: (több link esetén minden sorba külön-külön)'
		usedPlatform: 'A következőt használtam a vásárláshoz:'
		platform:
			mobile: Mobiltelefon
			desktop: Számítógép
			tablet: Tablet
			platform: Eszköz

		userNote: 'Megjegyzés:'
		chooseTransaction: Válassza ki a rendelést
		chooseFile: Fájl kiválasztása
		noFile: Nincs fájl kiválasztva
		addonUsed: A vásárláshoz a Tiplino böngészőbővítményt használtam
		mobileAppUsed: A vásárláshoz a Tiplino mobilalkalmazást használtam
		errors:
			csrf: 'Kérjük, próbálja újra később.'
			messageMissing: 'Kérjük, írja meg üzenetét'
			commission:
				invalidOrderAmount: Érvénytelen rendelési összeg
				invalidOrderDateFormat: Érvénytelen rendelési dátumformátum
				invoiceMissing: 'Kérjük, csatolja a rendelés visszaigazolását.'
				couponCodeLength: A kuponkód maximum 200 karakter hosszú lehet
				orderDateMissing: 'Kérjük, adja meg a vásárlás dátumát'
				orderAmountMissing: 'Kérjük, adja meg a rendelés teljes összegét'
				orderCurrencyMissing: Válassza ki a pénznemet
				dateBeforeUserRegistration: 'A vásárlás dátuma nem lehet régebbi, mint a Tiplinón való regisztrációjának dátuma'
				missingShop: 'Válassza ki azt a webáruházat, amelyben vásárolt'
				orderIdMissing: 'Kérjük, adja meg a rendelési számot'

			incorrectAmount:
				transactionIdMissing: 'Kérjük, válassza ki a jutalmat'
				expectedAmountMissing: 'Kérjük, adja meg az összeget'
				invalidExpectedAmount: Érvénytelen a jutalom összege
				transactionNotFound: A tranzakció nem létezik

			payout:
				transactionIdMissing: 'Kérjük, válassza ki a jutalmat'

	thankYou:
		title: 'Köszönjük, hogy elküldte a kérését'
		text: Kérését a lehető leghamarabb elbíráljuk.
		link: Vissza a Tiplinóhoz

phoneApp:
	metaTitle: Mobilalkalmazások
	title: Töltse le a mobilalkalmazást
	subTitle: a Tiplinótól
	text: 'A Tiplino mobilalkalmazás segítségével könnyedén szerezhet jutalmakat vásárlásai után, még akkor is, ha mobilon vásárol.'
	allInOnePlace: Minden egy helyen
	list:
		title: Az alkalmazásunkban elérheti
		item1: Jutalmakat szerezhet <strong>%count% üzletben</strong>
		item2: Azonnali értesítés az új jutalmakról
		item3: Információk az aktuális kedvezményes kuponokról és akciókról

	getApp: 'Itt töltheti le:'
	getApple: Letöltés
	getAndroid: Most

contactPage:
	title: Lépjen kapcsolatba velünk
	email: E-mail
	text: 'Ha mondani szeretne valamit vagy szívesen együttműködne velünk, mindenképpen írjon nekünk. Az alábbi címeken érhet el minket:'
	writeUs: Írjon nekünk
	writeUsText: 'Ha segítségre van szüksége jutalommal vagy bónusszal kapcsolatban, kérjük, lépjen kapcsolatba velünk az <a href="%link%" class="underline hover:no-underline">Elégedettségi garancia</a> űrlapon keresztül.'
	message: Üzenet
	send: Üzenet küldése
	defaultSubject: Általános kérdés

popups:
	banner:
		ctaCopyAndRedirectShop: Kód másolása és tovább
		ctaRedirectToShop: 'Tovább a(z) %shop% boltba'
		ctaRedirect: Tovább

	exit:
		title: 'Valóban el akarja veszíteni a <span class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%reward%</span> <span class="font-bold">jutalmat a vásárlásból</span> a(z) %shop% üzletben?'
		text: 'Csak <span class="font-bold text-primary-orange">ingyenesen regisztráljon</span>, és soha többé nem marad le jutalomról.'
		titleNonCashback: 'Valóban le akar maradni a <strong class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%shop%</strong> exkluzív kuponjairól és kedvezményeiről?'
		textNonCashback: 'Csak <span class="font-bold text-primary-orange">regisztráljon ingyen</span>, és soha többé nem veszít el kuponokat.'
		bonus: bónusz új fiókhoz
		bonusAmount: +%amount% Ft
		or: vagy
		footer: Már van fiókja?
		signIn: Bejelentkezés
		email: E-mail
		password: Jelszó
		submit: Regisztráció INGYEN

	redirect:
		title: 'Az átirányítás a(z) <span class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%shop%</span> webáruházba sikeres volt'
		footer: 'A jutalmat általában <span class="font-bold">48 órán belül</span> regisztráljuk a vásárlás után. Ha ez nem történik meg, kérjük, lépjen kapcsolatba velünk a <span class="underline xl:hover:no-underline"><EMAIL></span> címen.'
		steps:
			step1: 'Folytassa a webáruházba a <span class="font-bold">%shop% Tiplinón keresztül</span>'
			step2: 'Vásároljon a <strong>%shop%</strong> webáruházban, ahogy szokott.'
			step3: A vásárlás befejezése után <strong>regisztráljuk a jutalmat</strong>.

	education:
		title: 'Hogyan vásároljon először, és <span class="font-bold">szerezzen jutalmat?</span>'
		steps:
			step1: 'Folytassa a webáruházba a <span class="font-bold">%shop% Tiplinón keresztül</span>'
			step2: 'Vásároljon a <strong>%shop%</strong> webáruházban, ahogy szokott.'
			step3: A vásárlás befejezése után <strong>regisztráljuk a jutalmat</strong>.

		button: Folytatás a webáruházba %shop%
		adblock: 'Hogy ne maradjon le a jutalomról, kérjük, kapcsolja ki az Adblockot a vásárlás során.'

	dealDetail:
		cta: 'Folytatás a(z) %shop% áruházba'
		conditions: Feltételek megtekintése

	password:
		title: 'Kérjük, válasszon jelszót'
		placeholder: Adja meg a jelszót
		cta: Jelszó beállítása

	redirectionWithCashback:
		title: 'Vigyázz! Elveszítheti <span class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%reward%</span> <span class="font-bold">vissza a nyereményvásárlás</span> itt: %shop%'
		cta: Jutalmat szeretnék kapni
		continueToShop: Tovább jutalom nélkül

	redirectionWithoutCashback:
		title: 'Vigyázz! Lemaradhat a <strong class="inline-block bg-orange-gradient font-bold text-white px-3 py-1">%shop%</strong> áruház exkluzív kuponjairól és kedvezményeiről. '
		text: 'Csak regisztráljon, és soha többé nem mulaszt el egyetlen kupont sem.'
		cta: Szerezzen kuponokat
		continueToShop: Menj kuponok nélkül

	signIn:
		title: A <strong>%shop%</strong> boltban járó jutalom aktiválásához már csak be kell jelentkeznie
		footer: Nincs fiókja?
		signUp: Regisztráljon

addon:
	title: Tiplino böngészőbővítmény
	metaKeywords: Tiplino böngészőbővítmény
	metaDescription: Tiplino böngészőbővítmény a cashbackajánlatok követéséhez közvetlenül a böngészőben
	sampleBox:
		reward: vissza a vásárlásból
		cta: Jutalom megszerzése
		coupons: Kedvezményes kuponok megtekintése
		otherCoupons: További kedvezményes kuponok
		countOfCoupons: '1 kupon'
		fromTipli: A Tiplinótól
		sampleCoupon: Kedvezményes kupon 500 Ft
		useCoupon: Kupon használata
		useCoupons: Kuponok használata
		moreCoupons: + 2 további

	promo:
		title: Többé egy vásárlás sem jutalom vagy kupon nélkül!
		text: A Tipli emlékeztet rájuk vásárláskor akár %shopCount% webáruházban is.
		cta: Adja hozzá a Tiplit a böngészőhöz

	rating:
		score: '4,7/5'
		users: '60 ezer aktív felhasználó'

	tier1:
		smallTitle: intelligens <br> értesítés
		title: 'Soha többé nem feledkezik meg <span class="text-primary-orange mb-5">a jutalmakról és kuponokról!</span>'
		text: 'A <strong>Tiplino böngészőbővítmény</strong> emlékeztetni fogja, amikor a több mint %count% webáruház valamelyikét látogatja meg.'
		intencia: '+ ráadásul <strong class="page-addon__c-message__value">%amount%</strong> a Tiplino böngészőbővítmény hozzáadásáért.'
		button: Tiplino hozzáadása a böngészőhöz
		icon:
			chrome: Elérhető a Google&nbsp;Chrome böngészőhöz
			firefox: Elérhető a Firefox böngészőhöz
			safari: Elérhető a Safari böngészőhöz

		small: 'A biztonság és adatvédelem garanciája a Google, amely minden kiegészítőt ellenőriz a kínálatában.'
		noSupport: A Tiplino böngészőbővítmény nem elérhető ehhez a böngészőhöz.

	tier2:
		smallTitle: minden fillér számít
		title: Szerezzen jutalmat egy kattintással
		text: Kapjon vissza pénzt vásárlásai után több száz kedvenc üzletből.

	tier3:
		smallTitle: minden egy helyen
		title: Találja meg a kuponokat könnyedén
		text: Többé nem kell kuponokat keresgélnie. <br> Mindig kéznél lesznek.

	cta:
		title: A kiegészítőnek köszönhetően soha nem felejti el a cashbackjutalmakat
		button: Tiplino hozzáadása a böngészőhöz

static:
	guarantee:
		title: Elégedettségi garancia
		metaKeywords: 'garancia, elégedettség, Tiplino'
		metaDescription: Részletek az Elégedettségi garancia működését illetően

	cookies:
		title: Cookie-használati irányelvek
		metaKeywords: 'cookie-k, irányelvek'
		metaDescription: Ezek a cookie-használati irányelvek a Tiplino szolgáltatási feltételeinek szerves részét képezik

	conditions:
		title: Felhasználási feltételek
		metaKeywords: Kulcsszavak
		metaDescription: Leírás
		showDocument: Dokumentum megtekintése

	whereNow:
		title: Merre tovább?
		text: 'Érdekli még valami? Hogyan működünk, az Elégedettségi garanciáink, kik vagyunk és hol találhat meg minket? Kíváncsi arra, mit kérdeznek ügyfeleink, vagy szeretné tudni, ki a csapatunk része? Fedezze fel további oldalainkat.'

	lookingFor:
		title: 'Nem találta meg, amit keresett?'
		text: 'Ha valamit nem talált meg a szabályzatok között, nézze meg a <a href="%link%">Gyakran ismételt kérdéseket</a>. Ha ott sem találta meg a választ, ne habozzon kapcsolatba lépni velünk. Gyorsan visszajelzünk.'
		button: Gyakran ismételt kérdések megtekintése

	howItWorks:
		mainTitle: Hogyan szerezhet jutalmakat a Tiplinóval?
		recommendationBonus: '<span class="font-bold">%amount% Ft-ot szerezhet </span> 30 ajánlásért'
		title: Hogyan szerezhet jutalmakat?
		metaKeywords: 'cashback, Tiplino, hogyan működik'
		metaDescription: Egyszerűen és érthetően a cashbackjutalmakról a Tiplino portálon.
		subtitle: Hogyan kaphatok vissza pénzt a vásárlásaimból?
		faqTitle: Gyakran ismételt kérdések
		text1: 'Egyszerűen. Vásároljon a <a href="%link%">a <strong>Tiplino</strong></a> cashbackportálon, és a jutalom biztosan nem marad el. Mi az a <strong>cashback</strong>? A cashback szó szerint azt jelenti, hogy “pénz vissza”. A cashback segítségével visszakaphatja a vásárláskor elköltött pénz egy részét. A cashback egyszerűen azt jelenti, hogy jutalmat adunk Önnek a vásárlásaiért a Tiplinón.'
		play: Videó lejátszása
		steps:
			choose:
				title: '1. Válasszon webáruházat'
				text: 'Válassza ki a webáruházatat, ahol vásárolni szeretne. A kínálatunkban több mint <a href="%link%">%activeShops% webshop</a> található, és naponta újakat adunk hozzá.'

			transaction:
				title: '2. Vásároljon, ahogy szokott'
				text: 'A vásárláshoz csak keresse meg kedvenc webáruházatát a Tiplino oldalán, és kattintson az üzletbe vezető narancssárga gombra.'

			cashback:
				title: '3. Kapjon vissza pénzt'
				text: 'Amint vásárol a webáruházatban, az üzlet értesít minket a vásárlásáról. A vásárlás összegéből visszakap egy részt közvetlenül a számlájára.'

		practise:
			title: Hogyan működik a gyakorlatban?
			text: 'Most hall először a cashbackről? Többet szeretne tudni, de nem meri megkérdezni? Ne aggódjon. Készítettünk Önnek egy <strong>konkrét példát</strong>, amely pontosan megmutatja, hogyan működik a cashback a Tiplinón. Kezdjük. A példánkban Léna szeretne egy új pólót vásárolni.'
			steps:
				choose:
					title: '1. Webshop kiválasztása'
					text: 'Léna a Tiplinóra való bejelentkezés után megtalálta a divatwebshopot, amely <strong>10% visszatérítést</strong> kínál a vásárlásból. A webáruház Tiplino-profiljáról kattintott az e-shopba, ahol kiválasztotta az új pólóját. Pontosan úgy, mintha bárhol máshol választotta volna ki.'
					subtitle: Az üzlet cashbackje
					value: '10%'

				transaction:
					title: '2. Vásárlás a szokásos módon'
					text: 'Léna a <strong>7000 Ft-os</strong> pólót a kosárba tette, és kifizette. Egyszerűen úgy vásárolt, mint máskor, a vásárlás semmiben sem különbözött.'
					subtitle: a póló ára
					value: '7000 Ft'

				cashback:
					title: '3. A pénz végre a számlán van'
					text: 'A vásárlástól számított 15 percen belül Léna már meg is kapta a Tiplinóra a regisztrált jutalmat, azaz <strong>700 Ft</strong>-ot, ami 10% cashbacknek felel meg.'
					small: A cashback kiszámításának alapja az ÁFA és a szállítási költségek nélküli ár.
					subtitle: vissza a számlára
					value: '700 Ft'

		start:
			title: 'Hagyja, hogy a webáruházak fizessenek Önnek a vásárlásaiért!'
			sign: Kezdjen el jutalmakat szerezni

	faq:
		title: Gyakran ismételt kérdések - FAQ

articles:
	title: Vásárlási tippek
	text: 'Nálunk minden akciót, kedvezményt, kupont és újdonságot egy helyen talál. Ezenkívül útmutatókat is kínálunk, hogy a legtöbbet hozza ki vásárlásaiból.'
	otherArticles: További cikkek
	article:
		readMore: A teljes cikk elolvasása

shopConditionsProvider:
	generalConditions: Általános feltételek
	tax: 'A jutalmat általában az ÁFA nélküli tiszta árból számoljuk ki. Ez azt jelenti, hogy a rendelés teljes árából levonjuk a szállítási költségeket és az ÁFA-t.'
	coupons: 'Még akciós árakon történő vásárlás esetén is kap jutalmat. Figyeljen azonban a kedvezményes kuponokra. Ha más kuponkódot használ, mint amit a Tiplino kínál, előfordulhat, hogy nem kap jutalmat a vásárlás után.'
	couponsPartner: 'Még akciós árakon történő vásárlás esetén is kap jutalmat. Figyeljen azonban a kedvezményes kuponokra. Ha más kuponkódot használ, mint amit a Tiplino kínál, előfordulhat, hogy nem kap jutalmat a vásárlás után.'
	currency: Külföldi vásárlások esetén az aktuális forintárfolyam is szerepet játszik. Előfordulhatnak kisebb eltérések a jutalom összegében.
	cookies: 'A jutalom sikeres regisztrálásához mindig engedélyezni kell a cookie-kat, amikor az üzlet weboldala kéri.'
	refund: 'Nem íródott jóvá a jutalom? Töltse ki az <a href="%link%" class="underline xl:hover:no-underline">elégedettségi garancia űrlapot</a>, és azonnal megoldjuk a helyzetet.'
	button: Értem

social:
	facebookLogin: Bejelentkezés Facebookkal
	googleLogin: Bejelentkezés Google-fiókkal
	appleLogin: Bejelentkezés Apple-fiókkal

campaign300:
	bonus: %amount% Ft bónusz
	progressBar:
		title: 'Minden jutalmat a vásárlás után kétszer kap meg! (akár %amount%Ft összegig)'
		validTill: 'Eddig érvényes: %date%'
		finish:
			title: Gratulálunk! %campaignAllocatedBonus% Ft bónuszjutalmat szerzett.
			text: 'Elég megvárni minden vásárlásból származó jutalom megerősítését, amelyeknek köszönhetően a bónuszjutalmak is megerősítésre kerülnek.'

		complete:
			title: Gratulálunk! %campaignAllocatedBonus% Ft bónuszjutalmat szerzett
			text: 'Minden jutalmat megtekinthet a <a href="%link%">Jutalmak és kedvezmények</a>szekcióban'

		active: Aktív
		obtained: '<span class="text-secondary-green font-bold">Már megszerzett %obtained% Ft</span> a %total% Ft bónuszból'
		currency: Ft

	tellFriend:
		title: Ajánlja a barátjának
		text: 'Szerezzen akár  %amount% Ft-ot minden barátért, akinek ajánlja a Tiplinót. Összesen így akár %upTo% Ft-ot szerezhet.'

datalist:
	nextShops: Következő webáruház|Következő %count% webáruházak|Következő %count% webáruház
	nextItems: Következő tétel|Következő %count% tétel|Következő %count% tétel
	prev: Előző
	next: Következő

chatbot:
	csatQuestion: Elégedett volt a válaszunkkal?
	csatYesQuestion: 'Kérjük, értékelje, mennyire volt elégedett a válaszunkkal. Köszönjük!'
	csatNoQuestion: 'Sajnáljuk, hogy válaszunk nem segített. Kérjük, ossza meg velünk, hogy mivel volt elégedetlen. Véleménye fontos számunkra. Köszönjük.'
	csatThankyou: Köszönjük visszajelzését. Az Ön elégedettsége rendkívül fontos számunkra.
	csatRateHere: Küldje el értékelését itt.
	groupId: '19000158727'

links:
	homepage: https://www.tiplino.hu
	favorite: https://www.tiplino.hu/kedvenceim
	deals: https://www.tiplino.hu/kedvezmenyek
	refund: https://www.tiplino.hu/reklamacio
	shop: https://www.tiplino.hu/webaruhaz/%slug%
	luckyShops: https://www.tiplino.hu/tiplino-ajandekozas

vouchers:
	title: Az előnyeid áttekintése
	subTitle: Aktív és elérhető előnyeid
	expiredUnfinished: lejárt sikertelenül
	expiredFinished: lejárt sikeresen
	homepage:
		title: A Te extra előnyeid
		cta: Tekintse meg az összes extra előnyt »

	voucher:
		validTill: 'Érvényes eddig: %date%'
		conditions: Feltételek megtekintése
		activate: Előny aktiválása
		future: 'A következő előny feloldása:'

jobs:
	content:
		title: Karrier
		subtitle: Jelenleg elérhető állásajánlatok
		notFound: Jelenleg nem kínálunk állásajánlatokat.
		prague: Prága

	title: Dolgozz a Tipli.hu-nál
	description: 'Ha szereted a kihívásokat és fejlődni szeretnél, miközben azt csinálod, amit szeretsz, a Tipli a megfelelő hely számodra.'
	email: <EMAIL>
	jobs: Szabad pozíciók
	jobCta: Tudj meg többet
	gallery: 'Nézd meg, hogyan néz ki a munka Tiplinél'
	job:
		toForm: Jelentkezz az állásra
		expired: 'Elnézést kérünk, de az állásajánlat már nem elérhető.'
		form:
			title: Ez álmaid munkájának hangzik?
			name: Név
			email: Email
			phone: Telefon
			attachment: Motivációs levél
			cv: Önéletrajz kiválasztása
			noFile: Nincs kiválasztott fájl
			linkedIn: LinkedIn profil
			info: Mesélj magadról
			submit: Jelentkezés elküldése
			success: Az üzenetet sikeresen elküldtük. Köszönjük.
			gdpr: Hozzájárulás a személyes adatok feldolgozásához
			gdpr_text: 'Hozzájárulok, hogy a Tipli s.r.o. feldolgozza az általam beküldött személyes adatokat, annak érdekében, hogy nyilvántartásba vegyen potenciális alkalmazottként és hasonló álláslehetőségeket kínáljon.'
			validator:
				name: 'Kérjük, adja meg a nevét'
				surname: 'Kérjük, adja meg a vezetéknevét'
				email: 'Kérjük, adja meg az e-mail címét'
				phone: 'Kérjük, adja meg a telefonszámát'
				info: Mesélj magadról
				fileFormat: 'A feltöltött fájl formátuma nem megfelelő. A megengedett formátumok: doc, docx, gif, jpeg, jpg, png, pdf, txt.'
				cvOrLinkedIn: 'Kérjük, adjon meg egy önéletrajzot vagy egy LinkedIn linket.'
				captcha: 'Kérjük, erősítse meg, hogy nem robot.'

		responseEmailText:
			from: <EMAIL>
			subject: Köszönjük érdeklődését a Tipli s.r.o. iránt
			message: 'Kedves jelentkező,<br /><br />Köszönjük, hogy érdeklődik a nálunk való munkavégzés iránt, és hogy hozzájárul TIPLI további fejlődéséhez. A beküldött anyagok rendben megérkeztek, és felvettük a kapcsolatot a kiválasztási folyamatban.<br /><br />Sok sikert kívánunk, és várjuk a további együttműködés lehetőségét.'

miniBanners:
	title: Tippek akciókra
	showMore: További akciók megtekintése

calendar:
	months:
		1: Január
		2: Február
		3: Március
		4: Április
		5: Május
		6: Június
		7: Július
		8: Augusztus
		9: Szeptember
		10: Október
		11: November
		12: December

	days:
		- vasárnap
		- hétfő
		- kedd
		- szerda
		- csütörtök
		- péntek
		- szombat

timeAgo:
	years: %count% éve|%count% éve|%count% éve
	months: %count% hónapja|%count% hónapja|%count% hónapja
	days: tegnap|%count% napja|%count% napja
	hours: %count% órája|%count% órája|%count% órája
	minutes: %count% perce|%count% perce|%count% perce
	moments: egy pillanattal ezelőtt

luckyShops:
	title: Tiplino ajándékozás
	meta:
		title: Tiplino ajándékozás - Nyerjen 4 000 Ft-ot minden nap
		description: 'Minden nap kiválasztunk egy szerencsés webáruházat és azok, akik eltalálják, pénzdíjban részesülnek. Csatlakozzon még ma.'
		image: https://www.tipli.cz/images/OG_image_tipli_rozdava_cz.png

	amount: '4 000 Ft'
	everyDay: minden nap!
	text: 'Minden nap felfedjük a szerencsés webáruházat és azok, akik eltalálják és az adott napon felfedik azt, megosztoznak a pénzdíjban.'
	chooseShop: 'Elég csupán kiválasztani <span class="font-bold">szerencsés webáruház</span>'
	get: Szerezzen
	bottomLargeText: A Tiplino 4 000 Ft-ot ajándékoz minden nap. A Tiplino 4 000 Ft-ot ajándékoz minden nap.
	info:
		first: '100% ingyen'
		second: Elég egy e-mail cím és megerősítés SMS által
		third: A partnerektől kapott pénzt osztjuk meg felhasználóinkkal

	intro:
		title: Már csak választani kell
		title2: 'Szerencsés webáruház:'
		ctaUnloggedUser: Csatlakozás a játékhoz

	detail:
		userLuckyShops: Szerencsés webáruházai
		checkLuckyShops: Szerencsés webáruházak ellenőrzése
		checkLuckyShopsStreak: '<span class="text-primary-orange">1</span> nap egymás után|<span class="text-primary-orange">%count%</span> nap egymás után|<span class="text-primary-orange">%count%</span> nap egymás után'
		moreUserLuckyShops: Hogyan szerezhet nég?
		win:
			title: Gratulálunk
			text: A webáruháza szerencsés

	shopPicker:
		text: Akár 100 webáruház közül válogathat
		popular: Nagyon népszerű
		submit: Üzlet kiválasztásának megerősítése
		popup:
			title: Az összes webáruház listája a Tiplino ajándékozásban

		tooltip:
			title: 'Annak az esélye, hogy az adott webáruházat kiválasztják, mindig ugyanannyi.'
			text: 'Minél népszerűbb azonban egy webáruház, annál nagyobb a valószínűsége annak, hogy többen fognak jelentkezni a nyereményért.'

		errors:
			emptyShop: Válasszon webáruházat
			duplicityShop: Már választott webáruházat

	shopSearch:
		input: Webáruház keresése

	screens:
		all:
			luckyShopIs: A szerencsés webáruház
			userShops: 'Az Ön által kiválasztott üzletek:'

		lose:
			title: Ma nem járt sikerrel
			tomorrow: 'Próbálja meg<span class="text-primary-orange">holnap újra!</span>'
			streakExtended: 'Tevékenységét meghosszabbította:'
			streakExtendedCount: '1 nap sorban|%count% napok sorban|%count%</span> napok sorban'
			streakInfo: '7 nap sorban 1 következő ablakot ad hozzá'
			streakActive: A szerencseablak aktív

		win:
			congratulation: Gratulálunk

		reveal:
			text1: 'Tudja meg, hogy nyert-e ma'
			text2: Fedje fel a szerencsés webáruházat
			cta: Szerencsés webáruház felfedése

		next:
			title: 'A következő webáruházat kiválasztjuk:'
			tomorrow: Hamarosan az Ön által kiválasztott webáruház is nyerő lehet!
			lastLuckyShop: 'A legutóbb kiválasztott szerencsés webáruház a:'
			check: 'Adja hozzá ide <span class="text-primary-orange">ellenőrzés</span> 😎'
			hours: óra
			minutes: perc
			seconds: másodperc

	dealsSection:
		tip: TIPP
		title: 'Lehetősége van <span class="font-bold">növelni nyerési esélyeit</span> új ablak megszerzésével az által, hogy a Tiplinon keresztül vásárol.'
		text: Minden szerencsés webáruházzal növeli a nyerési esélyeit. Azonban minden áruházhoz szüksége van egy ablakra. Válasszon az éppen népszerű kuponok közül és minden vásárlása után kap egy ablakot.
		allCouponsCta: Az összes kupon megjelenítése

	phoneNumberVerification:
		step1:
			title: Elég megerősíteni
			text: Biztonsági okokból minden Tiplino ajándékozás játék résztvevőjének meg kell erősítenie a telefonszámát.
			submit: Megerősítő SMS küldése

		step2:
			code: SMS kód
			title: A megadott telefonszámra <strong>%phoneNumber%</strong> elküldtük a megerősítéshez szükséges kódot.
			wrongPhoneNumber: Rossz telefonszámot adott meg?
			changePhoneNumber: Telefonszám megváltoztatása
			submit: SMS kód megerősítése

	default:
		currentLuckyShop: Az utolsó szerencsés webáruház a következő volt
		checkLuckyShop:
			isNotLucky: '<span class="text-secondary-red">Sajnáljuk! 🥲</span> Ma nem járt sikerrel, a szerencsés webáruház a:'
			winners: Összes nyertes
			winnersWithRewardRequest: Átvett nyeremények
			totalRewardText: 'Ha senki más nem igényli a nyereményt, akkor ezt az összeget Ön kapja meg. A végleges nyereményösszeget éjfél után e-mailben erősítjük meg.'
			nextLuckyShop: A következő szerencsés webáruházat......múlva választjuk ki
			currentWinAmountPerUser: A nyeremény előzetes állapota

		luckyWheelPromo:
			title: Tiplino szerencsekerék 🎡
			text: Közben megpörgetheti a Tiplino szerencsekerekét
			cta: Átlépés a szerencsekerékhez

		delistedShop:
			title: Kihagyott webáruházak
			changeShop: A szerencsés webáruház megváltoztatása
			message:
				title: Ez a webáruház már nem vehet részt a játékban
				text: 'Ezért ezt az üzletet már nem lehet szerencsésnek választani. Kérjük, változtassa meg a választását, hogy újra nyerhessen.'

		editLuckyShop:
			title: Szerencsés webáruház módosítása
			validTill: Az ablak érvényessége
			changeShop: A szerencsés webáruház megváltoztatása
			forever: Örökre
			popularity: Népszerűség
			popularityValues:
				unpopular: Magasabb nyeremény
				popular: Közepes nyeremény
				very_popular: Alacsonyabb nyeremény

			chance:
				title: Minden webáruháznak egyenlő esélye van a nyerésre.
				text: 'A népszerűbb webáruházakat több felhasználó választja, ami azt eredményezi, hogy mindenki kisebb részesedést kap a nyereményből.'

		userLuckyShopDetail:
			title: A szerencsés webáruház módosítása
			promoTitle: 'Tudja, hogy...'
			text: %shop% ajánlata a Tiplinon keresztül
			coupons: kedvezményes kuponok
			types:
				default: Ezt a szerencseablakot a Tiplino ajándékozás nevezetű játékban való részvétellel szerezte
				streak: 'Ezt a szerencseablakot azzal szerezte, hogy 7 egymást követő napon'
				addon: 'Ezt a szerencseablakot bónuszként kapta, mert a Tiplino bövítményt hozzáadta a böngészőhöz'
				user_recommendation: 'Ezt a szerencseablakot azért kapta, mert ajánlotta a Tiplinot'
				transaction: 'Ezt a szerencseablakot azért kapta, mert vásárolt a Tiplinon keresztül'
				missed_reward: Ezt a szerencseablakot vigaszdíjként kapta az elszalasztott győzelemért
				guarantee: Ezt a szerencseablakot a Tiplino ügyfélszolgálatától kapta.

		tabs:
			howItWorks:
				title: Hogyan működik?
				steps:
					step1:
						title: '1. Válassza ki a szerencsés webáruházát'
						text: 'A 100 webáruházból álló listából válassza ki az ablakába amelyikben a legjobban hisz. Ha több ablakkal rendelkezik, több különböző webáruházat választhat.'

					step2:
						title: '2. Jöjjön és fedjen fel szerencsés webáruházat minden nap'
						text: Minden nap véletlenszerűen kiválasztunk egy szerencsés webáruházat. Jöjjön és fedje fel minden nap 12:00 és 00:00 között.

					step3:
						title: '3. Szerezzen nyereményt'
						text: 'Ha a szerencsés webáruház egyezik az ablakjában lévővel, és aznap ténylegesen felfedi azt, akkor osztozik a pénznyereményen.'

					tip:
						title: TIPP
						text: 'Minden nap részt vehet – minél gyakrabban fed fel szerencsés webáruházat, annál nagyobb az esélye a nyerésre. További ablakokat is szerezhet a Tiplinon végzett tevékenységeért, ami növeli a siker esélyét.'

			history:
				title: Előzmény
				totalWin: Nyereményei
				userLuckyShops: Ezeket választotta

			conditions:
				title: Feltételek
				condition1:
					title: Feltételek 1
					text: Feltételek 1

				condition2:
					title: Feltételek 2
					text: Feltételek 2

		moreSlotsPopup:
			title: Hogyan lehet szerencsésebb?
			active: Teljesítve – aktív ablak
			text: 'Minél több szerencsés üzletet választ, annál nagyobb az esélye a nyerésre. Azonban minden üzlethez szüksége van egy ablakra. <strong>További üzletválasztó ablakokat is feloldhat:</strong>'
			steps:
				step1:
					title: Egy barát meghívásával a Tiplinora
					text: 'Szerezzen 60 napig érvényes ablakot. Továbbá, ha a meghívott barát vásárol a Tiplinon, Ön 1&nbsp;800 Ft jutalmat kap.'
					link: '<a href="https://www.tiplino.hu/ajanlas-baratoknak">Meg akarok hívni egy barátot</a>'

				step2:
					title: 'A Tiplinon keresztül történő vásárlással '
					text: 'Szerezzen 30 napig érvényes ablakot. Minden olyan napért érvényes, amikor vásárolt.'
					link: '<a href="https://www.tiplino.hu">Vásárolni akarok</a>'

				step3:
					title: Hozzáadom a Tiplinot a böngészőhöz
					text: 'Amíg a bővítmény aktív, kap egy ablakot. Adja hozzá a laptopjához vagy a számítógépéhez.'
					link: '<a href="https://www.tiplino.hu/tipli-a-bongeszobe">Tiplino hozzáadása a böngészőhöz</a>'

				step4:
					title: Fedjen fel szerencsés webáruházat 7 egymást követő napon
					text: 'Úgy szerezhet ablakot, hogy egymást követő 7 napon felfed egy szerencsés webáruházat. Ahhoz, hogy megtartsa, minden nap fedjen fel szerencsés webáruházat. Ha egy napot kihagy, elveszíti ablakát, azonban később újra megszerezheti.'
					link: ''

		notifications:
			title: Emlékeztető beállítása
			text: 'A Tiplino emlékeztetni fogja, ha felfedheti a szerencsés webáruházat és átveheti a megérdemelt nyereményt. Módosítsa a beállításokat tetszése szerint.'
			email: E-mailes emlékeztetés
			emailText: A webáruház felfedésére e-mailben emlékeztetjük.
			push: Értesítések a mobilalkalmazásban
			pushText: A webáruház felfedéséről értesítést küldünk a Tiplino mobilalkalmazásban. A megfelelő működéshez engedélyezni kell az értesítéseket a telefon beállításaiban.

