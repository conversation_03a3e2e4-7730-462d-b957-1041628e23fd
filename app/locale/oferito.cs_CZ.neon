navbar:
	shops: "Obchody"
	leaflets: "Let<PERSON>y"
	search:
		placeholder: "<PERSON><PERSON><PERSON> obchody"
		submit: "Hledat"

	moreShops: "Další obchody"
	home: "<PERSON><PERSON>"

footer:
	copyright: "Oferito Všechna práva vyhrazena."	
	shops: "Obchody"
	category: "Kategorie"	
	aboutLetado: "O Oferito"
	cookies: "Cookies"
	leaflets: "Letáky"
	aboutUs: "O nás"
	nextCountries: "Dalš<PERSON> země"

search:
	title: "Výsledky vyhledávání \"%query%\""
	noResults: "A<PERSON> hledáme jak hledáme, nic jsme nena<PERSON>."

homepage:
	title: "Nejnovější letáky a zboží v akci"
	text: "Nejnovější letáky, které nabízí širokou nabídku zboží v akcí od těch největších prodejců."
	allLeaflets: "Všechny letáky"
	shops: "Obchody"
	allShops: "Všechny obchody"	

leaflets:
	title: "Let<PERSON>y"
	text: "Na<PERSON><PERSON><PERSON><PERSON> nejnovějš<PERSON>ch letáků. Letáky pro vás přidáváme ka<PERSON>d<PERSON> den, aby jste akční produkty vždy našli."

leaflet:
	metaTitle: 'Nejnovější leták %brand% platný od %validSince%'
	metaTitlePageSuffix: 'strana %page%'
	metaDesc: 'Nejnovější leták z obchodu %brand% platný od&nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Nejnovější leták z obchodu %leafletBrandLink% platný od %validSince% do %validTill%. Na %leafletPageCount% stránkách najdete všechny aktuální slevy. Na Oferito najdete vždy aktuální informace o všech letácích z nabídek oblíbených obchodů."
	longDesc1: "Využijte akční nabídky z obchodu %leafletBrandLink%, jež najdete v aktuálním akčním letáku v odbobí od %validSince% do %validTill%. V dnešní době zdražuje snad vše - osobní automobily, letenky, dovolená, zájezdy, elektronika, bílé spotřebiče, ale i oblečení a mnoho dalšího. Není však třeba si brát na běžnou měsíční útratu spotřebitelský úvěr nebo jinou půjčku. V Oferito děláme vše pro to, abychom vám přinášeli co nejdříve akční letáky ze všech nejoblíbenějších obchodů. Vy tak budete moci využít aktuální akční nabídky nebo slevy a ušetřit peníze svému domácímu rozpočtu."
	longDesc2: "Díky nám si tak nemusíte najímat finanční poradce, aby ulevili vašim financím, díky nám to totiž zvládnete sami. Zbylé peníze pak můžete využít například na dovolenou do zahraničí, výlety do tuzemských hotelů a penzionů nebo jako finanční rezervu pro další splátku hypotečního úvěru."
	longDesc3: "Je skvělý pocit být finančně nezávislý a mít přebytkové finance. Umožňuje totiž dovolit si i kvalitní pojištění, ať už se jedná o životní pojištění, pojištění domácnosti nebo povinné ručení a havarijní připojištění. Vaše finance jsou díky tomu chráněny před případnými nečekanými vlivy, jež by na ně mohly mít výrazný negativní dopad. Pojištění tudíž chrání stabilitu vašich financí."
	longDesc4: "My v Oferito budeme nadále dělat vše pro to, abychom vám pomohli ušetřit co nejvíc peněz ve vašich běžných nákupech a vy si tak mohli dovolit koupit vysněné auto, oblíbené oblečení, elektroniku nebo platit kvalitní pojištění. Přejeme, ať vám v tomto %leafletBrandLink% leták s platností od %validSince% do %validTill% alespoň trochu pomůže a vy budete blíže svým snům!"
	smallTitle: "%brand% platný od"	
	recommendedLeaflets: "Oblíbené letáky"
	similarLeaflets: "Další letáky %brand%"
	backToLeaflets: "Zpět na výpis všech letáků"	
	allBrandLeaflets: "Všechny letáky %brand%"
	goToShop: "Jdi do obchodu"

shops:
	title: "Obchody"
	text: "Nabídka těch nejoblíbenějších prodejců, pro které vám každý den přinášíme nové letáky."

shop:
	leaflets: "letáky"
	text: "Nejnovější leták %brand% s výhodnými nabídkami."
	button: "Přejít do obchodu %brand%"	
	noLeaflets: "Nejnovější leták pro vás hledáme... Zkuste to prosím později."
	otherShops: "Další obchody"
	defaultTitleSuffic: '%shopName% - nejnovější leták, zboží v akci'
	otherLeaflets: "Další letáky %brand%"
	type:
		shopTitle: "{$shopName|upper} leták{if $currentLeafletFromDate} od {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + akční leták příští týden"
		eshopTitle: "%brand% sleva"
		eshop: "Podívejte se na nejnovější akce %brand% v jejich katalogu plném inspirace a výhodných nabídek. Aktuální slevy %brand% máte vždy k dispozici, zlevněné zboží tak už nikdy neminete."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} leták příští týden od {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + aktuální leták"
	     withCurrentLeaflet: "{$shopName|upper} leták příští týden + aktuální leták od {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} leták příští týden + aktuální akční leták online"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} leták příští týden ✅ Prolistujte si akční {$shopName|upper} LETÁK na příští týden od {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Online dostupný je i aktuální PDF leták {$shopName|upper} na tento týden."
	    withCurrentLeaflet: "{$shopName|upper} leták příští týden ✅ Prolistujte si akční {$shopName|upper} LETÁK na příští týden. Online dostupný je i aktuální PDF leták {$shopName|upper} na tento týden od {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} leták příští týden ✅ Prolistujte si akční {$shopName|upper} LETÁK na příští týden. Online dostupný je i aktuální PDF leták {$shopName|upper} s akcemi na tento týden."

tag:
	text: "Nabídka nejnovějších letáku z kategorie %brand%."
	noLeaflets: "Nejnovější leták pro vás hledáme... Zkuste to prosím později."
	otherShops: "Další obchody"	

about:
	title: "O nás"
	text: "Naším cílem je uživatelum šetřit čas a peníze. Každý den přinášíme aktuální letáky od těch nejoblíbenějších prodejců a šetříme čas v hledání akčních nabídek produktů."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
