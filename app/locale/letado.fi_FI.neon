navbar:
	shops: "Kaupat"
	leaflets: "Esitteet"
	search:
		placeholder: "Etsi kauppoja"
		submit: "Etsi"

	moreShops: "Muut kaupat"
	home: "Ko<PERSON>"

footer:
	copyright: "Letado Kaikki oikeudet pidätetään."	
	shops: "Kaupat"
	category: "Kategoriat"	
	aboutLetado: "Tietoa Letado"
	cookies: "Cookies"
	leaflets: "Esitteet"
	aboutUs: "Tietoa meistä"
	nextCountries: "Muut maat"

search:
	title: "Haku<PERSON>lokset \"%query%\""
	noResults: "Vaikka etsimme kuinka kovasti, emme löydä mitään."

homepage:
	title: "Uusimmat esitteet ja myynnissä olevat tavarat"
	text: "Uusimmat esitteet, joissa on tarjolla laaja valikoima suurten vähittäiskauppiaiden tarjoamia tuotteita"
	allLeaflets: "Kaikki esitteet"
	shops: "Kaupat"
	allShops: "Kaik<PERSON> kaupat"	

leaflets:
	title: "Esitteet"
	text: "Uusimpien esitteiden tarjonta. Lisäämme esitteitä sinulle joka päivä, jotta löydät aina erikoistuotteita."

leaflet:
	metaTitle: 'Viimeisin %brand% esite voimassa alkaen %validSince%'
	metaTitlePageSuffix: 'sivu %page%'
	metaDesc: 'Viimeisin esite %brand%:lta voimassa alkaen &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Viimeisin esite %leafletBrandLink%:ltä voimassa %validSince% - %validTill%. Sivulta %leafletPageCount% löydät kaikki voimassa olevat alennukset. Letado-sivulta löydät aina ajantasaiset tiedot kaikista suosikkikauppojesi tarjoamista esitteistä."
	longDesc1: "Hyödynnä %leafletBrandLink%:n erikoistarjoukset, jotka löydät nykyisestä tarjouslehtisestä %validSince% - %validTill%. Kaikki kallistuu nykyään yhä enemmän - autot, lennot, lomat, matkat, elektroniikka, kodinkoneet, vaatteet ja paljon muuta. Sinun ei kuitenkaan tarvitse ottaa kulutus- tai muuta lainaa säännöllisiä kuukausittaisia menojasi varten. Me Letadassa pyrimme tuomaan sinulle alennuksia kaikista suosituimmista kaupoista mahdollisimman pian. Voit siis hyödyntää uusimmat kampanjat tai alennukset ja säästää rahaa kotitaloutesi budjetista."
	longDesc2: "Meidän avullamme sinun ei tarvitse palkata talousneuvojaa auttamaan sinua raha-asioissa, sillä me voimme tehdä sen puolestasi. Voit sitten käyttää jäljelle jäävän rahan esimerkiksi ulkomaanlomiin, matkoihin paikallisiin hotelleihin ja majataloihin tai taloudelliseksi puskuriksi seuraavaa asuntolainan maksua varten."
	longDesc3: "On hieno tunne olla taloudellisesti riippumaton ja saada ylijäämävaroja. Se tarkoittaa myös sitä, että sinulla on varaa laadukkaisiin vakuutuksiin, olipa kyse sitten henki- tai kotivakuutuksesta tai pakollisesta vakuutuksesta ja vikaantumisturvasta. Tämä suojaa talouttasi odottamattomilta vaikutuksilta, jotka voivat vaikuttaa siihen merkittävästi kielteisesti. Vakuutus suojaa siis taloutesi vakautta."		
	longDesc4: "Me Letado teemme jatkossakin kaikkemme auttaaksemme sinua säästämään mahdollisimman paljon rahaa jokapäiväisissä ostoksissasi, jotta sinulla on varaa ostaa unelmiesi auto, suosikkivaatteet, elektroniikka tai maksaa laadukkaita vakuutuksia. Toivomme, että tämä %leafletBrandLink% -esite, joka on voimassa %validSince% - %validTill% -aikavälillä, auttaa sinua edes hieman ja olet lähempänä unelmiasi!"
	smallTitle: "%brand% voimassa alkaen"	
	recommendedLeaflets: "Suosittuja esitteitä"
	similarLeaflets: " Muut esitteet %brand%"
	backToLeaflets: "Takaisin kaikkien esitteiden luetteloon"	
	allBrandLeaflets: "Kaikki esitteet %brand%"
	goToShop: "Mene kauppaan"

shops:
	title: "Kaupat"
	text: "Valikoima suosituimmista jälleenmyyjistä, joiden uusia esitteitä tuomme sinulle joka päivä."

shop:
	leaflets: "esitteet"
	text: "Uusin %brand% esite, jossa on hyviä tarjouksia."
	button: "Mene kauppaan %brand%"	
	noLeaflets: "Etsimme sinulle viimeisintä lentolehtistä... Yritä myöhemmin uudelleen."
	otherShops: "Muut kaupat."
	defaultTitleSuffic: '%shopName% - uusin esite, myynnissä olevat tavarat'
	otherLeaflets: "Muut esitteet %brand%"
	type:
		shopTitle: "{$shopName|upper} leaflet {if $currentLeafletFromDate} from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + ensi viikon toimintaesite"
		eshopTitle: "%brand% alennus"
		eshop: "Tutustu viimeisimpiin %brand% kampanjoihin heidän inspiraatiota ja edullisia tarjouksia sisältävässä luettelossaan. Uusimmat %brand% alennukset ovat aina saatavilla, joten et jää koskaan paitsi alennetuista tuotteista.."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} lehtinen ensi viikolla alkaen {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + nykyinen esite"
	     withCurrentLeaflet: "{$shopName|upper} esite ensi viikolla + nykyinen esite alkaen {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} esite ensi viikolla + nykyinen mainoslehtinen verkossa"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} flyer ensi viikolla ✅ Selaa erityistä {$shopName|upper} SEURAAVAN VIIKON LÄHETYSLEHTINEN {$seuraavanviikonLehtinen|päiväGenetiivi} {$nextLeafletFromDate|date:'j.n.Y'}. Verkossa on saatavilla myös tämän viikon {$shopName|upper} esitteen tämänhetkinen PDF-tiedosto."
	    withCurrentLeaflet: "{$shopName|upper} flyer ensi viikolla ✅ Selaa erityistä {$shopName|upper} FLYER ensi viikolla. Verkossa on saatavilla myös tämän viikon ajankohtainen PDF-flyer {$shopName|upper} osoitteesta {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} flyer ensi viikolla ✅ Selaa erityistä {$shopName|upper} FLYER ensi viikolla. Verkossa on saatavilla myös tämän viikon kampanjat sisältävä nykyinen PDF-flyer {$shopName|upper}."

tag:
	text: "Tarjous luokan uusimmista esitteistä %brand%."
	noLeaflets: "Etsimme sinulle viimeisintä lentolehtistä... Yritä myöhemmin uudelleen."
	otherShops: "Muut kaupat"	

about:
	title: "Tietoa meistä"
	text: "Tavoitteenamme on säästää käyttäjien aikaa ja rahaa. Tuomme sinulle joka päivä suosituimpien jälleenmyyjien ajantasaiset esitteet ja säästämme aikaa etsimällä erikoistarjouksia tuotteista."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
