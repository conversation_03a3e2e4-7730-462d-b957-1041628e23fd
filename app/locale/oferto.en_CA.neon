navbar:
	shops: Stores
	leaflets: Flyers
	articles: Blog
	search:
		placeholder: Search stores
		submit: Search

	moreShops: Other stores
	home: Home

footer:
	copyright: MrOferto All rights reserved.
	text: 'MrOferto is a website that enables you to find upcoming, current and, historical promotional flyers from all your favourite stores. We offer flyers from dozens of the most popular brands and are constantly adding more! Our team ensures that you have the opportunity to view all upcoming flyers as soon as they become available, giving you ample time to take advantage of the incredible promotions. Additionally, we provide various tips, tricks, and hints in our magazine that fuel creativity and provide inspiration for your everyday shopping and cooking endeavours. We hope that our website is useful in helping you to accomplish your shopping needs.'
	shops: Stores
	category: Category
	article: Blog
	aboutOferto: MrOfferto
	cookies: Cookies
	leaflets: Flyers
	articles: Blog
	aboutUs: About us
	nextCountries: Other countries

search:
	title: search results "%query%"
	noResults: 'No matter how hard we look, we can''t find anything.'

homepage:
	title: Latest promotional flyers
	h1: Latest promotional flyers
	text: Choose a flyers from one of your favourite stores. This will allow you to find the many flyers we offer from all of the most popular hypermarkets and supermarkets.
	allLeaflets: Other promotional flyers
	shops:
		title: Stores with promotional flyers
		text: 'In this offer, you can find many different individual shops that issue special promotional flyers. Start by clicking the store logo and you can view a selection of all future, current and past flyers. This will allow you to look through any of them in further detail.'

	article:
		title: Blog
		text: Get inspired by our tips for a better dining and shopping experience! Keep looking and find out more about the products we offer in our magazine.
		button: Other articles

	allShops: Other stores with promotional flyers

articles:
	title: Blog

article:
	button: View all articles

leaflets:
	title: Current promotional flyers
	text: Offer of current discount flyers. We add current flyers for you every day.
	expiredTitle: Expired promotional flyers
	expiredText: Check out the range of expired promotional flyers from the most popular retailers!
	expiredMetaTitle: Expired flyers %brand%

leaflet:
	metaTitle: %brand% flyer valid from %validSince%
	metaTitlePageSuffix: page %page%
	metaDesc: 'Check out the latest %brand% flyer and score amazing deals, eh?'
	leaflet: %brand% flyer
	desc:
		1: 'The %leafletBrandLink% shop flyer, which is current between %validSince% and %validTill%, offers all the special products on the %leafletPageCount% pages. Browse through the whole flyer to find out about all the current bargains %brand% has to offer.'
		2: 'If you have already seen the %leafletBrandLink% flyer from %validSinceDay% to %validTill% and are interested in viewing a different period, then you can either return to the listing of all %brand% flyers by clicking on the store logo above the flyer. Doing so will provide the option of choosing from all current and future offers. If you''re interested in browsing flyers from other stores, then the easiest way is to click on the MrOferto logo at the top of the page. This will take you to the homepage of the site which shows the most popular brands in your area, and what they have to offer.'
		3: 'On MrOferto, you will also find tips and tricks that help make your shopping experience easier! This includes tips on helping you prepare your favourite dishes or giving you new ideas and inspiration for what those daily meals might be. All this information can be found in our magazine.'

	smallTitle: %brand% flyer valid from
	recommendedLeaflets: Similar promotional flyers
	similarLeaflets: Other %brand% flyers
	backToLeaflets: Back to the list of all promotional flyers
	allBrandLeaflets: Other %brand% flyers
	button: View more flyer pages
	otherArticles: Latest Articles
	expiredLeafletTitle: %brand% leaflet valid until %validTill%
	expiredLeafletDescription: This %brand% leaflet is no longer current. Its validity expired on %validTill%.
	actualLeafletValidSince: 'The current leaflet is valid from %validSinceDay%, %validSince%. ✅'
	expiredLeafletHeading: This leaflet is no longer current
	expiredLeafletLinkToShop: You can find the current %brand% leaflet here
	futureLeafletTitle: Latest %brand% leaflet valid from %validSince% ⭐
	futureLeafletDescription: Check out upcoming promotions in the latest %brand% leaflet ⭐ Plan your next purchase and shop as advantageously as possible! ✅
	leafletPageDescription: 'Page %pageNumber% of the %brand% flyer, valid from %validSinceDay% %validSince%'

shops:
	title: Shops with promotional flyers
	metaTitle: Shops with promotional flyers
	text: List of all popular stores for which we bring you new flyers every day.
	metaDescription: List of all popular stores for which we bring you new flyers every day.

shop:
	showLeaflet: Browse Flyer
	leaflets: flyers
	text: 'Explore the newest %brand% flyer valid from %validSince%, discover amazing deals, and maximize your savings today!'
	button: Go to store %brand%
	noLeaflets: We're looking for a promotional flyer for you... Please try again later.
	bottomText: '%brand% is one of the most popular stores in the country and regularly publishes new promotional flyers containing dozens of products from a wide range of categories. This is the perfect place to get inspiration for your upcoming purchases! On this page, you will find a total of %actualLeaflets%. You can view each %brand% stores flyer on both your computer and/or mobile device.'
	offersLeaflets: Flyers from the category %category%
	otherShops:
		title: Other shops
		text: 'Have you already seen all the flyers and incredible promotions from this %brand% store? Make sure to stay informed by clicking the logo of a store and checking out what great products and promotions they have to offer! '

	defaultTitleSuffic: %shopName% flyer
	otherArticles: Latest Articles
	type:
		shopTitle: 'Latest {$shopName|upper} flyer{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''n.j.Y''}{/if}'
		eshopTitle: %brand% discount
		eshop: 'Check out the latest events %brand% in their catalogue full of inspiration and bargains. The latest %brand% discounts are always available to you, so you''ll never miss out on discounted items thanks to the promotional flyer.'

tag:
	text: Offer of promotional flyers from the %tag% category.
	noLeaflets: We're looking for a promotional flyer for you... Please try again later.
	otherShops: Other shops
	offers:
		title: %tag% deals in current catalogues
		metaDescription: Check out the deals on %tag% in the flyers of the most well-known stores. Don't miss out on discounts and promotional prices that can be found in the new flyers.
		titleWithBestOffer: %tag% on sale from ⏩ %price% in the current flyer.
		metaDescriptionWithBestOffer: Check out the promotions for product %tag% in the flyers of the most well-known stores. Don't miss out on discounts on products at promotional prices in the new flyers.
		text: 'Explore all the promotions for %tag% in the <a href="%leafletLink%">leaflets</a> and don''t miss out on additional discounts on selected products. The promotional prices for %tag% from the current leaflets are regularly updated, and finding the <strong>cheapest</strong> price is very easy.'

about:
	title: About us
	text: Our goal is to save users time and money. Every day we bring you up-to-date flyers from the most popular retailers and save you time searching for product deals.
	address: 'Adsalva s.r.o.<br>Prague, Czech Republic<br>Na Porici 1067/25, New Town Prague 1<br><br>Tax number CZ03786986'

city:
	city:
		title: Flyers in %city%
		text: 'Current promotional flyers in %city%. In the promotional flyers in the city of %city%, you will find not only discounted products but also many other discounts. Discounts can be found in stores such as %stores% and more.'
		text2: 'Promotional flyers and current discounts in the city of %city%. In the flyers of popular stores in the city of %city%, you will find not only discounted products but also many other discounts and offers at the best prices. Browse flyers from the largest stores in the city of %city%, such as %stores%.'
		h2: 'Flyers, promotions, and discounts of stores in %city%'
		storesTitle: Branches in %city%
		storesMoreButton: More branches »
		leafletStores:
			title: %brand% flyer
			store: %brand% flyer %city%
			storeWithCity: %brand% flyer %city%

		otherShops: Stores in %city%
		nearestCity: Other cities with flyers nearby
		nearestCityWithShop: Other cities with %shopName% flyers nearby
		categoriesInText: %category% flyers
		citiesInText: Flyers in %city%
		generatedText:
			1: 'The city of %city%, with a population of %population%, offers numerous stores, to which we bring new flyers every week. Interesting promotions and discounts are prepared not only for the city of %city% but also for others nearby, such as the cities of %cities%.'
			2: 'The list of current flyers from the largest stores in the city of %city% can be found here:'
			3: Promotional flyers from stores such as %stores% and many others are also available. Their promotional offers for the month of %month% can be found in the %shopsLink% section.
			leaflet: Flyer from %brand% current %validSince% – %validTill%
			and: and
			or: or

	store:
		store: %fullAddress%
		h2: Stores of %brand% in %city%
		title: '%brand% %address%, flyer, and opening hours 🕔'
		description: 'Compare offers in flyers, find the exact address and opening hours, or read about the assortment that awaits you at the %brand% store %address%.'
		open: Open
		closed: Closed
		text: 'The %brand% store %address% regularly offers advantageous promotions and discounts on a diverse range of products, where you can take advantage of the popular %brand% flyer when making purchases.'
		h2bottom: %brand% flyer %city% %street%
		text2WithoutStores: 'Customers can conveniently view it online, as well as promotions available at other branches.'
		text2: 'Customers can conveniently view it online, as well as promotions available at branches %stores%.'
		or: or
		others: and others
		textBottom: 'The %brand% store %address% offers customers not only a wide range of products but also low prices regularly communicated in the %brand% flyer. The %fullAddress% branch is a favorite place for those looking for competitively priced offers. Thanks to the online availability of the %brand% flyer, buyers always have current discounts at hand. If %brand% %address% does not offer everything the customer needs, they can also use other nearby stores, such as:'
		textBottom2: 'Find out the exact address, contact the customer service hotline, or opening hours of favorite stores all in one place. It also includes information on which branches are in your area and where additional advantageous offers can be found, as indicated by the promotional flyers of selected stores.'
		sections:
			leaflets: Other flyers in the category
			shops: Other stores nearby
			stores: Other %brand% stores nearby

	shop:
		title: %brand% flyers in %city%
		metaTitle: %brand% flyers in %city%
		storesTitle: %brand% branches in %city%
		h2: 'Flyers, promotions, and discounts at %brand% store in %city%'
		text: 'Promotional flyers from %brand% in %city% and their current discounts and promotions. In the %brand% flyers %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%.'
		metaDescription: 'Promotional flyers from %brand% in %city% and their current discounts and promotions. In the %brand% flyer %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%.'
		leafletStores:
			title: %brand% flyer %city%
			store: %brand% %city%

		cityLink: Flyers in %city%
		shopLink: Flyers at %shop%
		otherShops: Other stores in %city%
		shopLeaflet: %brand% flyers
		citiesInText: %brand% flyers in %city%
		offers: Offers from %brand% flyers in %city%
		generatedText:
			1: 'The store %brand% in the city of %city% offers promotional flyers, which we regularly update for you every week.'
			2: 'If you are looking for a new %brand% flyer, you can check it by clicking on this link: <a href="%actualLeafletUrl%">Current %brand% Flyer in %city%</a>. The validity of the flyer is from %validSince% to %validTill%. Each flyer contains interesting promotional offers and discounts, seasonal promotions, or club prices and a wide range of products.'
			3: 'However, the store %brand% is not only located in the city of %city%. Flyers from the store %brand% can also be found in other nearby stores %stores%. All promotional flyers are available in the <a href="%leafletsUrl%">Flyers</a> section.'
			4: 'If you are looking for other stores, popular ones include %stores%.'

