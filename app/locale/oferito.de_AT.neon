navbar:
	shops: "Geschäfte"
	leaflets: "Prospekte"
	search:
		placeholder: "Suche nach Geschäften"
		submit: "Suche"

	moreShops: "Andere Geschäfte"
	home: "Startseite"

footer:
	copyright: "Letado Alle Rechte vorbehalten."	
	shops: "Shops"
	category: "Kategorien"	
	aboutLetado: "Über Letado"
	cookies: "Cookies"
	leaflets: "Prospekte"
	aboutUs: "Über uns"
	nextCountries: "Andere Länder"

search:
	title: "Suchergebnisse \"%query%\""
	noResults: "Egal wie sehr wir suchen, wir können nichts finden."

homepage:
	title: "Aktuelle Prospekte und Verkaufswaren"
	text: "Aktuelle Prospekte mit einer breiten Palette von Warenangeboten großer Einzelhändler"
	allLeaflets: "Alle Broschüren"
	shops: "Geschäfte"
	allShops: "Alle Geschäfte"	

leaflets:
	title: "Prospekte"
	text: "Angebot der neuesten Prospekte. Wir fügen jeden Tag neue Prospekte für Sie hinzu, so dass Sie immer besondere Produkte finden können."

leaflet:
	metaTitle: 'Aktuelle %brand% Broschüre gültig ab %validSince%'
	metaTitlePageSuffix: 'seite %page%'
	metaDesc: 'Aktuelles Prospekt von %brand% gültig ab&nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Aktuelles Flugblatt von %leafletBrandLink% gültig von %validSince% bis %validTill%. Auf der Seite %leafletPageCount% finden Sie alle aktuellen Rabatte. Auf der Seite Letado finden Sie immer aktuelle Informationen über alle angebotenen Prospekte Ihrer Lieblingsgeschäfte."
	longDesc1: "Profitieren Sie von den Sonderangeboten von %leafletBrandLink%, die Sie im aktuellen Werbeprospekt von %validSince% bis %validTill% finden. Heutzutage wird alles immer teurer - Autos, Flüge, Ferien, Reisen, Elektronik, Haushaltsgeräte, aber auch Kleidung und vieles mehr. Für Ihre regelmäßigen monatlichen Ausgaben müssen Sie jedoch keinen Konsum- oder sonstigen Kredit aufnehmen. Bei Letada bemühen wir uns, Ihnen so schnell wie möglich Rabatte in den beliebtesten Geschäften anzubieten. So können Sie von den neuesten Sonderangeboten oder Rabatten profitieren und Ihr Haushaltsbudget schonen."
	longDesc2: "Bei uns brauchen Sie keinen Finanzberater, der Ihnen bei Ihren Finanzen hilft, denn das können wir für Sie tun. Das Geld, das übrig bleibt, können Sie dann für Dinge wie Auslandsurlaube, Reisen in örtliche Hotels und Pensionen oder als finanzielles Polster für Ihre nächste Hypothekenzahlung verwenden."
	longDesc3: "Es ist ein großartiges Gefühl, finanziell unabhängig zu sein und einen Überschuss an Geldmitteln zu haben. Das bedeutet auch, dass Sie sich eine gute Versicherung leisten können, sei es eine Lebensversicherung, eine Hausratversicherung oder eine Pflichtversicherung und einen Pannenschutz. So sind Ihre Finanzen vor unerwarteten Einflüssen geschützt, die sich erheblich negativ auf sie auswirken könnten. Eine Versicherung schützt also die Stabilität Ihrer Finanzen."		
	longDesc4: "Wir bei Letado werden weiterhin alles tun, um Ihnen zu helfen, bei Ihren täglichen Einkäufen so viel Geld wie möglich zu sparen, damit Sie sich Ihr Traumauto, Ihre Lieblingskleidung, Ihre Elektronik oder eine gute Versicherung leisten können. Wir hoffen, dass dieser %leafletBrandLink% Flyer, gültig von %validSince% bis %validTill%, Ihnen zumindest ein bisschen helfen wird und Sie Ihren Träumen ein Stück näher kommen werden!"
	smallTitle: "%brand% gültig ab"	
	recommendedLeaflets: "Beliebte Broschüren"
	similarLeaflets: "Nächstes Prospektey %brand%"
	backToLeaflets: "Zurück zur Liste aller Merkblätter"	
	allBrandLeaflets: "Alle Broschüren %brand%"
	goToShop: "Zum Shop gehen"

shops:
	title: "Geschäfte"
	text: "Eine Auswahl der beliebtesten Einzelhändler, deren neue Prospekte wir Ihnen jeden Tag präsentieren."

shop:
	leaflets: "Prospekte"
	text: "Der neueste Prospekt von %brand% mit tollen Angeboten."
	button: "Zum Shop gehen %brand%"	
	noLeaflets: "Wir suchen für Sie das aktuelle Flugblatt... Bitte versuchen Sie es später noch einmal."
	otherShops: "Andere Geschäfte"
	defaultTitleSuffic: '%shopName% - aktuelles Faltblatt, Verkauf von Waren'
	otherLeaflets: "Andere Broschüren %brand%"
	type:
		shopTitle: "{$shopName|upper} Prospekt {if $currentLeafletFromDate} ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + Aktionsbroschüre nächste Woche"
		eshopTitle: "%brand% Rabatt"
		eshop: "Schauen Sie sich die neuesten %brand% Sonderangebote in ihrem Katalog voller Inspiration und Schnäppchen an. Die neuesten %brand% Rabatte sind immer verfügbar, so dass Sie keine reduzierte Ware mehr verpassen werden."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} Flugblatt nächste Woche ab {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + aktuelles Merkblatt"
	     withCurrentLeaflet: "{$shopName|upper}Prospekt nächste Woche + aktueller Prospekt von {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} Broschüre nächste Woche + aktuelle Werbebroschüre online"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} Prospekt nächste Woche ✅ Stöbern Sie im speziellen {$shopName|upper} Prospekt der nächsten Woche von {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Ebenfalls online verfügbar ist das aktuelle PDF-Merkblatt dieser Woche {$shopName|upper}."
	    withCurrentLeaflet: "{$shopName|upper} Broschüre nächste Woche ✅ Blättern Sie in der speziellen {$shopName|oben} FLYER für die nächste Woche. Ebenfalls online verfügbar ist der aktuelle PDF-Flyer {$shopName|oben} für diese Woche von {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} Flyer nächste Woche ✅ Blättern Sie im speziellen {$shopName|oben} FLYER für die nächste Woche. Der aktuelle PDF-Flyer {$shopName|oben} mit den Sonderangeboten dieser Woche ist auch online verfügbar."

tag:
	text: "Angebot der neuesten Broschüren aus der Kategorie %tag%."
	noLeaflets: "Wir suchen für Sie das aktuelle Prospekt... Bitte versuchen Sie es später noch einmal."
	otherShops: "Andere Geschäfte"	

about:
	title: "Über uns"
	text: "Unser Ziel ist es, den Nutzern Zeit und Geld zu sparen. Wir bringen Ihnen täglich aktuelle Flyer der beliebtesten Einzelhändler und sparen Ihnen Zeit bei der Suche nach Sonderangeboten für Produkte."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."

