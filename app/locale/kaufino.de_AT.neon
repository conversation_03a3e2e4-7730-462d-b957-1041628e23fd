navbar:
	shops: Geschäfte
	leaflets: Flugblätter
	offers: Angebote
	search:
		placeholder: Geschäfte suchen
		submit: Suche

	moreShops: Weitere Geschäfte
	articles: Zeitschrift
	cities: Städte
	home: Startseite

footer:
	copyright: Kaufino Alle Rechte vorbehalten.
	shops: Geschäfte
	category: Kategorie
	offersCategory: Angebote
	aboutKaufino: Kaufino
	cookies: Cookies
	leaflets: Flugblätter
	aboutUs: Über uns
	nextCountries: Andere Länder

search:
	title: Suchergebnisse "%query%"
	noResults: 'Egal wie sehr wir suchen, wir können nichts finden.'

homepage:
	title: 'Flugblätter Kaufino '
	metaTitle: Aktuelle Flugblätter
	metaDescription: 'Sehen <PERSON>e sich die neuesten Flugblätter der beliebtesten Geschäfte an! Penny Flugblatt, Hoger Flugblatt, SPAR Flugblatt und mehr – alles an einem Ort.'
	text: '<PERSON><PERSON> sich die neuesten Flugblätter der beliebtesten Geschäfte an! Penny Flugblatt, <PERSON><PERSON>, SPAR Flugblatt und mehr – alles an einem Ort.'
	and: und
	leaflets: Aktuelle Flugblätter
	allLeaflets: Alle Flugblätter
	shops: Geschäfte
	allShops: Alle Geschäfte
	offersCategory: Angebote
	city: Flugblätter in Ihrer Stadt
	articles:
		title: Artikel aus der Zeitschrift

	bottomText:
		mainTitle: Über Kaufino
		section1:
			title: Stressfreies Einkaufserlebnis
			text: 'Kaufino ist die perfekte Wahl für Sie, wenn Sie das Beste aus den neuesten Angeboten Ihrer Lieblingsgeschäfte herausholen möchten. Durchstöbern Sie eine große Sammlung von Werbeprospekten, entdecken Sie aktuelle Angebote und sparen Sie bei Ihrem nächsten Einkauf. Sie können aus einer Vielzahl von <a href="./geschafte">Geschäften</a> wählen, die alles von Lebensmitteln und Getränken bis hin zu Haushaltswaren, Zubehör, Freizeitgeräten und vielem mehr anbieten. Mit Kaufino können Sie ein reibungsloses und stressfreies Einkaufserlebnis genießen.'

		section2:
			title: Aktuelle Flugblätter an einem Ort
			text: 'Wir aktualisieren unsere Flugblattsammlung täglich, um Ihnen die neuesten Angebote zu bringen. Ob Sie nach Lebensmitteln, Haushaltswaren oder Kleidung suchen, in den von uns bereitgestellten Flugblättern finden Sie immer, was Sie brauchen. Durchstöbern Sie die neuesten Flugblätter vieler Händler, darunter <a href=''./lidl''>Lidl</a>, <a href=''./billa''>Billa</a>, <a href=''./spar''>SPAR</a> und mehr.'

		section3:
			title: Komfort an Ihren Fingerspitzen
			text: 'Kaufino macht das Einkaufen einfacher als je zuvor. Wir haben alles für Sie optimiert, sodass Sie Ihre Einkäufe bequem von zu Hause aus planen können – kein mühsames Durchsuchen von Briefkästen nach Flugblättern mehr. Wählen Sie einfach Ihr bevorzugtes Geschäft, klicken Sie auf das neueste Flugblatt und stöbern Sie es von Anfang bis Ende durch. Entdecken Sie Ihre Lieblingsprodukte zu Sonderpreisen. Außerdem können Sie nach ähnlichen Artikeln in anderen Katalogen suchen, Preise vergleichen oder weitere Angebote finden.'

		section4:
			title: Geld und Zeit sparen
			text: 'Jede Woche die Familienausgaben zu verwalten kann sowohl anstrengend als auch besonders herausfordernd für Ihr Budget sein. Um das Beste aus Ihrem Einkaufserlebnis herauszuholen, sollten Sie die folgenden wichtigen Schritte in Betracht ziehen: Nutzen Sie Angebote in verschiedenen Produktkategorien und beginnen Sie mit der Planung Ihrer Einkäufe mit Kaufino. Erstellen Sie immer eine Einkaufsliste, bevor Sie sich auf den Weg machen oder online einkaufen. Diese einfachen Praktiken können Ihnen erheblich helfen, Geld zu sparen, und Kaufino ist hier, um Sie dabei zu unterstützen.'
			text2: ''

leaflets:
	title: Aktuelle Flugblätter an einem Ort
	metaTitle: Aktuelle Flugblätter online
	text: Sehen Sie sich die neuesten Flugblätter der beliebtesten Einzelhändler in Österreich an! Durchstöbern Sie die aktuellen Angebote und sparen Sie bei Ihrem nächsten Einkauf am meisten.
	metaDescription: 'Sehen Sie sich die neuesten Flugblätter der beliebtesten Einzelhändler in Österreich an! Lidl Flugblatt, SPAR Flugblatt und vieles mehr – alles griffbereit!'
	city: Flugblätter in Ihrer Stadt
	expiredTitle: Abgelaufene Flugblätter
	expiredText: Werfen Sie einen Blick auf unser Angebot an abgelaufenen Werbebroschüren der beliebtesten Einzelhändler!
	expiredMetaTitle: Abgelaufene Flugblätter %brand%

leaflet:
	ads: ANZEIGE
	metaTitle: 'Aktuelles %brand% Flugblatt, gültig ab %validSince% bis %validTill%'
	metaTitleUnChecked: 'Aktuelles %brand% Flugblatt, gültig ab %validSince% bis %validTill'
	metaDesc: 'Sehen Sie sich das aktuelle %brand% Flugblatt an, gültig ab %validSinceDay%, %validSince% ⭐ Sparen Sie bei Ihrem nächsten Einkauf.'
	metaDescUnChecked: Sehen Sie sich den aktuellen Flugblatt der Geschäfte an %brand%
	title: %brand% Flugblatt gültig ab %validSince% bis %validTill%
	titleUnChecked: Aktuelles %brand% Flugblatt
	leaflet: %brand%
	desc: 'Sie durchstöbern derzeit ein %leafletBrandLink% Flugblatt, gültig vom %validSince% bis %validTill%. Vergessen Sie nicht, Kaufino regelmäßig zu besuchen, um über die neuesten Angebote Ihrer Lieblingsgeschäfte auf dem Laufenden zu bleiben.'
	descUnChecked: 'Sie durchstöbern derzeit ein %leafletBrandLink% Flugblatt, gültig vom %validSince% bis %validTill%. Vergessen Sie nicht, Kaufino regelmäßig zu besuchen, um über die neuesten Angebote Ihrer Lieblingsgeschäfte auf dem Laufenden zu bleiben.'
	smallTitle: %brand% Flugblatt gültig ab
	recommendedLeaflets: Beliebte Flugblätter
	similarLeaflets: Weitere Flugblätter %brand%
	backToLeaflets: Zurück zur Übersicht aller Flugblätter
	allBrandLeaflets: Alle Flugblätter %brand%
	valid: Aktuelles Flugblatt
	brandLeafletFrom: %brand% Flugblatt gültig ab
	futureLeafletTitle: 'Neuestes %brand% Flugblatt, gültig ab %validSince% bis %validTill%'
	futureLeafletDescription: Schauen Sie sich die bevorstehenden Angebote im neuesten %brand% Flugblatt an ⭐ Kaufen Sie so günstig wie möglich ein! ✅
	archivedLeafletTitle: 'Abgelaufen: %brand% Flugblatt – gültig bis %validTill%'
	archivedLeafletDescription: Schauen Sie sich die abgelaufenen Angebote im letzten %brand% Flugblatt an
	expiredLeafletTitle: %brand% Flugblatt gültig ab %validSince% bis %validTill%
	expiredLeafletDescription: Dieser %brand% Flugblatt ist nicht mehr aktuell.
	actualLeafletValidSince: 'Das aktuelle Flugblatt ist verfügbar und gültig ab %validSinceDay%, %validSince%. ✅'
	expiredLeafletHeading: Dieses Flugblatt ist nicht mehr aktuell
	expiredLeafletLinkToShop: Das aktuelle %brand% Flugblatt finden Sie hier
	leaflets: %brand% Prospekte
	leafletValidTill: "Gültig bis: %validTill%"
	imageAltTitleWithDate: Flugblatt %brand% - %leafletName% gültig ab %validSince% bis %validTill% - seite %page%
	imageAltTitle: Flugblatt %brand% - %leafletName% - seite %page%

newsletter:
	metaTitle: Aktueller %brand% Newsletter gültig ab %validSince%
	metaDesc: 'Sehen Sie sich den aktuellen Newsletter des Geschäfts %brand% an, gültig ab %validSinceDay% %validSince%.'
	leaflet: %brand%
	desc: 'Aktueller Newsletter des Geschäfts %leafletBrandLink%, gültig ab %validSince%. Im Newsletter finden Sie aktuelle Aktionen und interessante Informationen.'
	smallTitle: %brand% Newsletter gültig ab
	recommendedLeaflets: Beliebte Newsletter
	similarLeaflets: Weitere Newsletter%brand%
	backToLeaflets: Zurück zur Übersicht aller Newsletter
	allBrandLeaflets: Alle Newsletter %brand%

shops:
	title: Alle Ihre Lieblingsgeschäfte
	metaTitle: Aktuelle Angebote in all Ihren Lieblingsgeschäften!
	text: Klicken Sie einfach auf das Logo und sehen Sie sich die aktuellen Angebote an!
	metaDescription: 'Aktuelle Angebote in all Ihren Lieblingsgeschäften! Lidl Flugblatt, Hofer Flugblatt, Billa Flugblatt und viele mehr!'
	otherShops:
		title: Andere Geschäfte

shop:
	title: %brand% - Flugblätter und Angebote
	showLeaflet: Flugblatt ansehen
	text: Sehen Sie sich das neueste %brand% Flugblatt an und sparen Sie bei Ihrem nächsten Einkauf ✅ Nutzen Sie das neueste Angebot ⭐
	textSubdomain: Sehen Sie sich das neueste %brand% Flugblatt an und sparen Sie bei Ihrem nächsten Einkauf ✅ Nutzen Sie das neueste Angebot ⭐
	button: Zum Geschäft wechseln %brand%
	type:
		shopTitle: '{$shopName|upper} Flugblatt {if $currentLeafletFromDate} gültig ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		shopTitleSubdomain: '{$shopName|upper} Flugblatt {if $currentLeafletFromDate} gültig ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		eshopTitle: %brand% Rabbat
		eshop: 'Sehen Sie sich die neuesten Aktionen von %brand% in ihrem Katalog voller Inspiration und vorteilhafter Angebote an. Aktuelle Rabatte von %brand% sind immer verfügbar, sodass Sie reduzierte Ware nie verpassen werden.'

	noLeaflets: Wir suchen gerade das aktuelle Flugblatt für Sie... Bitte versuchen Sie es später noch einmal.
	city: Flugblätter von %brand% auch in Ihrer Stadt
	otherShops: Weitere Geschäfte
	storeLeaflet: %brand% Flugblatt
	alternativeName: Was Leute ebenfalls suchen
	internationalVariants: Flugblätter von %brand% finden Sie auch in diesen Ländern
	defaultTitleSuffic: '%shopName% - aktuelle Flugblätter, Aktionen'
	offers: Angebote aus Flugblättern %brand%
	offersAll: Angebote aus Flugblättern
	link: '%brand% Flugblatt, Seite %page% »'
	expired: Angebot abgelaufen
	offersLeaflets: Flugblätter aus der Kategorie %category%
	offersExpire: Vergangene Aktionen aus der Kategorie %category%
	otherLeaflets: Weitere Flugblätter %brand%
	metaTitles:
		withFutureLeaflet: '{$shopName|upper} Flugblätter ab {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''} + Flugblatt nächste Woche'
		withCurrentLeaflet: '{$shopName|upper} aktuelles Flugblatt ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''} + Flugblatt nächste Woche'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} Flugblatt nächste Woche + Aktionsflugblatt online'

	metaDescriptions:
		withFutureLeaflet: '{$shopName|upper} Flugblatt ✅ Blättern Sie durch das Aktionsflugblatt von {$shopName|upper}, gültig ab {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}. Auch das Flugblatt von {$shopName|upper} für die nächste Woche ist online verfügbar.'
		withCurrentLeaflet: '{$shopName|upper} aktuelles Flugblatt ✅ Blättern Sie durch das Aktionsflugblatt von {$shopName|upper}, verfügbar ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}. Auch das Flugblatt von {$shopName|upper} für die nächste Woche ist online verfügbar.'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} Flugblatt nächste Woche ✅ Blättern Sie durch das Aktionsflugblatt von {$shopName|upper} für die nächste Woche. Auch das aktuelle PDF-Flugblatt von {$shopName|upper} mit den Aktionen für diese Woche ist online verfügbar.'

tag:
	title: %brand%
	metaTitle: %tag% - Flugblätter
	titleWithTag: Flugblätter in der Kategorie %tag%
	citiesWithTag: Städte mit Flugblättern in der Kategorie %tag%
	text: Durchstöbern Sie die neuesten Angebote in der Kategorie %brand% und freuen Sie sich auf erstaunliche Ersparnisse.
	noLeaflets: Wir suchen Ihr aktuelles Flugblatt... Bitte versuchen Sie es später noch einmal.
	otherShops: Andere Geschäfte
	offerTagTitle: %tag% im Angebot
	offerTagPriceFrom: ab %price%
	offers:
		title: %brand% in Aktion in den Aktionsflugblättern
		metaDescription: 'Sehen Sie sich die Aktionen bei %brand% in den Flugblättern der bekanntesten Geschäfte an. Verpassen Sie nicht die Rabatte und Aktionspreise, die in den neuen Flugblättern zu finden sind.'
		titleWithBestOffer: %brand% in Aktion ab ⏩ %price% im aktuellen Flugblatt
		metaDescriptionWithBestOffer: Sehen Sie sich die Aktionen für das Produkt %brand% in den Flugblättern der bekanntesten Geschäfte an. Verpassen Sie nicht die Rabatte auf Produkte zu Aktionspreisen in den neuen Flugblättern.
		text: 'Erforschen Sie alle Aktionen für %brand% in den <a href=\"%leafletLink%">Flugblättern</a> und verpassen Sie keine weiteren Rabatte auf ausgewählte Produkte. Die Aktionspreise für %brand% aus den aktuellen Flugblättern werden regelmäßig aktualisiert, und es ist sehr einfach, den <strong>günstigsten</strong> Preis zu finden.'
		currentLeaflet: Aktueller %brand% Prospekt
about:
	title: Über uns
	text: 'Unser Ziel ist es, den Nutzern Zeit und Geld zu sparen. Jeden Tag bieten wir aktuelle Flugblätter der beliebtesten Verkäufer an und sparen Ihnen die Zeit bei der Suche nach Aktionsangeboten für Produkte.'
	address: 'Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699 <br><br> info[at]vimvic.cz'

coupon:
	type:
		sale: Rabattgutschein

	showCode: Zeige den Rabattcode
	valid: Geltungsdauer bis
	copyText: Kopieren Sie den Code und fügen Sie ihn in den Warenkorb im Shop ein <strong>%brand%</strong>
	copy: Kopieren
	copied: Kopiert von
	button: Sparen bei%brand%

offer:
	title: Verkaufte Waren zu den besten Preisen
	text: 'Werfen Sie einen Blick auf das Angebot an neuen Produkten in den aktuellen Prospekten der beliebtesten Einzelhändler wie ALDI, Tesco, Netto, Penny und vielen anderen.'
	metaDescription: 'Werfen Sie einen Blick auf das Angebot an neuen Produkten in den aktuellen Prospekten der beliebtesten Einzelhändler wie ALDI, Tesco, Netto, Penny und vielen anderen.'
	whatOnSale: Was ist in Aktion?
	offerItem:
	    validTillDays: Es bleibt 1 Tag|Gültig für %count% Tage|Gültig für %count% Tage
	    validTillToday: Nur heute
	    validTillWeek: Mehr als eine Woche übrig
	    validSinceDays: Gültig ab morgen|Gültig in %count% Tagen|Gültig in %count% Tagen
	    validSinceWeek: Gültig für mehr als eine Woche
	futureOffers: 'Rabatte in zukünftigen Prospekten für das Produkt %name%'

city:
	city:
		metaTitle: Aktuelle Flugblätter in der Stadt %city%
		title: Aktuelle Flugblätter in der Stadt %city%
		text: 'Aktuelle Aktionsflugblätter in %city%. In den Flugblättern von %stores% in der Stadt %city% finden Sie viele Rabatte und Aktionsware, die online verfügbar ist.'
		metaDescription: 'Aktuelle Aktionsflugblätter in %city%. In den Flugblättern von %stores% in der Stadt %city% finden Sie viele Rabatte und Aktionsprodukte, die online verfügbar sind.'
		text2: 'Aktionsflugblätter und aktuelle Rabatte in der Stadt %city%. In den Flugblättern der beliebten Geschäfte in der Stadt %city% finden Sie nicht nur Aktionsprodukte, sondern auch viele weitere Rabatte und Angebote zu den besten Preisen. Blättern Sie durch die Flugblätter der größten Geschäfte in der Stadt %city%, wie zum Beispiel %stores%.'
		h2: 'Flugblätter, Aktionen und Rabatte der Geschäfte in der Stadt %city%'
		storesTitle: Filialen in der Stadt%city%
		storesMoreButton: Weitere Filialen »
		actualLeaflet: Aktuelle Broschüren
		leafletStores:
			title: Flugblätter %brand%
			store: Flugblätter %brand% %city%
			storeWithCity: Flugblätter %brand% %city%

		otherShops: Geschäfte in der Stadt %city%
		nearestCity: Weitere Städte mit Flugblättern in der Umgebung
		nearestCityWithShop: Weitere Städte mit Flugblättern von %shopName% in der Umgebung
		categoriesInText: %category% Flugblätter
		citiesInText: Flugblätter %city%
		generatedText:
			1: 'Die Stadt %city% mit einer Bevölkerung von %population% Einwohnern bietet unzählige Geschäfte, zu denen wir Ihnen jede Woche neue Flugblätter bringen. Interessante Aktionen und Rabatte sind nicht nur für die Stadt %city% vorbereitet, sondern auch für andere nahegelegene Städte wie %cities%.'
			2: 'Die Liste der aktuellen Flugblätter der größten Geschäfte in der Stadt %city% finden Sie hier:'
			3: Es stehen auch Aktionsflugblätter der Geschäfte %stores% und viele weitere zur Verfügung. Deren Aktionsangebote für den Monat %month% finden Sie im Abschnitt %shopsLink%.
			leaflet: Flugblätter %brand% Aktuell %validSince% – %validTill%
			and: und
			or: oder

	store:
		store: %fullAddress%
		h1: %brand% %city% %address%
		h2: Geschäfte %brand% in %city%
		title: '%brand% %address%, Flugblätter und Öffnungszeiten 🕔'
		description: 'Vergleichen Sie die Angebote in den Flugblättern, finden Sie die genaue Adresse und Öffnungszeiten heraus, oder lesen Sie, welches Sortiment Sie im Geschäft %brand% %address% erwartet.'
		open: Geöffnet
		closed: Geschlossen
		text: 'VVorteilhafte Aktionen und Rabatte auf ein vielfältiges Sortiment bietet regelmäßig das Geschäft %brand% %address%, wo Sie beim Einkauf das beliebte %brand% Flugblätter nutzen können.'
		h2bottom: %brand% Flugblätter %city% %street%
		text2WithoutStores: 'Diesen können die Kunden bequem online durchsehen, ebenso wie die Aktionen, die in weiteren Filialen verfügbar sind.'
		text2: 'Diesen können die Kunden bequem online durchsehen, ebenso wie die Aktionen, die in den Filialen verfügbar sind. %stores%.'
		or: oder
		others: und weitere
		textBottom: 'Das Geschäft %brand% %address% bietet den Kunden nicht nur ein breites Sortiment an Waren, sondern auch niedrige Preise, über die regelmäßig das %brand% Flugblätter informiert. Die Filiale %brand% %city% %street% ist ein beliebter Ort für diejenigen, die nach günstigen Angeboten suchen. Dank der Tatsache, dass das %brand% Flugblätter online verfügbar ist, haben Käufer die aktuellen Rabatte immer zur Hand. Wenn %brand% %address% nicht alles bietet, was der Kunde benötigt, kann er auch die weiteren Geschäfte in der Umgebung nutzen, wie zum Beispiel:'
		textBottom2: 'Erfahren Sie die genaue Adresse, den Kontakt zur Kundenhotline oder die Öffnungszeiten der beliebten Geschäfte, übersichtlich an einem Ort. Es fehlen auch nicht die Informationen darüber, welche Filialen sich in Ihrer Nähe befinden und wo Sie weitere günstige Angebote nutzen können, über die Aktionsflugblätter ausgewählter Geschäfte informieren.'
		sections:
			leaflets: Weitere Flugblätter aus der Kategorie
			shops: Andere Geschäfte in der Umgebung
			stores: Weitere %brand% Filialen in der Umgebung

	shop:
		title: Flugblätter %brand% %city%
		metaTitle: Flugblätter %brand% %city%
		storesTitle: Filialen von %brand% in der Stadt %city%
		h2: 'Flugblätter, akce a slevy v obchodu %brand% %city%'
		text: Aktion Flugblätter %brand% %city% und ihre aktuellen Rabatte und Sonderangebote. Bei Flugblätter %brand% %city% finden Sie eine große Auswahl an Produkten zu den besten Preisen. Aber nicht nur %brand% befindet sich in %city%. Andere beliebte Geschäfte sind %stores%.
		metaDescription: Aktion Flugblätter %brand% %city% und ihre aktuellen Rabatte und Sonderangebote. Bei Flugblätter %brand% %city% finden Sie eine große Auswahl an Produkten zu den besten Preisen. Aber nicht nur %brand% befindet sich in %city%. Andere beliebte Geschäfte sind %stores%.
		leafletStores:
			title: %brand% Flugblätter %city%
			store: %brand% %city%

		cityLink: Flugblätter %city%
		shopLink: Flugblätter %shop%
		otherShops: Andere Geschäfte in der Stadt %city%
		shopLeaflet: Flugblätter %brand%
		citiesInText: %brand% Flugblätter %city%
		offers: Angebote von Flugblättern %brand% %city%
		generatedText:
			1: 'Der %brand% Store in %city% bietet spezielle Flugblättchen an, die wir jede Woche für Sie aktualisieren.'
			2: 'Wenn Sie nach einem neuen Flugblatt von %brand% suchen, können Sie es sich ansehen, indem Sie auf diesen Link klicken: <a href\="%actualLeafletUrl%">Aktuelles Flugblatt %brand% %city%</a>. Die Gültigkeit des Flugblatts ist von %validSince% bis %validTill%. Jedes Flugblatt enthält interessante Aktionsangebote und Rabatte, saisonale Aktionen oder Clubpreise sowie ein breites Warensortiment.'
			3: 'Das Geschäft %brand% befindet sich jedoch nicht nur in der Stadt %city%. Die Flugblätter des Geschäfts %brand% finden Sie auch in weiteren nahegelegenen Filialen von %stores%. Alle Aktionsflugblätter sind in der Rubrik <a href="%leafletsUrl%">Flugblätter</a> verfügbar.'
			4: 'Wenn Sie nach anderen Geschäften suchen, finden Sie unter anderem folgende beliebte Läden %stores%.'

	tag:
		title: Flugblätter der Kategorie %tag% in der Stadt %city%
		titleWithTag: Flugblätter der Kategorie %tag% in der Stadt %city%
		text: Aktuelle Flugblätter aus der Kategorie %tag% in der Stadt %city%
		metaTitle: Aktuelle Flugblätter aus der Kategorie %tag% in %city%. Stöbern Sie im Rabattangebot und kaufen Sie noch heute günstig ein.

articles:
	title: Artikel über Aktionsflugblätter in Hyper- und Supermärkten.
	h1: Magazin
	description: Artikel voller Tipps für einfacheres Einkaufen mit neuen Flugblättern in inländischen Hyper- und Supermärkten. Nutzen Sie zeitlich begrenzte Aktionen und kaufen Sie zu Aktionspreisen ein!
	otherArticles: Andere Artikel
	author:
		title: Autor %author%
		description: Artikel des Autors %author%
		articlesCount: %count% Artikel|%count% články|%count% Artikel

showMore:
	offers: Andere Produkte
	cities: Andere Städte
	allCities: Alle Städte
	shops: Andere Geschäfte
	leaflets: Andere Flugblätter
	tags: Andere Angebote

tabs:
	leaflets: Flugblätter
	shop: Kunden Vorteile
	product: Produkte
	contact: Kontakt

