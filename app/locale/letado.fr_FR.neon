navbar:
	shops: "Boutiques"
	leaflets: "Dépliants"
	search:
		placeholder: "Recherche magasins"
		submit: "Recherche"

	moreShops: "Autres magasins"
	home: "Accueil"

footer:
	copyright: "Letado Tous droits réservés."	
	shops: "Magasins"
	category: "Catégories"	
	aboutLetado: "A propos de Letado"
	cookies: "Cookies"
	leaflets: "Dépliants"
	aboutUs: "A propos de nous"
	nextCountries: "Autres pays"

search:
	title: "Résultats de la recherche \"%query%\""
	noResults: "Nous avons beau chercher, nous ne trouvons rien."

homepage:
	title: "Dernières brochures et produits en vente"
	text: "Dernières brochures proposant un large éventail de produits en vente chez les principaux détaillants"
	allLeaflets: "Tous les dépliants"
	shops: "Magasins"
	allShops: "Tous les magasins"	

leaflets:
	title: "Dépliants"
	text: "Offre des derniers dépliants. Nous ajoutons chaque jour des dépliants pour vous, afin que vous puissiez toujours trouver des produits spéciaux."

leaflet:
	metaTitle: 'Dernier dépliant %brand% valable du %validSince%'
	metaTitlePageSuffix: 'page %page%'
	metaDesc: 'Dernier dépliant de %brand% valable du &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Dernier prospectus de %leafletBrandLink% valable du %validSince% au %validTill%. Sur la page %leafletPageCount% vous trouverez toutes les réductions actuelles. Sur la page Letado, vous trouverez toujours des informations actualisées sur tous les prospectus proposés par vos magasins préférés."
	longDesc1: "Profitez des offres spéciales de %leafletBrandLink%, que vous trouverez dans le dépliant promotionnel actuel de %validSince% à %validTill%. De nos jours, tout devient de plus en plus cher : les voitures, les vols, les vacances, les circuits, l'électronique, les appareils électroménagers, mais aussi les vêtements et bien d'autres choses encore. Cependant, vous n'avez pas besoin de contracter un prêt à la consommation ou autre pour vos dépenses mensuelles régulières. Chez Letada, nous nous efforçons de vous proposer le plus rapidement possible des réductions dans les magasins les plus populaires. Vous pouvez ainsi profiter des dernières promotions ou réductions et économiser de l'argent sur le budget de votre ménage."
	longDesc2: "Avec nous, vous n'avez pas besoin d'engager un conseiller financier pour vous aider à gérer vos finances, car nous le faisons pour vous. Vous pouvez ensuite utiliser l'argent restant pour des choses telles que des vacances à l'étranger, des voyages dans des hôtels locaux et des maisons d'hôtes, ou comme tampon financier pour votre prochain paiement hypothécaire."
	longDesc3: "C'est un sentiment agréable que d'être financièrement indépendant et de disposer d'un excédent de fonds. Cela signifie également que vous pouvez vous offrir une assurance de bonne qualité, qu'il s'agisse d'une assurance vie, d'une assurance habitation ou d'une assurance obligatoire et d'une couverture contre les pannes. Ces assurances protègent vos finances de toute influence inattendue qui pourrait avoir un impact négatif important sur elles. L'assurance protège donc la stabilité de vos finances."		
	longDesc4: "Chez Letado, nous continuerons à faire tout ce qui est en notre pouvoir pour vous aider à économiser le plus d'argent possible sur vos achats quotidiens afin que vous puissiez vous permettre d'acheter la voiture de vos rêves, vos vêtements préférés, de l'électronique ou de payer une assurance de qualité. Nous espérons que ce dépliant %leafletBrandLink%, valable du %validSince% au %validTill%, vous aidera au moins un peu et que vous vous rapprocherez de vos rêves !"
	smallTitle: "%brand% valable à partir de"	
	recommendedLeaflets: "Dépliants populaires"
	similarLeaflets: "Autres dépliants %brand%"
	backToLeaflets: "Retour à la liste de tous les dépliants"	
	allBrandLeaflets: "Tous les dépliants %brand%"
	goToShop: "Aller à la boutique"

shops:
	title: "Magasins"
	text: "Une sélection des détaillants les plus populaires dont nous vous proposons chaque jour de nouveaux prospectus."

shop:
	leaflets: "dépliants"
	text: "Le dernier dépliant %brand% avec de bonnes offres."
	button: "Aller au magasin %brand%"	
	noLeaflets: "Nous recherchons pour vous le dernier dépliant... Veuillez réessayer plus tard."
	otherShops: "Autres magasins."
	defaultTitleSuffic: '%shopName% - dernière brochure, marchandises en vente'
	otherLeaflets: "Autres dépliants %brand%"
	type:
		shopTitle: "{$shopName|upper} dépliant{if $currentLeafletFromDate} à partir du {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + dépliant d'action la semaine prochaine"
		eshopTitle: "%brand% remise"
		eshop: "Découvrez les dernières promotions %brand% dans leur catalogue plein d'inspiration et de bonnes affaires. Les dernières réductions %brand% sont toujours disponibles, vous ne manquerez donc jamais de marchandises à prix réduit."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} dépliant la semaine prochaine à partir du  {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + dépliant actuel"
	     withCurrentLeaflet: "{$shopName|upper} dépliant la semaine prochaine + dépliant actuel à partir de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} dépliant la semaine prochaine + dépliant promotionnel actuel en ligne"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} flyer la semaine prochaine ✅ Parcourez la rubrique spéciale {$shopName|upper} Le dépliant de la semaine prochaine de {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Le PDF actuel de la circulaire {$shopName|upper} de cette semaine est également disponible en ligne."
	    withCurrentLeaflet: "{$shopName|upper} flyer la semaine prochaine ✅ Parcourez le prospectus spécial {$shopName|upper} FLYER de la semaine prochaine. Le prospectus PDF actuel pour le {$shopName|upper} de cette semaine est également disponible en ligne à partir de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} La semaine prochaine ✅ Parcourez le flyer spécial {$shopName|upper} FLYER de la semaine prochaine. Le flyer PDF actuel {$shopName|upper} avec les promotions de cette semaine est également disponible en ligne.."

tag:
	text: "Offre des derniers dépliants de la catégorie %tag%."
	noLeaflets: "Nous recherchons pour vous le dernier flyer... Veuillez réessayer plus tard."
	otherShops: "Autres magasins"	

about:
	title: "A propos de nous"
	text: "Notre objectif est de faire gagner du temps et de l'argent aux utilisateurs. Chaque jour, nous vous proposons des prospectus actualisés des détaillants les plus populaires et vous faisons gagner du temps dans la recherche d'offres spéciales sur les produits."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
