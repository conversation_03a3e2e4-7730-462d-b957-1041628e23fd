navbar:
	shops: Geschäfte
	leaflets: Prospekte
	search:
		placeholder: <PERSON><PERSON><PERSON>äft<PERSON> suchen
		submit: Suche

	moreShops: Andere Geschäfte
	home: Startseite

footer:
	copyright: Letado Alle Rechte vorbehalten.
	shops: Geschäfte
	category: Kategorien
	aboutLetado: Über Letado
	cookies: Cookies
	leaflets: Prospekte
	aboutUs: Über uns
	nextCountries: Andere Länder

search:
	title: Suchergebnisse "%query%"
	noResults: 'Egal wie sehr wir suchen, wir können nichts finden.'

homepage:
	title: Aktuelle Prospekte und Angebote
	text: Entdecke aktuelle Prospekte und Angebote – verpasse keine Top-Deals!
	allLeaflets: Alle Prospekte
	shops: Geschäfte
	allShops: Alle Geschäfte

leaflets:
	title: Alle aktuellen Prospekte
	text: Hier findest du eine Übersicht über alle aktuellen Prospekte – entdecke die besten Angebote und spare bei deinem nächsten Einkauf!

leaflet:
	metaTitle: 'Aktueller %brand% Prospekt, gültig ab %validSince%'
	metaTitlePageSuffix: seite %page%
	metaDesc: Aktueller %brand% Prospekt gültig ab&nbsp;%validSinceDay% %validSince%.
	leaflet: %brand% Prospekt
	desc: ''
	longDesc1: ''
	longDesc2: ''
	longDesc3: ''
	longDesc4: ''
	smallTitle: %brand% Prospekt gültig ab
	recommendedLeaflets: Beliebte Prospekte
	similarLeaflets: Nächste %brand% Prospekte
	backToLeaflets: Zurück zur Liste der Prospekte
	allBrandLeaflets: Alle %brand% Prospekte
	goToShop: Zum Shop gehen

shops:
	title: Wähle dein Geschäft und entdecke Angebote
	text: Finde dein Lieblingsgeschäft und stöbere durch die besten aktuellen Angebote – alles auf einen Blick!

shop:
	leaflets: %brand% Prospekte
	text: Blättern Sie durch den neuesten Prospekt und sparen Sie groß!
	button: Zum Shop gehen %brand%
	noLeaflets: Wir suchen für Sie das aktuelle Flugblatt... Bitte versuchen Sie es später noch einmal.
	otherShops: Andere Geschäfte
	defaultTitleSuffic: %shopName% – Aktueller Prospekt & Top-Angebote
	otherLeaflets: Andere %brand% Prospekte
	type:
		shopTitle: '{$shopName|upper} Prospekt {if $currentLeafletFromDate} ab {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}{/if} + Aktionsbroschüre nächste Woche'
		eshopTitle: %brand% Rabatt
		eshop: 'Schauen Sie sich die neuesten %brand% Sonderangebote in ihrem Katalog voller Inspiration und Schnäppchen an. Die neuesten %brand% Rabatte sind immer verfügbar, so dass Sie keine reduzierte Ware mehr verpassen werden.'

	metaTitles:
		withFutureLeaflet: '{$shopName|upper} Flugblatt nächste Woche ab {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''} + aktuelles Merkblatt'
		withCurrentLeaflet: '{$shopName|upper}Prospekt nächste Woche + aktueller Prospekt von {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} Broschüre nächste Woche + aktuelle Werbebroschüre online'

	metaDescriptions:
		withFutureLeaflet: '{$shopName|upper} Prospekt nächste Woche ✅ Stöbern Sie im speziellen {$shopName|upper} Prospekt der nächsten Woche von {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}. Ebenfalls online verfügbar ist das aktuelle PDF-Merkblatt dieser Woche {$shopName|upper}.'
		withCurrentLeaflet: '{$shopName|upper} Broschüre nächste Woche ✅ Blättern Sie in der speziellen {$shopName|oben} FLYER für die nächste Woche. Ebenfalls online verfügbar ist der aktuelle PDF-Flyer {$shopName|oben} für diese Woche von {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}.'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} Flyer nächste Woche ✅ Blättern Sie im speziellen {$shopName|oben} FLYER für die nächste Woche. Der aktuelle PDF-Flyer {$shopName|oben} mit den Sonderangeboten dieser Woche ist auch online verfügbar.'

tag:
	text: Angebot der neuesten Broschüren aus der Kategorie %tag%.
	noLeaflets: Wir suchen für Sie das aktuelle Prospekt... Bitte versuchen Sie es später noch einmal.
	otherShops: Andere Geschäfte

about:
	title: Über uns
	text: 'Unser Ziel ist es, den Nutzern Zeit und Geld zu sparen. Wir bringen Ihnen täglich aktuelle Flyer der beliebtesten Einzelhändler und sparen Ihnen Zeit bei der Suche nach Sonderangeboten für Produkte.'
	address: 'Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699'

city:
	city:
		title: Flyers %city%
		text: Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores.
		text2: 'Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%.'
		h2: 'Flyers, promotions and discounts from shops in %city%'
		leafletStores:
			title: leaflet %brand%
			store: leaflet %brand% %city%
			storeWithCity: leaflet %brand% %city%

		otherShops: Stores in the city %city%
		nearestCity: Other cities with leaflets in the area
		nearestCityWithShop: Other cities with leaflets %shopName% in the area
		categoriesInText: %category% leaflets
		citiesInText: leaflets %city%
		generatedText:
			1: 'The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%.'
			2: 'A list of the current flyers of the biggest shops in %city% can be found here:'
			3: There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section.
			leaflet: Flyer %brand% current %validSince% - %validTill%
			and: and
			or: or

	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: Stores %brand% in %city%
		title: '%brand% %address%, leaflet and opening hours 🕔'
		description: 'Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%.'
		open: Open
		closed: Closed
		text: 'Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase.'
		text2WithoutStores: 'Customers can conveniently view this online, as well as promotions available at other branches.'
		text2: 'Customers can conveniently view this online, as well as the promotions available at %stores% branches.'
		or: nebo
		others: and others
		textBottom: 'The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn''t offer everything a shopper needs, they can also take advantage of other nearby stores such as:'
		textBottom2: 'Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores.'
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken

	shop:
		title: Leaflets %brand% %city%
		h2: 'Flyers, promotions, and discounts in the store %brand% %city%'
		text: 'Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%.'
		leafletStores:
			title: %brand% leaflet %city%
			store: %brand% %city%

		cityLink: leaflets %city%
		shopLink: leaflets %shop%
		otherShops: Other shops in the city %city%
		shopLeaflet: Leaflets %brand%
		citiesInText: %brand% leaflet %city%
		offers: Offers from flyers %brand% %city%
		generatedText:
			1: The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week.
			2: 'If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href="%actualLeafletUrl%">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise.'
			3: 'However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href="%leafletsUrl%">Letáky.</a>'
			4: 'If you''re looking for other stores, popular ones include %stores%.'

