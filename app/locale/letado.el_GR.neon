navbar:
	shops: "Καταστήματα"
	leaflets: "Φυλλάδια"
	search:
		placeholder: "Αναζήτηση καταστημάτων"
		submit: "Αναζήτηση"

	moreShops: "Άλλα καταστήματα"
	home: "Αρχική"

footer:
	copyright: "Letado Όλα τα δικαιώματα διατηρούνται."	
	shops: "Καταστήματα"
	category: "Κατηγορίες"	
	aboutLetado: "Σχετικά με το Letado"
	cookies: "Cookies"
	leaflets: "Φυλλάδια"
	aboutUs: "Σχετικά με εμάς"
	nextCountries: "Άλλες χώρες"

search:
	title: "Αποτελέσματα αναζήτησης \"%query%\""
	noResults: "Όσο σκληρά κι αν ψάξουμε, δεν μπορούμε να βρούμε τίποτα."

homepage:
	title: "Τελευταία φυλλάδια και προϊόντα προς πώληση"
	text: "Τελευταία φυλλάδια που προσφέρουν ένα ευρύ φάσμα προϊόντων προς πώληση από μεγάλους λιανοπωλητές"
	allLeaflets: "Όλα τα φυλλάδια"
	shops: "Καταστήματα"
	allShops: "Όλα τα καταστήματα"	

leaflets:
	title: "Φυλλάδια"
	text: "Προσφορά των τελευταίων φυλλαδίων. Προσθέτουμε φυλλάδια για εσάς κάθε μέρα, ώστε να μπορείτε πάντα να βρίσκετε ειδικά προϊόντα."

leaflet:
	metaTitle: 'Τελευταίο φυλλάδιο %brand% που ισχύει από %validSince%'
	metaTitlePageSuffix: 'Σελίδα %page%'
	metaDesc: 'Τελευταίο φυλλάδιο από την %brand% που ισχύει από &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Τελευταίο φυλλάδιο από το %leafletBrandLink% που ισχύει από %validSince% έως %validTill%. Στη σελίδα %leafletPageCount% θα βρείτε όλες τις τρέχουσες εκπτώσεις. Στη σελίδα Letado θα βρίσκετε πάντα ενημερωμένες πληροφορίες για όλα τα φυλλάδια που προσφέρονται από τα αγαπημένα σας καταστήματα."
	longDesc1: "Επωφεληθείτε από τις ειδικές προσφορές της %leafletBrandLink%, τις οποίες θα βρείτε στο τρέχον διαφημιστικό φυλλάδιο από %validSince% έως %validTill%. Τα πάντα γίνονται όλο και πιο ακριβά στις μέρες μας - αυτοκίνητα, πτήσεις, διακοπές, εκδρομές, ηλεκτρονικά είδη, λευκές συσκευές, αλλά και ρούχα και πολλά άλλα. Ωστόσο, δεν χρειάζεται να πάρετε καταναλωτικό ή άλλο δάνειο για τα συνήθη μηνιαία έξοδά σας. Στη Letada, προσπαθούμε να σας προσφέρουμε εκπτώσεις από όλα τα πιο δημοφιλή καταστήματα το συντομότερο δυνατό. Έτσι, μπορείτε να επωφεληθείτε από τις τελευταίες προσφορές ή εκπτώσεις και να εξοικονομήσετε χρήματα από τον προϋπολογισμό του νοικοκυριού σας."
	longDesc2: "Με εμάς, δεν χρειάζεται να προσλάβετε έναν οικονομικό σύμβουλο για να σας βοηθήσει με τα οικονομικά σας, επειδή μπορούμε να το κάνουμε εμείς για εσάς. Στη συνέχεια, μπορείτε να χρησιμοποιήσετε τα χρήματα που σας απομένουν για πράγματα όπως διακοπές στο εξωτερικό, ταξίδια σε τοπικά ξενοδοχεία και ξενώνες ή ως οικονομικό μαξιλάρι για την επόμενη πληρωμή του στεγαστικού σας δανείου."
	longDesc3: "Είναι υπέροχο συναίσθημα να είσαι οικονομικά ανεξάρτητος και να έχεις πλεόνασμα κεφαλαίων. Σημαίνει επίσης ότι μπορείτε να αντέξετε οικονομικά ασφάλειες καλής ποιότητας, είτε πρόκειται για ασφάλιση ζωής, είτε για ασφάλιση κατοικίας, είτε για υποχρεωτική ασφάλιση και κάλυψη βλάβης. Αυτό προστατεύει τα οικονομικά σας από τυχόν απροσδόκητες επιρροές που θα μπορούσαν να έχουν σημαντικά αρνητικές επιπτώσεις σε αυτά. Συνεπώς, η ασφάλιση προστατεύει τη σταθερότητα των οικονομικών σας."		
	longDesc4: "Στη Letado, θα συνεχίσουμε να κάνουμε ό,τι μπορούμε για να σας βοηθήσουμε να εξοικονομήσετε όσο το δυνατόν περισσότερα χρήματα από τις καθημερινές σας αγορές, ώστε να έχετε την οικονομική δυνατότητα να αγοράσετε το αυτοκίνητο των ονείρων σας, τα αγαπημένα σας ρούχα, τα ηλεκτρονικά είδη ή να πληρώσετε για ποιοτική ασφάλιση. Ελπίζουμε ότι αυτό το φυλλάδιο %leafletBrandLink%, που ισχύει από %validSince% έως %validTill%, θα σας βοηθήσει έστω και λίγο και ότι θα βρεθείτε πιο κοντά στα όνειρά σας!"
	smallTitle: "%brand% ισχύει από"	
	recommendedLeaflets: "Δημοφιλή φυλλάδια"
	similarLeaflets: " Άλλα φυλλάδια %brand%"
	backToLeaflets: "Επιστροφή στον κατάλογο όλων των φυλλαδίων"	
	allBrandLeaflets: "Όλα τα φυλλάδια %brand%"
	goToShop: "Πηγαίνετε στο κατάστημα"

shops:
	title: "Καταστήματα"
	text: "Μια επιλογή από τα πιο δημοφιλή καταστήματα λιανικής πώλησης, των οποίων τα νέα φυλλάδια σας φέρνουμε καθημερινά."

shop:
	leaflets: "φυλλάδια"
	text: "Το τελευταίο φυλλάδιο %brand% με μεγάλες προσφορές."
	button: "Πηγαίνετε στο κατάστημα %brand%"	
	noLeaflets: "Ψάχνουμε για εσάς το τελευταίο φυλλάδιο... Προσπαθήστε ξανά αργότερα."
	otherShops: "Άλλα καταστήματα."
	defaultTitleSuffic: '%shopName% - τελευταίο φυλλάδιο, αγαθά προς πώληση'
	otherLeaflets: "Άλλα φυλλάδια %brand%"
	type:
		shopTitle: "{$shopName|upper} φυλλάδιο {if $currentLeafletFromDate} από {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + φυλλάδιο δράσης της επόμενης εβδομάδας"
		eshopTitle: "%brand% Έκπτωση"
		eshop: "Ελέγξτε τις τελευταίες προσφορές της %brand% στον κατάλογό της που είναι γεμάτος έμπνευση και ευκαιρίες. Οι τελευταίες εκπτώσεις της %brand% είναι πάντα διαθέσιμες, ώστε να μη χάνετε ποτέ τα προϊόντα με έκπτωση."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα από {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + τρέχον φυλλάδιο"
	     withCurrentLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα + τρέχον φυλλάδιο από {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα + τρέχον διαφημιστικό φυλλάδιο σε απευθείας σύνδεση"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα ✅ Περιηγηθείτε στο ειδικό {$shopName|upper} ΦΥΛΛΑΔΙΟ ΤΗΣ ΕΠΟΜΕΝΗΣ ΕΒΔΟΜΑΔΑΣ από το {$επόμενοΦυλλάδιοΑπόΗμερομηνία|ημέραΓενικό} {$nextLeafletFromDate|date:'j.n.Y'}. Διαθέσιμο στο διαδίκτυο είναι επίσης το τρέχον PDF του φυλλαδίου {$$shopName|upper} αυτής της εβδομάδας."
	    withCurrentLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα ✅ Περιηγηθείτε στο ειδικό {$shopName|upper} FLYER για την επόμενη εβδομάδα. Επίσης, είναι διαθέσιμο online το τρέχον PDF του φυλλαδίου {$shopName|upper} αυτής της εβδομάδας από το {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} φυλλάδιο την επόμενη εβδομάδα ✅ Περιηγηθείτε στο ειδικό {$shopName|upper} FLYER για την επόμενη εβδομάδα. Διαθέσιμο στο διαδίκτυο είναι επίσης το τρέχον φυλλάδιο PDF {$shopName|upper} με τις προσφορές αυτής της εβδομάδας."

tag:
	text: "Προσφορά των τελευταίων φυλλαδίων από την κατηγορία %tag%."
	noLeaflets: "Ψάχνουμε για εσάς το τελευταίο φυλλάδιο... Προσπαθήστε ξανά αργότερα."
	otherShops: "Άλλα καταστήματα"	

about:
	title: "Σχετικά με εμάς"
	text: "Στόχος μας είναι να εξοικονομήσουμε χρόνο και χρήμα στους χρήστες. Κάθε μέρα σας φέρνουμε ενημερωμένα φυλλάδια από τους πιο δημοφιλείς λιανοπωλητές και σας γλιτώνουμε χρόνο από την αναζήτηση ειδικών προσφορών για προϊόντα."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
