navbar:
	shops: Negozi
	leaflets: Volantini
	search:
		placeholder: Ricerca di negozi
		submit: Cerca

	moreShops: Altri negozi
	home: Home

footer:
	copyright: Oferito Tutti i diritti riservati.
	shops: Negozi
	category: Categoria
	aboutLetado: Informazioni su Oferito
	cookies: Cookies
	leaflets: Volantini
	aboutUs: Chi siamo
	nextCountries: Altri paesi

search:
	title: Risultati della ricerca "%query%"
	noResults: "Non importa quanto cerchiamo, non riusciamo a trovare nulla."

homepage:
	title: Nuovi Volantini e offerte
	text: "Dai un'occhiata a tutti i volantini online dei rivenditori più noti, che offrono una vasta gamma di promozioni. Che tu stia cercando prodotti alimentari o non food, tutte le offerte online le troverai qui."
	allLeaflets: Tutti i volantini
	shops: Negozi
	allShops: Tutti i negozi

leaflets:
	title: Volantini
	text: Dai un'occhiata ai nuovi volantini offerte dei rivenditori più popolari in Italia! Aggiorniamo i volantini per te ogni giorno in modo che tu possa sempre ottenere una buona affare.

leaflet:
	metaTitle: Nuovo volantino %brand% valido da %validSince%
	metaTitlePageSuffix: pagina %page%
	metaDesc: Sfoglia il nuovo volantino %brand% valido da&nbsp;%validSinceDay% %validSince%. Approfitta degli sconti di oggi!
	leaflet: %brand%
	desc: Stai il nuovo volantino del negozio %leafletBrandLink% valido da %validSince% a %validTill%. L'opuscolo contiene  %leafletPageCount% pagine di informazioni sugli sconti attuali. Visita Oferito regolarmente e ottieni sempre informazioni aggiornate su tutti i volantini offerte online di supermercati e negozi in tutta Italia.
	longDesc1: "Approfittate delle nuove offerte %leafletBrandLink%, che si trovano nel volantino offerte valido da %validSince% a %validTill%. Tutto sta diventando più caro in questi giorni - auto, voli, vacanze, tour, elettronica, elettrodomestici, così come i vestiti e molto altro. Tuttavia, c'è sempre un modo per risparmiare! Da Oferito, facciamo del nostro meglio per portarti le offerte speciali di tutti i negozi più popolari in Italia. In questo modo, è possibile approfittare delle ultime offerte e sconti, così da risparmiare e rimanere nel budget."
	longDesc2: "Non c'è bisogno di assumere consulenti finanziari per alleggerire le proprie finanze: grazie a noi puoi fare tutto da solo. Si può poi utilizzare il denaro rimasto per cose come vacanze all'estero, viaggi in hotel e pensioni nazionali o come riserva finanziaria per la prossima rata del mutuo."
	longDesc3: "È una bella sensazione essere finanziariamente indipendenti e avere fondi in eccesso. Perché ti permette anche di permetterti un'assicurazione di buona qualità, che sia sulla vita, sulla casa o sulla responsabilità civile obbligatoria e sugli incidenti. Questo protegge le vostre finanze da qualsiasi influenza inaspettata che potrebbe avere un impatto negativo significativo su di esse. L'assicurazione protegge quindi la stabilità delle vostre finanze."
	longDesc4: "Noi di Oferito continueremo a fare tutto il possibile per aiutarti a risparmiare il più possibile sui tuoi acquisti quotidiani in modo che tu possa permetterti di comprare l'auto dei tuoi sogni, i tuoi vestiti preferiti, l'elettronica o pagare un'assicurazione di qualità. Speriamo che questo volantino %leafletBrandLink%, valido da %validSince% a %validTill%, ti aiuti almeno un po' e ti avvicini ai tuoi sogni!"
	smallTitle: %brand% valido da
	recommendedLeaflets: Volantini preferiti
	similarLeaflets: Altri volantini %brand%
	backToLeaflets: Torna alla lista di tutti i volantini
	allBrandLeaflets: Tutti i volantini %brand%

shops:
	title: Negozi
	text: "Dai un'occhiata alla lista dei rivenditori più popolari, per i quali aggiorniamo i volantini giornalmente."

shop:
	leaflets: volantini
	text: Visualizza il nuovo volantino %brand% e quello della prossima settimana. Lasciati ispirare dalle offerte in&nbsp;negozio %brand% e risparmia ora!
	button: Vai al negozio %brand%  @todo
	noLeaflets: Stiamo cercando il vostro attuale volantino... Si prega di riprovare più tardi.
	otherShops: Altri negozi
	defaultTitleSuffic: %shopName% - volantino e nuove offerte online
	otherLeaflets: Altri volantini %brand%
	type:
		shopTitle: "{$shopName|upper} volantino{if $currentLeafletFromDate} da {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + prossimana settimana"
		eshopTitle: Offerte %brand%
		eshop: "Scopri le ultime offerte %brand% nel loro catalogo ricco di ispirazioni e occasioni. Gli sconti attuali %brand% sono sempre consultabili, così da non perdere mai i prodotti scontati con il volantino delle offerte speciali."

	metaTitles:
		withFutureLeaflet: "Nuovo volantino {$shopName|upper} da {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}"
		withCurrentLeaflet: "Nuovo volantino {$shopName|upper} da {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
		withoutCurrentAndFutureLeaflet: "Nuovo volantino {$shopName|upper} disponibile online."

	metaDescriptions:
		withFutureLeaflet: "Sfoglia online tutte le ultime offerte del nuovo volantino {$shopName|upper} ✅ Tutti i prodotti delle offerte {$shopName|upper} da {$nextLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
		withCurrentLeaflet: "Sfoglia online tutte le ultime offerte del nuovo volantino {$shopName|upper} ✅ Tutti i prodotti e offerte {$shopName|upper} da {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
		withoutCurrentAndFutureLeaflet: "Sfoglia online tutte le ultime offerte del nuovo volantino {$shopName|upper} ✅ Tutti i prodotti scontati del volantino offerte {$shopName|upper}."

tag:
	text: "Sfoglia gli attuali volantini promozionali di questa settimana della categoria %tag%. Nei volantini troverete non solo merce in vendita, ma anche molti sconti su varie merci."
	noLeaflets: Stiamo cercando il vostro attuale volantino... Si prega di riprovare più tardi.
	otherShops: Altri negozi

about:
	title: Chi siamo
	text: Il nostro obiettivo è quello di far risparmiare agli utenti tempo e denaro. Ogni giorno ti portiamo i volantini aggiornati dei rivenditori più popolari e ti facciamo risparmiare tempo nella ricerca delle offerte sui prodotti.
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
