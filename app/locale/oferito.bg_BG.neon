navbar:
	shops: "Магазини"
	leaflets: "Листовки"
	search:
		placeholder: "Търсене на магазини"
		submit: "Търсене"

	moreShops: "Други магазини"
	home: "Начало"

footer:
	copyright: "Letado Всички права запазени."	
	shops: "Магазини"
	category: "Категории"	
	aboutLetado: "За Letado"
	cookies: "Cookies"
	leaflets: "Листовки"
	aboutUs: "За нас"
	nextCountries: "Други държави"

search:
	title: "Резултати от търсенето\"%query%\""
	noResults: "Колкото и да търсим, не можем да намерим нищо."

homepage:
	title: "Последни брошури и стоки в продажба"
	text: "Последни брошури, предлагащи широка гама от стоки в продажба от големи търговци на дребно"
	allLeaflets: "Всички листовки"
	shops: "Магазини"
	allShops: "Всички магазини"	

leaflets:
	title: "Листовки"
	text: "Предлагане на най-новите брошури. Добавяме листовки за вас всеки ден, така че винаги да можете да намерите специални продукти."

leaflet:
	metaTitle: 'Последна листовка на %brand%, валидна от %validSince%'
	metaTitlePageSuffix: 'Страница %page%'
	metaDesc: 'Последната листовка от %brand% е валидна от &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Последна листовка от %leafletBrandLink%, валидна от %validSince% до %validTill%. На страницата %leafletPageCount% ще намерите всички текущи отстъпки. На страницата Letado ще намерите винаги актуална информация за всички предлагани листовки от любимите ви магазини."
	longDesc1: "Възползвайте се от специалните оферти на %leafletBrandLink%, които ще намерите в настоящата промоционална брошура от %validSince% до %validTill%. В днешно време всичко става все по-скъпо - автомобили, полети, почивки, екскурзии, електроника, бяла техника, но също и дрехи и много други. Въпреки това не е необходимо да вземате потребителски или друг кредит за редовните си месечни разходи. В Letada се стремим да ви предоставяме отстъпки от всички най-популярни магазини възможно най-скоро. Така че можете да се възползвате от най-новите промоции или намаления и да спестите пари от домашния си бюджет."
	longDesc2: "При нас не е необходимо да наемате финансов консултант, който да ви помага с финансите, защото ние можем да го направим вместо вас. Останалите пари можете да използвате за почивки в чужбина, екскурзии в местни хотели и къщи за гости или като финансов буфер за следващата вноска по ипотеката."
	longDesc3: "Чувството да си финансово независим и да разполагаш с излишък от средства е страхотно. Това също така означава, че можете да си позволите качествена застраховка, независимо дали става въпрос за застраховка живот, застраховка на дома или задължителна застраховка и застраховка срещу авария. Това защитава финансите ви от всякакви неочаквани влияния, които биха могли да имат значително отрицателно въздействие върху тях. Следователно застраховката защитава стабилността на вашите финанси."		
	longDesc4: "В Letado ще продължим да правим всичко възможно, за да ви помогнем да спестите възможно най-много пари от ежедневните си покупки, така че да можете да си позволите да си купите мечтания автомобил, любимите си дрехи, електроника или да платите за качествена застраховка. Надяваме се, че тази листовка %leafletBrandLink%, валидна от %validSince% до %validTill%, ще ви помогне поне малко и ще бъдете по-близо до мечтите си!"
	smallTitle: "%brand% валиден от"	
	recommendedLeaflets: "Популярни листовки"
	similarLeaflets: " Други листовки %brand%"
	backToLeaflets: "Обратно към списъка с всички листовки"	
	allBrandLeaflets: "Всички листовки %brand%"
	goToShop: "Отидете в магазина"
	futureLeafletTitle: "@todo"
	futureLeafletDescription: "@todo"
	archivedLeafletTitle: "@todo"
	archivedLeafletDescription: "@todo"

shops:
	title: "Магазини"
	text: "Селекция от най-популярните търговци на дребно, чиито нови листовки ви предлагаме всеки ден."

shop:
	leaflets: "листовки"
	text: "Най-новата листовка на %brand% с чудесни оферти."
	button: "Отидете в магазина %brand%"	
	noLeaflets: "Търсим за вас най-новата листовка... Моля, опитайте отново по-късно."
	otherShops: "Други магазини."
	defaultTitleSuffic: '%shopName% - последна брошура, стоки в продажба'
	otherLeaflets: "Други листовки %brand%"
	type:
		shopTitle: "{$shopName|upper} листовка { if $currentLeafletFromDate} от {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + листовка за събитията през следващата седмица"
		eshopTitle: "%brand% Отстъпка"
		eshop: "Разгледайте най-новите промоции на %brand% в техния каталог, пълен с вдъхновение и изгодни предложения. Най-новите намаления на %brand% са винаги на разположение, така че никога няма да пропуснете стоки с намаление."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} листовка следващата седмица от {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + текущата листовка"
	     withCurrentLeaflet: "{$shopName|upper} листовка следващата седмица + текуща листовка от {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} листовка следващата седмица + текуща промоционална листовка онлайн"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} следващата седмица ✅ Разгледайте действието {$shopName|upper} ПОЛЕТ НА СЛЕДВАЩАТА СЕДМИЦА от {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Също така онлайн е наличен актуалният PDF файл на {$име на магазина|горната част} на листовката за тази седмица."
	    withCurrentLeaflet: "{$shopName|upper} флаер следващата седмица ✅ Разгледайте специалния {$shopName|upper} ФЛАЙЕР за следващата седмица. Налична е също така онлайн текущата PDF листовка {$shopName|upper} за тази седмица от {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} флаер следващата седмица ✅ Разгледайте специалния {$shopName|upper} ФЛАЙЕР за следващата седмица. Също така онлайн е налична актуалната PDF листовка {$shopName|upper} с промоциите за тази седмица."

tag:
	text: "Предлагане на най-новите брошури от категорията %brand%."
	noLeaflets: "Търсим за вас най-новата листовка... Моля, опитайте отново по-късно."
	otherShops: "Други магазини"	

about:
	title: "За нас"
	text: "Нашата цел е да спестим време и пари на потребителите. Всеки ден ви предлагаме актуални листовки от най-популярните търговци на дребно и ви спестяваме време за търсене на специални оферти за продукти.."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"
