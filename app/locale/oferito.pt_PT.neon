navbar:
	shops: "Lojas"
	leaflets: "Folhetos"
	search:
		placeholder: "Procurar lojas"
		submit: "Pesquisar"

	moreShops: "Outras lojas"
	home: "Início"

footer:
	copyright: "Letado Todos os direitos reservados."	
	shops: "Lojas"
	category: "Categorias"	
	aboutLetado: "Sobre o Letado"
	cookies: "Cookies"
	leaflets: "Folhetos"
	aboutUs: "Sobre nós"
	nextCountries: "Outros países"

search:
	title: "Resultados da pesquisa \"%query%\""
	noResults: "Por mais que procuremos, não conseguimos encontrar nada."

homepage:
	title: "Últimos folhetos e produtos à venda"
	text: "Os últimos folhetos com uma vasta gama de produtos à venda nos principais retalhistas"
	allLeaflets: "Todos os folhetos"
	shops: "Lojas"
	allShops: "Todas as lojas"	

leaflets:
	title: "Folhetos"
	text: "Oferta dos últimos folhetos. Adicionamos folhetos todos os dias, para que possa sempre encontrar produtos especiais."

leaflet:
	metaTitle: 'Último folheto de %brand% válido a partir de %validSince%'
	metaTitlePageSuffix: 'página %page%'
	metaDesc: 'Último folheto da %brand% válido a partir de &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Último folheto de %leafletBrandLink% válido de %validSince% a %validTill%. Na página %leafletPageCount% encontrará todos os descontos actuais. Na página Letado encontrará sempre informações actualizadas sobre todos os folhetos em oferta das suas lojas favoritas."
	longDesc1: "Tire partido das ofertas especiais de %leafletBrandLink%, que encontrará no folheto promocional atual de %validSince% a %validTill%. Hoje em dia, tudo é cada vez mais caro - automóveis, voos, férias, excursões, eletrónica, electrodomésticos, mas também vestuário e muito mais. No entanto, não precisa de contrair um empréstimo ao consumo ou outro para as suas despesas mensais regulares. Na Letada, esforçamo-nos por lhe trazer descontos de todas as lojas mais populares o mais rapidamente possível. Assim, pode aproveitar as últimas promoções ou descontos e poupar dinheiro do seu orçamento familiar."
	longDesc2: "Connosco, não precisa de contratar um consultor financeiro para o ajudar com as suas finanças, porque nós podemos fazê-lo por si. Pode então utilizar o dinheiro que sobra para coisas como férias no estrangeiro, viagens a hotéis e pensões locais ou como uma reserva financeira para o pagamento da sua próxima hipoteca."
	longDesc3: "É uma óptima sensação ser financeiramente independente e ter um excedente de fundos. Significa também que pode pagar um seguro de boa qualidade, quer se trate de um seguro de vida, de um seguro de habitação ou de um seguro obrigatório e de um seguro contra avarias. Isto protege as suas finanças de quaisquer influências inesperadas que possam ter um impacto negativo significativo sobre elas. Os seguros protegem, assim, a estabilidade das suas finanças."		
	longDesc4: "Na Letado, continuaremos a fazer tudo o que estiver ao nosso alcance para o ajudar a poupar o máximo de dinheiro possível nas suas compras diárias, para que possa comprar o carro dos seus sonhos, a sua roupa preferida, os seus aparelhos electrónicos ou pagar um seguro de qualidade. Esperamos que este folheto %leafletBrandLink%, válido de %validSince% a %validTill%, o ajude pelo menos um pouco e que fique mais perto dos seus sonhos!"
	smallTitle: "%brand% válido a partir de"	
	recommendedLeaflets: "Folhetos populares"
	similarLeaflets: " Outros folhetos %brand%"
	backToLeaflets: "Voltar à lista de todos os folhetos"	
	allBrandLeaflets: "Todos os folhetos %brand%"
	goToShop: "Ir para a loja"

shops:
	title: "Lojas"
	text: "Uma seleção dos retalhistas mais populares cujos novos folhetos lhe trazemos todos os dias."

shop:
	leaflets: "folhetos"
	text: "O último folheto da %marca% com boas ofertas."
	button: "Ir para a loja %brand%"	
	noLeaflets: "Estamos à sua procura para o último folheto... Por favor, tente novamente mais tarde."
	otherShops: "Outras lojas."
	defaultTitleSuffic: '%shopName% - último folheto, produtos à venda'
	otherLeaflets: "Outros folhetos %brand%"
	type:
		shopTitle: "{$shopName|upper} folheto {if $currentLeafletFromDate} de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + folheto de ação na próxima semana"
		eshopTitle: "%brand% desconto"
		eshop: "PVeja as últimas promoções de %brand% no seu catálogo cheio de inspiração e pechinchas. Os descontos %brand% mais recentes estão sempre disponíveis, para que nunca perca uma mercadoria com desconto."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} folheto da próxima semana de {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + folheto atual"
	     withCurrentLeaflet: "{$shopName|upper} folheto na próxima semana + folheto atual de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper}folheto na próxima semana + folheto promocional atual em linha"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} folheto da próxima semana ✅ Navegar no especial {$shopName|upper} CARTA DA PRÓXIMA SEMANA de {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Também está disponível online o PDF atual do folheto {$shopName|upper} desta semana."
	    withCurrentLeaflet: "{$shopName|upper} folheto para a próxima semana ✅ Veja o folheto especial {$shopName|upper} FLYER para a próxima semana. Também disponível online está o folheto atual em PDF {$shopName|upper} para esta semana a partir de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} folheto para a próxima semana ✅ Veja o folheto especial {$shopName|upper} FLYER para a próxima semana. Também está disponível online o flyer atual em PDF {$shopName|upper} com as promoções desta semana."

tag:
	text: "Oferta dos últimos folhetos da categoria %tag%."
	noLeaflets: "Estamos à sua procura para o último folheto... Por favor, tente novamente mais tarde."
	otherShops: "Outras lojas"	

about:
	title: "Sobre nós"
	text: "O nosso objetivo é poupar tempo e dinheiro aos utilizadores. Todos os dias, apresentamos-lhe folhetos actualizados dos retalhistas mais populares e poupamos-lhe tempo na procura de ofertas especiais de produtos."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
