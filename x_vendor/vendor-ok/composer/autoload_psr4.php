<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Urlbox\\Screenshots\\' => array($vendorDir . '/urlbox/screenshots/src'),
    'Ublaboo\\DataGrid\\' => array($vendorDir . '/ublaboo/datagrid/src'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Php73\\' => array($vendorDir . '/symfony/polyfill-php73'),
    'Symfony\\Polyfill\\Php72\\' => array($vendorDir . '/symfony/polyfill-php72'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\PropertyInfo\\' => array($vendorDir . '/symfony/property-info'),
    'Symfony\\Component\\PropertyAccess\\' => array($vendorDir . '/symfony/property-access'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\DomCrawler\\' => array($vendorDir . '/symfony/dom-crawler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Component\\BrowserKit\\' => array($vendorDir . '/symfony/browser-kit'),
    'Spatie\\PdfToImage\\' => array($vendorDir . '/spatie/pdf-to-image/src'),
    'React\\Promise\\' => array($vendorDir . '/react/promise/src'),
    'React\\EventLoop\\' => array($vendorDir . '/react/event-loop/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'Predis\\' => array($vendorDir . '/predis/predis/src'),
    'Nettrine\\ORM\\' => array($vendorDir . '/nettrine/orm/src'),
    'Nettrine\\DBAL\\' => array($vendorDir . '/nettrine/dbal/src'),
    'Nettrine\\Cache\\' => array($vendorDir . '/nettrine/cache/src'),
    'Nettrine\\Annotations\\' => array($vendorDir . '/nettrine/annotations/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Goutte\\' => array($vendorDir . '/fabpot/goutte/Goutte'),
    'Doctrine\\Persistence\\' => array($vendorDir . '/doctrine/persistence/src/Persistence'),
    'Doctrine\\ORM\\' => array($vendorDir . '/doctrine/orm/lib/Doctrine/ORM'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/lib/Doctrine/Deprecations'),
    'Doctrine\\DBAL\\' => array($vendorDir . '/doctrine/dbal/src'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/lib/Doctrine/Common/Lexer'),
    'Doctrine\\Common\\Collections\\' => array($vendorDir . '/doctrine/collections/lib/Doctrine/Common/Collections'),
    'Doctrine\\Common\\Cache\\' => array($vendorDir . '/doctrine/cache/lib/Doctrine/Common/Cache'),
    'Doctrine\\Common\\Annotations\\' => array($vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations'),
    'Doctrine\\Common\\' => array($vendorDir . '/doctrine/common/src', $vendorDir . '/doctrine/event-manager/lib/Doctrine/Common'),
    'DoctrineExtensions\\' => array($vendorDir . '/beberlei/doctrineextensions/src'),
    'Ddeboer\\Imap\\' => array($vendorDir . '/ddeboer/imap/src'),
    'Contributte\\Redis\\' => array($vendorDir . '/contributte/redis/src'),
    'Contributte\\RabbitMQ\\' => array($vendorDir . '/contributte/rabbitmq/src'),
    'Contributte\\Events\\Extra\\' => array($vendorDir . '/contributte/event-dispatcher-extra/src'),
    'Contributte\\EventDispatcher\\' => array($vendorDir . '/contributte/event-dispatcher/src'),
    'Contributte\\DI\\' => array($vendorDir . '/contributte/di/src'),
    'Contributte\\Console\\Extra\\' => array($vendorDir . '/contributte/console-extra/src'),
    'Contributte\\Console\\' => array($vendorDir . '/contributte/console/src'),
    'Contributte\\Application\\' => array($vendorDir . '/contributte/application/src'),
    'ContextIO\\' => array($vendorDir . '/contextio/php-contextio/src/ContextIO'),
    'Bunny\\' => array($vendorDir . '/bunny/bunny/src/Bunny'),
);
