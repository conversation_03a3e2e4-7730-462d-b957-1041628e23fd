{"name": "predis/predis", "type": "library", "description": "Flexible and feature-complete Redis client for PHP and HHVM", "keywords": ["nosql", "redis", "predis"], "homepage": "http://github.com/predis/predis", "license": "MIT", "support": {"issues": "https://github.com/predis/predis/issues"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/tillkruss"}], "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol", "ext-curl": "Allows access to Webdis when paired with phpiredis"}, "autoload": {"psr-4": {"Predis\\": "src/"}}, "scripts": {"post-update-cmd": "@php -f tests/apply-patches.php"}}