{"name": "doctrine/collections", "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "license": "MIT", "type": "library", "keywords": ["php", "collections", "array", "iterators"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "homepage": "https://www.doctrine-project.org/projects/collections.html", "require": {"php": "^8.1", "doctrine/deprecations": "^1"}, "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^10.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.11"}, "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}