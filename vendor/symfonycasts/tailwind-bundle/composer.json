{"name": "symfonycasts/tailwind-bundle", "description": "Delightful Tailwind Support for Symfony + AssetMapper", "license": "MIT", "type": "library", "keywords": ["asset-mapper", "tailwind"], "authors": [{"name": "<PERSON>", "homepage": "https://symfonycasts.com"}], "require": {"php": ">=8.1", "symfony/asset-mapper": "^6.3|^7.0", "symfony/console": "^5.4|^6.3|^7.0", "symfony/http-client": "^5.4|^6.3|^7.0", "symfony/process": "^5.4|^6.3|^7.0", "symfony/cache": "^6.3|^7.0", "symfony/deprecation-contracts": "^2.2|^3.0"}, "require-dev": {"symfony/filesystem": "^6.3|^7.0", "symfony/framework-bundle": "^6.3|^7.0", "symfony/phpunit-bridge": "^6.3.9|^7.0", "phpunit/phpunit": "^9.6"}, "minimum-stability": "dev", "autoload": {"psr-4": {"Symfonycasts\\TailwindBundle\\": "src"}}, "autoload-dev": {"psr-4": {"Symfonycasts\\TailwindBundle\\Tests\\": "tests/"}}, "scripts": {"tools:upgrade": ["@tools:upgrade:php-cs-fixer", "@tools:upgrade:phpstan"], "tools:upgrade:php-cs-fixer": "composer upgrade -W -d tools/php-cs-fixer", "tools:upgrade:phpstan": "composer upgrade -W -d tools/phpstan", "tools:run": ["@tools:run:php-cs-fixer", "@tools:run:phpstan"], "tools:run:php-cs-fixer": "tools/php-cs-fixer/vendor/bin/php-cs-fixer fix", "tools:run:phpstan": "tools/phpstan/vendor/bin/phpstan --memory-limit=1G"}}