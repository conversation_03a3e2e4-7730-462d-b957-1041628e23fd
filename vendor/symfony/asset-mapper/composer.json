{"name": "symfony/asset-mapper", "type": "library", "description": "Maps directories of assets & makes them available in a public directory with versioned filenames.", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.2", "composer/semver": "^3.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^7.1", "symfony/http-client": "^6.4|^7.0"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/event-dispatcher-contracts": "^3.0", "symfony/finder": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0"}, "conflict": {"symfony/framework-bundle": "<6.4"}, "autoload": {"psr-4": {"Symfony\\Component\\AssetMapper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}